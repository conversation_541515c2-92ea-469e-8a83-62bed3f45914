package com.deepinnet.spatiotemporalplatform.base.infra;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.dto.DictionaryDTO;
import com.deepinnet.spatiotemporalplatform.dto.DictionaryQueryDTO;

/**
 * 字典服务接口
 *
 * <AUTHOR>
 */
public interface DictionaryService {

    /**
     * 保存字典
     *
     * @param dictionaryDTO 字典数据
     * @return 已保存的字典ID
     */
    Long saveDictionary(DictionaryDTO dictionaryDTO);

    /**
     * 更新字典
     *
     * @param dictionaryDTO 字典数据
     * @return 更新是否成功
     */
    boolean updateDictionary(DictionaryDTO dictionaryDTO);

    /**
     * 根据ID获取字典
     *
     * @param id 字典ID
     * @return 字典数据
     */
    DictionaryDTO getDictionaryById(Long id);

    /**
     * 分页查询字典
     *
     * @param queryDTO 查询条件
     * @return 分页查询结果
     */
    CommonPage<DictionaryDTO> pageQueryDictionary(DictionaryQueryDTO queryDTO);

    /**
     * 删除字典
     *
     * @param id 字典ID
     * @return 删除是否成功
     */
    boolean deleteDictionary(Long id);
} 