package com.deepinnet.spatiotemporalplatform.base.dingding;

import com.github.jaemon.dinger.core.annatations.DingerMarkdown;
import com.github.jaemon.dinger.core.annatations.Parameter;

/**

 *
 * <AUTHOR>
 * @since 2024-12-03 星期二
 **/
public interface MonitorDinger {

    // 发送markdown类型消息
    @DingerMarkdown(title = "服务挂了",
            value = "#### 探活失败\n - 应用名称：${applicationName}\n - 环境： ${env}\n - 已死亡时间： ${seconds}秒")
    void heartbeatsDied(@Parameter("applicationName") String applicationName, @Parameter("env") String env, @Parameter("seconds") Long seconds);

    @DingerMarkdown(title = "服务挂了",
            value = "#### 上报阿里云心跳失败\n - 请检查阿里云环境是否运行正常！！！")
    void renewalFail();

    @DingerMarkdown(title = "服务出现异常",
            value = "#### 程序报错\n - 应用名称：${applicationName}\n - 环境： ${env}\n - errorMessage： ${errorMessage}\n - stack：${error} ")
    void error(@Parameter("applicationName") String applicationName, @Parameter("env") String env, @Parameter("errorMessage") String errorMessage, @Parameter("error") String error);
}
