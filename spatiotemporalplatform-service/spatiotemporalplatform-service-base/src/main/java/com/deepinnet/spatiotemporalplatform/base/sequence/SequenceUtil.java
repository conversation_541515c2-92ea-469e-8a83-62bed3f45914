package com.deepinnet.spatiotemporalplatform.base.sequence;

import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import lombok.experimental.UtilityClass;

import java.time.LocalDateTime;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;
import static cn.hutool.core.date.DatePattern.PURE_DATE_PATTERN;
import static cn.hutool.core.date.DatePattern.PURE_TIME_PATTERN;

/**
 * <AUTHOR>
 * @since 2024-07-29 星期一
 **/
@UtilityClass
public class SequenceUtil {

    /**
     * 随机后缀缓存
     */
    private final Cache<String, String> SEQUENCE_CACHE = CacheUtil.newFIFOCache(10, 1000);


    /**
     * fixme 单机后缀重复已经使用缓存解决了 多机器部署可能还会出现重复 需要解决 可增加一位机器号来解决
     * 生成序列号
     * 规则 年月日 + 类型 + 时分秒 + 4位随机数
     *
     * @param sequenceTypeEnum @see SequenceTypeEnum
     */
    public String getSequence(SequenceTypeEnum sequenceTypeEnum) {
        LocalDateTime now = LocalDateTime.now();
        String key = LocalDateTimeUtil.format(now, NORM_DATETIME_PATTERN);
        String dateStr = LocalDateTimeUtil.format(now, PURE_DATE_PATTERN);
        String timeStr = LocalDateTimeUtil.format(now, PURE_TIME_PATTERN);

        return dateStr + sequenceTypeEnum.getType() + timeStr + randomSuffix(key);
    }

    private String randomSuffix(String key) {
        String randomSuffix = RandomUtil.randomNumbers(4);
        while (true) {
            String suffixStrs = SEQUENCE_CACHE.get(key);
            if (StrUtil.isNotBlank(suffixStrs)) {
                if (suffixStrs.contains(randomSuffix)) {
                    randomSuffix = RandomUtil.randomNumbers(4);
                } else {
                    suffixStrs += (randomSuffix);
                    SEQUENCE_CACHE.put(key, suffixStrs);
                    break;
                }
            } else {
                SEQUENCE_CACHE.put(key, randomSuffix);
            }
        }
        return randomSuffix;
    }
}
