package com.deepinnet.spatiotemporalplatform.base.lock;

import lombok.Getter;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * Description:
 * Date: 2024/10/5
 * Author: lijunheng
 */
@Getter
public class ExpirationEntry {


    private String lockName;

    private String threadId;

    private AtomicInteger count = new AtomicInteger(1);

    public ExpirationEntry(String lockName, String threadId) {
        this.lockName = lockName;
        this.threadId = threadId;
    }

    public void addCount() {
        count.incrementAndGet();
    }

    public Integer reduceCount() {
        return count.decrementAndGet();
    }
}
