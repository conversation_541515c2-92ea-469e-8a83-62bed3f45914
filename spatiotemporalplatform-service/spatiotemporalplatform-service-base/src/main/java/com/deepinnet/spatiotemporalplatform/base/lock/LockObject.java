package com.deepinnet.spatiotemporalplatform.base.lock;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Description:
 * Date: 2024/9/27
 * Author: lijunheng
 */
@Data
public class LockObject implements Serializable {

    private String lockName;

    private String machineId;

    private Date expireTime;

    private Boolean isLocked;

    private Integer state;

    private String threadId;
}
