package com.deepinnet.spatiotemporalplatform.base.enums;

import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024-08-22
 **/
@Getter
@AllArgsConstructor
public enum SourceCityTypeEnum {
    CITY(1, "城市"),
    STREET(3, "街道"),
    GRID_1000(4, "1000米网格"),
    ;
    private final Integer type;
    private final String desc;

    public static SourceCityTypeEnum getEnum(Integer type) {
        for (SourceCityTypeEnum typeEnum : SourceCityTypeEnum.values()) {
            if (ObjectUtil.equals(type, typeEnum.getType())) {
                return typeEnum;
            }
        }

        return null;
    }
}
