package com.deepinnet.spatiotemporalplatform.base.convert;

import com.deepinnet.spatiotemporalplatform.dal.dataobject.SurveillanceCamerasMarkDO;
import com.deepinnet.spatiotemporalplatform.model.camera.SurveillanceCamerasMark;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * GeneralConvert
 * Author: chenkaiyang
 * Date: 2024/7/31
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SurveillanceCamerasMarkConvert extends Convert<SurveillanceCamerasMarkDO, SurveillanceCamerasMark> {

}
