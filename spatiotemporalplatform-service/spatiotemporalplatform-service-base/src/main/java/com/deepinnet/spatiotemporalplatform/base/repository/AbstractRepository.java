package com.deepinnet.spatiotemporalplatform.base.repository;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.deepinnet.spatiotemporalplatform.base.convert.Convert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.core.ResolvableType;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 抽象仓储层
 * 提供基于模型的基础增删改查能力
 * DO为数据库对象，Model为模型对象
 * Author: chenkaiyang
 * Date: 2023/8/16
 */
public abstract class AbstractRepository<DO extends Model<DO>, Domain> {

    /**
     * 默认批次提交数量
     */
    int _BATCH_SIZE = 1000;

    @Autowired
    private ApplicationContext applicationContext;

    protected IService<DO> iService;

    protected Convert<DO, Domain> convert;

    @Autowired
    public void init(Convert<DO, Domain> convert) {
        this.convert = convert;

        // 解析泛型 `DO`
        Class<DO> doClass = (Class<DO>) ResolvableType.forClass(getClass())
                .as(AbstractRepository.class)
                .getGenerics()[0]
                .resolve();

        // 获取所有 `IService` 实现
        Map<String, IService> serviceBeans = applicationContext.getBeansOfType(IService.class);

        // 过滤出匹配 `DO` 的 `IService<DO>`
        for (IService<?> service : serviceBeans.values()) {
            Class<?> entityClass = ResolvableType.forClass(service.getClass())
                    .as(IService.class)
                    .getGenerics()[0]
                    .resolve();
            if (entityClass != null && entityClass.equals(doClass)) {
                this.iService = (IService<DO>) service;
                return;
            }
        }

        throw new RuntimeException("未找到合适的 IService<" + doClass.getSimpleName() + "> 实现");
    }

     protected  <E extends IPage<DO>> E page(E page, Wrapper<DO> queryWrapper) {
        return iService.page(page, queryWrapper);
    }

    /**
     * 获取一条记录
     *
     * @param queryWrapper
     * @return
     */
    protected Domain getOne(Wrapper<DO> queryWrapper) {
        DO one = iService.getOne(queryWrapper);
        return convert.toModel(one);
    }

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param model 模型对象
     */
    @Transactional(rollbackFor = Exception.class)
    public long save(Domain model) {
        DO aDo = convert.toDO(model);
        getBaseMapper().insert(aDo);
        Serializable serializable = aDo.pkVal();
        if (serializable instanceof Number) {
            return ((Number) serializable).longValue();
        } else {
            throw new IllegalArgumentException("Primary key value is not a number");
        }
    }

    /**
     * 插入（批量）
     *
     * @param entityList 模型对象集合
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(List<Domain> entityList) {
        return iService.saveBatch(convert.toDOList(entityList), _BATCH_SIZE);
    }

    /**
     * <p>
     * 根据updateWrapper尝试更新，否继续执行saveOrUpdate(T)方法
     * 此次修改主要是减少了此项业务代码的代码量（存在性验证之后的saveOrUpdate操作）
     * </p>
     *
     * @param entity 模型对象
     */
    protected boolean saveOrUpdateEntity(Domain entity, Wrapper<DO> updateWrapper) {
        DO aDo = convert.toDO(entity);
        return iService.update(aDo, updateWrapper) || iService.saveOrUpdate(aDo);
    }

    /**
     * 批量修改插入
     *
     * @param entityList 模型对象集合
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateBatch(List<Domain> entityList) {
        return iService.saveOrUpdateBatch(convert.toDOList(entityList), _BATCH_SIZE);
    }

    /**
     * 根据 ID 删除
     *
     * @param id 主键ID
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean removeById(Serializable id) {
        return SqlHelper.retBool(getBaseMapper().deleteById(id));
    }


    /**
     * 根据 entity 条件，删除记录
     *
     * @param queryWrapper 实体包装类 {@link com.baomidou.mybatisplus.core.conditions.query.QueryWrapper}
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean remove(Wrapper<DO> queryWrapper) {
        return SqlHelper.retBool(getBaseMapper().delete(queryWrapper));
    }


    /**
     * 删除（根据ID 批量删除）
     *
     * @param list 主键ID或实体列表
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIds(Collection<?> list) {
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        return SqlHelper.retBool(getBaseMapper().deleteBatchIds(list));
    }

    /**
     * 根据 ID 选择修改
     *
     * @param entity 模型对象
     */
    public boolean updateById(Domain entity) {
        return SqlHelper.retBool(getBaseMapper().updateById(convert.toDO(entity)));
    }

    /**
     * 根据ID 批量更新
     *
     * @param entityList 模型对象集合
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatchById(List<Domain> entityList) {
        return iService.updateBatchById(convert.toDOList(entityList), _BATCH_SIZE);
    }

    /**
     * 根据 ID 查询
     *
     * @param id 主键ID
     */
    Domain getById(Serializable id) {
        return convert.toModel(getBaseMapper().selectById(id));
    }

    /**
     * 查询（根据ID 批量查询）
     *
     * @param idList 主键ID列表
     */
    List<Domain> listByIds(Collection<? extends Serializable> idList) {
        return convert.toModelList(getBaseMapper().selectBatchIds(idList));
    }

    /**
     * 查询列表
     *
     * @param queryWrapper 模型对象封装操作类 {@link com.baomidou.mybatisplus.core.conditions.query.QueryWrapper}
     */
    public List<Domain> list(Wrapper<DO> queryWrapper) {
        return convert.toModelList(getBaseMapper().selectList(queryWrapper));
    }

    /**
     * 查询所有
     *
     * @see Wrappers#emptyWrapper()
     */
    public List<Domain> list() {
        return list(Wrappers.emptyWrapper());
    }

    /**
     * 获取对应 entity 的 BaseMapper
     *
     * @return BaseMapper
     */
    BaseMapper<DO> getBaseMapper() {
        return iService.getBaseMapper();
    }

    /**
     * 获取 entity 的 class
     *
     * @return {@link Class< DO >}
     */
    Class<DO> getEntityClass() {
        return iService.getEntityClass();
    }

    /**
     * 根据 whereEntity 条件，更新记录
     *
     * @param entity        模型对象
     * @param updateWrapper 模型对象封装操作类 {@link com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper}
     */
    public boolean update(Domain entity, Wrapper<DO> updateWrapper) {
        return SqlHelper.retBool(getBaseMapper().update(convert.toDO(entity), updateWrapper));
    }

     protected boolean update(Wrapper<DO> updateWrapper) {
        return iService.update(updateWrapper);
    }


}
