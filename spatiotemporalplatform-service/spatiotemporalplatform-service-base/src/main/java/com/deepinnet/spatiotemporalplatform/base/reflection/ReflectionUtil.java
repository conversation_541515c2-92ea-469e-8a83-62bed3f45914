package com.deepinnet.spatiotemporalplatform.base.reflection;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2024-07-29
 */
public class ReflectionUtil {
    private static Field[] getAllFields(Class<?> clazz) {
        if (clazz == null) {
            return new Field[0];
        }

        Field[] declaredFields = clazz.getDeclaredFields();
        Field[] fields = getAllFields(clazz.getSuperclass());

        Field[] result = new Field[declaredFields.length + fields.length];
        System.arraycopy(declaredFields, 0, result, 0, declaredFields.length);
        System.arraycopy(fields, 0, result, declaredFields.length, fields.length);

        return result;
    }

    /**
     * 获取对象的所有字段及其值，并转换为键值对
     *
     * @param obj 要转换的对象
     * @return 包含对象字段及其值的键值对映射
     * @throws IllegalAccessException
     */
    public static Map<String, String> getObjectFields(Object obj) {
        Map<String, String> fieldMap = new HashMap<>();
        // 获取所有字段，包括父类
        Field[] fields = getAllFields(obj.getClass());

        for (Field field : fields) {
            field.setAccessible(true);
            try {
                fieldMap.put(field.getName(), String.valueOf(field.get(obj)));
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }

        return fieldMap;
    }

}