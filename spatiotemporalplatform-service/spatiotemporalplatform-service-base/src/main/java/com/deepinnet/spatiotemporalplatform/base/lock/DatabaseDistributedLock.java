package com.deepinnet.spatiotemporalplatform.base.lock;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.deepinnet.spatiotemporalplatform.base.repository.LockObjectRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Description: 数据库锁实现，支持可重入
 * Date: 2024/9/27
 * Author: lijunheng
 */
@Slf4j
@Service
public class DatabaseDistributedLock implements DistributedLock {

    private String machineId = UUID.randomUUID().toString();

    private long internalLockLeaseTime = 30 * 1000;

    /**
     * key: lockName, value: 锁的过期续约
     */
    private ConcurrentHashMap<String, ExpirationEntry> expirationRenewalMap = new ConcurrentHashMap<>();

    /**
     * 订阅锁释放的锁对象
     * key：lockName, value: 内置锁对象
     */
    private ConcurrentHashMap<String, LockEntry> subscribeMap = new ConcurrentHashMap<>();

    private ScheduledThreadPoolExecutor scheduledExecutor = new ScheduledThreadPoolExecutor(Runtime.getRuntime().availableProcessors() * 2 + 1,
            ThreadFactoryBuilder.create().setNamePrefix("EXPIRE-LOCK-INNER-RENEW-THREAD-").build());

    @Resource
    private LockObjectRepository lockRepository;

    @PostConstruct
    public void process() {
        scheduledExecutor.scheduleAtFixedRate(() -> {
            //执行本机订阅这把锁的检查任务
            List<String> needCheckLockNameList = subscribeMap.entrySet().stream()
                    .filter(entry -> entry.getValue().getCounter().get() != 0)
                    .map(entry -> entry.getKey())
                    .collect(Collectors.toList());
            //查询已经过期或者释放的锁
            List<String> lockNameList = lockRepository.queryAllowObtainLockList(needCheckLockNameList);

            //执行对应锁的唤醒操作
            lockNameList.forEach(lockName -> {
                LockEntry lockEntry = subscribeMap.get(lockName);
                if (lockEntry != null) {
                    //这里最多多唤醒一次，无非就是让等待线程多抢占一次，没什么关系，这种场景发生在tryAcquire正好过期，定时任务正好运行
                    //多一次判断可以大幅度减少冲突时多释放的信号
                    Semaphore semaphore = lockEntry.getLatch();
                    if (semaphore.hasQueuedThreads()) {
                        semaphore.release();
                        log.info("定时任务发起唤醒等待锁的通知");
                    }
                }
            });
        }, 0, 1, TimeUnit.SECONDS);
    }

    @Override
    public boolean tryLock(String lockName) {
        return tryLock(lockName, -1L, -1L, TimeUnit.MILLISECONDS);
    }

    @Override
    public boolean tryLock(String lockName, Long waitTime, TimeUnit timeUnit) {
        return tryLock(lockName, waitTime, -1L, timeUnit);
    }

    @Override
    public boolean tryLock(String lockName, Long waitTime, Long leaseTime, TimeUnit timeUnit) {
        long startTime = System.currentTimeMillis();
        String threadId = getCurrentThreadId();
        Long ttl = tryAcquire(lockName, leaseTime, timeUnit);
        // lock acquired
        if (ttl == null) {
            return true;
        }

        long time = timeUnit.toMillis(waitTime);
        if (waitTime != -1 && System.currentTimeMillis() - startTime < time) {
            //没有获取到锁，也没到等待时长，执行订阅释放锁的任务
            LockEntry lockEntry = subscribe(lockName, threadId, () -> {
            });

            try {
                while (true) {
                    ttl = tryAcquire(lockName, leaseTime, timeUnit);
                    // lock acquired
                    if (ttl == null) {
                        return true;
                    }
                    long remainTtl = time - System.currentTimeMillis() + startTime;
                    if (remainTtl < 0) {
                        return false;
                    }

                    // waiting for message
                    lockEntry.getLatch().tryAcquire(ttl >= 0 && ttl < remainTtl ? ttl : remainTtl, TimeUnit.MILLISECONDS);
                }
            } catch (InterruptedException e) {
                log.error("thread interrupted", e);
                throw new RuntimeException(e);
            } finally {
                unsubscribe(lockEntry, lockName);
            }
        } else {
            return false;
        }
    }

    @Override
    public void lock(String lockName) {
        lock(lockName, -1, TimeUnit.MILLISECONDS);
    }

    @Override
    public void lock(String lockName, long leaseTime, TimeUnit unit) {
        LockEntry lockEntry = null;

        try {
            while (true) {
                // 尝试获取锁
                Long ttl = tryAcquire(lockName, leaseTime, unit);

                if (ttl == null) {
                    // 成功获取到锁，直接退出
                    break;
                }

                // 未获取到锁，订阅锁释放通知（如果还没订阅）
                if (lockEntry == null) {
                    lockEntry = subscribe(lockName, getCurrentThreadId(), () -> {
                    });
                }

                // 等待锁释放通知，直到TTL时间结束
                try {
                    lockEntry.getLatch().tryAcquire(ttl, TimeUnit.MILLISECONDS);
                } catch (InterruptedException e) {
                    // 恢复线程的中断状态
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Thread was interrupted while waiting for the lock", e);
                }
            }
        } finally {
            // 确保在退出时释放锁并取消订阅
            if (lockEntry != null) {
                unsubscribe(lockEntry, getCurrentThreadId());
            }
        }
    }

    @Override
    public void unlock(String lockName) {
        if (releaseLock(lockName)) {
            //释放锁成功后去除看门狗的续期
            //如果解锁失败，比如自己获取到锁过期了，然后又去释放锁，因为他没有续约任务所以不需要移除
            cancelExpirationRenewal(lockName, getCurrentThreadId());

            //发送锁释放的通知
            // 这里只处理本机维护的等待锁的线程，其他的机器数据库没法主动发出通知，需要轮训或者由获取锁的线程下次获取锁时自行处理
            LockEntry lockEntry = subscribeMap.get(lockName);
            //要判空，因为如果没有阻塞中的线程，那么lockEntry会为空
            if (lockEntry != null) {
                Semaphore semaphore = lockEntry.getLatch();
                if (semaphore.hasQueuedThreads()) {
                    semaphore.release();
                }
            }
        }
    }

    @Override
    public String getMachineId() {
        return machineId;
    }

    /**
     * 订阅锁释放的通知，并返回这个锁的信息
     *
     * @param lockName
     * @return
     */
    protected LockEntry subscribe(String lockName, String threadId, Runnable listener) {
        LockEntry lockEntry = new LockEntry(lockName);
        LockEntry oldLockEntry = subscribeMap.putIfAbsent(lockName, lockEntry);
        if (oldLockEntry != null) {
            oldLockEntry.acquire();
            oldLockEntry.putListener(threadId, listener);
            return oldLockEntry;
        } else {
            lockEntry.acquire();
            lockEntry.putListener(threadId, listener);
            return lockEntry;
        }
    }

    /**
     * 获取锁后执行的方法
     *
     * @param lockEntry
     * @param threadId
     */
    protected void unsubscribe(LockEntry lockEntry, String threadId) {
        //减少等待锁的标记
        lockEntry.removeListener(threadId);
        lockEntry.release();
    }

    private Long tryAcquire(String lockName, long leaseTime, TimeUnit unit) {
        String currentThreadId = getCurrentThreadId();
        //设定了自动释放锁的时间
        if (leaseTime != -1) {
            return tryLockInner(leaseTime, unit, lockName, currentThreadId);
        }
        //没有设置自动过期时间，就需要在获取到之后使用看门狗续期
        Long remainTtl = tryLockInner(internalLockLeaseTime, TimeUnit.MILLISECONDS, lockName, currentThreadId);
        // lock acquired
        if (remainTtl == null) {
            scheduleExpirationRenewal(lockName, currentThreadId);
        }
        return remainTtl;
    }

    /**
     * 加锁成功返回null，否则返回锁的过期时间
     *
     * @param leaseTime
     * @param unit
     * @param lockName
     * @param threadId
     * @return
     */
    private Long tryLockInner(long leaseTime, TimeUnit unit, String lockName, String threadId) {
        long internalLockLeaseTime = unit.toMillis(leaseTime);

        //查询是否存在锁
        LockObject existLock = lockRepository.queryLock(lockName);
        LockObject lockObject = new LockObject();
        lockObject.setLockName(lockName);
        lockObject.setThreadId(threadId);
        lockObject.setMachineId(machineId);
        lockObject.setIsLocked(true);
        lockObject.setExpireTime(new Date(System.currentTimeMillis() + internalLockLeaseTime));
        if (existLock == null) {
            //保存锁
            lockObject.setState(1);
            try {
                lockRepository.save(lockObject);
            } catch (Exception e) {
                //抛出数据重复异常，说明被其他线程锁定了
                //返回需要等待的时间
                log.error("lock other thread occupy", e);
                return reCheckTtl(leaseTime, unit, lockName, threadId);
            }
        } else {
            //存在的锁会判断是否是当前线程的，如果是也允许加锁成功，支持可重入
            //如果正好其他锁释放了，那也会抢锁，具体是否公平由各数据库的内部锁决定
            int updateNum = lockRepository.reentrantLock(lockObject);
            if (updateNum == 0) {
                //返回需要等待的时间
                return reCheckTtl(leaseTime, unit, lockName, threadId);
            }
        }
        //加锁成功
        return null;
    }

    private Long reCheckTtl(long leaseTime, TimeUnit unit, String lockName, String threadId) {
        Long ttl = queryLockTtl(lockName);
        if (ttl == null) {
            //如果返回null，那就是获取锁的时候失败了，但是执行查询锁的过期时间的时候释放了
            //就需要重新执行上锁逻辑
            return tryLockInner(leaseTime, unit, lockName, threadId);
        } else {
            return ttl;
        }
    }

    /**
     * 获取锁的释放时间，单位毫秒，
     * 如果锁不存在 或者 未上锁 或者 已过期 则返回null
     *
     * @param lockName
     * @return
     */
    private Long queryLockTtl(String lockName) {
        LockObject lockObject = lockRepository.queryLock(lockName);
        if (lockObject != null && lockObject.getExpireTime() != null) {
            long intervalTime = lockObject.getExpireTime().getTime() - System.currentTimeMillis();
            if (intervalTime > 0) {
                return intervalTime;
            }
        }
        return null;
    }

    /**
     * 获取锁的时候生成续期任务
     * 可能在续期执行的时候，其他线程正好做了移除续约任务
     * @param lockName
     * @param threadId
     */
    private void scheduleExpirationRenewal(String lockName, String threadId) {
        ExpirationEntry entry = new ExpirationEntry(lockName, threadId);
        ExpirationEntry oldEntry = expirationRenewalMap.putIfAbsent(expirationRenewalKey(lockName, threadId), entry);
        if (oldEntry != null) {
            oldEntry.addCount();
        } else {
            //只对第一次获取锁的线程续约，后面的属于重入
            renewExpiration(lockName, threadId);
        }
    }

    private void renewExpiration(String lockName, String threadId) {
        String keyName = expirationRenewalKey(lockName, threadId);
        ExpirationEntry ee = expirationRenewalMap.get(keyName);
        if (ee == null) {
            return;
        }

        //获取到锁后过1/3时间开启续约任务
        scheduledExecutor.schedule(() -> {
            ExpirationEntry ent = expirationRenewalMap.get(keyName);
            if (ent == null) {
                return;
            }

            boolean renewResult = renewExpirationLock(lockName, ent.getThreadId());
            if (!renewResult) {
                //更新失败说明锁被释放了
                log.debug("Can't update lock " + lockName + " expiration");
                expirationRenewalMap.remove(keyName);
                return;
            }
            // reschedule itself
            renewExpiration(lockName, threadId);
        }, internalLockLeaseTime / 3, TimeUnit.MILLISECONDS);

    }

    private void cancelExpirationRenewal(String lockName, String threadId) {
        String keyName = expirationRenewalKey(lockName, threadId);
        ExpirationEntry task = expirationRenewalMap.get(keyName);
        if (task == null) {
            return;
        }
        Integer count = task.reduceCount();

        if (count == 0) {
            expirationRenewalMap.remove(keyName);
        }
    }

    private String expirationRenewalKey(String lockName, String threadId) {
        return lockName + "_" + threadId;
    }

    /**
     * 续期
     *
     * @param lockName
     * @param threadId
     */
    private boolean renewExpirationLock(String lockName, String threadId) {
        LockObject lockObject = new LockObject();
        lockObject.setLockName(lockName);
        lockObject.setThreadId(threadId);
        lockObject.setMachineId(machineId);
        lockObject.setExpireTime(new Date(System.currentTimeMillis() + internalLockLeaseTime));
        int updateNum = lockRepository.renewExpirationLock(lockObject);
        return updateNum != 0;
    }

    /**
     * 释放锁
     *
     * @param lockName
     * @return
     */
    private boolean releaseLock(String lockName) {
        LockObject lockObject = new LockObject();
        lockObject.setLockName(lockName);
        lockObject.setThreadId(getCurrentThreadId());
        lockObject.setMachineId(machineId);
        int updateNum = lockRepository.releaseLock(lockObject);
        return updateNum != 0;
    }

    private String getCurrentThreadId() {
        return String.valueOf(Thread.currentThread().getId());
    }
}
