package com.deepinnet.spatiotemporalplatform.base.log;


import cn.hutool.core.exceptions.ExceptionUtil;
import com.deepinnet.digitaltwin.common.exception.BizException;

/**
 * Description:
 * Date: 2023/2/13
 * Author: lijunheng
 */
public class LogFormatHelper {

    private static final String ERROR_LOG_PATTERN = "[%s,%s,%s,%s,%s,%s,%s]";

    private static final String API_REQUEST_LOG_PATTERN = "[%s,%s,%s,%s]";

    /**
     * 占位符
     */
    private static final String PLACE_HOLDER = "null";

    public static String logFormat(String applicationName, String env, String className, String methodName, Throwable ex) {
        if (ex instanceof BizException) {
            BizException e = (BizException) ex;
            return String.format(ERROR_LOG_PATTERN,
                    applicationName, env, className, methodName, BizException.class.getName(), e.getErrorCode(), e.getMessage());
        } else {
            return String.format(ERROR_LOG_PATTERN,
                    applicationName, env, className, methodName, ex.getClass().getName(), PLACE_HOLDER, ExceptionUtil.getMessage(ex));
        }
    }

    public static String logFormat(String api, String params, String body, boolean isSuccess) {
        return String.format(API_REQUEST_LOG_PATTERN, api, params, body, isSuccess);
    }
}
