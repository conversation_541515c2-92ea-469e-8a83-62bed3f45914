package com.deepinnet.spatiotemporalplatform.base.sequence;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 　　　　　　　　┏┓　　　┏┓+ +
 * 　　　　　　　┏┛┻━━━┛┻┓ + +
 * 　　　　　　　┃　　　　　　　┃
 * 　　　　　　　┃　　　━　　　┃ ++ + + +
 * 　　　　　　 ████━████ ┃+
 * 　　　　　　　┃　　　　　　　┃ +
 * 　　　　　　　┃　　　┻　　　┃
 * 　　　　　　　┃　　　　　　　┃ + +
 * 　　　　　　　┗━┓　　　  ┏━┛
 * 　　　　　　　　　┃　　　┃
 * 　　　　　　　　　┃　　　┃ + + + +
 * 　　　　　　　　　┃　　　┃　　　　Code is far away from bug with the animal protecting
 * 　　　　　　　　　┃　　　┃ + 　　　　神兽保佑,代码无bug
 * 　　　　　　　　　┃　　　┃
 * 　　　　　　　　　┃　　　┃　　+
 * 　　　　　　　　　┃　 　　┗━━━┓ + +
 * 　　　　　　　　　┃ 　　　　　　　┣┓
 * 　　　　　　　　　┃ 　　　　　　　┏┛
 * 　　　　　　　　　┗┓┓┏━┳┓┏┛ + + + +
 * 　　　　　　　　　　┃┫┫　┃┫┫
 * 　　　　　　　　　　┗┻┛　┗┻┛+ + + +
 *
 * <AUTHOR>
 * @since 2024-07-29 星期一
 **/
@AllArgsConstructor
@Getter
public enum SequenceTypeEnum {

    AREA("001", "区域"),
    MARKED_CHANNEL("002", "通道"),
    MAIN_INTERSECTION("003", "路口"),
    CONTINGENCY_PLAN("004", "应急预案"),
    CONTINGENCY_PLAN_CONTROL_MEASURE("005", "应急预案控制措施"),
    SITE("006", "站点"),
    CIRCLE_LAYER("007", "圈层"),

    TRAFFIC_SECURITY_TASK("011", "交保任务"),
    TASK_ROUTE("012", "交保任务线路"),
    TASK_DISPATCH_INSTANCE("013", "交保任务下发实例"),
    TASK_EXECUTION_RECORD("014", "交保任务执行记录"),
    ;

    private final String type;

    private final String desc;

}
