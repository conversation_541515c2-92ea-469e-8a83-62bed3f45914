package com.deepinnet.spatiotemporalplatform.base.ftp;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.ftp.Ftp;
import cn.hutool.extra.ftp.FtpException;
import cn.hutool.extra.ftp.FtpMode;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.spatiotemporalplatform.base.config.FtpConfig;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;

import java.io.File;
import java.io.FilterInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 *    FTP工具类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/12
 */
public class FtpUtil {

    /**
     * 获取 FTPClient对象
     */
    private static Ftp getFTPClient() {
        try {
            if(StringUtils.isBlank(FtpConfig.getHost()) || FtpConfig.getPort() == null
                    || StringUtils.isBlank(FtpConfig.getUsername()) || StringUtils.isBlank(FtpConfig.getPassword())) {
                throw new BizException(FtpErrorCode.GET_FTP_CONFIG_ERROR.getCode(), FtpErrorCode.GET_FTP_CONFIG_ERROR.getDesc());
            }

            Ftp ftp = new Ftp(FtpConfig.getHost(), FtpConfig.getPort(), FtpConfig.getUsername(), FtpConfig.getPassword());
            //设置为被动模式，防止防火墙拦截
            ftp.setMode(FtpMode.Passive);
            return ftp;
        } catch (Exception e) {
            LogUtil.error("获取ftp客户端异常", e);
            throw new BizException(FtpErrorCode.GET_FTP_CLIENT_ERROR.getCode(), FtpErrorCode.GET_FTP_CLIENT_ERROR.getDesc());
        }
    }

    /**
     * 下载ftp服务器上的文件到本地
     * @param remoteFile    ftp上的文件路径
     * @param localPath     输出的目录，使用服务端的文件名
     */
    public static void download(String remoteFile, String localPath) {
        if(StringUtils.isBlank(remoteFile) || StringUtils.isBlank(localPath)) {
            return;
        }

        // 确保本地目录存在
        if (!FileUtil.exist(localPath)) {
            FileUtil.mkdir(localPath);
        }
        File lFile = FileUtil.file(localPath);

        try (Ftp ftp = getFTPClient()) {
            ftp.download(remoteFile, lFile);
        } catch (Exception e) {
            throw new BizException(FtpErrorCode.FTP_FILE_DOWNLOAD_ERROR.getCode(), FtpErrorCode.FTP_FILE_DOWNLOAD_ERROR.getDesc());
        }
    }

    /**
     * 下载FTP服务器上的文件，并返回文件流。
     * 注意：调用方在使用完该流后，必须调用 close() 方法，
     * 以便关闭FTP连接并调用 completePendingCommand() 完成传输。
     *
     * @param remoteFile FTP服务器上的文件路径
     * @return 文件输入流
     */
    public static InputStream download(String remoteFile) {
        if (StringUtils.isBlank(remoteFile)) {
            return null;
        }
        final Ftp ftp = getFTPClient();
        FTPClient ftpClient = ftp.getClient();
        try {
            InputStream is = ftpClient.retrieveFileStream(remoteFile);
            if (is == null) {
                throw new BizException(FtpErrorCode.FTP_FILE_DOWNLOAD_ERROR.getCode(),
                        FtpErrorCode.FTP_FILE_DOWNLOAD_ERROR.getDesc());
            }
            return new FilterInputStream(is) {
                @Override
                public void close() throws IOException {
                    try {
                        super.close();
                        // 检查FTP连接是否仍然有效
                        if (ftpClient.isConnected()) {
                            boolean success = false;
                            try {
                                success = ftpClient.completePendingCommand();
                            } catch (NullPointerException npe) {
                                // 可能是内部socket为null导致的异常
                                LogUtil.error("completePendingCommand 过程中出现空指针异常，可能FTP连接已关闭", npe);
                            }
                            if (!success) {
                                throw new BizException(FtpErrorCode.FTP_FILE_STREAM_COMPLETE_ERROR.getCode(),
                                        FtpErrorCode.FTP_FILE_STREAM_COMPLETE_ERROR.getDesc());
                            }
                        }
                    } finally {
                        ftp.close();
                    }
                }
            };
        } catch (IOException e) {
            LogUtil.error("Failed to load FTP file: {}", remoteFile, e);

            try {
                ftp.close();
            } catch (IOException ex) {
                LogUtil.error("Failed to close FTP connection for file: {}", remoteFile, ex);
            }
            throw new BizException(FtpErrorCode.FTP_FILE_DOWNLOAD_ERROR.getCode(),
                    FtpErrorCode.FTP_FILE_DOWNLOAD_ERROR.getDesc(), e);
        }
    }

    /**
     * 本地文件上传到ftp服务器上
     * @param remoteDir 上传的ftp目录
     * @param remoteFileName  保存到ftp服务器上的名称
     * @param fileStream 文件流
     */
    public static boolean upload(String remoteDir, String remoteFileName, InputStream fileStream) {
        if(StringUtils.isBlank(remoteDir) || StringUtils.isBlank(remoteFileName) || ObjectUtil.isNull(fileStream)) {
            return false;
        }

        try (Ftp ftp = getFTPClient()) {
            return ftp.upload(remoteDir, remoteFileName, fileStream);
        } catch (Exception e) {
            LogUtil.error("文件:{}, 上传至:{}, FTP异常", remoteFileName, remoteDir, e);
            return false;
        }
    }

    /**
     * 本地文件上传到ftp服务器上
     * @param remoteDir 上传的ftp目录
     * @param remoteFileName  保存到ftp服务器上的名称
     * @param localFile 本地文件全名称
     */
    public static boolean upload(String remoteDir, String remoteFileName, String localFile) {
        if(StringUtils.isBlank(remoteDir) || StringUtils.isBlank(remoteFileName) || StringUtils.isBlank(localFile)) {
            return false;
        }

        File lFile = FileUtil.file(localFile);
        if(!lFile.exists()) {
            LogUtil.error("本地文件:{}, 不存在", localFile);
            return false;
        }

        try (Ftp ftp = getFTPClient()) {
            if(StringUtils.isBlank(remoteFileName)) {
                return ftp.upload(remoteDir, lFile);
            } else {
                return ftp.upload(remoteDir, remoteFileName, lFile);
            }
        } catch (Exception e) {
            LogUtil.error("文件:{}, 上传FTP异常", localFile, e);
            return false;
        }
    }

    /**
     * 删除FTP服务器中的文件
     * @param remoteFile    ftp上的文件路径
     */
    public static boolean delFile(String remoteFile) {
        if(StringUtils.isBlank(remoteFile)) {
            return false;
        }

        try (Ftp ftp = getFTPClient()) {
            return ftp.delFile(remoteFile);
        } catch (Exception e) {
            LogUtil.error("删除FTP服务器中的文件:{}, 异常", remoteFile,e);
            return false;
        }
    }

    /**
     * 遍历某个目录下所有文件（非递归）
     * @param path    目录
     */
    public static List<String> listFile(String path) {
        List<String> listFile = new ArrayList<>();

        try (Ftp ftp = getFTPClient()) {
            FTPFile[] ftpFiles = ftp.lsFiles(path);
            for (FTPFile ftpFile : ftpFiles) {
                if (ftpFile.isFile()) {
                    listFile.add(ftpFile.getName());
                }
            }
            return listFile;
        } catch (FtpException e) {
            LogUtil.warn("遍历目录: {} 异常, 当前目录可能不存在! 异常信息: {}", path, e.getMessage());
            return null;
        } catch (Exception e) {
            LogUtil.error("遍历目录: {} 异常, 异常信息: {}", path, e.getMessage());
            return null;
        }
    }

    /**
     * 遍历某个目录下所有文件, 根据文件创建时间倒序排序取第一个文件 (非递归)
     * @param path    目录
     */
    public static String getLastFileByPath(String path) {
        List<String> listFile = new ArrayList<>();

        try (Ftp ftp = getFTPClient()) {
            FTPFile[] ftpFiles = ftp.lsFiles(path);
            Arrays.sort(ftpFiles, (f1, f2) -> f2.getTimestamp().compareTo(f1.getTimestamp()));
            for (FTPFile ftpFile : ftpFiles) {
                if (ftpFile.isFile()) {
                    listFile.add(ftpFile.getName());
                }
            }
            return listFile.get(0);
        } catch (FtpException e) {
            LogUtil.warn("遍历目录: {} 异常, 当前目录可能不存在! 异常信息: {}", path, e.getMessage());
            return null;
        } catch (Exception e) {
            LogUtil.error("遍历目录: {} 异常, 异常信息: {}", path, e.getMessage());
            return null;
        }

    }

    /**
     * 判断路径是否存在
     */
    public static boolean pathExist(String path) {
        try (Ftp ftp = getFTPClient()) {
            return ftp.exist(path);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 判断某个路径下的某个文件是否存在
     * @param remoteFile    ftp上的文件路径
     * @param fileName      文件名称
     */
    public static boolean fileExist(String remoteFile, String fileName) {
        if (StringUtils.isBlank(remoteFile) || StringUtils.isBlank(fileName)) {
            LogUtil.warn("remoteFile or fileName do not be null!");
            return false;
        }

        try (Ftp ftp = getFTPClient()) {

            String fullPathFile = remoteFile + "/" + fileName;

            if (!ftp.exist(fullPathFile)) {
                LogUtil.warn("this file:{}, not exist!", fullPathFile);
                return false;
            }

            return true;
        }catch (Exception e) {
            LogUtil.error("ftp client connection error");
            throw new BizException(FtpErrorCode.GET_FTP_CLIENT_ERROR.getCode(), FtpErrorCode.GET_FTP_CLIENT_ERROR.getDesc());
        }

    }

}
