package com.deepinnet.spatiotemporalplatform.base.config;

import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.thread.RejectPolicy;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.deepinnet.digitaltwin.common.trace.wrapper.ExecutorServiceWrapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

/**

 * 线程池配置类
 *
 * <AUTHOR>
 * @since 2024-08-09 星期五
 **/
@Configuration
public class ThreadPoolConfig {
    public static final int CPU_CORE_NUMBER = Runtime.getRuntime().availableProcessors();

    public static final int CORE_POOL_SIZE = CPU_CORE_NUMBER;

    public static final String COMMON_TASK_POOL_PREFIX = "common-task-pool-";

    /**
     * 通用任务线程池
     */
    @Bean(name = "commonTaskPool")
    public ExecutorService commonTaskPool() {
        ThreadPoolExecutor threadPoolExecutor = ExecutorBuilder
                .create().setCorePoolSize(CORE_POOL_SIZE)
                .setMaxPoolSize(200)
                .setThreadFactory(ThreadFactoryBuilder.create().setNamePrefix(COMMON_TASK_POOL_PREFIX).build())
                .setHandler(RejectPolicy.CALLER_RUNS.getValue()) //如果达到阻塞队列上限直接由调用线程执行)
                .setKeepAliveTime(60, TimeUnit.SECONDS)
                .useSynchronousQueue()//不存储阻塞队列直接new线程调用
                .build();
        //预热核心线程 等待任务提交
        threadPoolExecutor.prestartCoreThread();
        return ExecutorServiceWrapper.getTtlExecutorService(threadPoolExecutor);
    }

    @Bean(name = "applicationEventTaskExecutor")
    public ThreadPoolExecutor applicationEventTaskExecutor() {
        return ExecutorBuilder.create()
                .setCorePoolSize(4)
                .setMaxPoolSize(8)
                .setWorkQueue(new LinkedBlockingQueue<>(1000))
                .setThreadFactory(ThreadFactoryBuilder.create().setNamePrefix("applicationEvent-task-exec-").build())
                .setHandler(new ThreadPoolExecutor.CallerRunsPolicy()) // 拒绝策略：由调用者线程执行
                .build();
    }

    @Bean(name = "scheduleTaskExecutor")
    public ScheduledThreadPoolExecutor scheduleTaskExecutor() {
        return new ScheduledThreadPoolExecutor(10, ThreadFactoryBuilder.create().setNamePrefix("schedule-task-exec-").build(), new ThreadPoolExecutor.CallerRunsPolicy());
    }
}
