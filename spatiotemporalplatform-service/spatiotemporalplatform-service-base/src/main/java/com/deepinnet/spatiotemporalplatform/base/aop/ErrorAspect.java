package com.deepinnet.spatiotemporalplatform.base.aop;

import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.spatiotemporalplatform.base.exception.GDRequestException;
import com.deepinnet.spatiotemporalplatform.common.constants.GlobalConstant;
import com.deepinnet.spatiotemporalplatform.model.monitor.ErrorMonitorUrlParams;
import com.deepinnet.spatiotemporalplatform.model.monitor.MonitorRequest;
import com.deepinnet.spatiotemporalplatform.model.monitor.error.ErrorType;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.dao.DuplicateKeyException;

import javax.annotation.Resource;

/**

 *
 * <AUTHOR>
 * @since 2022-11-02 星期三
 * facade 切面
 **/
//@Aspect
//@Component
@SuppressWarnings("all")
public class ErrorAspect {

    @Resource
    private CommonDataService commonDataService;

    @Pointcut("within(@org.springframework.stereotype.Controller *) || within(@org.springframework.web.bind.annotation.RestController *) || @annotation(org.springframework.scheduling.annotation.Scheduled)")
    public void pointCut() {}

    @AfterThrowing(pointcut = "pointCut()", throwing = "e")
    public void exceptionHandle(JoinPoint pj, Exception e) {
        // 如果是重复插入异常，则不处理
        if (e instanceof DuplicateKeyException) {
            return;
        }
        if (e instanceof GDRequestException) {
            //已经在请求端处理过 无需处理
            return;
        }
        String env = commonDataService.getDeploymentEnv();
        ErrorMonitorUrlParams monitorUrlParams = new ErrorMonitorUrlParams();
        monitorUrlParams.setEnv(env);
        // 找到异常栈中属于项目代码的第一条记录
        StackTraceElement firstProjectElement = getFirstProjectStackTraceElement(e);
        monitorUrlParams.setClassName(firstProjectElement.getClassName());
        monitorUrlParams.setMethodName(firstProjectElement.getMethodName());
        monitorUrlParams.setLineNumber(firstProjectElement.getLineNumber());
        monitorUrlParams.setException(e);
        if (e instanceof BizException) {
            monitorUrlParams.setErrorType(ErrorType.BIZ.name());
        }
        MonitorRequest monitorRequest = new MonitorRequest(monitorUrlParams);
        switch (env) {
            case GlobalConstant.TEST:
            case GlobalConstant.ENV_ALI_CLOUD:
                break;
            default:
                commonDataService.fetchData(monitorRequest);
        }
    }

    // 从异常栈中找出属于项目代码的第一条记录
    private StackTraceElement getFirstProjectStackTraceElement(Throwable e) {
        for (StackTraceElement element : e.getStackTrace()) {
            // 过滤出属于项目包名的类
            if (element.getClassName().startsWith("com.deepinnet")) {
                return element;
            }
        }
        return null;
    }
}

