package com.deepinnet.spatiotemporalplatform.base.ftp;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.base.enums.BizEnum;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;

import static org.apache.commons.lang3.StringEscapeUtils.*;

/**
 * <p>
 *   FTP 文件下载处理器抽象类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/14
 */
@Slf4j
@SuppressWarnings("unchecked")
public abstract class AbstractFtpDownloadHandler<R extends HttpApiRequest> implements FtpDownloadHandler {
    /**
     * 获取业务枚举（可以在子类中直接覆盖该方法）
     */
    public abstract BizEnum getBizEnum();

    /**
     * 子类实现，根据请求参数构造文件存储路径，例如：
     *  drive_destination_ranking/330800/2025-02-12
     */
    protected abstract String buildFilePath(R request);

    /**
     * 子类实现，根据文件路径及请求参数解析或获取实际文件名
     */
    protected String resolveFileName(String filePath, R request) {
        if (!FtpUtil.pathExist(filePath)) {
            log.warn("文件目录不存在:{}", filePath);
            return null;
        }
        return FtpUtil.getLastFileByPath(filePath);
    }

    /**
     * JSON 反序列化目标类型
     */
    protected abstract Type getResponseType();

    /**
     * 公共的文件下载、读取、反序列化逻辑
     */
    @Override
    public Object handle(HttpApiRequest request) {
        R req = (R) request;

        // 通过子类动态构造文件路径和文件名
        String filePath = buildFilePath(req);
        String fileName = resolveFileName(filePath, req);

        if (StrUtil.isBlank(fileName)) {
            return null;
        }

        try (InputStream fileInputStream = FtpUtil.download(filePath + "/" + fileName)) {

            LogUtil.info("访问ftp文件成功, 文件路径:{}, 文件名:{}", filePath, fileName);

            String content = IoUtil.readUtf8(fileInputStream);

            return JSONObject.parseObject(processJsonString(content), getResponseType());

        } catch (Exception e) {

            LogUtil.error("读取ftp文件异常, 请求参数:{}, 文件路径:{}, 文件名:{}, 异常信息:{}",
                    JSONObject.toJSONString(request.urlParams()), filePath, fileName, e);
            return null;
        }
    }

    /**
     * 如果字符串的首尾存在双引号，则移除，并对内部进行反转义。
     *
     * @param str 输入字符串
     * @return 去除外层引号并反转义后的字符串
     */
    public static String processJsonString(String str) {
        if (str == null || str.length() < 2) {
            return str;
        }
        // 检查外层是否有双引号
        if (str.startsWith("\"") && str.endsWith("\"")) {
            str = str.substring(1, str.length() - 1);
            // 反转义内部的转义字符
            str = unescapeJson(str);
        }
        return str;
    }

}
