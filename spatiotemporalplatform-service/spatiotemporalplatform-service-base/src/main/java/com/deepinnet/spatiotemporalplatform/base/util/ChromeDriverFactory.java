package com.deepinnet.spatiotemporalplatform.base.util;

import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

@Component
@Profile("!qz-gov-inner-network")

public class ChromeDriverFactory {

    @Value("${chrome.driver.path}")
    private String chromeDriverPath;

    public ChromeDriver getNewChromeDriver() {
        // 设置系统属性，指定chromedriver的路径
        System.setProperty("webdriver.chrome.driver", chromeDriverPath);

        // 创建ChromeOptions实例并配置参数
        ChromeOptions options = new ChromeOptions();
        options.addArguments("--headless");
        options.addArguments("--no-sandbox");
        options.addArguments("--disable-dev-shm-usage");
        options.addArguments("--window-size=1920,1080"); // 设置窗口大小
        //options.addArguments("--disable-gpu");

        // 创建并返回一个新的ChromeDriver实例
        return new ChromeDriver(options);
    }
}