package com.deepinnet.spatiotemporalplatform.base.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2024-12-02
 */
@Component
@ConfigurationProperties(prefix = "net.transfer")
@Data
public class NetTransferProperties {
    private List<String> list;
}
