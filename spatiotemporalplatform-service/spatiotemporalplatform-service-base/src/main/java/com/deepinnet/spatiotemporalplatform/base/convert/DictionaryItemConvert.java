package com.deepinnet.spatiotemporalplatform.base.convert;

import com.deepinnet.spatiotemporalplatform.dal.dataobject.DictionaryItemDO;
import com.deepinnet.spatiotemporalplatform.dto.DictionaryItemDTO;
import com.deepinnet.spatiotemporalplatform.vo.DictionaryItemVO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 字典项对象转换器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface DictionaryItemConvert {

    DictionaryItemConvert INSTANCE = Mappers.getMapper(DictionaryItemConvert.class);

    /**
     * DO转DTO
     *
     * @param dictionaryItemDO DO对象
     * @return DTO对象
     */
    DictionaryItemDTO convert(DictionaryItemDO dictionaryItemDO);

    /**
     * DTO转DO
     *
     * @param dictionaryItemDTO DTO对象
     * @return DO对象
     */
    DictionaryItemDO convertToDO(DictionaryItemDTO dictionaryItemDTO);

    /**
     * DTO转VO
     *
     * @param dictionaryItemDTO DTO对象
     * @return VO对象
     */
    DictionaryItemVO convertToVO(DictionaryItemDTO dictionaryItemDTO);

    /**
     * DO列表转DTO列表
     *
     * @param dictionaryItemDOList DO列表
     * @return DTO列表
     */
    List<DictionaryItemDTO> convertList(List<DictionaryItemDO> dictionaryItemDOList);

    /**
     * DTO列表转VO列表
     *
     * @param dictionaryItemDTOList DTO列表
     * @return VO列表
     */
    List<DictionaryItemVO> convertToVOList(List<DictionaryItemDTO> dictionaryItemDTOList);
} 