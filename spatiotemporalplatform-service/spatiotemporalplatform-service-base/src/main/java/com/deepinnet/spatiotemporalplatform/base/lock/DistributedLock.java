package com.deepinnet.spatiotemporalplatform.base.lock;

import java.util.concurrent.TimeUnit;

/**
 * Description:
 * Date: 2024/9/27
 * Author: lijunheng
 */
public interface DistributedLock {

    /**
     * 尝试获取锁，获取补刀直接返回false，不会线程阻塞
     *
     * @param lockName
     * @return
     */
    boolean tryLock(String lockName);

    /**
     * 尝试获取锁，如果等待时间内未获取到，则会线程等待，直到等待时间到，返回是否获取到锁
     *
     * @param lockName
     * @param waitTime
     * @param timeUnit
     * @return
     */
    boolean tryLock(String lockName, Long waitTime, TimeUnit timeUnit);

    /**
     * 尝试获取锁，如果等待时间内未获取到，则会线程等待，直到等待时间到，返回是否获取到锁
     * 锁定时间超过leaseTime规定值将会自动释放锁
     *
     * @param lockName
     * @param waitTime
     * @param leaseTime
     * @param timeUnit
     * @return
     */
    boolean tryLock(String lockName, Long waitTime, Long leaseTime, TimeUnit timeUnit);

    /**
     * 获取锁，获取不到就一直等待
     *
     * @param lockName
     */
    void lock(String lockName);

    /**
     * 获取锁，获取到后过指定时间过期
     *
     * @param lockName
     * @param leaseTime
     * @param unit
     */
    void lock(String lockName, long leaseTime, TimeUnit unit);

    /**
     * 释放锁
     *
     * @param lockName
     */
    void unlock(String lockName);

    /**
     * 获取本机的id
     * @return
     */
    String getMachineId();
}
