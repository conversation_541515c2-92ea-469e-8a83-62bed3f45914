package com.deepinnet.spatiotemporalplatform.base.ftp;

import com.deepinnet.digitaltwin.common.error.ErrorLevel;
import com.deepinnet.digitaltwin.common.error.ErrorType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 2025-03-10
 */
@Getter
@AllArgsConstructor
public enum FtpErrorCode {

    /*------------------------------------------------------------------------*/
    /*                        ftp[20开头]                             */
    /*------------------------------------------------------------------------*/
    GET_FTP_CLIENT_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "201", "FTP客户端获取异常"),

    GET_FTP_CONFIG_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "202", "获取FTP连接参数异常"),

    FTP_FILE_DOWNLOAD_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "203", "FTP文件下载异常"),

    FTP_CLIENT_CLOSE_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "204", "FTP客户端关闭异常"),

    FTP_FILE_STREAM_COMPLETE_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "205", "FTP stream 完整性异常")

    ;

    /**
     * 错误级别
     */
    private ErrorLevel errorLevel;

    /**
     * 错误类型
     */
    private ErrorType errorType;

    /**
     * 错误编码
     */
    private String code;

    /**
     * 错误描述
     */
    private String desc;
}
