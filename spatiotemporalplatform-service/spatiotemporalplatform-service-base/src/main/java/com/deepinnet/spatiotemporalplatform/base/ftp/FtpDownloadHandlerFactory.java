package com.deepinnet.spatiotemporalplatform.base.ftp;

import cn.hutool.core.util.ObjectUtil;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.base.enums.BizEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/14
 */

@Component
@RequiredArgsConstructor
public class FtpDownloadHandlerFactory {

    private final Map<String, FtpDownloadHandler> ftpDownloadHandlerMap;

    private final ConcurrentMap<BizEnum, FtpDownloadHandler> ftpEnumMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        ftpDownloadHandlerMap.values().parallelStream().forEach(handler -> ftpEnumMap.put(handler.getBizEnum(), handler));
    }


    public Object handle(HttpApiRequest request) {
        BizEnum bizEnum = BizEnum.getBizEnumByUrl(request.apiUrl());

        if (ObjectUtil.isNull(bizEnum)) {
            LogUtil.error("当前url:{}, 未找到对应的下载ftpEnum", request.apiUrl());
            return null;
        }

        return ftpEnumMap.get(bizEnum).handle(request);

    }
}
