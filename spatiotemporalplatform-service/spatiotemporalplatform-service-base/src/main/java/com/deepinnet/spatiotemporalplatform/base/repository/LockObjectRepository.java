package com.deepinnet.spatiotemporalplatform.base.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deepinnet.spatiotemporalplatform.base.lock.LockObject;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.LockObjectDO;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.LockObjectDaoService;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description:
 * Date: 2024/9/27
 * Author: lijunheng
 */
@Repository
public class LockObjectRepository extends AbstractRepository<LockObjectDO, LockObject> {

    @Resource
    private LockObjectDaoService lockObjectDaoService;

    public LockObject queryLock(String lockName) {
        LambdaQueryWrapper<LockObjectDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LockObjectDO::getLockName, lockName);
        return getOne(queryWrapper);
    }

    public int reentrantLock(LockObject lockObject) {
        return lockObjectDaoService.updateReentrantLock(convert.toDO(lockObject));
    }

    public int releaseLock(LockObject lockObject) {
        return lockObjectDaoService.updateReleaseLock(convert.toDO(lockObject));
    }

    public int renewExpirationLock(LockObject lockObject) {
        return lockObjectDaoService.updateRenewExpirationLock(convert.toDO(lockObject));
    }

    public List<String> queryAllowObtainLockList(List<String> lockNameList) {
        return lockObjectDaoService.queryAllowObtainLockList(lockNameList);
    }
}
