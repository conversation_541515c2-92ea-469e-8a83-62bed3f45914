package com.deepinnet.spatiotemporalplatform.base.lock;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Description: 一把锁会有很多个线程抢夺，由一个信号量来控制
 * Date: 2024/10/5
 * Author: lijunheng
 */
public class LockEntry {

    private String lockName;

    /**
     * 本机当前锁上等待的线程数
     */
    private AtomicInteger counter = new AtomicInteger(0);

    private final Semaphore latch = new Semaphore(0);

    /**
     * key:线程id, value:回调函数
     */
    private final ConcurrentHashMap<String, Runnable> listeners = new ConcurrentHashMap<>();

    public LockEntry(String lockName) {
        this.lockName = lockName;
    }


    public void acquire() {
        counter.incrementAndGet();
    }

    public int release() {
        return counter.decrementAndGet();
    }

    public void putListener(String threadId, Runnable listener) {
        listeners.put(threadId, listener);
    }

    public void removeListener(String threadId) {
        listeners.remove(threadId);
    }

    public ConcurrentHashMap<String, Runnable> getListeners() {
        return listeners;
    }

    public Semaphore getLatch() {
        return latch;
    }

    public String getLockName() {
        return lockName;
    }

    public AtomicInteger getCounter() {
        return counter;
    }
}
