package com.deepinnet.spatiotemporalplatform.base.util;

import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.*;
import org.openqa.selenium.chrome.ChromeDriver;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ScreenshotUtil {
    private static final String TOKEN = "token";

    /**
     * WebDriver截屏方法
     *
     * @param driver
     * @return
     * @throws IOException
     */
    public static byte[] takeScreenshot(WebDriver driver) throws IOException {
        TakesScreenshot takesScreenshot = (TakesScreenshot) driver;
        return takesScreenshot.getScreenshotAs(OutputType.BYTES);
    }
    public static String createElementImage(WebDriver driver,  String imagePath) {
        return createElementImage(driver, null, imagePath);
    }

    public static void setToken(String token, ChromeDriver webDriver) {
        // 设置localStorage中的值
        String key = TOKEN;
        String value = token;
        String script = String.format("localStorage.setItem('%s', '%s');", key, value);

        // 执行JavaScript代码设置localStorage
        webDriver.executeScript(script);

        // 验证localStorage是否设置成功
        String getValueScript = String.format("return localStorage.getItem('%s');", key);
        String storedValue = (String) webDriver.executeScript(getValueScript);
        System.out.println("localStorage value for key '" + key + "' is: " + storedValue);
    }
    /**
     * 根据节点位置，对节点进行裁剪，获得截图
     *
     * @param driver
     * @param webElement
     * @return
     * @throws IOException
     */
    public static String createElementImage(WebDriver driver, WebElement webElement, String imagePath) {
        // 获得webElement的位置和大小。
        //Point location = webElement.getLocation();
        //Dimension size = webElement.getSize();
        // 创建全屏截图。
        BufferedImage originalImage = null;
        try {
            originalImage = ImageIO.read(new ByteArrayInputStream(takeScreenshot(driver)));

        } catch (IOException e) {
            log.error("take screenshot error,url:{}", driver.getCurrentUrl(), e);
            throw new RuntimeException(e);
        }
        // 截取webElement所在位置的子图。
        if (webElement != null) {
            Point location = webElement.getLocation();
            Dimension size = webElement.getSize();
            originalImage = originalImage.getSubimage(location.getX(), location.getY(), size.getWidth(), size.getHeight());
        }
        String filePath = null;
        try {
            filePath = writeImageFile(originalImage, imagePath);
        } catch (IOException e) {
            log.error("write image file error,url:{}", driver.getCurrentUrl(), e);
            throw new RuntimeException(e);
        }
        return filePath;
    }

    /**
     * 保存截图文件
     *
     * @param bi
     * @param imagePath
     * @throws IOException
     */
    public static String writeImageFile(BufferedImage bi, String imagePath) throws IOException {
        File outputfile = new File(imagePath);
        ImageIO.write(bi, "png", outputfile);
        return imagePath;
    }
}