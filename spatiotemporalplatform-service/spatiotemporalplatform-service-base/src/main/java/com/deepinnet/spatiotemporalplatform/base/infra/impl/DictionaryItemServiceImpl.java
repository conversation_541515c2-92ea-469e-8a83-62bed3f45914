package com.deepinnet.spatiotemporalplatform.base.infra.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.base.convert.DictionaryConvert;
import com.deepinnet.spatiotemporalplatform.base.convert.DictionaryItemConvert;
import com.deepinnet.spatiotemporalplatform.base.error.BizErrorCode;
import com.deepinnet.spatiotemporalplatform.base.infra.DictionaryItemService;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.DictionaryItemRepository;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.DictionaryRepository;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.DictionaryDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.DictionaryItemDO;
import com.deepinnet.spatiotemporalplatform.dto.*;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 字典项服务实现类
 *
 * <AUTHOR>
 */
@Service
public class DictionaryItemServiceImpl implements DictionaryItemService {

    @Resource
    private DictionaryItemRepository dictionaryItemRepository;

    @Resource
    private DictionaryRepository dictionaryRepository;

    @Resource
    private DictionaryItemConvert dictionaryItemConvert;
    
    @Resource
    private DictionaryConvert dictionaryConvert;

    @Override
    public Long saveDictionaryItem(DictionaryItemDTO dictionaryItemDTO) {
        // 检查字典是否存在
        DictionaryDO dictionaryDO = dictionaryRepository.getById(dictionaryItemDTO.getDictionaryId());
        if (dictionaryDO == null) {
            LogUtil.error("保存字典项失败，字典不存在: {}", dictionaryItemDTO.getDictionaryId());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "字典不存在");
        }

        // 检查是否已存在相同编码的字典项
        DictionaryItemDO existItem = dictionaryItemRepository.getOne(
                Wrappers.lambdaQuery(DictionaryItemDO.class)
                        .eq(DictionaryItemDO::getDictionaryId, dictionaryItemDTO.getDictionaryId())
                        .eq(DictionaryItemDO::getCode, dictionaryItemDTO.getCode())
        );
        if (existItem != null) {
            LogUtil.error("保存字典项失败，字典项编码已存在: {}", dictionaryItemDTO.getCode());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "字典项编码已存在");
        }

        // 确保排序字段有值，默认为1
        if (dictionaryItemDTO.getSort() == null) {
            dictionaryItemDTO.setSort(1);
        }

        DictionaryItemDO dictionaryItemDO = dictionaryItemConvert.convertToDO(dictionaryItemDTO);
        dictionaryItemDO.setGmtCreated(LocalDateTime.now());
        dictionaryItemDO.setGmtModified(LocalDateTime.now());
        dictionaryItemDO.setIsDeleted(0);
        
        boolean success = dictionaryItemRepository.save(dictionaryItemDO);
        if (!success) {
            LogUtil.error("保存字典项失败: {}", dictionaryItemDTO.getCode());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }
        
        LogUtil.info("保存字典项成功: {}", dictionaryItemDO.getId());
        return dictionaryItemDO.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveDictionaryItems(List<DictionaryItemDTO> dictionaryItemDTOList) {
        if (CollectionUtils.isEmpty(dictionaryItemDTOList)) {
            return true;
        }

        // 检查字典是否存在
        Long dictionaryId = dictionaryItemDTOList.get(0).getDictionaryId();
        DictionaryDO dictionaryDO = dictionaryRepository.getById(dictionaryId);
        if (dictionaryDO == null) {
            LogUtil.error("批量保存字典项失败，字典不存在: {}", dictionaryId);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "字典不存在");
        }

        // 确保所有元素的ID为null，使用数据库生成新ID
        dictionaryItemDTOList.forEach(item -> {
            item.setId(null);
            // 确保排序字段有值，默认为1
            if (item.getSort() == null) {
                item.setSort(1);
            }
        });

        // 获取已存在的字典项编码
        List<String> codeList = dictionaryItemDTOList.stream()
                .map(DictionaryItemDTO::getCode)
                .collect(Collectors.toList());
        List<DictionaryItemDO> existItems = dictionaryItemRepository.list(
                Wrappers.lambdaQuery(DictionaryItemDO.class)
                        .eq(DictionaryItemDO::getDictionaryId, dictionaryId)
                        .in(DictionaryItemDO::getCode, codeList)
        );
        if (!CollectionUtils.isEmpty(existItems)) {
            String existCodes = existItems.stream()
                    .map(DictionaryItemDO::getCode)
                    .collect(Collectors.joining(","));
            LogUtil.error("批量保存字典项失败，存在重复的编码: {}", existCodes);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "存在重复的字典项编码: " + existCodes);
        }

        // 批量保存
        List<DictionaryItemDO> dictionaryItemDOList = new ArrayList<>(dictionaryItemDTOList.size());
        LocalDateTime now = LocalDateTime.now();
        for (DictionaryItemDTO dictionaryItemDTO : dictionaryItemDTOList) {
            DictionaryItemDO dictionaryItemDO = dictionaryItemConvert.convertToDO(dictionaryItemDTO);
            // 确保ID为null
            dictionaryItemDO.setId(null);
            dictionaryItemDO.setGmtCreated(now);
            dictionaryItemDO.setGmtModified(now);
            dictionaryItemDO.setIsDeleted(0);
            dictionaryItemDOList.add(dictionaryItemDO);
        }
        
        boolean success = dictionaryItemRepository.saveBatch(dictionaryItemDOList);
        if (!success) {
            LogUtil.error("批量保存字典项失败，字典ID: {}", dictionaryId);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }
        
        LogUtil.info("批量保存字典项成功，字典ID: {}, 数量: {}", dictionaryId, dictionaryItemDOList.size());
        return true;
    }

    @Override
    public boolean updateDictionaryItem(DictionaryItemDTO dictionaryItemDTO) {
        if (dictionaryItemDTO.getId() == null) {
            LogUtil.error("更新字典项失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 检查字典项是否存在
        DictionaryItemDO existItem = dictionaryItemRepository.getById(dictionaryItemDTO.getId());
        if (existItem == null) {
            LogUtil.error("更新字典项失败，字典项不存在: {}", dictionaryItemDTO.getId());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "字典项不存在");
        }

        // 如果修改了编码，需要检查编码是否冲突
        if (!existItem.getCode().equals(dictionaryItemDTO.getCode())) {
            DictionaryItemDO conflictItem = dictionaryItemRepository.getOne(
                    Wrappers.lambdaQuery(DictionaryItemDO.class)
                            .eq(DictionaryItemDO::getDictionaryId, dictionaryItemDTO.getDictionaryId())
                            .eq(DictionaryItemDO::getCode, dictionaryItemDTO.getCode())
                            .ne(DictionaryItemDO::getId, dictionaryItemDTO.getId())
            );
            if (conflictItem != null) {
                LogUtil.error("更新字典项失败，字典项编码已被其他项使用: {}", dictionaryItemDTO.getCode());
                throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "字典项编码已存在");
            }
        }

        DictionaryItemDO dictionaryItemDO = dictionaryItemConvert.convertToDO(dictionaryItemDTO);
        dictionaryItemDO.setGmtModified(LocalDateTime.now());
        
        boolean success = dictionaryItemRepository.updateById(dictionaryItemDO);
        if (!success) {
            LogUtil.error("更新字典项失败: {}", dictionaryItemDTO.getId());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }
        
        LogUtil.info("更新字典项成功: {}", dictionaryItemDO.getId());
        return true;
    }

    @Override
    public DictionaryItemDTO getDictionaryItemById(Long id) {
        if (id == null) {
            LogUtil.error("获取字典项失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        DictionaryItemDO dictionaryItemDO = dictionaryItemRepository.getById(id);
        if (dictionaryItemDO == null) {
            LogUtil.error("获取字典项失败，字典项不存在: {}", id);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "字典项不存在");
        }

        return dictionaryItemConvert.convert(dictionaryItemDO);
    }

    @Override
    public DictionaryItemDTO getDictionaryItemByCode(Long dictionaryId, String code) {
        if (dictionaryId == null || StringUtils.isBlank(code)) {
            LogUtil.error("获取字典项失败，参数不完整，字典ID: {}, 编码: {}", dictionaryId, code);
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        DictionaryItemDO dictionaryItemDO = dictionaryItemRepository.getOne(
                Wrappers.lambdaQuery(DictionaryItemDO.class)
                        .eq(DictionaryItemDO::getDictionaryId, dictionaryId)
                        .eq(DictionaryItemDO::getCode, code)
        );
        if (dictionaryItemDO == null) {
            LogUtil.error("获取字典项失败，字典项不存在，字典ID: {}, 编码: {}", dictionaryId, code);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "字典项不存在");
        }

        return dictionaryItemConvert.convert(dictionaryItemDO);
    }

    @Override
    public List<DictionaryItemDTO> listDictionaryItemsByDictionaryId(Long dictionaryId) {
        if (dictionaryId == null) {
            LogUtil.error("获取字典项列表失败，缺少字典ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        List<DictionaryItemDO> dictionaryItemDOList = dictionaryItemRepository.list(
                Wrappers.lambdaQuery(DictionaryItemDO.class)
                        .eq(DictionaryItemDO::getDictionaryId, dictionaryId)
                        .orderByAsc(DictionaryItemDO::getSort)
                        .orderByAsc(DictionaryItemDO::getLevel)
                        .orderByAsc(DictionaryItemDO::getCode)
        );
        if (CollectionUtils.isEmpty(dictionaryItemDOList)) {
            return new ArrayList<>();
        }

        return dictionaryItemConvert.convertList(dictionaryItemDOList);
    }

    @Override
    public CommonPage<DictionaryItemDTO> pageQueryDictionaryItems(DictionaryItemQueryDTO queryDTO) {
        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        LambdaQueryWrapper<DictionaryItemDO> queryWrapper = Wrappers.lambdaQuery(DictionaryItemDO.class)
                .eq(queryDTO.getDictionaryId() != null, DictionaryItemDO::getDictionaryId, queryDTO.getDictionaryId())
                .eq(StringUtils.isNotBlank(queryDTO.getCode()), DictionaryItemDO::getCode, queryDTO.getCode())
                .like(StringUtils.isNotBlank(queryDTO.getName()), DictionaryItemDO::getName, queryDTO.getName())
                .eq(StringUtils.isNotBlank(queryDTO.getState()), DictionaryItemDO::getState, queryDTO.getState())
                .eq(StringUtils.isNotBlank(queryDTO.getParentCode()), DictionaryItemDO::getParentCode, queryDTO.getParentCode())
                .eq(queryDTO.getLevel() != null, DictionaryItemDO::getLevel, queryDTO.getLevel())
                .eq(queryDTO.getSort() != null, DictionaryItemDO::getSort, queryDTO.getSort())
                .eq(StringUtils.isNotBlank(queryDTO.getTenantId()), DictionaryItemDO::getTenantId, queryDTO.getTenantId())
                .orderByAsc(DictionaryItemDO::getSort)
                .orderByAsc(DictionaryItemDO::getLevel)
                .orderByAsc(DictionaryItemDO::getCode);

        List<DictionaryItemDO> dictionaryItemDOList = dictionaryItemRepository.list(queryWrapper);
        if (CollectionUtils.isEmpty(dictionaryItemDOList)) {
            return CommonPage.buildEmptyPage();
        }

        List<DictionaryItemDTO> dictionaryItemDTOList = dictionaryItemConvert.convertList(dictionaryItemDOList);
        PageInfo<DictionaryItemDTO> pageInfo = new PageInfo<>(dictionaryItemDTOList);
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPages(page.getPages());

        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), pageInfo.getPages(), pageInfo.getTotal(), dictionaryItemDTOList);
    }

    @Override
    public boolean deleteDictionaryItem(Long id) {
        if (id == null) {
            LogUtil.error("删除字典项失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 检查字典项是否存在
        DictionaryItemDO existItem = dictionaryItemRepository.getById(id);
        if (existItem == null) {
            LogUtil.error("删除字典项失败，字典项不存在: {}", id);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "字典项不存在");
        }

        boolean success = dictionaryItemRepository.removeById(id);
        if (!success) {
            LogUtil.error("删除字典项失败: {}", id);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        LogUtil.info("删除字典项成功: {}", id);
        return true;
    }

    @Override
    public boolean deleteDictionaryItemsByDictionaryId(Long dictionaryId) {
        if (dictionaryId == null) {
            LogUtil.error("删除字典项失败，缺少字典ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        boolean success = dictionaryItemRepository.remove(
                Wrappers.lambdaQuery(DictionaryItemDO.class)
                        .eq(DictionaryItemDO::getDictionaryId, dictionaryId)
        );

        LogUtil.info("删除字典项成功，字典ID: {}", dictionaryId);
        return success;
    }

    @Override
    public List<DictionaryTreeDTO> getDictionaryTreeByTypeAndTenantId(String type, String tenantId) {
        if (StringUtils.isBlank(type) ) {
            LogUtil.error("查询字典树失败，参数不完整，字典类型: {}, 租户ID: {}", type, tenantId);
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 查询符合条件的字典列表
        List<DictionaryDO> dictionaryDOList = dictionaryRepository.list(
                Wrappers.lambdaQuery(DictionaryDO.class)
                        .eq(DictionaryDO::getType, type)
        );
        if (CollectionUtils.isEmpty(dictionaryDOList)) {
            LogUtil.info("未找到字典数据，字典类型: {}, 租户ID: {}", type, tenantId);
            return new ArrayList<>();
        }

        List<DictionaryTreeDTO> result = new ArrayList<>();
        
        // 遍历每个字典，构建树结构
        for (DictionaryDO dictionaryDO : dictionaryDOList) {
            DictionaryTreeDTO treeDTO = new DictionaryTreeDTO();
            BeanUtils.copyProperties(dictionaryDO, treeDTO);
            
            // 查询该字典下的所有字典项
            List<DictionaryItemDO> itemDOList = dictionaryItemRepository.list(
                    Wrappers.lambdaQuery(DictionaryItemDO.class)
                            .eq(DictionaryItemDO::getDictionaryId, dictionaryDO.getDictionaryId())
                            .orderByAsc(DictionaryItemDO::getSort)
                            .orderByAsc(DictionaryItemDO::getLevel)
                            .orderByAsc(DictionaryItemDO::getCode)
            );
            
            if (CollectionUtils.isEmpty(itemDOList)) {
                treeDTO.setItems(new ArrayList<>());
                result.add(treeDTO);
                continue;
            }
            
            // 将字典项转换为树结构
            List<DictionaryItemTreeDTO> itemTreeDTOs = buildDictionaryItemTree(itemDOList);
            treeDTO.setItems(itemTreeDTOs);
            
            result.add(treeDTO);
        }
        
        LogUtil.info("查询字典树成功，字典类型: {}, 租户ID: {}, 字典数量: {}", type, tenantId, result.size());
        return result;
    }
    
    /**
     * 构建字典项树结构
     *
     * @param itemDOList 字典项列表
     * @return 树结构字典项列表
     */
    private List<DictionaryItemTreeDTO> buildDictionaryItemTree(List<DictionaryItemDO> itemDOList) {
        // 转换为 DictionaryItemTreeDTO
        List<DictionaryItemTreeDTO> itemTreeDTOList = new ArrayList<>(itemDOList.size());
        for (DictionaryItemDO itemDO : itemDOList) {
            DictionaryItemTreeDTO treeDTO = new DictionaryItemTreeDTO();
            BeanUtils.copyProperties(itemDO, treeDTO);
            treeDTO.setChildren(new ArrayList<>());
            itemTreeDTOList.add(treeDTO);
        }
        
        // 构建树结构
        Map<String, DictionaryItemTreeDTO> codeMap = new HashMap<>(itemTreeDTOList.size());
        for (DictionaryItemTreeDTO itemTreeDTO : itemTreeDTOList) {
            codeMap.put(itemTreeDTO.getCode(), itemTreeDTO);
        }
        
        List<DictionaryItemTreeDTO> rootList = new ArrayList<>();
        for (DictionaryItemTreeDTO itemTreeDTO : itemTreeDTOList) {
            // 父编码为空或找不到父节点，则为根节点
            if (StringUtils.isBlank(itemTreeDTO.getParentCode()) || !codeMap.containsKey(itemTreeDTO.getParentCode())) {
                rootList.add(itemTreeDTO);
            } else {
                // 找到父节点，添加到父节点的子节点中
                DictionaryItemTreeDTO parent = codeMap.get(itemTreeDTO.getParentCode());
                parent.getChildren().add(itemTreeDTO);
            }
        }
        
        return rootList;
    }

    @Override
    public List<DictionaryItemDTO> listDictionaryItemsByTypeAndParentCode(String tenantId, String type, String parentCode) {
        if (StringUtils.isBlank(type)) {
            LogUtil.error("查询字典项列表失败，参数不完整，租户ID: {}, 字典类型: {}", tenantId, type);
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }
        
        // 首先查询满足条件的字典
        List<DictionaryDO> dictionaryDOList = dictionaryRepository.list(
                Wrappers.lambdaQuery(DictionaryDO.class)
                        .eq(DictionaryDO::getType, type)
        );
        
        if (CollectionUtils.isEmpty(dictionaryDOList)) {
            LogUtil.info("未找到字典数据，字典类型: {}, 租户ID: {}", type, tenantId);
            return new ArrayList<>();
        }
        
        // 获取所有匹配字典的ID列表
        List<Long> dictionaryIds = dictionaryDOList.stream()
                .map(DictionaryDO::getDictionaryId)
                .collect(Collectors.toList());
        
        // 然后根据字典ID列表和父编码查询字典项
        LambdaQueryWrapper<DictionaryItemDO> queryWrapper = Wrappers.lambdaQuery(DictionaryItemDO.class)
                .in(DictionaryItemDO::getDictionaryId, dictionaryIds)
                .orderByAsc(DictionaryItemDO::getSort)
                .orderByAsc(DictionaryItemDO::getLevel)
                .orderByAsc(DictionaryItemDO::getCode);
        
        // 如果父编码不为空，则添加父编码条件
        if (StringUtils.isNotBlank(parentCode)) {
            queryWrapper.eq(DictionaryItemDO::getParentCode, parentCode);
        } else {
            // 如果父编码为空，则查询顶级节点（父编码为空的节点）
            queryWrapper.isNull(DictionaryItemDO::getParentCode).or().eq(DictionaryItemDO::getParentCode, "0");
        }
        
        List<DictionaryItemDO> dictionaryItemDOList = dictionaryItemRepository.list(queryWrapper);
        
        if (CollectionUtils.isEmpty(dictionaryItemDOList)) {
            LogUtil.info("未找到匹配的字典项，字典类型: {}, 租户ID: {}, 父编码: {}", type, tenantId, parentCode);
            return new ArrayList<>();
        }

        return dictionaryItemConvert.convertList(dictionaryItemDOList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchOperateDictionaryItems(DictionaryItemBatchOperationDTO batchOperationDTO) {
        if (batchOperationDTO.getDictionaryId() == null) {
            LogUtil.error("批量操作字典项失败，缺少字典ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }
        
        // 检查字典是否存在
        DictionaryDO dictionaryDO = dictionaryRepository.getById(batchOperationDTO.getDictionaryId());
        if (dictionaryDO == null) {
            LogUtil.error("批量操作字典项失败，字典不存在: {}", batchOperationDTO.getDictionaryId());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "字典不存在");
        }
        
        // 批量删除操作
        if (!CollectionUtils.isEmpty(batchOperationDTO.getDeleteIds())) {
            LogUtil.info("执行批量删除字典项操作，字典ID: {}, 待删除ID数量: {}", 
                     batchOperationDTO.getDictionaryId(), batchOperationDTO.getDeleteIds().size());
                     
            // 验证删除的项是否都属于指定的字典
            List<DictionaryItemDO> itemsToDelete = dictionaryItemRepository.list(
                    Wrappers.lambdaQuery(DictionaryItemDO.class)
                            .eq(DictionaryItemDO::getDictionaryId, batchOperationDTO.getDictionaryId())
                            .in(DictionaryItemDO::getId, batchOperationDTO.getDeleteIds())
            );
            
            if (itemsToDelete.size() != batchOperationDTO.getDeleteIds().size()) {
                LogUtil.error("批量删除字典项失败，存在不属于该字典的字典项");
                throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "存在不属于该字典的字典项");
            }
            
            boolean deleteSuccess = dictionaryItemRepository.removeBatchByIds(batchOperationDTO.getDeleteIds());
            if (!deleteSuccess) {
                LogUtil.error("批量删除字典项失败，字典ID: {}", batchOperationDTO.getDictionaryId());
                throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "批量删除字典项失败");
            }
        }
        
        // 批量新增操作
        if (!CollectionUtils.isEmpty(batchOperationDTO.getCreateItems())) {
            LogUtil.info("执行批量新增字典项操作，字典ID: {}, 待新增数量: {}", 
                     batchOperationDTO.getDictionaryId(), batchOperationDTO.getCreateItems().size());
                     
            // 确保所有要新增的项都属于指定的字典并设置ID为null（强制使用数据库生成的ID）
            for (DictionaryItemDTO itemDTO : batchOperationDTO.getCreateItems()) {
                itemDTO.setDictionaryId(batchOperationDTO.getDictionaryId());
                itemDTO.setId(null); // 设置ID为null，确保使用数据库生成的ID
                
                // 确保排序字段有值，默认为1
                if (itemDTO.getSort() == null) {
                    itemDTO.setSort(1);
                }
            }
            
            // 使用现有的批量保存方法
            boolean createSuccess = batchSaveDictionaryItems(batchOperationDTO.getCreateItems());
            if (!createSuccess) {
                LogUtil.error("批量新增字典项失败，字典ID: {}", batchOperationDTO.getDictionaryId());
                throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "批量新增字典项失败");
            }
        }
        
        LogUtil.info("批量操作字典项成功，字典ID: {}", batchOperationDTO.getDictionaryId());
        return true;
    }
} 