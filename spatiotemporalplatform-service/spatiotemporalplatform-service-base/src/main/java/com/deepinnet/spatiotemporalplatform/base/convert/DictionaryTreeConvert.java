package com.deepinnet.spatiotemporalplatform.base.convert;

import com.deepinnet.spatiotemporalplatform.dto.DictionaryItemTreeDTO;
import com.deepinnet.spatiotemporalplatform.dto.DictionaryTreeDTO;
import com.deepinnet.spatiotemporalplatform.vo.DictionaryItemTreeVO;
import com.deepinnet.spatiotemporalplatform.vo.DictionaryTreeVO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 字典树对象转换器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface DictionaryTreeConvert {

    DictionaryTreeConvert INSTANCE = Mappers.getMapper(DictionaryTreeConvert.class);

    /**
     * 字典树DTO转VO
     *
     * @param dictionaryTreeDTO DTO对象
     * @return VO对象
     */
    DictionaryTreeVO convertToVO(DictionaryTreeDTO dictionaryTreeDTO);

    /**
     * 字典项树DTO转VO
     *
     * @param dictionaryItemTreeDTO DTO对象
     * @return VO对象
     */
    DictionaryItemTreeVO convertItemToVO(DictionaryItemTreeDTO dictionaryItemTreeDTO);

    /**
     * 字典树DTO列表转VO列表
     *
     * @param dictionaryTreeDTOList DTO列表
     * @return VO列表
     */
    List<DictionaryTreeVO> convertToVOList(List<DictionaryTreeDTO> dictionaryTreeDTOList);

    /**
     * 字典项树DTO列表转VO列表
     *
     * @param dictionaryItemTreeDTOList DTO列表
     * @return VO列表
     */
    List<DictionaryItemTreeVO> convertItemToVOList(List<DictionaryItemTreeDTO> dictionaryItemTreeDTOList);
} 