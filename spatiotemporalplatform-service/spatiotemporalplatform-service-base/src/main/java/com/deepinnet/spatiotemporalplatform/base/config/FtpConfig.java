package com.deepinnet.spatiotemporalplatform.base.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <p>
 *    ftp配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/12
 */


@Configuration
public class FtpConfig {

    private static String host;

    private static Integer port;

    private static String username;

    private static String password;

    @Value("${ftp.host:}")
    public void setHost(String host) {
        FtpConfig.host = host;
    }

    @Value("${ftp.port:}")
    public void setPort(Integer port) {
        FtpConfig.port = port;
    }

    @Value("${ftp.username:}")
    public void setUsername(String username) {
        FtpConfig.username = username;
    }

    @Value("${ftp.password:}")
    public void setPassword(String password) {
        FtpConfig.password = password;
    }

    public static String getHost() {
        return host;
    }

    public static Integer getPort() {
        return port;
    }

    public static String getUsername() {
        return username;
    }

    public static String getPassword() {
        return password;
    }
}
