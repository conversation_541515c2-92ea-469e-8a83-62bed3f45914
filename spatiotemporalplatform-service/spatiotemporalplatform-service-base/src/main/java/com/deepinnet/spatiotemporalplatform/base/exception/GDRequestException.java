package com.deepinnet.spatiotemporalplatform.base.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**

 *
 * <AUTHOR>
 * @since 2025-01-06 星期一
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class GDRequestException extends RuntimeException{
    private static final long serialVersionUID = -6650976233946674121L;

    private String errorCode;

    public GDRequestException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

}
