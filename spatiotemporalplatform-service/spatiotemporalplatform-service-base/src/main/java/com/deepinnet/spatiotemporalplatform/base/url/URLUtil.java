package com.deepinnet.spatiotemporalplatform.base.url;

import cn.hutool.core.map.MapUtil;
import cn.hutool.crypto.digest.DigestUtil;
import org.apache.commons.codec.binary.Hex;

import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2024-07-29
 */
public class URLUtil {
    /**
     * 从URL中提取参数键值对
     *
     * @param urlString 包含参数的URL字符串
     * @return 包含参数键值对的映射
     * @throws UnsupportedEncodingException
     */
    public static Map<String, String> getURLParameters(String urlString) {
        Map<String, String> params = new HashMap<>();

        // 找到 ? 之后的查询字符串部分
        String[] urlParts = urlString.split("\\?");
        if (urlParts.length > 1) {
            String query = urlParts[1];
            // 分割参数
            String[] pairs = query.split("&");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=");
                String key = null;
                try {
                    key = URLDecoder.decode(keyValue[0], "UTF-8");
                    String value = "";
                    if (keyValue.length > 1) {
                        value = URLDecoder.decode(keyValue[1], "UTF-8");
                    }
                    params.put(key, value);
                } catch (UnsupportedEncodingException e) {
                    throw new RuntimeException(e);
                }
            }
        }

        return params;
    }

    /**
     * 将url参数参数排序后拼接value值
     *
     * @param url
     * @return
     */
    private static String concatAllParams(String url) {
        Map<String, String> urlParams = URLUtil.getURLParameters(url);

        // 使用 TreeMap 对参数进行排序
        Map<String, Object> sortedMap = new TreeMap<>();
        sortedMap.putAll(urlParams);

        return sortedMap.values().stream().filter(Objects::nonNull).map(Object::toString).collect(Collectors.joining());
    }

    /**
     * 计算签名
     *
     * @param url
     * @param secret
     * @return
     */
    public static String generateDigest(String url, String secret) {
        String params = concatAllParams(url);
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            byte[] secretByte = secret.getBytes(StandardCharsets.UTF_8);
            byte[] dataBytes = params.getBytes(StandardCharsets.UTF_8);

            SecretKey secretKey = new SecretKeySpec(secretByte, "HMACSHA256");
            mac.init(secretKey);

            byte[] doFinal = mac.doFinal(dataBytes);
            byte[] hexB = new Hex().encode(doFinal);

            // 计算好的签名
            return new String(hexB, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static String generateToken(String url, String apiSecret) {
        Map<String, String> urlParameters = URLUtil.getURLParameters(url);
        TreeMap<String, String> treeMap = MapUtil.sort(urlParameters);
        return DigestUtil.md5Hex(String.join(";", treeMap.values()) + ";" + apiSecret);
    }
}
