package com.deepinnet.spatiotemporalplatform.base.bridge;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.http.webservice.SoapClient;
import cn.hutool.http.webservice.SoapProtocol;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2024-08-05
 */
@Slf4j
@Component
public class WebServiceBridgeClient implements IWebServiceBridgeClient {
    private static final String BRIDGE_STPF_DEEPINNET_COM = "http://bridge.service.stpf.deepinnet.com/";

    @Value("${network.edge.url}")
    private String webServiceUrl;

    @Value("${application.deployment.env}")
    private String env;

    @SneakyThrows
    @Override
    public Object invoke(HttpApiRequest request) {
        long startTime = System.currentTimeMillis();

        // 发送soap请求
        String invokeResult = sendSoapRequest(request);

        log.info("[webservice invoke finish,request:{},cost time:{}ms", request.apiUrl(), System.currentTimeMillis() - startTime);

        if (StringUtils.isEmpty(invokeResult) || invokeResult.equals("null")) {
            return null;
        }

        if (!JSONUtil.isTypeJSON(invokeResult)) {
            return invokeResult.replaceAll("\"", "");
        }

        Object jsonObject = JSON.parse(invokeResult);
        String className;

        if (jsonObject instanceof JSONObject) {
            // 根据参数类型反序列化成对象
            className = (String) ((JSONObject) jsonObject).get("@type");
            return JSON.parseObject(invokeResult, Class.forName(className));

        } else if (jsonObject instanceof JSONArray) {

            // 根据参数类型反序列化成对象数组
            JSONArray jsonArray = (JSONArray) jsonObject;
            if (jsonArray.isEmpty()) {
                return jsonArray;
            }
            JSONObject jsonObj = (JSONObject) jsonArray.get(0);
            className = (String) jsonObj.get("@type");
            return JSON.parseArray(invokeResult, Class.forName(className));

        } else {
            throw new UnsupportedOperationException("invokeResult is not JSONObject or JSONArray");
        }
    }

    private String sendSoapRequest(HttpApiRequest request) throws Exception {
        // 调用网闸webservice服务
        Map<String, Object> params = new HashMap<>();
        params.put("data", JSON.toJSONString(request, JSONWriter.Feature.WriteClassName));
        params.put("token", "6CDE3126D77EDB0305F1104759D0011B");

        // 新建客户端
        String nameSpaceURI = null;
        if (isLocal(env)) {
            nameSpaceURI = BRIDGE_STPF_DEEPINNET_COM;
        }
        SoapClient client = SoapClient.create(webServiceUrl, SoapProtocol.SOAP_1_1, nameSpaceURI)
                // 设置要请求的方法,网闸方法固定是vss_server1
                .setMethod("vss_server1")
                .setParams(params, false);

        // 返回内容为XML字符串
        String xmlResult = client.send(false);

        log.debug("webservice result:" + xmlResult);

        // 创建并配置 DocumentBuilderFactory
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        // 允许 DOCTYPE 声明
        factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", false);

        // 创建 DocumentBuilder
        DocumentBuilder builder = factory.newDocumentBuilder();

        // 解析XML字符串
        Document doc = builder.parse(new InputSource(new StringReader(xmlResult)));

        // 使用XPath表达式获取<return>节点
        Node returnNode = XmlUtil.getNodeByXPath("//return", doc);

        if (returnNode == null) {
            return null;
        }

        // 解析结果
        BridgeResponse bridgeResponse = getBridgeResponse(returnNode, env);

        if (StringUtils.isEmpty(bridgeResponse)) {
            return null;
        }

        String responseData = Base64.decodeStr(bridgeResponse.getMessage());
        if (bridgeResponse.getCode().equals(FAIL_CODE)) {
            log.error("调用webservice失败:", responseData);
            return null;
        }

        // 解码webservice结果
        if (bridgeResponse.getMessage() == null) {
            return null;
        }

        log.debug("webservice response data:{}", responseData);

        return responseData;
    }

    private static BridgeResponse getBridgeResponse(Node returnNode, String env) throws JAXBException {
        // local环境解析xml
        if (isLocal(env)) {
            return getBridgeResponseForLocal(returnNode);
        }

        // 其他环境解析 xml
        String cdataContent = returnNode.getTextContent();
        String xml = cdataContent.replaceAll("^<!\\[CDATA\\[", "").replaceAll("\\]\\]>$", "");

        // 解析 <return> 元素为 WebServiceResponse 对象
        JAXBContext jaxbContext = JAXBContext.newInstance(BridgeResponse.class);
        Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
        BridgeResponse bridgeResponse = (BridgeResponse) unmarshaller.unmarshal(new StringReader(xml));
        return bridgeResponse;
    }

    private static boolean isLocal(String env) {
        return Objects.equals(env, "local") || Objects.equals(env, "local-inner");
    }

    /**
     * local网闸解析方式和衢州不同，local的网闸返回的xml是直接在<return>标签内新增子节点，而衢州的网闸返回的xml在<return>标签内嵌cdata标签，所以需要特殊处理
     * @param returnNode
     * @return
     */
    private static BridgeResponse getBridgeResponseForLocal(Node returnNode) {
        BridgeResponse bridgeResponse = new BridgeResponse();
        NodeList childNodes = returnNode.getChildNodes();
        int length = childNodes.getLength();
        for (int i = 0; i < length; i++) {
            Node node = childNodes.item(i);
            if (Objects.equals(node.getNodeName(), "code")) {
                bridgeResponse.setCode(node.getTextContent());
            }
            if (Objects.equals(node.getNodeName(), "message")) {
                bridgeResponse.setMessage(node.getTextContent());
            }
            if (Objects.equals(node.getNodeName(), "time")) {
                bridgeResponse.setTime(node.getTextContent());
            }
        }
        return bridgeResponse;
    }

}