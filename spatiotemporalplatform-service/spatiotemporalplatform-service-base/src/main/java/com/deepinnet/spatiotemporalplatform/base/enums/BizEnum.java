package com.deepinnet.spatiotemporalplatform.base.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

/**
 * <p>
 *   业务类型
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/11
 */

@Getter
public enum BizEnum {

    /**
     * 司机目的地排行榜
     */
    DRIVE_DESTINATION_RANKING("https://et-api.amap.com/support/survey/out/hotod/destination/ranking?clientKey=%s&timestamp=%s&userKey=%s&adcode=%s&name=%s&types=%s&ids=%s&pageNum=%s&pageSize=%s", "drive_destination_ranking", "司机目的地排行榜"),

    /**
     * 实时路口评价
     */
    REAL_TIME_INTERSECTION_EVALUATION("https://et-api.amap.com/diagnosis/real/inter/realInterIndexDetail/city?clientKey=%s&timestamp=%s&adcode=%s&interIds=%s&pageSize=%s&pageNum=%s", "real_time_intersection_evaluation", "实时路口评价"),

    /**
     * 历史人流
     */
    HISTORY_PEOPLE_FLOW("https://et-api.amap.com/support/survey/out/peopleflow/history/area/query?userKey=%s&clientKey=%s&flowType=%s&customAreaId=%s&startDate=%s&timeGrading=%s&endDate=%s&timestamp=%s", "history_people_flow", "历史人流"),

    /**
     * 实时人流
     */
    REAL_TIME_PEOPLE_FLOW("https://et-api.amap.com/support/survey/out/peopleflow/area/query?userKey=%s&clientKey=%s&timestamp=%s&customAreaId=%s", "real_time_people_flow", "实时人流"),

    /**
     * 实时网格人流
     */
    REAL_TIME_GRID_PEOPLE_FLOW("https://et-api.amap.com/support/survey/out/peopleflow/grid/queryList?userKey=%s&clientKey=%s&timestamp=%s&customAreaId=%s&page=%s&size=%s", "real_time_grid_people_flow", "实时网格人流"),

    /**
     * 实时人流来源地
     */
    REAL_TIME_PEOPLE_SOURCE_FLOW("https://et-api.amap.com/support/survey/out/routeflow/v2/customArea/fromOrToObj/list?userKey=%s&clientKey=%s&timestamp=%s&adcode=%s&fromOrTo=%s&customAreaId=%s&type=%s&pageNo=%s&pageSize=%s", "real_time_people_source_flow", "实时人流来源地"),

    /**
     * 区域拥堵指数
     */
    REGION_CONGESTION_INDEX("http://et-api.amap.com/index/districtRanking?clientKey=%s&timestamp=%s&adcode=%s&types=%s&district=%s&withArea=%s&ids=%s&size=%s", "region_congestion_index", "区域拥堵指数"),

    /**
     * 拥堵里程
     */
    CONGESTION_MILES("http://et-api.amap.com/index/roadRanking?adcode=%s&type=%s&roadLength=%s&roadClass=%s&district=%s&ids=%s&size=%s&clientKey=%s&timestamp=%s", "congestion_miles", "拥堵里程"),

    /**
     * 实时车流来源地
     */
    REAL_TIME_TRAFFIC_SOURCE_FLOW("https://et-api.amap.com/support/survey/out/hotod/sourceDestination/detail?adcode=%s&userKey=%s&id=%s&inOut=%s&sourceType=%s&pageNum=%s&pageSize=%s&type=%s&clientKey=%s&timestamp=%s", "real_time_traffic_source_flow", "实时车流来源地"),

    /**
     * 交通事件
     */
    TRAFFIC_EVENT("https://et-api.amap.com/event/queryByAdcode?clientKey=%s&timestamp=%s&adcode=%s", "traffic_event", "交通事件"),

    /**
     * 交通拥堵事件
     */
    TRAFFIC_CONGESTION_EVENT("https://et-api.amap.com/congestion/realtime?clientKey=%s&timestamp=%s", "traffic_congestion_event", "交通拥堵事件"),
    ;

    private final String url;

    private final String code;

    private final String desc;

    BizEnum(String url, String code, String desc) {
        this.url = url;
        this.code = code;
        this.desc = desc;
    }

    public static BizEnum getBizEnumByUrl(String url){
        BizEnum[] values = BizEnum.values();
        for (BizEnum bizEnum : values) {
            if (StrUtil.equals(bizEnum.getUrl(), url)) {
                return bizEnum;
            }
        }
        return null;
    }
}
