package com.deepinnet.spatiotemporalplatform.base.http;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.deepinnet.spatiotemporalplatform.base.bridge.DataServiceBridge;
import com.deepinnet.spatiotemporalplatform.base.config.FtpEnvProperties;
import com.deepinnet.spatiotemporalplatform.base.ftp.FtpDownloadHandlerFactory;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.response.BridgeDataResponse;
import com.deepinnet.spatiotemporalplatform.base.bridge.IWebServiceBridgeClient;
import com.deepinnet.spatiotemporalplatform.base.config.NetTransferProperties;
import com.deepinnet.spatiotemporalplatform.base.env.DeploymentEnv;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * 判断当前部署环境，决策是调用本地时空数据服务，还是走政府网络边界通道：例如衢州政府网闸
 *
 * <AUTHOR>
 * @version 2024-07-30
 */
@Service
@Slf4j
public class CommonDataService {

    @Resource
    private IWebServiceBridgeClient webserviceBridgeClient;

    @Resource
    private DeploymentEnv deploymentEnv;

    @Resource
    private NetTransferProperties netTransferProperties;

    @Resource
    private FtpEnvProperties ftpEnvProperties;

    @Resource
    private DataServiceBridge dataServiceBridge;

    @Resource
    private FtpDownloadHandlerFactory ftpDownloadHandlerFactory;

    @PostConstruct
    public void init() {
        log.warn("current deploy env：{}, need transfer env configed:{}", getDeploymentEnv(), netTransferProperties.getList());
    }

    /**
     * 获取当前部署环境
     *
     * @return
     */
    public String getDeploymentEnv() {
        return deploymentEnv.getDeploymentEnv();
    }

    public Object fetchData(HttpApiRequest request) {
        // 是否需要走网闸
        if (transferToNetEdge()) {
            // 政府内网环境从边界通道调用外网时空数据服务，例如衢州政府走网闸webservice调用政务网时空数据网关，再调用阿里云时空数据服务
            log.info("公安内网走网闸调用政务网服务");
            return fetchDataViaNetworkGateway(request);
        }

        // 直接调用ftp服务返回文件解析
        if (accessFtp()) {
            log.info("深圳内网走FTP文件服务器");
            return ftpDownloadHandlerFactory.handle(request);
        }

        // 直接调用时空数据服务的API
        return callDataService(request);
    }

    private Object callDataService(HttpApiRequest request) {
        long start = System.currentTimeMillis();
        String fetchParams = JSON.toJSONString(request, JSONWriter.Feature.WriteClassName);
        BridgeDataResponse bridgeDataResponse = dataServiceBridge.callDataService(fetchParams);
        log.info("调用时空数据服务成功,耗时:{}ms", System.currentTimeMillis() - start);
        if (bridgeDataResponse == null) {
            log.error("调用时空数据服务失败");
            return null;
        }
        return bridgeDataResponse.getData();
    }

    /**
     * 判断是否需要走ftp拉取文件
     *
     * @return true | false
     */
    private boolean accessFtp() {
        // 环境条件判断
        return CollectionUtil.contains(ftpEnvProperties.getList()   , getDeploymentEnv());
    }

    /**
     * 判断是否直接走时空数据服务
     *
     * @return
     */
    private boolean transferToNetEdge() {
        // 环境条件判断
        return CollectionUtil.contains(netTransferProperties.getList(), getDeploymentEnv());
    }

    private Object fetchDataViaNetworkGateway(HttpApiRequest request) {
        return webserviceBridgeClient.invoke(request);
    }
}
