package com.deepinnet.spatiotemporalplatform.skyflow.service.impl;

import com.deepinnet.spatiotemporalplatform.skyflow.condition.WeatherIntelligenceQueryCondition;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.WeatherIntelligenceDO;
import com.deepinnet.spatiotemporalplatform.dto.WeatherIntelligenceQueryDTO;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.WeatherIntelligenceRepository;
import com.deepinnet.spatiotemporalplatform.skyflow.service.WeatherIntelligenceService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 管控情报服务接口
 *
 * <AUTHOR> wong
 */
@Service
public class WeatherIntelligenceServiceImpl implements WeatherIntelligenceService {

    @Resource
    private WeatherIntelligenceRepository weatherIntelligenceRepository;

    @Override
    public boolean save(WeatherIntelligenceDO weatherIntelligenceDO) {
        return weatherIntelligenceRepository.save(weatherIntelligenceDO);
    }

    @Override
    public boolean saveBatch(List<WeatherIntelligenceDO> weatherIntelligenceDOList) {
        return weatherIntelligenceRepository.saveBatch(weatherIntelligenceDOList);
    }

    @Override
    public WeatherIntelligenceDO getById(Long id) {
        return weatherIntelligenceRepository.getById(id);
    }

    @Override
    public List<WeatherIntelligenceDO> listByTimeRange(WeatherIntelligenceQueryDTO queryDTO) {
        WeatherIntelligenceQueryCondition queryCondition = new WeatherIntelligenceQueryCondition();
        queryCondition.setStartTime(queryDTO.getStartTime());
        queryCondition.setEndTime(queryDTO.getEndTime());
        return weatherIntelligenceRepository.listByCondition(queryCondition);
    }

    @Override
    public boolean updateById(WeatherIntelligenceDO weatherIntelligenceDO) {
        return weatherIntelligenceRepository.updateById(weatherIntelligenceDO);
    }

    @Override
    public boolean removeById(Long id) {
        return weatherIntelligenceRepository.removeById(id);
    }

    @Override
    public boolean removeByIds(List<Long> ids) {
        return weatherIntelligenceRepository.removeByIds(ids);
    }
}