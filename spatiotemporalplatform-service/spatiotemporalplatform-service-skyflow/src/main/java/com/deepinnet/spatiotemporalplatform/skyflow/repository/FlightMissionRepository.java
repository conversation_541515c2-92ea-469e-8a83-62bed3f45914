package com.deepinnet.spatiotemporalplatform.skyflow.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightMissionDO;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightQuery;

import java.util.List;

/**
 * <p>
 * 飞行任务表，记录飞行任务的基本信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
public interface FlightMissionRepository extends IService<FlightMissionDO> {

    /**
     * 查询飞行任务列表
     * @param flightQuery 查询条件
     * @return 飞行任务列表
     */
    List<FlightMissionDO> listFlightMissionByCondition(FlightQuery flightQuery);
}
