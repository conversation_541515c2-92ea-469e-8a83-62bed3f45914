package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.AerodromeDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.AerodromeMapper;
import com.deepinnet.spatiotemporalplatform.model.skyflow.LandingPoint;
import com.deepinnet.spatiotemporalplatform.skyflow.convert.LandingPointConvert;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.AerodromeRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 机场（起降场地）信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
@Service
public class AerodromeRepositoryImpl extends ServiceImpl<AerodromeMapper, AerodromeDO> implements AerodromeRepository {
    @Resource
    private LandingPointConvert landingPointConvert;

    @Override
    public List<LandingPoint> queryAllLandingPoint() {
        return list().stream().map(item -> landingPointConvert.toDomain(item)).collect(Collectors.toList());
    }
}
