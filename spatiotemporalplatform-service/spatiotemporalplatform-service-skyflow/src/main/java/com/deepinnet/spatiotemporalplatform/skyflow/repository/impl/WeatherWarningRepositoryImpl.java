package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.*;
import com.deepinnet.spatiotemporalplatform.dal.mapper.WeatherWarningMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.WeatherWarningRepository;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * 天气预警Repository实现类
 *
 * <AUTHOR>
 */
@Repository
public class WeatherWarningRepositoryImpl extends ServiceImpl<WeatherWarningMapper, WeatherWarningDO>
        implements WeatherWarningRepository {

    @Resource
    private WeatherWarningMapper weatherWarningMapper;

    @Override
    public List<IntelligenceDO> getIntelligenceList(Long startTime, Long endTime) {
        return weatherWarningMapper.getIntelligenceList(startTime, endTime);
    }

    @Override
    public List<IntelligenceDO> getHistoryIntelligenceList(Long startTime, Long endTime) {
        return weatherWarningMapper.getHistoryIntelligenceList(startTime, endTime);
    }
}