package com.deepinnet.spatiotemporalplatform.skyflow.task;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightPlanDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.RiskWarningDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.TaskFlightWarningsDO;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.FlightPlanRepository;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.RiskWarningRepository;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.TaskFlightWarningsRepository;
import com.deepinnet.spatiotemporalplatform.skyflow.util.beidou.HistoryAverageCalUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Profile;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *   累计预警统计任务
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/5
 */
@Component
@Profile({"local", "test","alicloud", "demo", "sz-lg-gov"})
@RequiredArgsConstructor
public class FlightWarningTask {

    private final TaskFlightWarningsRepository taskFlightWarningsRepository;

    private final RiskWarningRepository riskWarningRepository;

    private final FlightPlanRepository flightPlanRepository;

    @Scheduled(cron = "0 0/2 * * * *")
    public void statistics() {
        LogUtil.info("[飞行概览]-[累计预警]任务开始执行");

        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalDate localDate = currentDateTime.toLocalDate();

        List<String> tenantIdList = Optional.ofNullable(
                        flightPlanRepository.list(
                                Wrappers.<FlightPlanDO>lambdaQuery()
                                        .select(FlightPlanDO::getTenantId)
                                        .groupBy(FlightPlanDO::getTenantId)
                        ))
                .orElseGet(Collections::emptyList)
                .stream()
                .filter(Objects::nonNull)
                .map(FlightPlanDO::getTenantId)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(tenantIdList)) {
            LogUtil.warn("[飞行概览]-[累计预警]任务暂无租户数据");
            return;
        }

        tenantIdList.forEach(tenantId -> {

            // 获取当前时间段的预警数量
            int currentFlightCount = countUniqueFlightsInTimeWindow(tenantId);

            // 获取当前时间的小时和分钟，格式化为HH:mm
            String currentTimeStr = currentDateTime.format(DateTimeFormatter.ofPattern("HH:mm"));

            // 计算历史同期的数据平均值
            String historyAvgCount = calculateHistoricalAverage(currentDateTime, tenantId);

            // 保存统计结果到TaskFlightSortiesDO
            TaskFlightWarningsDO taskFlightWarningDO = new TaskFlightWarningsDO();
            taskFlightWarningDO.setStatisticalDate(localDate);
            taskFlightWarningDO.setStatisticalTime(currentTimeStr);
            taskFlightWarningDO.setStatisticalCount(currentFlightCount);
            taskFlightWarningDO.setStatisticalHistoryAvgCount(historyAvgCount);
            taskFlightWarningDO.setGmtCreated(currentDateTime);
            taskFlightWarningDO.setGmtModified(currentDateTime);
            taskFlightWarningDO.setTenantId(tenantId);

            // 保存统计结果
            try {
                taskFlightWarningsRepository.save(taskFlightWarningDO);
            } catch (DuplicateKeyException duplicateKeyException) {
                LogUtil.warn("[飞行概览]-[累计预警], 统计数据:{}, 重复保存!", JSONObject.toJSONString(taskFlightWarningDO));
            } catch (Exception e) {
                LogUtil.error("[飞行概览]-[累计预警], 统计数据:{}, 保存失败!", JSONObject.toJSONString(taskFlightWarningDO), e);
            }

        });

    }
    
    /**
     * 计算历史同期的平均无人机飞行数量
     * 历史同期定义为：前四周同一星期几的同一时间段
     * 
     * @param currentDateTime 当前时间
     * @return 历史同期平均值
     */
    private String calculateHistoricalAverage(LocalDateTime currentDateTime, String tenantId) {
        // 获取当前星期几
        DayOfWeek currentDayOfWeek = currentDateTime.getDayOfWeek();
        
        // 获取当前时间（小时和分钟）
        LocalTime currentTime = currentDateTime.toLocalTime().withSecond(0).withNano(0);
        
        // 历史同期开始时间（往前推2分钟）
        LocalTime historyStartTime = currentTime.minusMinutes(2);
        
        // 历史数据列表
        List<Integer> historicalCounts = new ArrayList<>();
        
        // 查询前1周同一星期几同一时间段的数据
        for (int weekOffset = 1; weekOffset <= 1; weekOffset++) {
            // 计算对应的历史日期（前 weekOffset 周的同一星期几）
            LocalDate historicalDate = getHistoricalDateForSameDayOfWeek(currentDateTime, weekOffset, currentDayOfWeek);

            // 获取历史时间段的预警数量
            Integer historicalCount = calHistory(historicalDate, currentTime, tenantId);

            if (ObjectUtil.isNull(historicalCount)) {
                continue;
            }

            historicalCounts.add(historicalCount);
        }
        
        // 计算平均值，如果没有历史数据，返回0
        return calculateAverage(historicalCounts);
    }

    private Integer calHistory(LocalDate historicalDate, LocalTime currentTime, String tenantId) {
        TaskFlightWarningsDO warningsDO = taskFlightWarningsRepository.getOne(Wrappers.<TaskFlightWarningsDO>lambdaQuery()
                .eq(TaskFlightWarningsDO::getStatisticalDate, historicalDate)
                .eq(TaskFlightWarningsDO::getStatisticalTime, currentTime.toString())
                .eq(TaskFlightWarningsDO::getTenantId, tenantId));
        return ObjectUtil.isNull(warningsDO) ? null : warningsDO.getStatisticalCount();
    }

    /**
     * 计算指定时间窗口内的无人机飞行数量（去重）
     *
     * @return 无人机数量
     */
    private int countUniqueFlightsInTimeWindow(String tenantId) {
        // 获取当天起始时间和结束时间
        LocalDate today = LocalDate.now();
        LocalDateTime startOfDay = today.atStartOfDay();
        LocalDateTime endOfDay = today.atTime(23, 59, 59);

        // 查询当天全部的预警数据
        LambdaQueryWrapper<RiskWarningDO> queryWrapper = Wrappers.lambdaQuery(RiskWarningDO.class)
                .ge(RiskWarningDO::getWarningTime, startOfDay)
                .le(RiskWarningDO::getWarningTime, endOfDay)
                .eq(RiskWarningDO::getTenantId, tenantId)
                .orderByDesc(RiskWarningDO::getWarningTime);

        List<RiskWarningDO> flights = riskWarningRepository.list(queryWrapper);
        
        if (CollectionUtils.isEmpty(flights)) {
            return 0;
        }

        return flights.size();
    }
    
    /**
     * 计算前N周的同一星期几的日期
     *
     * @param currentDateTime 当前日期时间
     * @param weekOffset 周偏移量
     * @param targetDayOfWeek 目标星期几
     * @return 计算得到的历史日期
     */
    private LocalDate getHistoricalDateForSameDayOfWeek(LocalDateTime currentDateTime, int weekOffset, DayOfWeek targetDayOfWeek) {
        // 计算对应的历史日期（前 weekOffset 周的同一星期几）
        LocalDate historicalDate = currentDateTime.toLocalDate().minusWeeks(weekOffset);
        // 调整到同一星期几
        while (!historicalDate.getDayOfWeek().equals(targetDayOfWeek)) {
            historicalDate = historicalDate.plusDays(1);
        }
        return historicalDate;
    }
    
    /**
     * 计算整数列表的平均值
     *
     * @param numbers 整数列表
     * @return 平均值（四舍五入为整数）
     */
    private String calculateAverage(List<Integer> numbers) {
        if (CollectionUtils.isEmpty(numbers)) {
            return "0";
        }

        BigDecimal average = BigDecimal.valueOf(numbers.stream()
                        .mapToInt(Integer::intValue).average()
                        .orElse(0))
                .setScale(0, RoundingMode.DOWN)
                .stripTrailingZeros();

        return average.toString();
    }
}
