package com.deepinnet.spatiotemporalplatform.skyflow.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.*;
import com.deepinnet.spatiotemporalplatform.dto.*;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.*;
import com.deepinnet.spatiotemporalplatform.skyflow.service.FlightDetectionService;
import com.deepinnet.spatiotemporalplatform.skyflow.util.beidou.TimeUtil;
import com.github.pagehelper.*;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.*;

/**
 * 飞行监测领域服务
 *
 * <AUTHOR> wong
 */
@Service
public class FlightDetectionServiceImpl implements FlightDetectionService {

    @Resource
    private UnplannedFlightDetectionRepository unplannedFlightDetectionRepository;

    @Resource
    private FlightDetectionRepository flightDetectionRepository;

    @Override
    public CommonPage<FlightDetectionDTO> pageFlightDetection(FlightDetectionQueryDTO queryDTO) {
        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        LambdaQueryWrapper<UnplannedFlightDetectionDO> wrappers;
        if (queryDTO.getStartTime() == null || queryDTO.getEndTime() == null) {
            wrappers = Wrappers.lambdaQuery(UnplannedFlightDetectionDO.class)
                    .ge(UnplannedFlightDetectionDO::getReportedTime, TimeUtil.getStartOfDay())
                    .le(UnplannedFlightDetectionDO::getReportedTime, TimeUtil.getEndOfDay())
                    .orderByDesc(UnplannedFlightDetectionDO::getReportedTime);
        } else {
            wrappers = Wrappers.lambdaQuery(UnplannedFlightDetectionDO.class)
                    .ge(queryDTO.getStartTime() != null, UnplannedFlightDetectionDO::getReportedTime, queryDTO.getStartTime())
                    .le(queryDTO.getEndTime() != null, UnplannedFlightDetectionDO::getReportedTime, queryDTO.getEndTime())
                    .orderByDesc(UnplannedFlightDetectionDO::getReportedTime);
        }

        List<UnplannedFlightDetectionDO> unplannedFlights = unplannedFlightDetectionRepository.list(wrappers);
        if (CollectionUtils.isEmpty(unplannedFlights)) {
            return CommonPage.buildEmptyPage();
        }

        List<UnplannedFlightDetectionDO> activeFlightDetections = unplannedFlights.stream()
                .filter(u -> u.getEndFlightDetectionId() == null)
                .collect(Collectors.toList());
        List<UnplannedFlightDetectionDO> completeFlightDetections = unplannedFlights.stream()
                .filter(u -> u.getEndFlightDetectionId() != null)
                .collect(Collectors.toList());

        Map<String, FlightDetectionDO> activeLatestFlightMap = new HashMap<>();

        // 这些数据查询最新的记录
        if (CollUtil.isNotEmpty(activeFlightDetections)) {
            List<String> snCodes = activeFlightDetections.stream()
                    .map(UnplannedFlightDetectionDO::getSn)
                    .distinct()
                    .collect(Collectors.toList());
            List<FlightDetectionDO> osIdLatestFlight = flightDetectionRepository.getOsIdLatestFlight(snCodes);
            activeLatestFlightMap = osIdLatestFlight.stream()
                    .collect(Collectors.toMap(FlightDetectionDO::getOsid, Function.identity()));
        }

        List<FlightDetectionDO> unPlannedFlightDetections = getUnplannedFlightDetections(completeFlightDetections);

        // 将飞行检测记录按SN码分组
        Map<String, List<FlightDetectionDO>> completeFlightDetectionMap = unPlannedFlightDetections.stream()
                .collect(Collectors.groupingBy(FlightDetectionDO::getOsid));

        // 组装FlightDetectionDTO列表
        List<FlightDetectionDTO> flightDetectionDTOList = buildFlightDetectionDTOList(unplannedFlights, completeFlightDetectionMap, activeLatestFlightMap);

        PageInfo<UnplannedFlightDetectionDO> pageInfo = PageInfo.of(unplannedFlights);
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPages(page.getPages());
        return CommonPage.buildPage(queryDTO.getPageNum(), pageInfo.getPageSize(), pageInfo.getPages(), pageInfo.getTotal(), flightDetectionDTOList);
    }

    @Override
    public List<FlightPathDTO> getFlightDetectionPath(FlightPathQueryDTO queryDTO) {
        List<UnplannedFlightDetectionDO> unplannedFlightDetectionDOs;

        if (CollectionUtils.isNotEmpty(queryDTO.getUnplannedFlightIds())) {
            unplannedFlightDetectionDOs = unplannedFlightDetectionRepository.listByIds(queryDTO.getUnplannedFlightIds());
        } else {
            unplannedFlightDetectionDOs = unplannedFlightDetectionRepository.list(Wrappers.lambdaQuery(UnplannedFlightDetectionDO.class)
                    .ge(UnplannedFlightDetectionDO::getReportedTime, TimeUtil.getStartOfDay())
                    .le(UnplannedFlightDetectionDO::getReportedTime, TimeUtil.getEndOfDay())
                    .in(CollUtil.isNotEmpty(queryDTO.getTargetStatusList()), UnplannedFlightDetectionDO::getStatus, queryDTO.getTargetStatusList()));
        }

        if (CollectionUtils.isEmpty(unplannedFlightDetectionDOs)) {
            return null;
        }

        // 获取飞行轨迹
        List<FlightPathDTO> flightPathDTOList = new ArrayList<>();

        Map<Long, UnplannedFlightDetectionDO> unplannedFlightMap = unplannedFlightDetectionDOs.stream()
                .collect(Collectors.toMap(UnplannedFlightDetectionDO::getId, Function.identity()));

        // 给未结束的黑飞记录来填充最新的飞行数据
        Map<Long, List<FlightDetectionDO>> unplannedFlightPath = getUnplannedFullFlightDetectionList(unplannedFlightDetectionDOs);
        unplannedFlightPath.forEach((k, v) -> {
            FlightPathDTO flightPathDTO = new FlightPathDTO();
            UnplannedFlightDetectionDO unplannedFlightDetectionDO = unplannedFlightMap.get(k);

            if (unplannedFlightDetectionDO != null) {
                flightPathDTO.setStatus(unplannedFlightDetectionDO.getStatus());
            }

            flightPathDTO.setUnplannedFlightId(k);
            flightPathDTO.setFlightPath(buildFlightDetectionDTOs(v));
            flightPathDTOList.add(flightPathDTO);
        });

        return flightPathDTOList;
    }

    private Map<Long, List<FlightDetectionDO>> getUnplannedFullFlightDetectionList(List<UnplannedFlightDetectionDO> unplannedFlightDetectionDOs) {
        // 查询每个黑飞记录对应的从startFlightDetectionId到endFlightDetectionId之间的所有FlightDetectionDO数据
        Map<Long, List<FlightDetectionDO>> unplannedIdToFlightDetectionsMap = new HashMap<>();
        for (UnplannedFlightDetectionDO unplannedFlight : unplannedFlightDetectionDOs) {
            Long startId = unplannedFlight.getStartFlightDetectionId();
            Long endId = unplannedFlight.getEndFlightDetectionId();

            // 如果没有结束ID，则只查询从开始ID到当前的所有记录
            List<FlightDetectionDO> flightDetections;
            if (endId == null) {
                flightDetections = flightDetectionRepository.list(
                        Wrappers.lambdaQuery(FlightDetectionDO.class)
                                .ge(FlightDetectionDO::getId, startId)
                                .eq(FlightDetectionDO::getOsid, unplannedFlight.getSn())
                                .orderByAsc(FlightDetectionDO::getTime)
                );
            } else {
                // 如果有结束ID，则查询开始ID到结束ID之间的所有记录
                flightDetections = flightDetectionRepository.list(
                        Wrappers.lambdaQuery(FlightDetectionDO.class)
                                .ge(FlightDetectionDO::getId, startId)
                                .le(FlightDetectionDO::getId, endId)
                                .eq(FlightDetectionDO::getOsid, unplannedFlight.getSn())
                                .orderByAsc(FlightDetectionDO::getTime)
                );
            }
            unplannedIdToFlightDetectionsMap.put(unplannedFlight.getId(), flightDetections);
        }

        return unplannedIdToFlightDetectionsMap;
    }

    private Map<Long, FlightDetectionDO> getNewestFlightDetection(List<UnplannedFlightDetectionDO> unplannedFlightDetectionDOs) {
        // 查询每个黑飞记录对应的从startFlightDetectionId到endFlightDetectionId之间的所有FlightDetectionDO数据
        Map<Long, FlightDetectionDO> unplannedIdToFlightDetectionsMap = new HashMap<>();
        for (UnplannedFlightDetectionDO unplannedFlight : unplannedFlightDetectionDOs) {

            Long startId = unplannedFlight.getStartFlightDetectionId();
            Long endId = unplannedFlight.getEndFlightDetectionId();

            // 如果没有结束ID，则只查询从开始ID到当前的所有记录
            List<FlightDetectionDO> flightDetections;
            if (endId == null) {
                flightDetections = flightDetectionRepository.list(
                        Wrappers.lambdaQuery(FlightDetectionDO.class)
                                .ge(FlightDetectionDO::getId, startId)
                                .eq(FlightDetectionDO::getOsid, unplannedFlight.getSn())
                                .orderByDesc(FlightDetectionDO::getTime)
                                .orderByDesc(FlightDetectionDO::getId)
                                .last(" LIMIT 1")
                );

            } else {
                // 如果有结束ID，则查询开始ID到结束ID之间的所有记录
                flightDetections = flightDetectionRepository.list(
                        Wrappers.lambdaQuery(FlightDetectionDO.class)
                                .ge(FlightDetectionDO::getId, startId)
                                .le(FlightDetectionDO::getId, endId)
                                .eq(FlightDetectionDO::getOsid, unplannedFlight.getSn())
                                .orderByDesc(FlightDetectionDO::getTime)
                                .orderByDesc(FlightDetectionDO::getId)
                                .last(" LIMIT 1")
                );
            }

            unplannedIdToFlightDetectionsMap.put(unplannedFlight.getId(), flightDetections.get(0));
        }

        return unplannedIdToFlightDetectionsMap;
    }

    @Override
    public List<FlightDetectionLatestPathDTO> getLatestFlightPosition(FlightDetectionLatestPositionDTO queryDTO) {
        List<UnplannedFlightDetectionDO> unplannedFlightDetectionDOs = unplannedFlightDetectionRepository.list(Wrappers.lambdaQuery(UnplannedFlightDetectionDO.class)
                .ge(UnplannedFlightDetectionDO::getReportedTime, TimeUtil.getStartOfDay())
                .le(UnplannedFlightDetectionDO::getReportedTime, TimeUtil.getEndOfDay())
                .in(CollUtil.isNotEmpty(queryDTO.getTargetStatusList()), UnplannedFlightDetectionDO::getStatus, queryDTO.getTargetStatusList()));
        Map<Long, FlightDetectionDO> newestFlightDetectionMap = getNewestFlightDetection(unplannedFlightDetectionDOs);

        Map<Long, UnplannedFlightDetectionDO> unplannedFlightDetectionMap = unplannedFlightDetectionDOs.stream()
                .collect(Collectors.toMap(UnplannedFlightDetectionDO::getId, Function.identity()));

        List<FlightDetectionLatestPathDTO> flightDetectionLatestPathDTOList = new ArrayList<>();
        newestFlightDetectionMap.forEach((k, v) -> {
            UnplannedFlightDetectionDO unplannedFlightDetectionDO = unplannedFlightDetectionMap.get(k);
            FlightDetectionLatestPathDTO flightDetectionLatestPathDTO = new FlightDetectionLatestPathDTO();
            flightDetectionLatestPathDTO.setUnplannedFlightId(k);
            if (unplannedFlightDetectionDO != null) {
                flightDetectionLatestPathDTO.setStatus(unplannedFlightDetectionDO.getStatus());
            }
            flightDetectionLatestPathDTO.setLatestFlightPath(buildFlightDetectionDTO(v));
            flightDetectionLatestPathDTOList.add(flightDetectionLatestPathDTO);
        });

        return flightDetectionLatestPathDTOList;
    }

    private List<FlightDetectionDTO> buildFlightDetectionDTOList(List<UnplannedFlightDetectionDO> unplannedFlights, Map<String, List<FlightDetectionDO>> completeFlightDetectionMap,
                                                                 Map<String, FlightDetectionDO> activeLatestFlightMap) {
        List<FlightDetectionDTO> flightDetectionDTOList = Lists.newArrayList();

        for (UnplannedFlightDetectionDO unplannedFlight : unplannedFlights) {
            String sn = unplannedFlight.getSn();
            Long endId = unplannedFlight.getEndFlightDetectionId();

            FlightDetectionDO latestFlightDetection;
            if (unplannedFlight.getEndFlightDetectionId() != null) {
                // 获取该SN码对应的所有飞行检测记录
                List<FlightDetectionDO> snFlightDetections = completeFlightDetectionMap.getOrDefault(sn, Collections.emptyList());
                if (CollectionUtils.isEmpty(snFlightDetections)) {
                    continue;
                }

                List<FlightDetectionDO> currentFlightDetectionDOs = snFlightDetections.stream()
                        .filter(flightDetection -> Objects.equals(flightDetection.getId(), endId))
                        .collect(Collectors.toList());
                latestFlightDetection = currentFlightDetectionDOs.get(0);
            } else {
                latestFlightDetection = activeLatestFlightMap.get(sn);
                if (latestFlightDetection == null) {
                    continue;
                }
            }

            // 创建DTO对象
            FlightDetectionDTO dto = buildFlightDetectionDTO(unplannedFlight.getId(), unplannedFlight.getReportedTime(), latestFlightDetection);
            dto.setStatus(unplannedFlight.getStatus());
            flightDetectionDTOList.add(dto);
        }

        // 按照上报时间倒序排序
        flightDetectionDTOList.sort(Comparator.comparing(FlightDetectionDTO::getTime).reversed());
        return flightDetectionDTOList;
    }

    private static FlightDetectionDTO buildFlightDetectionDTO(Long unplannedId, Long reportTime, FlightDetectionDO latestFlightDetection) {
        FlightDetectionDTO dto = buildFlightDetectionDTO(latestFlightDetection);
        dto.setTime(reportTime);
        dto.setId(unplannedId);
        return dto;
    }

    private List<FlightDetectionDO> getUnplannedFlightDetections(List<UnplannedFlightDetectionDO> unplannedFlights) {
        // 所有需要查询的SN码集合
        List<String> snCodeList = unplannedFlights.stream()
                .map(UnplannedFlightDetectionDO::getSn)
                .distinct()
                .collect(Collectors.toList());

        // 查询所有未计划飞行的起始和结束飞行检测ID
        List<Long> startFlightDetectionIds = unplannedFlights.stream()
                .map(UnplannedFlightDetectionDO::getStartFlightDetectionId)
                .distinct()
                .collect(Collectors.toList());

        List<Long> endFlightDetectionIds = unplannedFlights.stream()
                .map(UnplannedFlightDetectionDO::getEndFlightDetectionId)
                .distinct()
                .collect(Collectors.toList());

        // 合并所有需要查询的飞行检测ID
        List<Long> allFlightDetectionIds = Stream.concat(startFlightDetectionIds.stream(), endFlightDetectionIds.stream())
                .distinct()
                .collect(Collectors.toList());

        // 根据SN码和飞行检测ID查询飞行监测记录
        List<FlightDetectionDO> flightDetections = CollectionUtils.isEmpty(allFlightDetectionIds) ?
                Collections.emptyList() :
                flightDetectionRepository.list(Wrappers.lambdaQuery(FlightDetectionDO.class)
                        .in(FlightDetectionDO::getId, allFlightDetectionIds)
                        .in(FlightDetectionDO::getOsid, snCodeList));
        return flightDetections;
    }

    private static List<FlightDetectionDTO> buildFlightDetectionDTOs(List<FlightDetectionDO> flightDetections) {
        List<FlightDetectionDTO> flightDetectionDTOList = Lists.newArrayList();
        flightDetections.forEach(flightDetection -> {
            FlightDetectionDTO flightDetectionDTO = buildFlightDetectionDTO(flightDetection);
            flightDetectionDTOList.add(flightDetectionDTO);
        });
        return flightDetectionDTOList;
    }

    private static FlightDetectionDTO buildFlightDetectionDTO(FlightDetectionDO flightDetection) {
        FlightDetectionDTO dto = new FlightDetectionDTO();
        dto.setDevId(flightDetection.getDevId());
        dto.setBigType(flightDetection.getBigType());
        dto.setLatitude(flightDetection.getLatitude());
        dto.setLongitude(flightDetection.getLongitude());
        dto.setModel(flightDetection.getModel());
        dto.setOsid(flightDetection.getOsid());
        dto.setUaType(flightDetection.getUaType());
        dto.setDirection(flightDetection.getDirection());
        if (ObjectUtil.isNotNull(flightDetection.getSpeedHorizontal())) {
            dto.setSpeedHorizontal(new BigDecimal(flightDetection.getSpeedHorizontal()).multiply(new BigDecimal("3.6")).stripTrailingZeros().toPlainString());
        }

        if (ObjectUtil.isNotNull(flightDetection.getSpeedVertical())) {
            dto.setSpeedVertical(new BigDecimal(flightDetection.getSpeedVertical()).multiply(new BigDecimal("3.6")).stripTrailingZeros().toPlainString());
        }
        dto.setAltitude(flightDetection.getAltitude());
        dto.setAltitudeBaro(flightDetection.getAltitudeBaro());
        dto.setHeight(flightDetection.getHeight());
        dto.setOperatorLongitude(flightDetection.getOperatorLongitude());
        dto.setOperatorLatitude(flightDetection.getOperatorLatitude());
        dto.setOperatorAltitudeGeo(flightDetection.getOperatorAltitudeGeo());
        dto.setRssi(flightDetection.getRssi());
        dto.setRssi0(flightDetection.getRssi0());
        dto.setRssi1(flightDetection.getRssi1());
        dto.setTemperature(flightDetection.getTemperature());
        dto.setMac(flightDetection.getMac());
        dto.setFrequency(flightDetection.getFrequency());
        dto.setBandwidth(flightDetection.getBandwidth());
        return dto;
    }

}