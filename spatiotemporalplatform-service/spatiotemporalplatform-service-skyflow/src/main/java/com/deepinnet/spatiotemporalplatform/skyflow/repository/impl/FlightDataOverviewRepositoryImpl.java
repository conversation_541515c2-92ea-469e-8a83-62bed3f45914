package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightDataOverviewDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.FlightDataOverviewMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.FlightDataOverviewRepository;
import org.springframework.stereotype.Repository;

/**
 * 飞行数据概览Repository实现类
 *
 * <AUTHOR> wong
 * @create 2025/01/14
 */
@Repository
public class FlightDataOverviewRepositoryImpl extends ServiceImpl<FlightDataOverviewMapper, FlightDataOverviewDO> 
        implements FlightDataOverviewRepository {
} 