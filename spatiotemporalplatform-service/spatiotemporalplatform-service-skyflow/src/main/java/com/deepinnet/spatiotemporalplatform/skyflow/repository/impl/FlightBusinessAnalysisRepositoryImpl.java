package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightBusinessAnalysisDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.FlightBusinessAnalysisMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.FlightBusinessAnalysisRepository;
import org.springframework.stereotype.Repository;

/**
 * 飞行业务分析Repository实现类
 *
 * <AUTHOR> wong
 * @create 2025/01/14
 */
@Repository
public class FlightBusinessAnalysisRepositoryImpl extends ServiceImpl<FlightBusinessAnalysisMapper, FlightBusinessAnalysisDO> 
        implements FlightBusinessAnalysisRepository {
} 