package com.deepinnet.spatiotemporalplatform.skyflow.service;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.vo.DetectedDeviceVO;

import java.util.List;

/**
 * 侦测设备Service接口
 *
 * <AUTHOR>
 */
public interface DetectedDeviceService {

    /**
     * 分页查询侦测设备数据
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 分页数据
     */
    CommonPage<DetectedDeviceVO> pageQueryDetectedDevices(Integer pageNum, Integer pageSize);

    /**
     * 查询所有侦测设备数据
     *
     * @return 侦测设备数据列表
     */
    List<DetectedDeviceVO> listAllDetectedDevices();

    /**
     * 统计侦测设备总数
     *
     * @return 侦测设备总数
     */
    int countDetectedDevices();
} 