package com.deepinnet.spatiotemporalplatform.skyflow.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.AirspaceDO;

import java.util.List;

/**
 * <p>
 * 空域 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
public interface AirspaceRepository extends IService<AirspaceDO> {

    List<AirspaceDO> listByCodes(List<String> codes);
}