package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.RiskWarningDO;
import com.deepinnet.spatiotemporalplatform.dal.dto.*;
import com.deepinnet.spatiotemporalplatform.dal.mapper.RiskWarningMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.condition.RiskWarningQueryCondition;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.RiskWarningRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 风险预警Repository实现类
 *
 * <AUTHOR>
 */
@Repository
@Slf4j
public class RiskWarningRepositoryImpl extends ServiceImpl<RiskWarningMapper, RiskWarningDO>
        implements RiskWarningRepository {

    @Resource
    private RiskWarningMapper riskWarningMapper;

    @Override
    public boolean save(RiskWarningDO riskWarningDO) {
        return super.save(riskWarningDO);
    }

    @Override
    public boolean saveBatch(List<RiskWarningDO> riskWarningDOList) {
        return super.saveBatch(riskWarningDOList);
    }

    @Override
    public RiskWarningDO getById(Long id) {
        return super.getById(id);
    }

    @Override
    public RiskWarningDO getByCondition(LambdaQueryWrapper<RiskWarningDO> queryWrapper) {
        return super.getOne(queryWrapper);
    }

    @Override
    public List<RiskWarningDO> list(LambdaQueryWrapper<RiskWarningDO> queryWrapper) {
        return super.list(queryWrapper);
    }

    @Override
    public List<RiskWarningDO> listByTypeAndTimeRange(RiskWarningQueryCondition queryCondition) {
        LocalDateTime startTime = queryCondition.getStartTime();
        LocalDateTime endTime = queryCondition.getEndTime();

        LambdaQueryWrapper<RiskWarningDO> wrapper = new LambdaQueryWrapper<RiskWarningDO>()
                .ge(startTime != null, RiskWarningDO::getWarningTime, startTime)
                .le(endTime != null, RiskWarningDO::getWarningTime, endTime)
                .in(CollUtil.isNotEmpty(queryCondition.getNeedWarningTypeList()), RiskWarningDO::getSubType, queryCondition.getNeedWarningTypeList())
                .orderByDesc(RiskWarningDO::getWarningTime);
        return super.list(wrapper);
    }

    @Override
    public List<SubTypeCountDTO> getSubTypeCountList(RiskWarningQueryCondition queryCondition) {
        return riskWarningMapper.getSubTypeCountList(queryCondition.getStartTime(), queryCondition.getEndTime(), queryCondition.getNeedWarningTypeList());
    }

    @Override
    public List<StatusCountDTO> getStatusCountList(RiskWarningQueryCondition queryCondition) {
        return riskWarningMapper.getStatusCountList(queryCondition.getStartTime(), queryCondition.getEndTime(), queryCondition.getNeedWarningTypeList());
    }

    @Override
    public List<RiskWarningDO> listByWarningStatus(String warningStatus) {
        LambdaQueryWrapper<RiskWarningDO> wrapper = new LambdaQueryWrapper<RiskWarningDO>()
                .eq(StringUtils.hasLength(warningStatus), RiskWarningDO::getWarningStatus, warningStatus)
                .orderByDesc(RiskWarningDO::getGmtCreated);

        return super.list(wrapper);
    }
    
    @Override
    public List<RiskWarningDO> listTopRiskWarningsByPlanIds(List<String> planIds, Integer limit) {
        if (CollUtil.isEmpty(planIds)) {
            return null;
        }
        return riskWarningMapper.listTopRiskWarningsByPlanIds(planIds, limit);
    }

    public Long getRiskWarningCount(Long startTime, Long endTime) {
        return super.count(Wrappers.lambdaQuery(RiskWarningDO.class)
                .ge(RiskWarningDO::getWarningTime, LocalDateTimeUtil.of(startTime))
                .le(RiskWarningDO::getWarningTime, LocalDateTimeUtil.of(endTime)));
    }

    @Override
    public boolean updateById(RiskWarningDO riskWarningDO) {
        return super.updateById(riskWarningDO);
    }

    @Override
    public boolean updateBatchByIds(List<RiskWarningDO> riskWarningDOs) {
        return super.updateBatchById(riskWarningDOs);
    }

    @Override
    public boolean removeById(Long id) {
        return super.removeById(id);
    }

    @Override
    public boolean removeByIds(List<Long> ids) {
        return super.removeByIds(ids);
    }
} 