package com.deepinnet.spatiotemporalplatform.skyflow.mqtt.body;

import lombok.Data;

/**
 * {
 *             "flightId": "7213395440224afd8c6655c6c65cc350",
 *                 "id": "b8fe8e6ca8584a8d92961fe290fe725f",
 *                 "message": "航空器触发闯入禁飞区告警",
 *                 "planId": "b2f9735ffcb34e3ba954470858f00106",
 *                 "teamId": "9467d9f1ed7a43d287d850629b97fddf",
 *                 "tenantId": "ac88ceb386aa4231b09bf472cb937c24",
 *                 "type": "no_fly",
 *                 "uavId": "6a6b310a605045f988af38144d0960e6",
 *                 "userId": "c1075e1baf7549e1af3c70376a07a74d",
 *                 "warningType": "3"
 *  }
 */
@Data
public class WarningMsgBody {
    /**
     * 架次ID
     */
    private String flightId;
    /**
     * 消息ID
     */
    private String id;
    /**
     * 消息内容
     */
    private String message;
    /**
     * 计划ID
     */
    private String planId;
    /**
     * 团队ID
     */
    private String teamId;
    /**
     * 企业名称
     */
    private String teamName;
    /**
     * 类型
     */
    private String type;
    /**
     * 无人机ID
     */
    private String uavId;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 预警类型
     */
    private String warningType;
}
