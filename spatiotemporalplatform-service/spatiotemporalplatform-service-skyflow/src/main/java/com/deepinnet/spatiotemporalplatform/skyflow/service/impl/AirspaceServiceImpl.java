package com.deepinnet.spatiotemporalplatform.skyflow.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.AirspaceDO;
import com.deepinnet.spatiotemporalplatform.dto.AirspacePageQueryDTO;
import com.deepinnet.spatiotemporalplatform.model.skyflow.Airspace;
import com.deepinnet.spatiotemporalplatform.skyflow.convert.AirspaceConvert;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.AirspaceRepository;
import com.deepinnet.spatiotemporalplatform.skyflow.service.AirspaceService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description: 空域服务实现类
 * Date: 2025/3/13
 * Author: claude-ai
 */
@Service
public class AirspaceServiceImpl implements AirspaceService {

    @Resource
    private AirspaceRepository airspaceRepository;

    @Resource
    private AirspaceConvert airspaceConvert;

    @Override
    public String createAirspace(Airspace airspace) {
        // 保存空域信息
        airspaceRepository.save(airspaceConvert.toDO(airspace));
        return airspace.getCode();
    }

    @Override
    public CommonPage<Airspace> pageQueryAirspace(AirspacePageQueryDTO queryDTO) {
        // 构建查询条件
        LambdaQueryWrapper<AirspaceDO> queryWrapper = new LambdaQueryWrapper<>();

        // 添加查询条件
        if (StringUtils.isNotBlank(queryDTO.getName())) {
            queryWrapper.like(AirspaceDO::getName, queryDTO.getName());
        }
        if (StringUtils.isNotBlank(queryDTO.getStatus())) {
            queryWrapper.eq(AirspaceDO::getStatus, queryDTO.getStatus());
        }
        if (StringUtils.isNotBlank(queryDTO.getPrivateType())) {
            queryWrapper.eq(AirspaceDO::getPrivateType, queryDTO.getPrivateType());
        }
        if (StringUtils.isNotBlank(queryDTO.getRangeType())) {
            queryWrapper.eq(AirspaceDO::getRangeType, queryDTO.getRangeType());
        }
        if (queryDTO.getMinHeight() != null && queryDTO.getMaxHeight() != null) {
            queryWrapper
                    .and(wrapper -> wrapper
                            // 左闭右开 高度相交
                            .or()
                            .ge(AirspaceDO::getMinHeight, queryDTO.getMinHeight()) // 最小高度相交
                            .lt(AirspaceDO::getMinHeight, queryDTO.getMaxHeight())
                            .or()
                            .ge(AirspaceDO::getMaxHeight, queryDTO.getMinHeight()) // 最高高度相交
                            .lt(AirspaceDO::getMaxHeight, queryDTO.getMaxHeight())
                            .or()
                            .le(AirspaceDO::getMinHeight, queryDTO.getMinHeight()) // 包含
                            .gt(AirspaceDO::getMaxHeight, queryDTO.getMaxHeight())


                    );
        }

        if(StringUtils.isNotBlank(queryDTO.getSource())) {
            queryWrapper.eq(AirspaceDO::getSource, queryDTO.getSource());
        }

        // 按创建时间倒序排序
        queryWrapper.orderByDesc(AirspaceDO::getGmtCreated);

        // 执行分页查询
        Page<AirspaceDO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        page = airspaceRepository.page(page, queryWrapper);

        // 转换为通用分页对象
        List<AirspaceDO> records = page.getRecords();
        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), (int) page.getPages(), page.getTotal(), airspaceConvert.toDomainList(records));
    }

    @Override
    public Airspace getAirspaceByCode(String code) {
        LambdaQueryWrapper<AirspaceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AirspaceDO::getCode, code);
        return airspaceConvert.toDomain(airspaceRepository.getOne(queryWrapper));
    }

    @Override
    public Airspace getAirspaceByAirspaceId(String airspaceId) {
        LambdaQueryWrapper<AirspaceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AirspaceDO::getAirspaceId, airspaceId);
        return airspaceConvert.toDomain(airspaceRepository.getOne(queryWrapper));
    }
} 