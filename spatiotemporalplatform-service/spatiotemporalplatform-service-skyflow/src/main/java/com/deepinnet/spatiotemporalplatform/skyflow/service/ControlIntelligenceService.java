package com.deepinnet.spatiotemporalplatform.skyflow.service;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.dto.ControlIntelligenceDTO;
import com.deepinnet.spatiotemporalplatform.dto.ControlIntelligenceQueryDTO;

import java.util.List;

/**
 * 管控情报服务接口
 *
 * <AUTHOR>
 */
public interface ControlIntelligenceService {

    /**
     * 分页查询管控情报
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    CommonPage<ControlIntelligenceDTO> pageQuery(ControlIntelligenceQueryDTO queryDTO);

    /**
     * 保存管控情报
     *
     * @param controlIntelligenceDTO 管控情报信息
     * @return 是否保存成功
     */
    boolean save(ControlIntelligenceDTO controlIntelligenceDTO);

    /**
     * 批量保存管控情报
     *
     * @param controlIntelligenceDTOList 管控情报信息列表
     * @return 是否保存成功
     */
    boolean saveBatch(List<ControlIntelligenceDTO> controlIntelligenceDTOList);

    /**
     * 更新管控情报
     *
     * @param controlIntelligenceDTO 管控情报信息
     * @return 是否更新成功
     */
    boolean update(ControlIntelligenceDTO controlIntelligenceDTO);

    /**
     * 根据ID删除管控情报
     *
     * @param id 管控情报ID
     * @return 是否删除成功
     */
    boolean removeById(Long id);

    /**
     * 根据ID批量删除管控情报
     *
     * @param ids 管控情报ID列表
     * @return 是否删除成功
     */
    boolean removeByIds(List<Long> ids);
} 