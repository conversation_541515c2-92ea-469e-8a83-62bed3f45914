package com.deepinnet.spatiotemporalplatform.skyflow.service;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.model.staticpoi.StaticPoi;
import com.deepinnet.spatiotemporalplatform.model.staticpoi.StaticPoiQueryDTO;

/**
 * <p>
 * 静态核心POI 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-10
 */
public interface StaticPoiService {

    /**
     * 分页查询静态POI
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    CommonPage<StaticPoi> pageStaticPoi(StaticPoiQueryDTO queryDTO);

    /**
     * 根据POI ID获取静态POI
     *
     * @param poiId POI ID
     * @return 静态POI
     */
    StaticPoi getByPoiId(String poiId);

} 