package com.deepinnet.spatiotemporalplatform.skyflow.util.weather;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONArray;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.spatiotemporalplatform.skyflow.error.BizErrorCode;
import com.deepinnet.spatiotemporalplatform.skyflow.flush.GridCenterCoordinate;
import com.fasterxml.jackson.core.*;
import com.fasterxml.jackson.databind.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.*;
import org.apache.http.impl.client.*;

import java.io.*;
import java.nio.file.Files;
import java.util.*;
import java.util.zip.GZIPInputStream;

/**
 * 天气数据反序列化工具类
 * 用于将API返回的JSON数据反序列化为内存模型
 * 支持处理包含24个高度层的天气数据
 */
public class WeatherDataProcessUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 从API响应JSON中提取并反序列化天气数据
     *
     * @param jsonResponse API完整响应的JSON字符串
     * @return 反序列化后的天气数据模型
     * @throws IOException 如果JSON解析失败
     */
    public static Map<String, LocationWeatherData> deserialize(String jsonResponse) {
        try {
            JsonNode rootNode = objectMapper.readTree(jsonResponse);

            // 解析data字段
            JsonNode dataNode = rootNode.get("data");
            Map<String, LocationWeatherData> locationDataMap = new HashMap<>();

            // 遍历data中的所有位置节点
            dataNode.fields().forEachRemaining(entry -> {
                String locationCode = entry.getKey();
                JsonNode locationArrays = entry.getValue();

                // 创建位置数据对象
                LocationWeatherData locationData = parseLocationData(locationCode, locationArrays);
                locationDataMap.put(locationCode, locationData);
            });

            return locationDataMap;
        } catch (Exception e) {
            LogUtil.error("反序列化JSON失败，json={}", jsonResponse);
            throw new BizException(BizErrorCode.DESIALIZATION_FAILED.getCode(), BizErrorCode.DESIALIZATION_FAILED.getDesc());
        }

    }

    public static Map<String, LocationWeatherData> downloadAndDeserializeWeatherData(String url) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet httpGet = new HttpGet(url);
            try (CloseableHttpResponse response = httpClient.execute(httpGet);
                 InputStream inputStream = response.getEntity().getContent();
                 BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream);
                 GZIPInputStream gzipInputStream = new GZIPInputStream(bufferedInputStream)) {

                // 直接从流中反序列化，而不是先保存到磁盘
                return WeatherDataProcessUtil.deserializeDataFromStream(gzipInputStream);
            }
        } catch (IOException e) {
            LogUtil.error("下载或解析气象数据失败", e);
            throw new BizException(BizErrorCode.INVOKE_WEATHER_CLIENT_ERROR.getCode(),
                    BizErrorCode.INVOKE_WEATHER_CLIENT_ERROR.getDesc());
        }
    }

    /**
     * 根据网格code获取网格中心点坐标
     */
    public static Map<String, GridCenterCoordinate> getGridCenterFromNode(String requestUrl, Set<String> gridCodes) {
        if (gridCodes == null || gridCodes.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, GridCenterCoordinate> result = new HashMap<>();

        // 分批处理，每批最多处理100个网格编码，避免请求体过大
        List<String> gridCodeList = new ArrayList<>(gridCodes);
        int batchSize = 1000;

        for (int i = 0; i < gridCodeList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, gridCodeList.size());
            List<String> batch = gridCodeList.subList(i, endIndex);

            try {
                Map<String, GridCenterCoordinate> batchResult = getGridCenterFromNodeBatch(requestUrl, batch);
                result.putAll(batchResult);
                LogUtil.info("成功获取网格中心点坐标批次，处理范围: {}-{}, 获取到{}个坐标",
                        i, endIndex - 1, batchResult.size());
            } catch (Exception e) {
                LogUtil.error("获取网格中心点坐标批次失败，处理范围: {}-{}", i, endIndex - 1, e);
                // 继续处理下一批，不中断整个流程
            }
        }

        return result;
    }


    /**
     * 批量获取网格中心点坐标
     */
    private static Map<String, GridCenterCoordinate> getGridCenterFromNodeBatch(String requestUrl, List<String> gridCodes) {
        String response = null;
        try {
            String body = JSONUtil.createObj()
                    .set("codes", gridCodes)
                    .toString();
            response = HttpUtil.post(requestUrl, body);

            // 检查响应是否为有效JSON
            if (StringUtils.isBlank(response)) {
                return new HashMap<>();
            }

            // 直接反序列化为 Map<String, GridCenterCoordinate>
            return JSONUtil.toBean(
                    response,
                    new cn.hutool.core.lang.TypeReference<Map<String, GridCenterCoordinate>>() {
                    },
                    false
            );
        } catch (Exception e) {
            LogUtil.error("计算网格中心点错误，response={}", response != null ? response.substring(0, Math.min(500, response.length())) : "null", e);
            return new HashMap<>();
        }
    }


    /**
     * 从嵌套在data字段中的JSON数据直接反序列化
     * 适用于已经提取出data字段内容的场景
     *
     * @param dataJson data字段的JSON字符串
     * @return 反序列化后的位置数据映射
     * @throws IOException 如果JSON解析失败
     */
    public static Map<String, LocationWeatherData> deserializeDataJson(String dataJson) {
        try {
            JsonNode dataNode = objectMapper.readTree(dataJson);
            Map<String, LocationWeatherData> locationDataMap = new HashMap<>();

            dataNode.fields().forEachRemaining(entry -> {
                String locationCode = entry.getKey();
                JsonNode locationArrays = entry.getValue();

                LocationWeatherData locationData = parseLocationData(locationCode, locationArrays);
                locationDataMap.put(locationCode, locationData);
            });

            return locationDataMap;
        } catch (Exception e) {
            LogUtil.error("反序列化数据失败，dataJson={}", dataJson);
            throw new BizException(BizErrorCode.DESIALIZATION_FAILED.getCode(), BizErrorCode.DESIALIZATION_FAILED.getDesc());
        }
    }

    /**
     * 从嵌套在data字段中的JSON数据直接反序列化
     * 适用于已经提取出data字段内容的场景
     *
     * @param dataBytes data字段的byte数组
     * @return 反序列化后的位置数据映射
     * @throws IOException 如果JSON解析失败
     */
    public static Map<String, LocationWeatherData> deserializeDataJson(byte[] dataBytes) {
        Map<String, LocationWeatherData> locationDataMap = new HashMap<>();
        try (InputStream is = new ByteArrayInputStream(dataBytes);
             JsonParser parser = new JsonFactory().createParser(is)) {

            if (parser.nextToken() != JsonToken.START_OBJECT) {
                LogUtil.error("反序列化数据失败");
                throw new BizException(BizErrorCode.DESIALIZATION_FAILED.getCode(), BizErrorCode.DESIALIZATION_FAILED.getDesc());
            }

            while (parser.nextToken() != JsonToken.END_OBJECT) {
                String locationCode = parser.getCurrentName();
                //parser.nextToken();

                LocationWeatherData locationData = parseLocationData(parser);
                locationDataMap.put(locationCode, locationData);
            }
        } catch (Exception e) {
            LogUtil.error("反序列化数据失败，异常信息：{}", e);
            throw new BizException(BizErrorCode.DESIALIZATION_FAILED.getCode(), BizErrorCode.DESIALIZATION_FAILED.getDesc());
        }

        return locationDataMap;
    }

    /**
     * 直接从输入流反序列化天气数据
     * 通过流式处理减少内存占用，无需中间文件存储
     *
     * @param inputStream 包含天气数据JSON的输入流
     * @return 反序列化后的位置数据映射
     */
    public static Map<String, LocationWeatherData> deserializeDataFromStream(InputStream inputStream) {
        Map<String, LocationWeatherData> locationDataMap = new HashMap<>();
        try {
            JsonParser parser = new JsonFactory().createParser(inputStream);

            if (parser.nextToken() != JsonToken.START_OBJECT) {
                LogUtil.error("从流中反序列化数据失败，JSON格式错误");
                throw new BizException(BizErrorCode.DESIALIZATION_FAILED.getCode(), BizErrorCode.DESIALIZATION_FAILED.getDesc());
            }

            while (parser.nextToken() != JsonToken.END_OBJECT) {
                String locationCode = parser.getCurrentName();
                LocationWeatherData locationData = parseLocationData(parser);
                locationDataMap.put(locationCode, locationData);
            }

            parser.close();
        } catch (Exception e) {
            LogUtil.error("从流中反序列化数据失败，异常信息：{}", e.getMessage(), e);
            throw new BizException(BizErrorCode.DESIALIZATION_FAILED.getCode(), BizErrorCode.DESIALIZATION_FAILED.getDesc());
        }

        return locationDataMap;
    }

    public static LocationWeatherData parseLocationData(JsonParser parser) throws IOException {
        LocationWeatherData locationData = new LocationWeatherData();
        locationData.setLocationCode(parser.getCurrentName());
        locationData.code2Point();
        List<WeatherLayer> layers = new ArrayList<>();

        if (parser.nextToken() != JsonToken.START_ARRAY) {
            LogUtil.error("反序列化数据失败");
            throw new BizException(BizErrorCode.DESIALIZATION_FAILED.getCode(), BizErrorCode.DESIALIZATION_FAILED.getDesc());
        }

        int index = 0;
        while (parser.nextToken() != JsonToken.END_ARRAY) {
            JsonNode arrayNode = objectMapper.readTree(parser);
            if (index == 0) {
                // 第一个数组是基础地理信息
                BaseGeographicInfo baseInfo = new BaseGeographicInfo();
                baseInfo.setCloudBaseHeight(arrayNode.get(0).asDouble());
                baseInfo.setVisibility(arrayNode.get(1).asDouble());
                baseInfo.setPrecipitationIntensity(arrayNode.get(2).asDouble());
                baseInfo.setAirpressure(arrayNode.get(3).asDouble());
                baseInfo.setHumidity(arrayNode.get(4).asDouble());
                baseInfo.setTemperature(arrayNode.get(5).asDouble());
                locationData.setBaseInfo(baseInfo);
            } else {
                // 其余数组代表不同高度的气象数据
                WeatherLayer layer = new WeatherLayer();
                layer.setAltitude(arrayNode.get(0).asDouble());
                layer.setHorizontalWindSpeed(arrayNode.get(1).asDouble());
                layer.setVerticalWindSpeed(arrayNode.get(2).asDouble());
                layer.setWindDirection(arrayNode.get(3).asDouble());
                layers.add(layer);
            }
            index++;
        }

        layers.sort(Comparator.comparing(WeatherLayer::getAltitude));
        locationData.setWeatherLayers(layers);
        return locationData;
    }

    public static int calculateMaxIndexFromHeight(double height) {
        int index = (int) Math.ceil(height / 10.0) - 1;
        return Math.min(Math.max(index, 0), 23); // 限制最大为23，最小为0
    }

    public static int calculateMinIndexFromHeight(double height) {
        int index = (int) Math.floor(height / 10.0) - 1;
        return Math.max(index, 0); // 防止负数，最小为0
    }


    /**
     * 解析单个位置的天气数据数组
     *
     * @param locationCode   位置编码
     * @param locationArrays 位置数据数组节点
     * @return 解析后的位置天气数据
     */
    private static LocationWeatherData parseLocationData(String locationCode, JsonNode locationArrays) {
        LocationWeatherData locationData = new LocationWeatherData();
        locationData.setLocationCode(locationCode);

        List<WeatherLayer> layers = new ArrayList<>();

        // 遍历位置的所有数组
        for (int i = 0; i < locationArrays.size(); i++) {
            JsonNode arrayNode = locationArrays.get(i);

            if (i == 0) {
                // 第一个数组是基础地理信息
                BaseGeographicInfo baseInfo = new BaseGeographicInfo();
                baseInfo.setCloudBaseHeight(arrayNode.get(0).asDouble());
                baseInfo.setVisibility(arrayNode.get(1).asDouble());
                baseInfo.setPrecipitationIntensity(arrayNode.get(2).asDouble());
                baseInfo.setAirpressure(arrayNode.get(3).asDouble());
                baseInfo.setHumidity(arrayNode.get(4).asDouble());
                baseInfo.setTemperature(arrayNode.get(5).asDouble());
                locationData.setBaseInfo(baseInfo);
            } else {
                // 其余数组代表不同高度的气象数据 (1-24对应24个高度层)
                WeatherLayer layer = new WeatherLayer();
                layer.setAltitude(arrayNode.get(0).asDouble());
                layer.setHorizontalWindSpeed(arrayNode.get(1).asDouble());
                layer.setVerticalWindSpeed(arrayNode.get(2).asDouble());
                layer.setWindDirection(arrayNode.get(3).asDouble());
                layers.add(layer);
            }
        }

        layers.sort(Comparator.comparing(WeatherLayer::getAltitude));
        locationData.setWeatherLayers(layers);
        return locationData;
    }

    /**
     * 生成气象数据文件名
     * 格式：前缀_气象类型_yyyyMMdd_总数量_N序号_时间戳.json
     *
     * @param prefix      文件前缀
     * @param weatherType 气象类型
     * @param dateStr     日期字符串
     * @param totalGrids  总网格数
     * @param gridIndex   当前网格索引
     * @param timestamp   时间戳
     * @return 文件名
     */
    public static String generateFileName(String prefix, String weatherType, String dateStr, int totalGrids, int gridIndex, long timestamp) {
        return prefix + "_" + weatherType + "_" + dateStr + "_" + totalGrids + "_N" + gridIndex + "_" + timestamp + ".json";
    }

    /**
     * 生成温度文件
     *
     * @param fileName        文件名
     * @param weatherDataPath 文件保存路径
     * @param weatherDataMap  气象数据
     * @param gridCenterMap   网格中心点坐标
     */
    public static void generateTemperatureFile(String fileName, String weatherDataPath,
                                               Map<String, LocationWeatherData> weatherDataMap,
                                               Map<String, GridCenterCoordinate> gridCenterMap) {
        try {
            JSONObject temperatureData = new JSONObject();
            temperatureData.set("name", "temperature");

            JSONArray dataArray = new JSONArray();

            for (Map.Entry<String, LocationWeatherData> entry : weatherDataMap.entrySet()) {
                String locationCode = entry.getKey();
                LocationWeatherData locationData = entry.getValue();

                if (locationData != null && locationData.getBaseInfo() != null && gridCenterMap.containsKey(locationCode)) {
                    GridCenterCoordinate center = gridCenterMap.get(locationCode);

                    JSONObject tempPoint = new JSONObject();
                    JSONObject point = new JSONObject();
                    point.set("lng", center.getLngDegree());
                    point.set("lat", center.getLatDegree());
                    point.set("code", locationCode);
                    tempPoint.set("point", point);
                    tempPoint.set("temp", locationData.getBaseInfo().getTemperature());

                    dataArray.add(tempPoint);
                }
            }

            temperatureData.set("data", dataArray);

            // 保存到文件
            File file = new File(weatherDataPath, fileName);
            Files.write(file.toPath(), temperatureData.toString().getBytes());
            LogUtil.info("温度数据文件已保存: {}", file.getAbsolutePath());

        } catch (Exception e) {
            LogUtil.error("生成温度文件失败: {}", fileName, e);
            throw new BizException(BizErrorCode.DESIALIZATION_FAILED.getCode(), BizErrorCode.DESIALIZATION_FAILED.getDesc());
        }
    }

    /**
     * 生成降雨文件
     *
     * @param fileName        文件名
     * @param weatherDataPath 文件保存路径
     * @param weatherDataMap  气象数据
     * @param gridCenterMap   网格中心点坐标
     */
    public static void generatePrecipitationFile(String fileName, String weatherDataPath,
                                                 Map<String, LocationWeatherData> weatherDataMap,
                                                 Map<String, GridCenterCoordinate> gridCenterMap) {
        try {
            JSONObject precipitationData = new JSONObject();
            precipitationData.set("name", "precipitation");

            JSONArray dataArray = new JSONArray();

            for (Map.Entry<String, LocationWeatherData> entry : weatherDataMap.entrySet()) {
                String locationCode = entry.getKey();
                LocationWeatherData locationData = entry.getValue();

                if (locationData != null && locationData.getBaseInfo() != null && gridCenterMap.containsKey(locationCode)) {
                    GridCenterCoordinate center = gridCenterMap.get(locationCode);

                    JSONObject precipitationPoint = new JSONObject();
                    JSONObject point = new JSONObject();
                    point.set("lng", center.getLngDegree());
                    point.set("lat", center.getLatDegree());
                    point.set("code", locationCode);
                    precipitationPoint.set("point", point);
                    precipitationPoint.set("prc", locationData.getBaseInfo().getPrecipitationIntensity());

                    dataArray.add(precipitationPoint);
                }
            }

            precipitationData.set("data", dataArray);

            // 保存到文件
            File file = new File(weatherDataPath, fileName);
            Files.write(file.toPath(), precipitationData.toString().getBytes());
            LogUtil.info("降雨数据文件已保存: {}", file.getAbsolutePath());

        } catch (Exception e) {
            LogUtil.error("生成降雨文件失败: {}", fileName, e);
            throw new BizException(BizErrorCode.DESIALIZATION_FAILED.getCode(), BizErrorCode.DESIALIZATION_FAILED.getDesc());
        }
    }

    /**
     * 生成风力文件
     *
     * @param fileName        文件名
     * @param weatherDataPath 文件保存路径
     * @param weatherDataMap  气象数据
     * @param gridCenterMap   网格中心点坐标
     * @param maxHeight       最大高度（可选，如果为null则使用默认值200）
     */
    public static void generateWindFile(String fileName, String weatherDataPath,
                                        Map<String, LocationWeatherData> weatherDataMap,
                                        Map<String, GridCenterCoordinate> gridCenterMap,
                                        Integer maxHeight) {
        try {
            JSONObject windData = new JSONObject();
            windData.set("name", "wind");

            JSONObject data = new JSONObject();
            data.set("height", maxHeight);

            JSONArray gridsArray = new JSONArray();

            for (Map.Entry<String, LocationWeatherData> entry : weatherDataMap.entrySet()) {
                String locationCode = entry.getKey();
                LocationWeatherData locationData = entry.getValue();

                if (locationData != null && locationData.getWeatherLayers() != null && gridCenterMap.containsKey(locationCode)) {
                    GridCenterCoordinate center = gridCenterMap.get(locationCode);

                    // 遍历每一层的风速数据
                    for (WeatherLayer weatherLayer : locationData.getWeatherLayers()) {
                        if (weatherLayer != null) {
                            JSONObject gridPoint = new JSONObject();
                            JSONObject position = new JSONObject();
                            position.set("lng", center.getLngDegree());
                            position.set("lat", center.getLatDegree());
                            position.set("alt", weatherLayer.getAltitude()); // 使用当前层的高度
                            gridPoint.set("position", position);
                            gridPoint.set("windspeed", weatherLayer.getHorizontalWindSpeed()); // 当前层的风速
                            gridPoint.set("winddirection", weatherLayer.getWindDirection()); // 当前层的风向
                            gridPoint.set("code", locationCode);

                            gridsArray.add(gridPoint);
                        }
                    }
                }
            }

            data.set("grids", gridsArray);
            windData.set("data", data);

            // 保存到文件
            File file = new File(weatherDataPath, fileName);
            Files.write(file.toPath(), windData.toString().getBytes());
            LogUtil.info("风力数据文件已保存: {}, 包含{}个风力数据点", file.getAbsolutePath(), gridsArray.size());

        } catch (Exception e) {
            LogUtil.error("生成风力文件失败: {}", fileName, e);
            throw new BizException(BizErrorCode.DESIALIZATION_FAILED.getCode(), BizErrorCode.DESIALIZATION_FAILED.getDesc());
        }
    }

    /**
     * 批量生成标准气象文件（温度、降雨、风力）
     *
     * @param filePrefix      文件前缀
     * @param weatherDataPath 文件保存路径
     * @param urlPrefix       URL前缀
     * @param weatherDataMap  气象数据
     * @param gridCenterMap   网格中心点坐标
     * @param totalGrids      总网格数
     * @param gridIndex       当前网格索引
     * @param timestamp       时间戳
     * @param dateStr         日期字符串
     * @param maxHeight       最大高度（可选）
     * @return 生成的文件URL列表
     */
    public static List<String> generateStandardWeatherFiles(String filePrefix, String weatherDataPath, String urlPrefix,
                                                            Map<String, LocationWeatherData> weatherDataMap,
                                                            Map<String, GridCenterCoordinate> gridCenterMap,
                                                            int totalGrids, int gridIndex, long timestamp, String dateStr,
                                                            Integer maxHeight) {
        List<String> fileUrls = new ArrayList<>();

        try {
            // 1. 生成温度文件
            String tempFileName = generateFileName(filePrefix, "温度", dateStr, totalGrids, gridIndex, timestamp);
            generateTemperatureFile(tempFileName, weatherDataPath, weatherDataMap, gridCenterMap);
            fileUrls.add(urlPrefix + tempFileName);

            // 2. 生成降雨文件
            String precipitationFileName = generateFileName(filePrefix, "降雨", dateStr, totalGrids, gridIndex, timestamp);
            generatePrecipitationFile(precipitationFileName, weatherDataPath, weatherDataMap, gridCenterMap);
            fileUrls.add(urlPrefix + precipitationFileName);

            // 3. 生成风力文件
            String windFileName = generateFileName(filePrefix, "风力", dateStr, totalGrids, gridIndex, timestamp);
            generateWindFile(windFileName, weatherDataPath, weatherDataMap, gridCenterMap, maxHeight);
            fileUrls.add(urlPrefix + windFileName);

        } catch (Exception e) {
            LogUtil.error("批量生成气象文件失败，网格索引: {}", gridIndex, e);
            throw e;
        }

        return fileUrls;
    }
}