package com.deepinnet.spatiotemporalplatform.skyflow.service;

import com.deepinnet.spatiotemporalplatform.vo.WeatherDeviceVO;

import java.util.List;

/**
 * 天气设备Service接口
 *
 * <AUTHOR> wong
 * @create 2025/01/14
 */
public interface WeatherDeviceService {

    /**
     * 查询所有天气设备
     *
     * @return 天气设备列表
     */
    List<WeatherDeviceVO> listAllWeatherDevices();

    /**
     * 统计当前租户的天气设备总数
     *
     * @return 天气设备总数
     */
    Long countWeatherDevices();
} 