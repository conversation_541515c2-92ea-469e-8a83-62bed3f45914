package com.deepinnet.spatiotemporalplatform.skyflow.client;

import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.client.demand.FlightDemandClient;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandQueryDTO;
import com.deepinnet.skyflow.operationcenter.vo.FlightDemandVO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/8
 */

@Component
public class FlightDemandRemoteClient {

    @Resource
    private FlightDemandClient flightDemandClient;

    public FlightDemandVO getDemandDetail(String requirementNo){
        Result<FlightDemandVO> flightDemandByNo = flightDemandClient.getFlightDemandByNo(requirementNo);

        if (!flightDemandByNo.isSuccess()){
            LogUtil.error("FlightDemandClient getFlightDemandByNo failed, requirementNo:{}, error:{}",requirementNo, flightDemandByNo.getErrorDesc());
            // throw new BizException(flightDemandByNo.getErrorCode(), flightDemandByNo.getErrorDesc());
            return null;
        }

        return flightDemandByNo.getData();
    }

    public FlightDemandDTO getFlightDemandByDemandNo(String planId){
        Result<FlightDemandDTO> result = flightDemandClient.getFlightDemandByPlanId(planId);

        if (!result.isSuccess()){
            LogUtil.error("FlightDemandClient getFlightDemandByPlanId failed, planId:{}, error:{}",planId, result.getErrorDesc());
            throw new BizException(result.getErrorCode(), result.getErrorDesc());
        }

        return result.getData();
    }

    public List<FlightDemandVO> getFlightDemandByDemandNoList(List<String> demandNoList){
        FlightDemandQueryDTO flightDemandQueryDTO = new FlightDemandQueryDTO();
        flightDemandQueryDTO.setDemandNoList(demandNoList);
        Result<List<FlightDemandVO>> result =
                flightDemandClient.getPassFlightDemandByNoList(flightDemandQueryDTO);
        if (!result.isSuccess()){
            LogUtil.error("FlightDemandClient getPassFlightDemandByNoList failed, demandNoList:{}, error:{}",demandNoList, result.getErrorDesc());
            throw new BizException(result.getErrorCode(), result.getErrorDesc());
        }

        return result.getData();
    }



}
