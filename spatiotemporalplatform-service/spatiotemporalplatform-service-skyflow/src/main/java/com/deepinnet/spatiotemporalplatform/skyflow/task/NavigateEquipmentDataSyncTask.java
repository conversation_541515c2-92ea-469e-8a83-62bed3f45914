package com.deepinnet.spatiotemporalplatform.skyflow.task;

import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.localdata.integration.NavigateEquipmentClient;
import com.deepinnet.localdata.integration.model.output.NavigateEquipmentDataDTO;
import com.deepinnet.localdata.integration.model.output.NavigateEquipmentListResponseDTO;
import com.deepinnet.spatiotemporalplatform.base.config.TenantConfig;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.NavigateEquipmentDO;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.NavigateEquipmentRepository;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 导航设备数据同步定时任务
 *
 * <AUTHOR>
 */
@Component
public class NavigateEquipmentDataSyncTask {

    @Resource
    private NavigateEquipmentClient navigateEquipmentClient;

    @Resource
    private NavigateEquipmentRepository navigateEquipmentRepository;

    @Resource
    private TenantConfig tenantConfig;

    /**
     * 同步导航设备数据
     * 每5分钟执行一次
     */
    @Scheduled(cron = "0 */5 * * * ?")
    public void syncNavigateEquipmentData() {
        LogUtil.info("开始同步导航设备数据");
        
        long batchNo = System.currentTimeMillis();
        int successCount = 0;
        int errorCount = 0;
        
        try {
            // 调用外部接口获取导航设备数据
            NavigateEquipmentListResponseDTO response = navigateEquipmentClient.getAllNavigateEquipments();
            
            if (response == null || response.getData() == null) {
                LogUtil.info("导航设备数据接口返回为空，批次号：{}", batchNo);
                return;
            }
            
            List<NavigateEquipmentDataDTO> navigateEquipmentDataList = response.getData();
            
            if (CollectionUtils.isEmpty(navigateEquipmentDataList)) {
                LogUtil.info("导航设备数据列表为空，批次号：{}", batchNo);
                return;
            }
            
            LogUtil.info("获取到导航设备数据 {} 条，批次号：{}", navigateEquipmentDataList.size(), batchNo);
            
            // 转换数据并保存
            List<NavigateEquipmentDO> saveList = new ArrayList<>();
            List<NavigateEquipmentDO> updateList = new ArrayList<>();
            
            for (NavigateEquipmentDataDTO dto : navigateEquipmentDataList) {
                try {
                    NavigateEquipmentDO navigateEquipmentDO = convertToNavigateEquipmentDO(dto, batchNo);
                    
                    // 检查是否已存在
                    NavigateEquipmentDO existing = navigateEquipmentRepository.getByExternalId(navigateEquipmentDO.getExternalId());
                    if (existing != null) {
                        // 更新现有记录
                        navigateEquipmentDO.setId(existing.getId());
                        navigateEquipmentDO.setGmtCreated(existing.getGmtCreated());
                        navigateEquipmentDO.setGmtModified(new Date());
                        updateList.add(navigateEquipmentDO);
                    } else {
                        // 新增记录
                        saveList.add(navigateEquipmentDO);
                    }
                    
                    successCount++;
                } catch (Exception e) {
                    errorCount++;
                    LogUtil.error("处理导航设备数据失败，externalId：{}，错误信息：{}", 
                            dto.getId(), e.getMessage(), e);
                }
            }
            
            // 批量保存新增数据
            if (!CollectionUtils.isEmpty(saveList)) {
                boolean saveResult = navigateEquipmentRepository.saveBatch(saveList);
                if (saveResult) {
                    LogUtil.info("批量新增导航设备数据成功，数量：{}，批次号：{}", saveList.size(), batchNo);
                } else {
                    LogUtil.error("批量新增导航设备数据失败，批次号：{}", batchNo);
                }
            }
            
            // 批量更新数据
            if (!CollectionUtils.isEmpty(updateList)) {
                boolean updateResult = navigateEquipmentRepository.updateBatchById(updateList);
                if (updateResult) {
                    LogUtil.info("批量更新导航设备数据成功，数量：{}，批次号：{}", updateList.size(), batchNo);
                } else {
                    LogUtil.error("批量更新导航设备数据失败，批次号：{}", batchNo);
                }
            }
            
            LogUtil.info("导航设备数据同步完成，成功：{}条，失败：{}条，批次号：{}", successCount, errorCount, batchNo);
            
        } catch (Exception e) {
            LogUtil.error("同步导航设备数据发生异常，批次号：{}，错误信息：{}", batchNo, e.getMessage(), e);
        }
    }

    /**
     * 将DTO转换为DO
     *
     * @param dto     导航设备数据DTO
     * @param batchNo 批次号
     * @return 导航设备数据DO
     */
    private NavigateEquipmentDO convertToNavigateEquipmentDO(NavigateEquipmentDataDTO dto, long batchNo) {
        NavigateEquipmentDO navigateEquipmentDO = new NavigateEquipmentDO();
        
        // 注意：DTO中的id字段映射为DO中的externalId字段
        navigateEquipmentDO.setExternalId(dto.getId());
        navigateEquipmentDO.setManufacturer(dto.getManufacturer());
        navigateEquipmentDO.setDeviceName(dto.getDeviceName());
        navigateEquipmentDO.setBrandModel(dto.getBrandModel());
        navigateEquipmentDO.setSerialNo(dto.getSerialNo());
        navigateEquipmentDO.setStationType(dto.getStationType());
        navigateEquipmentDO.setTechParams(dto.getTechParams());
        navigateEquipmentDO.setDataStandard(dto.getDataStandard());
        navigateEquipmentDO.setEquipUsage(dto.getEquipUsage());
        navigateEquipmentDO.setInstallEnvir(dto.getInstallEnvir());
        navigateEquipmentDO.setInstallAdress(dto.getInstallAdress());
        navigateEquipmentDO.setAerialHeight(dto.getAerialHeight());
        navigateEquipmentDO.setOperatingFrequency(dto.getOperatingFrequency());
        navigateEquipmentDO.setInstallDate(dto.getInstallDate());
        navigateEquipmentDO.setInstallCount(dto.getInstallCount());
        navigateEquipmentDO.setInstallSituation(dto.getInstallSituation());
        navigateEquipmentDO.setPropCompany(dto.getPropCompany());
        navigateEquipmentDO.setPropLinkUser(dto.getPropLinkUser());
        navigateEquipmentDO.setLinkPhone(dto.getLinkPhone());
        navigateEquipmentDO.setDeviceManager(dto.getDeviceManager());
        navigateEquipmentDO.setDevicePhone(dto.getDevicePhone());
        navigateEquipmentDO.setManageRequire(dto.getManageRequire());
        navigateEquipmentDO.setRemark(dto.getRemark());
        navigateEquipmentDO.setCrtUserName(dto.getCrtUserName());
        navigateEquipmentDO.setCrtUserId(dto.getCrtUserId());
        navigateEquipmentDO.setCrtTime(dto.getCrtTime());
        navigateEquipmentDO.setIsDeleted(dto.getIsDeleted());
        navigateEquipmentDO.setUpdUserName(dto.getUpdUserName());
        navigateEquipmentDO.setUpdUserId(dto.getUpdUserId());
        navigateEquipmentDO.setUpdTime(dto.getUpdTime());
        navigateEquipmentDO.setDepartId(dto.getDepartId());
        navigateEquipmentDO.setDepartIds(dto.getDepartIds());
        navigateEquipmentDO.setExternalTenantId(dto.getTenantId());
        navigateEquipmentDO.setTeamId(dto.getTeamId());
        navigateEquipmentDO.setReceiverGain(dto.getReceiverGain());
        navigateEquipmentDO.setTransPower(dto.getTransPower());
        navigateEquipmentDO.setPulseWaveform(dto.getPulseWaveform());
        navigateEquipmentDO.setPolarizationMode(dto.getPolarizationMode());
        navigateEquipmentDO.setFigure(dto.getFigure());
        navigateEquipmentDO.setBandWidth(dto.getBandWidth());
        navigateEquipmentDO.setSitePicUrl(dto.getSitePicUrl());
        navigateEquipmentDO.setLon(dto.getLon());
        navigateEquipmentDO.setLat(dto.getLat());
        navigateEquipmentDO.setGcoverRage(dto.getGcoverRage());
        navigateEquipmentDO.setVcoverRage(dto.getVcoverRage());
        navigateEquipmentDO.setTenantId(tenantConfig.getTenantId());

        // 设置批次号和时间戳
        navigateEquipmentDO.setBatchNo(batchNo);
        navigateEquipmentDO.setGmtCreated(new Date());
        navigateEquipmentDO.setGmtModified(new Date());
        
        return navigateEquipmentDO;
    }
} 