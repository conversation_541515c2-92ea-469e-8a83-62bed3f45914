package com.deepinnet.spatiotemporalplatform.skyflow.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.UavRealtimeMqRecordDO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 从消息队列接收的无人机实时数据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
public interface UavRealtimeMqRecordRepository extends IService<UavRealtimeMqRecordDO> {

    /**
     * 获取指定无人机ID和计划ID的最新飞行记录
     *
     * @param uavIds 无人机ID列表
     * @param planIds 计划ID列表
     * @return 每个无人机ID对应的最新记录Map
     */
    Map<String, UavRealtimeMqRecordDO> getLatestFlightRecords(List<String> uavIds, List<String> planIds);
}
