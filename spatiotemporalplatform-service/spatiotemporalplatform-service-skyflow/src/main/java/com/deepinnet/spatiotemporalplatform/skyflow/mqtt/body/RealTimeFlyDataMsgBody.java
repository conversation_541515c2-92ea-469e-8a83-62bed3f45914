package com.deepinnet.spatiotemporalplatform.skyflow.mqtt.body;

import lombok.Data;

import java.io.Serializable;

/**
 * 无人机实时飞行数据消息体
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@Data
public class RealTimeFlyDataMsgBody implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 无人机ID
     */
    private String uavId;

    /**
     * 架次ID
     */
    private String flightId;

    /**
     * 放飞申请ID
     */
    private String relationId;

    /**
     * 计划ID
     */
    private String planId;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 无人机CPUID
     */
    private String cpuid;

    /**
     * 经度, wgs84经度 X 1E7
     */
    private Long lon;

    /**
     * 纬度, wgs84纬度 X 1E7
     */
    private Long lat;

    /**
     * 海拔高度（米） X 1000
     */
    private Integer alt;

    /**
     * 对地高度（米） X 1000
     */
    private Integer height;

    /**
     * 相对起飞点高度（米） X 1000
     */
    private Integer relativeHeight;

    /**
     * 航迹角（°） 北向为0，顺时针 0 ~ 359.9 X 10 真实航向
     */
    private Integer gpsYaw;

    /**
     * 偏航角（°） 北向为0，顺时针 0 ~ 359.9 X 10 机头朝向
     */
    private Integer yaw;

    /**
     * 俯仰角（°） 水平为0，上正下负 -90 ~ 90 X 10
     */
    private Integer pitch;

    /**
     * 横滚角（°） 水平为0，顺时针正，逆时针负， -180 ~ 180 X 10
     */
    private Integer roll;

    /**
     * 地面速度（m/s） X 100
     */
    private Integer speed;

    /**
     * UTC毫秒数
     */
    private Long timestamp;

    /**
     * 延迟（毫秒）
     */
    private Integer delay;

    /**
     * 飞行状态 0-地面 1-起飞/进入 2-航线 3-返航 4-降落 5-过渡（垂起固定翼特有）
     */
    private Integer state;

    /**
     * 航线总里程（米）
     */
    private Integer wpDistance;

    /**
     * a完成航线里程（米）
     */
    private Integer finishDistance;

    /**
     * 剩余航线里程（米）
     */
    private Integer surplusCircle;

    /**
     * 起飞点距离（米）
     */
    private Integer homeDistance;

    /**
     * 飞行时间（秒）
     */
    private Integer flightDuration;

    /**
     * 偏航距离（米） X 10
     */
    private Integer leeway;

    /**
     * GPS卫星数
     */
    private Integer gpsStars;

    /**
     * GPS定位精度
     */
    private Integer gpsPdop;

    /**
     * GPS状态 0-定位中 1-2D 2-3D 3-float 4-FIX
     */
    private Integer gpsState;

    /**
     * 动力锁定状态 0-解锁 1-锁定
     */
    private Integer lock;

    /**
     * 电压（V） X 10
     */
    private Integer voltage;

    /**
     * 剩余电量（%）
     */
    private Integer soc;

    /**
     * 载荷信息
     */
    private Load load;

    /**
     * 是否参与空间计算 0-不参与 1-参与
     */
    private Integer isKey;

    /**
     * 根据航向未来30秒预测经度, wgs84经度 X 1E7 (isKey为1时存在)
     */
    private Long calculateLon;

    /**
     * 根据航向未来30秒预测纬度, wgs84纬度 X 1E7 (isKey为1时存在)
     */
    private Long calculateLat;

    /**
     * 地形海拔高度（米） X 1000 (isKey为1时存在)
     */
    private Integer terrain;

    /**
     * 未来30秒地形海拔高度（米） X 1000，为三个数值，分别为 10s，20s，30s (isKey为1时存在)
     */
    private Integer[] calculateTerrain;

    /**
     * 当前6级网格码 (isKey为1时存在)
     */
    private String gridCode;

    /**
     * 风险等级 0-低风险 1-中风险 2-高风险 (isKey为1时存在)
     */
    private Integer risk;

    /**
     * 各预警状态 (isKey为1时存在)
     */
    private Warning waring;

    /**
     * 各告警状态 (isKey为1时存在)
     */
    private Error error;

    /**
     * 载荷信息内部类
     */
    @Data
    public static class Load implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 偏航角（°） 北向为0，顺时针 0 ~ 359.9 X 10
         */
        private Integer yaw;

        /**
         * 俯仰角（°） 水平为0，上正下负 -90 ~ 90 X 10
         */
        private Integer pitch;

        /**
         * 横滚角（°） 水平为0，顺时针正，逆时针负， -180 ~ 180 X 10
         */
        private Integer roll;

        /**
         * 变焦量（%） 0 ~ 100% 0不变焦，100放到最大
         */
        private Integer zoom;

        /**
         * 激光测距（米）X 1000
         */
        private Integer distance;

        /**
         * 跟踪状态 0-不跟踪 1-跟踪
         */
        private Integer lock;

        /**
         * 稳定状态 0-增稳，1-跟随，2-锁定
         */
        private Integer stable;

        /**
         * 录像状态 0-未录像 1-录像
         */
        private Integer video;

        /**
         * 画中画 0-融合 1-一号镜头 2-二号镜头
         */
        private Integer frame;
    }

    /**
     * 各预警状态内部类
     */
    @Data
    public static class Warning implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 超域预警 0-不存在 1-存在
         */
        private Integer superScope;

        /**
         * 飞机冲突预警 0-不存在 1-存在
         */
        private Integer conflict;

        /**
         * 地面冲突预警 0-不存在 1-存在
         */
        private Integer impact;

        /**
         * 闯禁飞区预警 0-不存在 1-存在
         */
        private Integer noFly;

        /**
         * 周边存在异常 0-不存在 1-存在
         */
        private Integer around;

        /**
         * 天气异常 0-不存在 1-存在
         */
        private Integer weather;
    }

    /**
     * 各告警状态内部类
     */
    @Data
    public static class Error implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 黑飞告警 0-不存在 1-存在
         */
        private Integer black;

        /**
         * 闯禁飞区告警 0-不存在 1-存在
         */
        private Integer noFly;

        /**
         * 超域告警 0-不存在 1-存在
         */
        private Integer superScope;
    }
}