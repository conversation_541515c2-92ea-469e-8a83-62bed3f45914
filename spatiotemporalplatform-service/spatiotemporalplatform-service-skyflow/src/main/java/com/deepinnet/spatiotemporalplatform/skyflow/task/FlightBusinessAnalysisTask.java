package com.deepinnet.spatiotemporalplatform.skyflow.task;

import cn.hutool.core.collection.CollUtil;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.localdata.integration.FlightBusinessAnalysisClient;
import com.deepinnet.localdata.integration.model.input.FlightBusinessAnalysisQueryDTO;
import com.deepinnet.localdata.integration.model.output.*;
import com.deepinnet.spatiotemporalplatform.base.config.TenantConfig;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightBusinessAnalysisDO;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.FlightBusinessAnalysisRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 飞行业务分析数据同步任务
 * 每5分钟从外部系统拉取飞行业务分析数据并保存到本地数据库
 *
 * <AUTHOR> wong
 * @create 2025/01/14
 */
@Component
@RequiredArgsConstructor
@Profile({"local", "local-inner", "test", "demo", "alicloud"})
public class FlightBusinessAnalysisTask {

    private final FlightBusinessAnalysisRepository flightBusinessAnalysisRepository;

    private final FlightBusinessAnalysisClient flightBusinessAnalysisClient;

    private final TenantConfig tenantConfig;

    /**
     * 每5分钟执行一次
     */
    @Scheduled(cron = "0 0/5 * * * *")
    public void syncFlightBusinessAnalysisData() {
        LogUtil.info("[飞行业务分析数据同步] 任务开始执行");

        // 调用外部接口获取飞行业务分析数据
        FlightBusinessAnalysisQueryDTO queryDTO = new FlightBusinessAnalysisQueryDTO();
        FlightBusinessAnalysisResponseDTO response = flightBusinessAnalysisClient.getFlightBusinessAnalysis(queryDTO);

        if (response == null) {
            LogUtil.error("外部系统返回飞行业务分析响应为空");
            return;
        }

        if (response.getStatus() != 200) {
            LogUtil.error("调用外部飞行业务分析接口失败，status={}", response.getStatus());
            return;
        }

        List<BusinessTypeStatisticsDTO> analysisDataList = response.getData().getBusinessTypes();
        if (CollUtil.isEmpty(analysisDataList)) {
            LogUtil.info("[飞行业务分析数据同步] 没有飞行业务分析数据需要同步");
            return;
        }

        try {
            // 生成本次同步的批次号
            Long batchNo = System.currentTimeMillis();
            Date now = new Date();

            // 转换数据并设置批次号
            List<FlightBusinessAnalysisDO> analysisDOList = new ArrayList<>();
            for (BusinessTypeStatisticsDTO dto : analysisDataList) {
                FlightBusinessAnalysisDO analysisDO = convertToFlightBusinessAnalysisDO(dto);
                if (analysisDO != null) {
                    analysisDO.setBatchNo(batchNo);
                    analysisDO.setGmtCreated(now);
                    analysisDO.setGmtModified(now);
                    analysisDO.setTenantId(tenantConfig.getTenantId());
                    analysisDOList.add(analysisDO);
                }
            }

            if (CollUtil.isEmpty(analysisDOList)) {
                LogUtil.warn("[飞行业务分析数据同步] 数据转换后为空，跳过处理");
                return;
            }

            // 批量保存新数据
            boolean saveResult = flightBusinessAnalysisRepository.saveBatch(analysisDOList);
            if (!saveResult) {
                LogUtil.error("[飞行业务分析数据同步] 保存新数据失败");
                return;
            }

            LogUtil.info("[飞行业务分析数据同步] 成功保存 {} 条新数据，批次号: {}", analysisDOList.size(), batchNo);
        } catch (Exception e) {
            LogUtil.error("[飞行业务分析数据同步] 同步飞行业务分析数据失败", e);
            throw e;
        }
    }

    /**
     * 将外部响应数据转换为FlightBusinessAnalysisDO
     *
     * @param dto 外部接口响应的统计数据
     * @return FlightBusinessAnalysisDO对象
     */
    private FlightBusinessAnalysisDO convertToFlightBusinessAnalysisDO(BusinessTypeStatisticsDTO dto) {
        if (dto == null) {
            return null;
        }

        FlightBusinessAnalysisDO analysisDO = new FlightBusinessAnalysisDO();
        analysisDO.setBusinessType(dto.getBusinessType());
        analysisDO.setStatisticsCount(dto.getCount());
        return analysisDO;
    }
}