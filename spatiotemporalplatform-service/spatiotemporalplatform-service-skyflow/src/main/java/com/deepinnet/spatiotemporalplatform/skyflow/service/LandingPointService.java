package com.deepinnet.spatiotemporalplatform.skyflow.service;

import com.deepinnet.spatiotemporalplatform.dto.LandingPointQueryDTO;
import com.deepinnet.spatiotemporalplatform.vo.LandingPointVO;

import java.util.List;

/**
 * 起降点Service接口
 *
 * <AUTHOR>
 */
public interface LandingPointService {

    /**
     * 查询起降点列表
     *
     * @param queryDTO 查询条件
     * @return 起降点列表
     */
    List<LandingPointVO> listLandingPoint(LandingPointQueryDTO queryDTO);

    /**
     * 根据ID查询起降点详情
     *
     * @param id 主键ID
     * @return 起降点详情
     */
    LandingPointVO getLandingPointById(Long id);

    /**
     * 根据外部ID查询起降点详情
     *
     * @param externalId 外部ID
     * @return 起降点详情
     */
    LandingPointVO getLandingPointByExternalId(String externalId);

    /**
     * 查询所有起降点数据
     *
     * @return 起降点数据列表
     */
    List<LandingPointVO> listAllLandingPoints();

    /**
     * 根据类型统计起降点数量
     *
     * @param type 起降点类型
     * @return 该类型的起降点数量
     */
    int countByAirType(String type);
} 