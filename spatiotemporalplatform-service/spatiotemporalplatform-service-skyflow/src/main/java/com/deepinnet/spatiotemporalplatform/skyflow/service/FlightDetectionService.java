package com.deepinnet.spatiotemporalplatform.skyflow.service;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.dto.*;

import java.util.List;

/**
 * 飞行监测领域服务
 *
 * <AUTHOR>
 */
public interface FlightDetectionService {
    /**
     * 分页查询飞行检测数据
     *
     * @param dto
     * @return
     */
    CommonPage<FlightDetectionDTO> pageFlightDetection(FlightDetectionQueryDTO dto);

    /**
     * 查询飞行完整轨迹
     *
     * @param queryDTO
     * @return
     */
    List<FlightPathDTO> getFlightDetectionPath(FlightPathQueryDTO queryDTO);

    /**
     * 查询最新的飞行器位置
     *
     * @return
     */
    List<FlightDetectionLatestPathDTO> getLatestFlightPosition(FlightDetectionLatestPositionDTO queryDTO);
}