package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightPlanDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.FlightPlanMapper;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightDemandPlanStatDO;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightDemandPlanStatQuery;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightQuery;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.FlightPlanRepository;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 飞行计划表，每个飞行任务包含多个飞行计划 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
@Service
public class FlightPlanRepositoryImpl extends ServiceImpl<FlightPlanMapper, FlightPlanDO> implements FlightPlanRepository {

    @Override
    public List<FlightPlanDO> listFlightPlanByCondition(FlightQuery flightQuery) {
        return baseMapper.listFlightPlanByCondition(flightQuery);
    }

    @Override
    public List<FlightDemandPlanStatDO> queryFlightDemandPlanStat(FlightDemandPlanStatQuery query) {
        return baseMapper.queryFlightDemandPlanStat(query);
    }

    @Override
    public List<String> queryHasAchievementPlan() {
        return baseMapper.queryHasAchievementPlan();
    }
}
