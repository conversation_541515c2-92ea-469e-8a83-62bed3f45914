package com.deepinnet.spatiotemporalplatform.skyflow.algorithm;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.dto.FlightEventsDTO;
import com.deepinnet.spatiotemporalplatform.dto.FlightEventsQueryDTO;
import com.deepinnet.spatiotemporalplatform.dto.FlightEventsStatDTO;
import com.deepinnet.spatiotemporalplatform.dto.FlightEventsStatQueryDTO;

import java.util.List;

/**
 * Description: 算法检测事件服务
 *
 * Date: 2025/5/8
 * Author: lijunheng
 */
public interface EventMonitorService {

    /**
     * 保存飞行事件
     *
     * @param flightEventsDTO 飞行事件DTO
     * @return 事件ID
     */
    String saveEvent(FlightEventsDTO flightEventsDTO);

    /**
     * 批量保存飞行事件
     *
     * @param flightEventsDTOList 飞行事件DTO列表
     * @return 是否保存成功
     */
    void saveEventBatch(List<FlightEventsDTO> flightEventsDTOList);

    /**
     * 批量保存或更新飞行事件
     *
     * @param convertedEvents
     */
    void saveOrUpdateEventBatch(List<FlightEventsDTO> convertedEvents);

    /**
     * 根据ID查询飞行事件
     *
     * @param id 事件ID
     * @return 飞行事件DTO
     */
    FlightEventsDTO getEventById(String id);

    /**
     * 分页查询飞行事件
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    CommonPage<FlightEventsDTO> pageQuery(FlightEventsQueryDTO queryDTO);

    /**
     * 根据飞行任务ID查询飞行事件列表
     *
     * @param flightTaskCode 飞行任务ID
     * @return 飞行事件DTO列表
     */
    List<FlightEventsDTO> queryListByFlightTaskCode(String flightTaskCode, List<String> flightDemandNoList);

    /**
     * 查询飞行事件统计
     *
     * @param queryDTO
     * @return
     */
    List<FlightEventsStatDTO> queryFlightEventsStat(FlightEventsStatQueryDTO queryDTO);
}
