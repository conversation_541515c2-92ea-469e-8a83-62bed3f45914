package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.LandingPointDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.LandingPointMapper;
import com.deepinnet.spatiotemporalplatform.enums.AirType;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.LandingPointRepository;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 起降点数据Repository实现
 *
 * <AUTHOR>
 */
@Repository
public class LandingPointRepositoryImpl extends ServiceImpl<LandingPointMapper, LandingPointDO>
        implements LandingPointRepository {

    @Override
    public LandingPointDO getByExternalId(String externalId) {
        if (!StringUtils.hasText(externalId)) {
            return null;
        }
        return baseMapper.selectByExternalId(externalId);
    }

    @Override
    public void deleteByTenantId(String tenantId) {
        super.remove(Wrappers.lambdaQuery(LandingPointDO.class).eq(LandingPointDO::getTenantId, tenantId)) ;
    }

    @Override
    public List<LandingPointDO> listByCondition(String code, String type, String name, String address,
                                                String teamId, String tenantId, String departId, String owner,
                                                String operator, String busiType, String airType, String street) {
        return super.list(Wrappers.lambdaQuery(LandingPointDO.class)
                .like(StringUtils.hasText(code), LandingPointDO::getCode, code)
                .like(StringUtils.hasText(type), LandingPointDO::getType, type)
                .like(StringUtils.hasText(name), LandingPointDO::getName, name)
                .like(StringUtils.hasText(address), LandingPointDO::getAddress, address)
                .eq(StringUtils.hasText(teamId), LandingPointDO::getTeamId, teamId)
                .eq(StringUtils.hasText(tenantId), LandingPointDO::getTenantId, tenantId)
                .eq(StringUtils.hasText(departId), LandingPointDO::getDepartId, departId)
                .like(StringUtils.hasText(owner), LandingPointDO::getOwner, owner)
                .like(StringUtils.hasText(operator), LandingPointDO::getOperator, operator)
                .eq(StringUtils.hasText(busiType), LandingPointDO::getBusiType, busiType)
                .eq(StringUtils.hasText(airType), LandingPointDO::getAirType, airType)
                .like(StringUtils.hasText(street), LandingPointDO::getStreet, street)
                // 类型不可为空
                .in(LandingPointDO::getAirType, Arrays.stream(AirType.values()).map(AirType::getCode).collect(Collectors.toList()))
                .orderByDesc(LandingPointDO::getGmtCreated));
    }
}