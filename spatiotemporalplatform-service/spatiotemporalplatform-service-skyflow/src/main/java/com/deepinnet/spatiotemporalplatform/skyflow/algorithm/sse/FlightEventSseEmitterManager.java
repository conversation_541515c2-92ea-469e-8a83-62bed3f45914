package com.deepinnet.spatiotemporalplatform.skyflow.algorithm.sse;

import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

/**
 * Manager for SSE emitters to handle workflow log streaming
 * Date: 2025/3/26
 * Author: lijunheng
 */
@Component
public class FlightEventSseEmitterManager {

    private static final String DATA = "data";
    private static final String COMPLETE = "complete";

    private final Map<String, SseEmitter> emitters = new ConcurrentHashMap<>();

    /**
     * Create a new SseEmitter for a specific workflow version
     *
     * @param taskCode The workflow version code to subscribe to
     * @return A new SseEmitter instance
     */
    public SseEmitter createEmitter(String taskCode) {

        // Get or create map for this workflow version
        SseEmitter emitter = emitters.computeIfAbsent(
                taskCode, k -> new SseEmitter(Long.MAX_VALUE));

        // Set completion callback to remove the emitter when done
        emitter.onCompletion(() -> removeEmitter(taskCode));
        emitter.onTimeout(() -> removeEmitter(taskCode));
        emitter.onError(e -> removeEmitter(taskCode));

        return emitter;
    }

    /**
     * Send log message to all subscribers of a specific workflow version
     * If no subscribers exist yet, buffer the logs
     *
     * @param taskCode Workflow version code
     * @param data
     */
    public void sendData(String taskCode, Object data) {
        // If emitter exists, send log immediately
        SseEmitter emitter = emitters.get(taskCode);
        if (emitter != null) {
            sendData(emitter, DATA, data, taskCode);
        }
    }

    /**
     * 过滤出符合条件的SSE连接，并广播
     * @param func
     * @param data
     */
    public void broadcastData(Function<String, Boolean> func, Object data) {
        emitters.entrySet().forEach(entry -> {
            Boolean apply = func.apply(entry.getKey());
            if (apply) {
                sendData(entry.getValue(), DATA, data, entry.getKey());
            }
        });
    }

    /**
     * Send workflow completion notification and close all connections
     *
     * @param taskCode Workflow version code that completed execution
     */
    public void complete(String taskCode) {
        SseEmitter emitter = emitters.get(taskCode);
        if (emitter != null) {
            sendData(emitter, COMPLETE, COMPLETE, taskCode);
            emitter.complete();
        }
        // Clean up resources
        removeEmitter(taskCode);
    }

    /**
     * 过滤出符合条件的SSE连接，并广播
     *
     * @param func
     */
    public void broadcastComplete(Function<String, Boolean> func) {
        emitters.entrySet().forEach(entry -> {
            Boolean apply = func.apply(entry.getKey());
            if (apply) {
                sendData(entry.getValue(), COMPLETE, COMPLETE, entry.getKey());
            }
        });
    }

    /**
     * Remove a specific emitter
     *
     * @param taskCode
     */
    private void removeEmitter(String taskCode) {
        emitters.remove(taskCode);
    }

    private void sendData(SseEmitter emitter, String name, Object data, String taskCode) {
        try {
            emitter.send(SseEmitter.event()
                    .name(name)
                    .data(data));
        } catch (IOException e) {
            // If sending fails, remove the emitter
            removeEmitter(taskCode);
        }
    }
} 