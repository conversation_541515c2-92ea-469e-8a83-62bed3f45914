package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.ControlIntelligenceDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.ControlIntelligenceMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.ControlIntelligenceRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;


/**
 * 管控情报Repository实现类
 *
 * <AUTHOR>
 */
@Repository
@Slf4j
public class ControlIntelligenceRepositoryImpl extends ServiceImpl<ControlIntelligenceMapper, ControlIntelligenceDO>
        implements ControlIntelligenceRepository {

    @Override
    public boolean save(ControlIntelligenceDO controlIntelligenceDO) {
        return super.save(controlIntelligenceDO);
    }

    @Override
    public boolean saveBatch(List<ControlIntelligenceDO> controlIntelligenceDOList) {
        return super.saveBatch(controlIntelligenceDOList);
    }

    @Override
    public ControlIntelligenceDO getById(Long id) {
        return super.getById(id);
    }

    @Override
    public List<ControlIntelligenceDO> listByCondition(LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<ControlIntelligenceDO> wrapper = new LambdaQueryWrapper<ControlIntelligenceDO>()
                .gt(startTime != null, ControlIntelligenceDO::getPublishTime, startTime)
                .lt(endTime != null, ControlIntelligenceDO::getPublishTime, endTime)
                .orderByDesc(ControlIntelligenceDO::getGmtCreated);

        return super.list(wrapper);
    }

    @Override
    public List<ControlIntelligenceDO> list() {
        return super.list();
    }

    @Override
    public boolean updateById(ControlIntelligenceDO controlIntelligenceDO) {
        return super.updateById(controlIntelligenceDO);
    }

    @Override
    public boolean updateBatchById(List<ControlIntelligenceDO> controlIntelligenceDOs) {
        return super.updateBatchById(controlIntelligenceDOs);
    }

    @Override
    public boolean removeById(Long id) {
        return super.removeById(id);
    }

    @Override
    public boolean removeByIds(List<Long> ids) {
        return super.removeByIds(ids);
    }
}