package com.deepinnet.spatiotemporalplatform.skyflow.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.RiskWarningDO;
import com.deepinnet.spatiotemporalplatform.dal.dto.*;
import com.deepinnet.spatiotemporalplatform.skyflow.condition.RiskWarningQueryCondition;

import java.util.List;

/**
 * 风险预警Repository接口
 *
 * <AUTHOR>
 */
public interface RiskWarningRepository {

    /**
     * 保存风险预警信息
     *
     * @param riskWarningDO 风险预警信息
     * @return 是否保存成功
     */
    boolean save(RiskWarningDO riskWarningDO);

    /**
     * 批量保存风险预警信息
     *
     * @param riskWarningDOList 风险预警信息列表
     * @return 是否保存成功
     */
    boolean saveBatch(List<RiskWarningDO> riskWarningDOList);

    /**
     * 根据ID查询风险预警信息
     *
     * @param id 风险预警ID
     * @return 风险预警信息
     */
    RiskWarningDO getById(Long id);

    /**
     * 查询风险预警
     */
    RiskWarningDO getByCondition(LambdaQueryWrapper<RiskWarningDO> queryWrapper);

    /**
     * 根据条件查询风险预警列表
     *
     * @param queryWrapper 查询条件
     * @return 风险预警列表
     */
    List<RiskWarningDO> list(LambdaQueryWrapper<RiskWarningDO> queryWrapper);

    /**
     * 根据预警类型和时间范围查询风险预警
     *
     * @return 风险预警列表
     */
    List<RiskWarningDO> listByTypeAndTimeRange(RiskWarningQueryCondition queryCondition);


    List<SubTypeCountDTO> getSubTypeCountList(RiskWarningQueryCondition queryCondition);

    List<StatusCountDTO> getStatusCountList(RiskWarningQueryCondition queryCondition);

    /**
     * 查询时间段内的风险预警总数量
     * @param startTime
     * @param endTime
     * @return
     */
    Long getRiskWarningCount(Long startTime, Long endTime);

    /**
     * 根据预警状态查询风险预警
     *
     * @param warningStatus 预警状态
     * @return 风险预警列表
     */
    List<RiskWarningDO> listByWarningStatus(String warningStatus);
    
    /**
     * 查询每个planId的最新N条风险预警记录
     *
     * @param planIds 计划ID列表
     * @param limit 每个planId的记录限制数
     * @return 风险预警列表
     */
    List<RiskWarningDO> listTopRiskWarningsByPlanIds(List<String> planIds, Integer limit);

    /**
     * 更新风险预警信息
     *
     * @param riskWarningDO 风险预警信息
     * @return 是否更新成功
     */
    boolean updateById(RiskWarningDO riskWarningDO);

    /**
     * 批量更新风险预警信息
     *
     * @param riskWarningDOs
     * @return
     */
    boolean updateBatchByIds(List<RiskWarningDO> riskWarningDOs);

    /**
     * 根据ID删除风险预警信息
     *
     * @param id 风险预警ID
     * @return 是否删除成功
     */
    boolean removeById(Long id);

    /**
     * 批量删除风险预警信息
     *
     * @param ids ID列表
     * @return 是否删除成功
     */
    boolean removeByIds(List<Long> ids);
} 