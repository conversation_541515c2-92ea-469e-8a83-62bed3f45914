package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.TaskInFlightDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.TaskInFlightMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.TaskInFlightRepository;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Service
public class TaskInFlightRepositoryImpl extends ServiceImpl<TaskInFlightMapper, TaskInFlightDO> implements TaskInFlightRepository {

}
