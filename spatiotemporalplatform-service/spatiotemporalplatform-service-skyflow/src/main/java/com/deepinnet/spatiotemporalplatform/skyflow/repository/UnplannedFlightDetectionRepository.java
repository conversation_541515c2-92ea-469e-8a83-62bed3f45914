package com.deepinnet.spatiotemporalplatform.skyflow.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.UnplannedFlightDetectionDO;

/**
 * 未在飞行计划内的无人机检测 仓储接口
 */
public interface UnplannedFlightDetectionRepository extends IService<UnplannedFlightDetectionDO> {

    Long getUnplannedFlightCount(Long startTime, Long endTime);
}