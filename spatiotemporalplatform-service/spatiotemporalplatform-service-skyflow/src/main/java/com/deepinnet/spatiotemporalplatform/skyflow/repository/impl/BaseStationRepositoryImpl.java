package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.BaseStationDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.BaseStationMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.BaseStationRepository;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

/**
 * 基站数据Repository实现类
 *
 * <AUTHOR>
 */
@Repository
public class BaseStationRepositoryImpl extends ServiceImpl<BaseStationMapper, BaseStationDO> 
        implements BaseStationRepository {

    @Override
    public BaseStationDO getByExternalId(String externalId) {
        if (!StringUtils.hasText(externalId)) {
            return null;
        }
        
        LambdaQueryWrapper<BaseStationDO> wrapper = Wrappers.lambdaQuery(BaseStationDO.class)
                .eq(BaseStationDO::getExternalId, externalId);
        return super.getOne(wrapper);
    }
    
    @Override
    public void deleteByTenantId(String tenantId) {
        baseMapper.deleteByTenantId(tenantId);
    }
}