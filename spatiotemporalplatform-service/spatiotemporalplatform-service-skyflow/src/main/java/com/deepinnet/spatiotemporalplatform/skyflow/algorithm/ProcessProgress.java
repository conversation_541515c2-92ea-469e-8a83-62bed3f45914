package com.deepinnet.spatiotemporalplatform.skyflow.algorithm;

import lombok.Data;

import java.io.Serializable;

/**
 * Description: 处理进度类，用于记录数据处理的游标位置
 * Date: 2025/5/30
 * Author: lijunheng
 */
@Data
public class ProcessProgress implements Serializable {

    /**
     * 上次处理的最后一条数据的时间
     */
    private volatile String lastProcessedTime;

    /**
     * 上次处理的最后一条数据的ID
     * 与lastProcessedTime组成复合游标，避免时间相同时的数据遗漏
     */
    private volatile Long lastProcessedId;
}
