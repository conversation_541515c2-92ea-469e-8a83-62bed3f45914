package com.deepinnet.spatiotemporalplatform.skyflow.task;

import cn.hutool.core.collection.CollUtil;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.localdata.integration.DetectedDeviceClient;
import com.deepinnet.localdata.integration.model.output.DetectedDeviceDataDTO;
import com.deepinnet.localdata.integration.model.output.DetectedDeviceListResponseDTO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.DetectedDeviceDO;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.DetectedDeviceRepository;
import com.deepinnet.spatiotemporalplatform.base.config.TenantConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import javax.annotation.Resource;

/**
 * 侦测定位设备数据同步任务
 * 每5分钟从外部系统拉取设备数据并保存到本地数据库
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Profile({"local", "local-inner", "test", "demo", "alicloud"})
public class DetectedDeviceDataSyncTask {

    private final DetectedDeviceRepository detectedDeviceRepository;

    private final DetectedDeviceClient detectedDeviceClient;

    @Resource
    private TenantConfig tenantConfig;

    /**
     * 每5分钟执行一次
     */
    @Scheduled(cron = "0 0/5 * * * *")
    public void syncDetectedDeviceData() {
        LogUtil.info("[侦测定位设备数据同步] 任务开始执行");
        
        try {
            // 调用外部接口获取设备数据
            DetectedDeviceListResponseDTO response = detectedDeviceClient.getAllDetectedDevices();

            if (response == null) {
                LogUtil.error("外部系统返回侦测定位设备数据响应为空");
                return;
            }

            if (response.getStatus() != 200) {
                LogUtil.error("调用外部侦测定位设备数据接口失败，status={}", response.getStatus());
                return;
            }

            List<DetectedDeviceDataDTO> deviceDataList = response.getData();
            if (CollUtil.isEmpty(deviceDataList)) {
                LogUtil.info("[侦测定位设备数据同步] 没有设备数据需要同步");
                return;
            }

            // 生成本次同步的批次号
            Long batchNo = System.currentTimeMillis();
            Date now = new Date();

            // 处理数据：分为新增和更新
            List<DetectedDeviceDO> insertList = new ArrayList<>();
            List<DetectedDeviceDO> updateList = new ArrayList<>();
            
            for (DetectedDeviceDataDTO dto : deviceDataList) {
                DetectedDeviceDO detectedDeviceDO = convertToDetectedDeviceDO(dto);
                if (detectedDeviceDO == null || detectedDeviceDO.getExternalId() == null) {
                    continue;
                }
                
                // 根据externalId查询是否已存在
                DetectedDeviceDO existingDevice = detectedDeviceRepository.getByExternalId(detectedDeviceDO.getExternalId());
                
                if (existingDevice != null) {
                    // 已存在，更新数据
                    detectedDeviceDO.setId(existingDevice.getId()); // 保持原有的主键ID
                    detectedDeviceDO.setGmtCreated(existingDevice.getGmtCreated()); // 保持原有的创建时间
                    detectedDeviceDO.setGmtModified(now); // 更新修改时间
                    detectedDeviceDO.setBatchNo(batchNo);
                    updateList.add(detectedDeviceDO);
                } else {
                    // 不存在，新增数据
                    detectedDeviceDO.setBatchNo(batchNo);
                    detectedDeviceDO.setGmtCreated(now);
                    detectedDeviceDO.setGmtModified(now);
                    insertList.add(detectedDeviceDO);
                }
            }

            int totalCount = insertList.size() + updateList.size();
            if (totalCount == 0) {
                LogUtil.warn("[侦测定位设备数据同步] 数据转换后为空，跳过处理");
                return;
            }

            // 批量新增
            if (CollUtil.isNotEmpty(insertList)) {
                boolean insertResult = detectedDeviceRepository.saveBatch(insertList);
                if (!insertResult) {
                    LogUtil.error("[侦测定位设备数据同步] 批量新增数据失败");
                    return;
                }
                LogUtil.info("[侦测定位设备数据同步] 成功新增 {} 条数据", insertList.size());
            }

            // 批量更新
            if (CollUtil.isNotEmpty(updateList)) {
                boolean updateResult = detectedDeviceRepository.updateBatchById(updateList);
                if (!updateResult) {
                    LogUtil.error("[侦测定位设备数据同步] 批量更新数据失败");
                    return;
                }
                LogUtil.info("[侦测定位设备数据同步] 成功更新 {} 条数据", updateList.size());
            }

            LogUtil.info("[侦测定位设备数据同步] 同步完成，新增: {} 条，更新: {} 条，批次号: {}", 
                    insertList.size(), updateList.size(), batchNo);
                    
        } catch (Exception e) {
            LogUtil.error("[侦测定位设备数据同步] 同步设备数据失败", e);
            throw e;
        }
    }

    /**
     * 将外部响应数据转换为DetectedDeviceDO
     *
     * @param dto 外部接口响应的设备数据
     * @return DetectedDeviceDO对象
     */
    private DetectedDeviceDO convertToDetectedDeviceDO(DetectedDeviceDataDTO dto) {
        if (dto == null) {
            return null;
        }

        DetectedDeviceDO detectedDeviceDO = new DetectedDeviceDO();
        // 将DTO中的id映射为DO中的externalId
        detectedDeviceDO.setExternalId(dto.getId());
        detectedDeviceDO.setManufacturer(dto.getManufacturer());
        detectedDeviceDO.setDeviceName(dto.getDeviceName());
        detectedDeviceDO.setBrandModel(dto.getBrandModel());
        detectedDeviceDO.setSerialNo(dto.getSerialNo());
        detectedDeviceDO.setStationType(dto.getStationType());
        detectedDeviceDO.setTechParams(dto.getTechParams());
        detectedDeviceDO.setDataStandard(dto.getDataStandard());
        detectedDeviceDO.setEquipUsage(dto.getEquipUsage());
        detectedDeviceDO.setInstallEnvir(dto.getInstallEnvir());
        detectedDeviceDO.setInstallAdress(dto.getInstallAdress());
        detectedDeviceDO.setAerialHeight(dto.getAerialHeight());
        detectedDeviceDO.setOperatingFrequency(dto.getOperatingFrequency());
        detectedDeviceDO.setInstallDate(dto.getInstallDate());
        detectedDeviceDO.setInstallCount(dto.getInstallCount());
        detectedDeviceDO.setInstallSituation(dto.getInstallSituation());
        detectedDeviceDO.setPropCompany(dto.getPropCompany());
        detectedDeviceDO.setPropLinkUser(dto.getPropLinkUser());
        detectedDeviceDO.setLinkPhone(dto.getLinkPhone());
        detectedDeviceDO.setDeviceManager(dto.getDeviceManager());
        detectedDeviceDO.setDevicePhone(dto.getDevicePhone());
        detectedDeviceDO.setManageRequire(dto.getManageRequire());
        detectedDeviceDO.setRemark(dto.getRemark());
        detectedDeviceDO.setCrtUserName(dto.getCrtUserName());
        detectedDeviceDO.setCrtUserId(dto.getCrtUserId());
        detectedDeviceDO.setCrtTime(dto.getCrtTime());
        detectedDeviceDO.setIsDeleted(dto.getIsDeleted());
        detectedDeviceDO.setUpdUserName(dto.getUpdUserName());
        detectedDeviceDO.setUpdUserId(dto.getUpdUserId());
        detectedDeviceDO.setUpdTime(dto.getUpdTime());
        detectedDeviceDO.setDepartId(dto.getDepartId());
        detectedDeviceDO.setDepartIds(dto.getDepartIds());
        detectedDeviceDO.setExternalTenantId(dto.getTenantId());
        detectedDeviceDO.setTeamId(dto.getTeamId());
        detectedDeviceDO.setReceiverGain(dto.getReceiverGain());
        detectedDeviceDO.setTransPower(dto.getTransPower());
        detectedDeviceDO.setPulseWaveform(dto.getPulseWaveform());
        detectedDeviceDO.setPolarizationMode(dto.getPolarizationMode());
        detectedDeviceDO.setFigure(dto.getFigure());
        detectedDeviceDO.setBandWidth(dto.getBandWidth());
        detectedDeviceDO.setSitePicUrl(dto.getSitePicUrl());
        detectedDeviceDO.setLon(dto.getLon());
        detectedDeviceDO.setLat(dto.getLat());
        detectedDeviceDO.setGcoverRage(dto.getGcoverRage());
        detectedDeviceDO.setVcoverRage(dto.getVcoverRage());
        detectedDeviceDO.setTenantId(tenantConfig.getTenantId());
        return detectedDeviceDO;
    }
} 