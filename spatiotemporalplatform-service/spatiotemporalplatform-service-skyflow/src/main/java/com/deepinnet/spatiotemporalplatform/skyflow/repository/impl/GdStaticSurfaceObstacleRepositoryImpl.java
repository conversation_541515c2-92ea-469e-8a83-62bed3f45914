package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.GdStaticSurfaceObstacleDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.GdStaticSurfaceObstacleMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.GdStaticSurfaceObstacleRepository;
import org.locationtech.jts.geom.Point;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/15
 */
@Service
public class GdStaticSurfaceObstacleRepositoryImpl extends ServiceImpl<GdStaticSurfaceObstacleMapper, GdStaticSurfaceObstacleDO>
        implements GdStaticSurfaceObstacleRepository {


    @Override
    public List<GdStaticSurfaceObstacleDO> getStaticSurfaceObstacles(List<String> bd2DGridLocationCodes, List<Point> pointList, String region) {
        return baseMapper.getStaticSurfaceObstacles(bd2DGridLocationCodes, pointList, region);
    }
}
