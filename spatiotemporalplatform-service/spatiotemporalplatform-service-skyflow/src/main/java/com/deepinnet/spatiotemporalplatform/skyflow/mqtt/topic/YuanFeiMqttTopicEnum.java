package com.deepinnet.spatiotemporalplatform.skyflow.mqtt.topic;

/**
 * <AUTHOR>
 * @version 2025-03-14
 */
public enum YuanFeiMqttTopicEnum {
    WARNING("/jtgk/warning/f6d6378f2e48466e9a930251891e96c7"),

    REAL_TIME_FLY_DATA("/anyang/realtime/fly/uav/"),
//    REAL_TIME_FLY_DATA("/jtyy/realtime/fly/uav/"),

    AIRSPACE_STATUS_UPDATE("/hatc/airspace/AirspaceStatusUpdateMessage"),
    ;

    private String topicPrefix;

    YuanFeiMqttTopicEnum(String topicPrefix) {
        this.topicPrefix = topicPrefix;
    }

    public String getTopic() {
        return topicPrefix;
    }
}
