package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.GdAoiDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.GdAoiMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.GdAoiRepository;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024-08-08 16:08:12
 */
@Repository
public class GdAoiRepositoryImpl extends ServiceImpl<GdAoiMapper, GdAoiDO>
        implements GdAoiRepository {

    @Resource
    private GdAoiMapper gdAoiMapper;

    @Override
    public List<GdAoiDO> getAoiList(String region) {
        return gdAoiMapper.getAoiList(region);
    }
}




