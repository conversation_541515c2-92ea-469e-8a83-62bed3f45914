package com.deepinnet.spatiotemporalplatform.skyflow.service;

import com.deepinnet.spatiotemporalplatform.dal.dataobject.WeatherIntelligenceDO;
import com.deepinnet.spatiotemporalplatform.dto.WeatherIntelligenceQueryDTO;

import java.util.List;

/**
 * 气象情报服务
 *
 * <AUTHOR>
 */
public interface WeatherIntelligenceService {

    /**
     * 保存气象情报信息
     *
     * @param weatherIntelligenceDO 气象情报信息
     * @return 是否保存成功
     */
    boolean save(WeatherIntelligenceDO weatherIntelligenceDO);

    /**
     * 批量保存气象情报信息
     *
     * @param weatherIntelligenceDOList 气象情报信息列表
     * @return 是否保存成功
     */
    boolean saveBatch(List<WeatherIntelligenceDO> weatherIntelligenceDOList);

    /**
     * 根据ID查询气象情报信息
     *
     * @param id 气象情报ID
     * @return 气象情报信息
     */
    WeatherIntelligenceDO getById(Long id);

    /**
     * 根据时间范围查询气象情报数据
     *
     * @return 气象情报数据列表
     */
    List<WeatherIntelligenceDO> listByTimeRange(WeatherIntelligenceQueryDTO queryDTO);

    /**
     * 更新气象情报信息
     *
     * @param weatherIntelligenceDO 气象情报信息
     * @return 是否更新成功
     */
    boolean updateById(WeatherIntelligenceDO weatherIntelligenceDO);

    /**
     * 根据ID删除气象情报信息
     *
     * @param id 气象情报ID
     * @return 是否删除成功
     */
    boolean removeById(Long id);

    /**
     * 批量删除气象情报信息
     *
     * @param ids ID列表
     * @return 是否删除成功
     */
    boolean removeByIds(List<Long> ids);
} 