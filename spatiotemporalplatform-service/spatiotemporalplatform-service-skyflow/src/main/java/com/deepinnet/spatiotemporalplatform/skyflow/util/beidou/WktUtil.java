package com.deepinnet.spatiotemporalplatform.skyflow.util.beidou;

import com.deepinnet.digitaltwin.common.util.PointCoordinate;
import com.deepinnet.spatiotemporalplatform.skyflow.enums.CoordinateReferenceSystemNameEnum;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.geom.*;
import org.locationtech.jts.io.WKTReader;
import org.locationtech.jts.io.WKTWriter;
import org.locationtech.jts.linearref.LengthIndexedLine;
import org.locationtech.jts.operation.buffer.BufferOp;
import org.locationtech.jts.operation.buffer.BufferParameters;
import org.locationtech.proj4j.*;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * <PERSON><PERSON> zengju<PERSON>ui
 * Date 2024-06-17
 **/

@Slf4j
@UtilityClass
public class WktUtil {

    private static final ThreadLocal<WKTReader> threadLocalReader = ThreadLocal.withInitial(WKTReader::new);
    private static final ThreadLocal<WKTWriter> threadLocalWriter = ThreadLocal.withInitial(WKTWriter::new);

    public static final PrecisionModel precisionModel = new PrecisionModel(Math.pow(10, 6));
    public static final GeometryFactory geometryFactory = new GeometryFactory(precisionModel, CoordinateReferenceSystemNameEnum.WGS_84.getSrid());

    private static final double lineCenterFraction = 0.5D;


    private static final CoordinateTransformFactory ctFactory = new CoordinateTransformFactory();
    private static final CRSFactory crsFactory = new CRSFactory();

    /**
     * 将单位米转换成度
     * 可以用于Geometry.buffer方法
     *
     * @param distance
     * @return
     */
    public static double radiusInDegrees(double distance) {
        return (distance / 40075016.686) * 360.0;
    }

    public static Geometry transformGeometry(Geometry geometry, CoordinateReferenceSystemNameEnum sourceCRSNameEnum, CoordinateReferenceSystemNameEnum targetCRSNameEnum) {
        CoordinateReferenceSystem sourceCRS = crsFactory.createFromName(sourceCRSNameEnum.getCrsName());
        CoordinateReferenceSystem targetCRS = crsFactory.createFromName(targetCRSNameEnum.getCrsName());
        return transformGeometry(geometry, sourceCRS, targetCRS);
    }

    /**
     * CGCS2000 到 WGS84 的转换
     * @param cgcs2000Coordinate
     * @return
     */
    public static Coordinate convertCGCS2000ToWGS84(Coordinate cgcs2000Coordinate) {
        // 创建 CGCS2000 和 WGS84 的坐标参考系统
        CoordinateReferenceSystem cgcs2000CRS = crsFactory.createFromName("EPSG:4490"); // CGCS2000
        CoordinateReferenceSystem wgs84CRS = crsFactory.createFromName("EPSG:4326"); // WGS84

        // 创建坐标转换
        CoordinateTransform transform = ctFactory.createTransform(cgcs2000CRS, wgs84CRS);

        // 转换坐标
        ProjCoordinate cgcsCoord = new ProjCoordinate(cgcs2000Coordinate.x, cgcs2000Coordinate.y);
        ProjCoordinate wgsCoord = new ProjCoordinate();

        // 进行坐标转换
        transform.transform(cgcsCoord, wgsCoord);

        return new Coordinate(wgsCoord.x, wgsCoord.y);
    }


    public static Geometry toGeometry(String wkt) {
        WKTReader reader = threadLocalReader.get();

        try {
            return reader.read(wkt);
        } catch (Exception e) {
            log.error("parse wkt error, " + wkt);
        }

        return null;
    }

    public static LineString toLineString(String wkt) {
        return (LineString) toGeometry(wkt);
    }

    public static Polygon toPolygon(String wkt) {
        return (Polygon) toGeometry(wkt);
    }

    public static String toWkt(Geometry geometry) {
        if (geometry == null) {
            return "";
        }

        return threadLocalWriter.get().write(geometry);
    }

    public static Point toPoint(String lng, String lat) {
        return toPoint(Double.parseDouble(lng), Double.parseDouble(lat));
    }

    public static Point toPoint(double lng, double lat) {
        return geometryFactory.createPoint(new Coordinate(lng, lat));
    }

    public static Point toPoint(String wkt) {
        if (StringUtils.isBlank(wkt)) {
            return null;
        }

        Pattern pattern = Pattern.compile("-?\\d+(?:\\.\\d+)?");
        Matcher matcher = pattern.matcher(wkt);
        List<String> coordinates = new ArrayList<>();
        while (matcher.find()) {
            coordinates.add(matcher.group());
        }

        return toPoint(coordinates.get(0), coordinates.get(1));
    }

    public static Point getLineCenterPoint(LineString lineString) {
        LengthIndexedLine indexedLine = new LengthIndexedLine(lineString);
        double totalLength = indexedLine.getEndIndex();
        double pointLocation = lineCenterFraction * totalLength;
        Coordinate interpolatedCoord = indexedLine.extractPoint(pointLocation);
        return geometryFactory.createPoint(interpolatedCoord);
    }

    public static LineString lineAddPoint(LineString origin, Point point) {
        // 获取原始 LineString 的坐标数组
        Coordinate[] originalCoordinates = origin.getCoordinates();

        // 创建新的坐标数组，长度为原始坐标数组长度加一
        Coordinate[] newCoordinates = new Coordinate[originalCoordinates.length + 1];

        // 将原始坐标复制到新的坐标数组
        System.arraycopy(originalCoordinates, 0, newCoordinates, 0, originalCoordinates.length);

        // 在新的坐标数组的最后添加新点
        newCoordinates[newCoordinates.length - 1] = point.getCoordinate();

        // 使用新的坐标数组创建新的 LineString
        return geometryFactory.createLineString(newCoordinates);
    }

    public static LineString pointListToLineString(List<Point> pointList) {
        Assert.isTrue(CollectionUtils.isNotEmpty(pointList), "坐标数组不可为空");

        if (pointList.size() < LineString.MINIMUM_VALID_SIZE) {
            pointList.add(pointList.get(pointList.size() - 1));
        }

        return geometryFactory.createLineString(pointList.stream().map(Point::getCoordinate).toArray(Coordinate[]::new));
    }

    public static boolean isPolygon(String geo) {
        return toPolygon(geo) != null;
    }
    /**
     * 校验给定的经纬度是否合法。
     *
     * @param latitude  纬度，范围应在 -90 到 +90 之间
     * @param longitude 经度，范围应在 -180 到 +180 之间
     * @return 如果经纬度合法，返回 true；否则返回 false
     */
    public static boolean isValidCoordinates(double latitude, double longitude) {
        // 检查纬度是否在 -90 到 +90 之间
        if (latitude < -90 || latitude > 90) {
            return false;
        }
        // 检查经度是否在 -180 到 +180 之间
        if (longitude < -180 || longitude > 180) {
            return false;
        }
        // 如果两个条件都满足，则经纬度合法
        return true;
    }
    public static Polygon getBufferdPolygon(Polygon polygon, double distance) {
        BufferParameters bufferParams = new BufferParameters();
        bufferParams.setEndCapStyle(BufferParameters.CAP_FLAT);

        // 创建缓冲操作对象
        BufferOp bufferOp = new BufferOp(polygon,bufferParams);
        return (Polygon) bufferOp.getResultGeometry(distance);
    }

    /**
     * 坐标系转换
     *
     * @param geometry
     * @param sourceCRS
     * @param targetCRS
     * @return
     */
    private static Geometry transformGeometry(Geometry geometry, CoordinateReferenceSystem sourceCRS, CoordinateReferenceSystem targetCRS) {
        // 创建投影转换器
        CoordinateTransform transform = ctFactory.createTransform(sourceCRS, targetCRS);

        // 遍历几何的每个点并进行投影转换
        Coordinate[] originalCoordinates = geometry.getCoordinates();
        Coordinate[] transformedCoordinates = new Coordinate[originalCoordinates.length];

        ProjCoordinate sourceCoord = new ProjCoordinate();
        ProjCoordinate targetCoord = new ProjCoordinate();

        for (int i = 0; i < originalCoordinates.length; i++) {
            sourceCoord.x = originalCoordinates[i].x;
            sourceCoord.y = originalCoordinates[i].y;
            transform.transform(sourceCoord, targetCoord);
            transformedCoordinates[i] = new Coordinate(targetCoord.x, targetCoord.y);
        }

        GeometryFactory geometryFactory = new GeometryFactory(precisionModel, CoordinateReferenceSystemNameEnum.getByCrsName(targetCRS.getName()).getSrid());
        // 根据几何类型重新创建几何
        if (geometry instanceof Polygon) {
            return geometryFactory.createPolygon(transformedCoordinates);
        } else if (geometry instanceof LineString) {
            return geometryFactory.createLineString(transformedCoordinates);
        } else if (geometry instanceof Point) {
            return geometryFactory.createPoint(transformedCoordinates[0]);
        } else {
            throw new UnsupportedOperationException("Unsupported geometry type: " + geometry.getGeometryType());
        }
    }

    public static boolean isPoint(String wkt) {
        return isGeometry(wkt, Geometry.TYPENAME_POINT);
    }

    public static boolean isLineString(String wkt) {
        return isGeometry(wkt, Geometry.TYPENAME_LINESTRING);
    }

    private static boolean isGeometry(String wkt, String type) {
        return StringUtils.isNotBlank(wkt) && StringUtils.startsWithIgnoreCase(wkt, type);
    }

    public static double getAreaKm(Geometry geometry) {
        return reprojectToMeters(geometry).getArea() / 1_000_000;
    }

    public static Geometry reprojectToMeters(Geometry geometry) {
        CRSFactory crsFactory = new CRSFactory();
        CoordinateReferenceSystem srcCrs = crsFactory.createFromName("EPSG:4490"); // GCJ02
        CoordinateReferenceSystem dstCrs = crsFactory.createFromName("EPSG:3857"); // Web Mercator (单位为米)

        CoordinateTransformFactory ctFactory = new CoordinateTransformFactory();
        CoordinateTransform transform = ctFactory.createTransform(srcCrs, dstCrs);

        Geometry copy = geometry.copy();
        copy.apply((CoordinateFilter) coord -> {
            ProjCoordinate src = new ProjCoordinate(coord.getX(), coord.getY());
            ProjCoordinate dst = new ProjCoordinate();
            transform.transform(src, dst);
            coord.x = dst.x;
            coord.y = dst.y;
        });

        return copy;
    }
}

