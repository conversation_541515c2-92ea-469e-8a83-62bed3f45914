package com.deepinnet.spatiotemporalplatform.skyflow.convert;

import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightDataOverviewDO;
import com.deepinnet.spatiotemporalplatform.vo.FlightDataOverviewVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 飞行数据概览转换器
 *
 * <AUTHOR> wong
 * @create 2025/01/14
 */
@Mapper(componentModel = "spring")
public interface FlightDataOverviewConvert {

    FlightDataOverviewConvert INSTANCE = Mappers.getMapper(FlightDataOverviewConvert.class);

    /**
     * DO转VO
     *
     * @param flightDataOverviewDO DO对象
     * @return VO对象
     */
    FlightDataOverviewVO toFlightDataOverviewVO(FlightDataOverviewDO flightDataOverviewDO);
} 