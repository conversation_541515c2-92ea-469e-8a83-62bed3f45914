package com.deepinnet.spatiotemporalplatform.skyflow.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.BaseStationDO;

/**
 * 基站数据Repository接口
 *
 * <AUTHOR>
 */
public interface BaseStationRepository extends IService<BaseStationDO> {
    
    /**
     * 根据外部ID查询基站数据
     *
     * @param externalId 外部ID
     * @return 基站数据
     */
    BaseStationDO getByExternalId(String externalId);
    
    /**
     * 根据租户ID删除基站数据
     *
     * @param tenantId 租户ID
     */
    void deleteByTenantId(String tenantId);
} 