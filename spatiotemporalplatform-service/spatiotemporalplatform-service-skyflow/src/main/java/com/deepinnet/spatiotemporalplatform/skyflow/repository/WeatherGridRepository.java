package com.deepinnet.spatiotemporalplatform.skyflow.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.WeatherGridDO;
import com.deepinnet.spatiotemporalplatform.skyflow.condition.WeatherGridQueryCondition;

import java.util.List;

/**
 * 气象网格表 仓储接口
 */
public interface WeatherGridRepository extends IService<WeatherGridDO> {

    int upsertBatch(List<WeatherGridDO> list);

    List<WeatherGridDO> listByCondition(WeatherGridQueryCondition condition);
}