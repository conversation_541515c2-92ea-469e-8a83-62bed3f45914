package com.deepinnet.spatiotemporalplatform.skyflow.service;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.model.skyflow.WeatherGridPostBody;
import com.deepinnet.spatiotemporalplatform.skyflow.util.weather.LocationWeatherData;

import java.util.List;

/**
 * Creator zengjuerui
 * Date 2025-04-19
 **/
public interface WeatherGridService {

    List<LocationWeatherData> queryWeatherGrid(WeatherGridPostBody weatherGridPostBody);
    
    /**
     * 处理气象网格数据并生成文件
     *
     * @param weatherGridPostBody 气象网格请求参数
     * @return 生成的文件URL列表
     */
    List<String> processWeatherGridDataAndGenerateFiles(WeatherGridPostBody weatherGridPostBody);
}
