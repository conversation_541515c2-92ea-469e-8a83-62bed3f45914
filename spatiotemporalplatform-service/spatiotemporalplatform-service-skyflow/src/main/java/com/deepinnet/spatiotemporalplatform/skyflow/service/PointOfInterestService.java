package com.deepinnet.spatiotemporalplatform.skyflow.service;



import com.deepinnet.spatiotemporalplatform.dto.*;

import java.util.List;

/**
 * <p>
 *  service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
public interface PointOfInterestService {


    List<Poi> getPoi(POIQueryDTO dto);

    List<StaticSurfaceObstacle> getStaticObstacle(StaticSurfaceObstacleQueryDTO dto);

    List<Poi> getPolygonPoi(POIPolygonQueryDTO dto);
}
