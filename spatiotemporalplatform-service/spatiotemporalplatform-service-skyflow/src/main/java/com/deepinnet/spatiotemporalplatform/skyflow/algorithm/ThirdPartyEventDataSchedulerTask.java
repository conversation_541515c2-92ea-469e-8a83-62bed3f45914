package com.deepinnet.spatiotemporalplatform.skyflow.algorithm;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.util.JsonConvertUtil;
import com.deepinnet.digitaltwin.common.util.PointCoordinate;
import com.deepinnet.spatiotemporalplatform.base.lock.DatabaseDistributedLock;
import com.deepinnet.spatiotemporalplatform.common.ScheduleTaskExecService;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.FlightEventsRepository;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.ThirdPartyEventRepository;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightEventsDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.ThirdPartyEventDO;
import com.deepinnet.spatiotemporalplatform.dto.FlightEventsDTO;
import com.deepinnet.spatiotemporalplatform.enums.FlightEventStatusEnum;
import com.deepinnet.spatiotemporalplatform.enums.FlightEventTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 算法侧数据监听和转换任务
 * Description: 监听算法侧的事件数据变更，将其转换为EventMonitorService支持的数据格式
 * Date: 2025/5/30
 * Author: lijunheng
 */
@Slf4j
@Component
@Profile({"local", "test", "alicloud", "sz-lg-gov"})
public class ThirdPartyEventDataSchedulerTask implements ScheduleTaskExecService {

    private static final String LOCK_NAME = "third_party_algorithm_event_data_transfer_scheduler_task";

    @Resource
    private DatabaseDistributedLock lock;

    /**
     * 每批次处理的最大数据量
     */
    private static final int BATCH_SIZE = 1000;

    /**
     * 处理进度，内存存储，重启后会丢失
     */
    private volatile ProcessProgress processProgress;

    @Resource
    private ThirdPartyEventRepository algorithmEventRepository;

    @Resource
    private FlightEventsRepository flightEventsRepository;

    @Resource
    private EventMonitorService eventMonitorService;

    @Scheduled(fixedDelay = 10000)
    @Override
    public void execute() {
        boolean canExec = lock.tryLock(LOCK_NAME);
        try {
            if (canExec) {
                log.info("get lock to do task,开始处理算法侧事件数据转换任务");
                try {
                    // 查询算法侧的新增事件数据
                    List<ThirdPartyEventDO> newEvents;
                    while (true) {
                        // 读取上次处理进度
                        ProcessProgress progress = loadProgress();
                        newEvents = queryNewAlgorithmEvents(progress);
                        log.info("发现 {} 条新的三方算法侧事件数据", newEvents.size());
                        if (CollectionUtil.isEmpty(newEvents)) {
                            break;
                        }

                        // 转换并保存事件数据
                        List<FlightEventsDTO> convertedEvents = convertAlgorithmEventsToFlightEvents(newEvents);

                        // 批量保存到EventMonitorService
                        eventMonitorService.saveOrUpdateEventBatch(convertedEvents);
                        log.info("成功转换并保存 {} 条飞行事件", convertedEvents.size());

                        // 更新处理进度
                        ThirdPartyEventDO lastEvent = newEvents.get(newEvents.size() - 1);
                        updateProgress(lastEvent);
                    }

                    log.info("没有发现新的三方算法侧事件数据");
                } catch (Exception e) {
                    log.error("三方算法侧事件数据转换任务异常", e);
                }
            }
        } finally {
            if (canExec) {
                lock.unlock(LOCK_NAME);
            }
        }
    }

    /**
     * 查询算法侧的新增事件数据
     * 使用复合游标（时间+ID）避免数据遗漏和重复读取
     */
    private List<ThirdPartyEventDO> queryNewAlgorithmEvents(ProcessProgress progress) {
        LambdaQueryWrapper<ThirdPartyEventDO> wrapper = Wrappers.lambdaQuery(ThirdPartyEventDO.class);
        //过滤掉初始化和已取消的事件
        wrapper.notIn(ThirdPartyEventDO::getEventStatus, ListUtil.toList(FlightEventStatusEnum.INIT.name(), FlightEventStatusEnum.CANCELLED.name()));

        String lastProcessedTime = progress.getLastProcessedTime();
        if (lastProcessedTime != null) {
            // 使用系统默认时区
            OffsetDateTime lastProcessedTimeOffsetDateTime = OffsetDateTime.parse(lastProcessedTime);
            // 使用复合条件：时间大于最后处理时间，或时间等于最后处理时间但ID大于最后处理ID
            wrapper.gt(ThirdPartyEventDO::getGmtModified, lastProcessedTimeOffsetDateTime)
                    .or(subWrapper -> subWrapper
                            .eq(ThirdPartyEventDO::getGmtModified, lastProcessedTimeOffsetDateTime)
                            .gt(progress.getLastProcessedId() != null, ThirdPartyEventDO::getId, progress.getLastProcessedId())
                    );
        }

        wrapper.orderByAsc(ThirdPartyEventDO::getGmtModified)
                .orderByAsc(ThirdPartyEventDO::getId)
                .last("LIMIT " + BATCH_SIZE);
        return algorithmEventRepository.list(wrapper);
    }

    /**
     * 将算法事件转换为飞行事件
     */
    private List<FlightEventsDTO> convertAlgorithmEventsToFlightEvents(List<ThirdPartyEventDO> algorithmEvents) {
        return algorithmEvents.stream().map(algorithmEvent -> {
                    try {
                        FlightEventsDTO flightEventsDTO = convertToFlightEventsDTO(algorithmEvent);
                        log.debug("成功转换算法事件: eventId={}, flightTaskId={}, eventType={}",
                                algorithmEvent.getId(), algorithmEvent.getFlightTaskId(), algorithmEvent.getEventType());

                        return flightEventsDTO;
                    } catch (Exception e) {
                        log.error("转换算法事件异常: eventId={}", algorithmEvent.getId(), e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private FlightEventsDTO convertToFlightEventsDTO(ThirdPartyEventDO algorithmEvent) {
        FlightEventsDTO flightEventsDTO = new FlightEventsDTO();
        flightEventsDTO.setAlgorithmEventId(String.valueOf(algorithmEvent.getId()));
        FlightEventTypeEnum flightEventTypeEnum = FlightEventTypeEnum.valueOf(algorithmEvent.getEventType());
        flightEventsDTO.setEventType(flightEventTypeEnum);
        flightEventsDTO.setEventName(flightEventTypeEnum.getName());
        flightEventsDTO.setDescription(algorithmEvent.getEventDescription());
        flightEventsDTO.setEventStartTime(convertToLocalDateTime(algorithmEvent.getEventStartTime()));
        flightEventsDTO.setEventEndTime(convertToLocalDateTime(algorithmEvent.getEventEndTime()));
        flightEventsDTO.setDuration(Math.abs(Duration.between(algorithmEvent.getEventEndTime(), algorithmEvent.getEventStartTime()).getSeconds()));
        flightEventsDTO.setStatus(FlightEventStatusEnum.valueOf(algorithmEvent.getEventStatus()));
        flightEventsDTO.setEvidenceImages(JsonConvertUtil.toJsonStr(algorithmEvent.getEvidenceImageUrls()));
        flightEventsDTO.setEvidenceVideos(JsonConvertUtil.toJsonStr(algorithmEvent.getEvidenceVideoUrls()));
        flightEventsDTO.setFlightTaskCode(algorithmEvent.getFlightTaskId());
        flightEventsDTO.setTenantId(algorithmEvent.getTenantId());
        flightEventsDTO.setAlgorithmEventGmtModified(convertToLocalDateTime(algorithmEvent.getGmtModified()));
        flightEventsDTO.setFlightId(algorithmEvent.getFlightSortieId());
        //违章建筑分析算法的有地址
        if (flightEventTypeEnum == FlightEventTypeEnum.ILLEGAL_CONSTRUCTION_ANALYSIS) {
            flightEventsDTO.setEventLocation(algorithmEvent.getAddress());
            String locationStr = algorithmEvent.getLocationStr();
            if (StringUtils.isNotBlank(locationStr)) {
                String[] split = locationStr.split(",");
                try {
                    PointCoordinate pointCoordinate = new PointCoordinate();
                    pointCoordinate.setLongitude(Double.valueOf(split[0]));
                    pointCoordinate.setLatitude(Double.valueOf(split[1]));
                    flightEventsDTO.setEventPoint(pointCoordinate);
                } catch (Exception e) {}
            }
        }
        return flightEventsDTO;
    }

    /**
     * 加载处理进度
     */
    private ProcessProgress loadProgress() {
        // 如果内存中的进度不为空，直接返回
        if (processProgress != null) {
            log.info("从内存加载处理进度: lastProcessedTime={}", processProgress.getLastProcessedTime());
            return processProgress;
        }

        // 内存中进度为空，从flight_events表查询最新更新时间作为起始进度
        try {
            FlightEventsDO latestUpdateEvents = flightEventsRepository.queryLatestEvent();
            if (latestUpdateEvents != null) {
                processProgress = new ProcessProgress();

                OffsetDateTime offsetDateTime = latestUpdateEvents.getAlgorithmEventGmtModified().atZone(ZoneId.systemDefault()).toOffsetDateTime();
                processProgress.setLastProcessedTime(offsetDateTime.toString());
                log.info("从flight_events表加载处理进度: lastProcessedTime={}", offsetDateTime);
                return processProgress;
            }
        } catch (Exception e) {
            log.error("从flight_events表查询最新更新时间失败", e);
        }

        // 如果表为空或查询失败，从头开始
        processProgress = new ProcessProgress();
        log.info("初始化处理进度，从头开始");
        return processProgress;
    }

    /**
     * 更新处理进度
     * 同时记录最后处理的时间和ID
     */
    private void updateProgress(ThirdPartyEventDO lastEvent) {
        try {
            if (processProgress == null) {
                processProgress = new ProcessProgress();
            }
            processProgress.setLastProcessedTime(lastEvent.getGmtModified().toString());
            processProgress.setLastProcessedId(lastEvent.getId());

            log.info("更新处理进度: lastProcessedTime={}, lastProcessedId={}",
                    processProgress.getLastProcessedTime(), processProgress.getLastProcessedId());

        } catch (Exception e) {
            log.error("更新处理进度失败", e);
        }
    }

    private LocalDateTime convertToLocalDateTime(OffsetDateTime offsetDateTime) {
        if (offsetDateTime == null) {
            return null;
        }
        return offsetDateTime.atZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime();
    }
}
