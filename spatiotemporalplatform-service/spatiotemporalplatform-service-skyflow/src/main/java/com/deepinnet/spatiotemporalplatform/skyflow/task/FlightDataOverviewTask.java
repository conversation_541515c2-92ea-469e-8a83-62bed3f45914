package com.deepinnet.spatiotemporalplatform.skyflow.task;

import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.localdata.integration.FlightDataOverviewClient;
import com.deepinnet.localdata.integration.model.input.FlightDataOverviewQueryDTO;
import com.deepinnet.localdata.integration.model.output.FlightDataOverviewResponseDTO;
import com.deepinnet.localdata.integration.model.output.FlightDataOverviewDataDTO;
import com.deepinnet.spatiotemporalplatform.base.config.TenantConfig;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightDataOverviewDO;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.FlightDataOverviewRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 飞行数据概览同步任务
 * 每5分钟从外部系统拉取飞行数据概览并保存到本地数据库
 *
 * <AUTHOR> wong
 * @create 2025/01/14
 */
@Component
@RequiredArgsConstructor
@Profile({"local", "local-inner", "test", "demo", "alicloud"})
public class FlightDataOverviewTask {

    private final FlightDataOverviewRepository flightDataOverviewRepository;

    private final FlightDataOverviewClient flightDataOverviewClient;

    private final TenantConfig tenantConfig;

    /**
     * 每5分钟执行一次
     */
    //@Scheduled(cron = "0 0/5 * * * *")
    @Scheduled(fixedRate = 5, timeUnit = TimeUnit.SECONDS)
    public void syncFlightDataOverview() {
        LogUtil.info("[飞行数据概览同步] 任务开始执行");

        try {
            // 调用外部接口获取飞行数据概览
            FlightDataOverviewQueryDTO queryDTO = new FlightDataOverviewQueryDTO();
            FlightDataOverviewResponseDTO response = flightDataOverviewClient.getFlightDataOverview(queryDTO);

            if (response == null) {
                LogUtil.error("[飞行数据概览同步] 外部系统返回响应为空");
                return;
            }

            if (response.getStatus() != 200) {
                LogUtil.error("[飞行数据概览同步] 调用外部接口失败，status={}", response.getStatus());
                return;
            }

            FlightDataOverviewDataDTO data = response.getData();
            if (data == null) {
                LogUtil.info("[飞行数据概览同步] 没有飞行数据概览需要同步");
                return;
            }

            // 生成本次同步的批次号（定时任务触发时的时间戳）
            Long batchNo = System.currentTimeMillis();
            Date now = new Date();

            // 转换数据并设置批次号
            FlightDataOverviewDO overviewDO = convertToFlightDataOverviewDO(data);
            if (overviewDO != null) {
                overviewDO.setBatchNo(batchNo);
                overviewDO.setGmtCreated(now);
                overviewDO.setGmtModified(now);
                overviewDO.setGmtModified(now);
                overviewDO.setTenantId(tenantConfig.getTenantId());

                // 保存数据
                boolean saveResult = flightDataOverviewRepository.save(overviewDO);
                if (!saveResult) {
                    LogUtil.error("[飞行数据概览同步] 保存数据失败");
                    return;
                }

                LogUtil.info("[飞行数据概览同步] 成功保存飞行数据概览，批次号: {}", batchNo);
            }
        } catch (Exception e) {
            LogUtil.error("[飞行数据概览同步] 同步飞行数据概览失败", e);
            throw e;
        }
    }

    /**
     * 将外部响应数据转换为FlightDataOverviewDO
     *
     * @param data 外部接口响应的数据
     * @return FlightDataOverviewDO对象
     */
    private FlightDataOverviewDO convertToFlightDataOverviewDO(FlightDataOverviewDataDTO data) {
        if (data == null) {
            return null;
        }

        FlightDataOverviewDO overviewDO = new FlightDataOverviewDO();
        overviewDO.setLastYearFlightDurationGrowth(data.getLastYearFlightDurationGrowth());
        overviewDO.setLastMonthDailyAverageFlightCount(data.getLastMonthDailyAverageFlightCount());
        overviewDO.setLastMonthFlightCount(data.getLastMonthFlightCount());
        overviewDO.setLastMonthFlightCountGrowth(data.getLastMonthFlightCountGrowth());
        overviewDO.setLastWeekFlightCount(data.getLastWeekFlightCount());
        overviewDO.setLastWeekFlightCountGrowth(data.getLastWeekFlightCountGrowth());
        overviewDO.setLastYearFlightCount(data.getLastYearFlightCount());
        overviewDO.setLastYearFlightCountGrowth(data.getLastYearFlightCountGrowth());
        overviewDO.setLastYearDistance(data.getLastYearDistance());
        overviewDO.setLastYearDistanceGrowth(data.getLastYearDistanceGrowth());
        overviewDO.setLastYearFlightDuration(data.getLastYearFlightDuration());
        overviewDO.setLastMonthDailyAverageFlightCountGrowth(data.getLastMonthDailyAverageFlightCountGrowth());

        return overviewDO;
    }
}