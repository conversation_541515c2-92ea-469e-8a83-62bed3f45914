package com.deepinnet.spatiotemporalplatform.skyflow.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.FlightPlanSyncLogRepository;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightPlanDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightPlanSyncLogDO;
import com.deepinnet.spatiotemporalplatform.skyflow.client.LocalDataFlightClient;
import com.deepinnet.spatiotemporalplatform.skyflow.enums.FlightPlanSyncStatusEnum;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.FlightPlanRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Profile;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <p>
 *    飞行计划成果拉取
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/15
 */

@Component
@RequiredArgsConstructor
@Profile({"local", "test", "alicloud", "demo", "sz-lg-gov"})
public class FlightPlanAchievementPullTask {

    private final static Integer LIMIT = 100;

    private final static String LIMIT_SQL = "limit " + LIMIT;

    private final LocalDataFlightClient flightClient;

    private final FlightPlanRepository flightPlanRepository;

    private final FlightPlanSyncLogRepository flightPlanSyncLogRepository;

    private final TransactionTemplate transactionTemplate;

    @PostConstruct
    public void runAtStartup() {
        CompletableFuture.runAsync(this::runOnSchedule);
    }

    // @Scheduled(cron = "0 0 0/1 * * ?")
    // @Scheduled(fixedDelay = 1000 * 60 * 60)
    public void pullFlightPlan() {

        runOnSchedule();

    }

    private void runOnSchedule() {
        LocalDateTime current = LocalDateTime.now().withMinute(0).withSecond(0).withNano(0);
        Long lastId = null;
        List<FlightPlanDO> batchPlan;

        while (true) {
            batchPlan = getUnSyncPlanList(lastId);

            if (CollUtil.isEmpty(batchPlan)) {
                break;
            }

            // 业务数据处理
            doBusiness(batchPlan, current);

            // 如果当前批次数量小于阈值直接退出循环
            if (batchPlan.size() < LIMIT) {
                break;
            }

            // 重置lastId
            lastId = batchPlan.get(batchPlan.size() - 1).getId();
        }
    }

    private void doBusiness(List<FlightPlanDO> batchPlan, LocalDateTime current) {

        // 判断当前时间是否已超过syncEnd时间
        batchPlan.forEach(plan -> {

            boolean syncDatePreCheck = syncDatePreCheck(current, plan);
            if (syncDatePreCheck) {
                return;
            }

            // 拉取鸢飞数据 TODO
            // if (true) return;

            // 写入日志表
            syncLogSave(current, plan);

        });
    }

    private void syncLogSave(LocalDateTime current, FlightPlanDO plan) {

        try {
            transactionTemplate.executeWithoutResult(e -> {
                flightPlanSyncLogRepository.save(buildSyncLog(current, plan));

                if (current.isEqual(plan.getSyncEnd())) {
                    flightPlanRepository.update(Wrappers.<FlightPlanDO>lambdaUpdate()
                            .eq(FlightPlanDO::getId, plan.getId())
                            .set(FlightPlanDO::getSyncStatus, FlightPlanSyncStatusEnum.SYNCED.getCode())
                            .set(FlightPlanDO::getGmtModified, LocalDateTime.now()));
                }
            });
        } catch (DuplicateKeyException duplicateKeyException) {
            LogUtil.warn("当前计划ID:{}, planNo:{}, 重复保存", plan.getId(), plan.getOutPlanNo());
        } catch (Exception e) {
            LogUtil.error("当前计划ID:{}, planNo:{}, 保存同步日志并更改状态失败!", plan.getId(), plan.getOutPlanNo(), e);
        }
    }

    private static FlightPlanSyncLogDO buildSyncLog(LocalDateTime current, FlightPlanDO plan) {
        FlightPlanSyncLogDO syncLog = new FlightPlanSyncLogDO();
        syncLog.setPlanNo(plan.getOutPlanNo());
        syncLog.setSyncDate(current.toLocalDate());
        syncLog.setSyncTime(current.toLocalTime());
        syncLog.setSyncDateTime(current);
        syncLog.setSyncData(null);
        syncLog.setTenantId(plan.getTenantId());
        return syncLog;
    }

    private boolean syncDatePreCheck(LocalDateTime current, FlightPlanDO plan) {
        try {
            List<FlightPlanSyncLogDO> syncLogList = flightPlanSyncLogRepository.list(Wrappers.<FlightPlanSyncLogDO>lambdaQuery()
                    .eq(FlightPlanSyncLogDO::getPlanNo, plan.getOutPlanNo())
                    .orderByDesc(FlightPlanSyncLogDO::getSyncDateTime));

            // 当前超时, 更新同步状态, 不再执行
            if (current.isAfter(plan.getSyncEnd())) {
                flightPlanRepository.update(Wrappers.<FlightPlanDO>lambdaUpdate()
                        .eq(FlightPlanDO::getId, plan.getId())
                        .set(FlightPlanDO::getSyncStatus, CollUtil.isEmpty(syncLogList)
                                ? FlightPlanSyncStatusEnum.TERMINATED.getCode() : FlightPlanSyncStatusEnum.SYNCED.getCode()));

                return true;
            }

            return false;
        } catch (Exception e) {
            LogUtil.error("当前计划ID:{}, planNo:{}, 更新同步状态失败!", plan.getPlanId(), plan.getOutPlanNo(), e);
            return true;
        }
    }

    /**
     * 每批次查询100条
     * @param startId 开始的ID
     * @return 未同步成果的计划
     */
    private List<FlightPlanDO> getUnSyncPlanList(Long startId) {
        return flightPlanRepository.list(Wrappers.<FlightPlanDO>lambdaQuery()
                .eq(FlightPlanDO::getSyncStatus, FlightPlanSyncStatusEnum.UN_SYNC.getCode())
                .ge(ObjectUtil.isNotNull(startId), FlightPlanDO::getId, startId)
                .orderByAsc(FlightPlanDO::getId)
                .last(LIMIT_SQL));
    }

}
