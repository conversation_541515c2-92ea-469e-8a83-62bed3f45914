package com.deepinnet.spatiotemporalplatform.skyflow.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.localdata.integration.model.output.FlightPlanAchievementResponseVO;
import com.deepinnet.skyflow.operationcenter.vo.FlightDemandVO;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.FlightPlanAchievementRepository;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.FlightPlanSyncLogRepository;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightPlanAchievementDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightPlanDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightPlanSyncLogDO;
import com.deepinnet.spatiotemporalplatform.skyflow.client.FlightDemandRemoteClient;
import com.deepinnet.spatiotemporalplatform.skyflow.client.LocalDataFlightClient;
import com.deepinnet.spatiotemporalplatform.enums.FlightAchievementTypeEnum;
import com.deepinnet.spatiotemporalplatform.skyflow.enums.FlightPlanSyncStatusEnum;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.FlightPlanRepository;
import com.deepinnet.spatiotemporalplatform.skyflow.util.CoordUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.springframework.context.annotation.Profile;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <p>
 *    飞行计划成果拉取
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/15
 */

@Component
@RequiredArgsConstructor
@Profile({"local", "test", "alicloud", "demo", "sz-lg-gov"})
public class FlightPlanAchievementPullTask {

    private final static Integer LIMIT = 100;

    private final static String LIMIT_SQL = "limit " + LIMIT;

    private final LocalDataFlightClient flightClient;

    private final FlightPlanRepository flightPlanRepository;

    private final FlightPlanSyncLogRepository flightPlanSyncLogRepository;

    private final TransactionTemplate transactionTemplate;

    private final FlightPlanAchievementRepository achievementRepository;

    private final FlightDemandRemoteClient flightDemandRemoteClient;

    @PostConstruct
    public void runAtStartup() {
        CompletableFuture.runAsync(this::runOnSchedule);
    }

    @Scheduled(cron = "0 0 0/1 * * ?")
    //@Scheduled(fixedDelay = 1000 * 60 * 60)
    public void pullFlightPlan() {

        runOnSchedule();

    }

    private void runOnSchedule() {
        LocalDateTime current = LocalDateTime.now().withMinute(0).withSecond(0).withNano(0);
        Long lastId = null;
        List<FlightPlanDO> batchPlan;

        while (true) {
            batchPlan = getUnSyncPlanList(lastId);

            if (CollUtil.isEmpty(batchPlan)) {
                break;
            }

            // 业务数据处理
            doBusiness(batchPlan, current);

            // 如果当前批次数量小于阈值直接退出循环
            if (batchPlan.size() < LIMIT) {
                break;
            }

            // 重置lastId
            lastId = batchPlan.get(batchPlan.size() - 1).getId();
        }
    }

    private void doBusiness(List<FlightPlanDO> batchPlan, LocalDateTime current) {

        List<String> demandNos = batchPlan.stream().map(FlightPlanDO::getBizNo).filter(StrUtil::isNotBlank).collect(Collectors.toList());

        List<FlightDemandVO> demandList = Lists.newArrayList();

        if (CollUtil.isNotEmpty(demandNos)) {
            demandList = flightDemandRemoteClient.getFlightDemandByDemandNoList(demandNos);
        }

        List<String> planNos = batchPlan.stream().map(FlightPlanDO::getOutPlanNo).filter(StrUtil::isNotBlank).collect(Collectors.toList());

        List<FlightPlanAchievementDO> existAchievements = achievementRepository
                .list(Wrappers.<FlightPlanAchievementDO>lambdaQuery()
                        .in(FlightPlanAchievementDO::getPlanNo, planNos));

        Set<String> existIds = CollUtil.isEmpty(existAchievements)
                ? Collections.emptySet()
                : existAchievements.stream()
                .map(FlightPlanAchievementDO::getAchievementId)
                .collect(Collectors.toSet());

        // 判断当前时间是否已超过syncEnd时间
        batchPlan.forEach(plan -> {

            boolean syncDatePreCheck = syncDatePreCheck(current, plan);
            if (syncDatePreCheck) {
                return;
            }

            List<FlightPlanAchievementResponseVO.Achievement> achievements = queryAchievement(plan);

            if (CollUtil.isEmpty(achievements)) {
                return;
            }

            List<FlightPlanAchievementDO> achievementList = buildUnExistAchievements(plan, achievements, existIds);

            // 保存成果
            saveAchievements(achievementList);

            // 写入日志表
            syncLogSave(current, plan);

        });
    }

    private void saveAchievements(List<FlightPlanAchievementDO> achievementList) {
        achievementList.forEach(achievement -> {
            try {
                achievementRepository.save(achievement);
            } catch (DuplicateKeyException duplicateKeyException) {
                LogUtil.warn("当前成果已存在, achievementId:{}", achievement.getAchievementId());
            } catch (Exception e) {
                LogUtil.error("当前成果保存失败, achievementId:{}", achievement.getAchievementId(), e);
            }
        });
    }

    private static List<FlightPlanAchievementDO> buildUnExistAchievements(FlightPlanDO plan, List<FlightPlanAchievementResponseVO.Achievement> achievements, Set<String> existIds) {
        return achievements.stream()
                .filter(achievement -> !existIds.contains(achievement.getId()))
                .map(achievement -> buildAchievement(plan, achievement)).collect(Collectors.toList());
    }

    private static FlightPlanAchievementDO buildAchievement(FlightPlanDO plan, FlightPlanAchievementResponseVO.Achievement achievement) {
        FlightAchievementTypeEnum type = FlightAchievementTypeEnum.getEnumByYfCode(achievement.getOutcomeType());

        if (ObjectUtil.isNull(type)) {
            type = FlightAchievementTypeEnum.OTHER;
        }

        FlightPlanAchievementDO flightPlanAchievementDO = new FlightPlanAchievementDO();
        flightPlanAchievementDO.setPlanNo(plan.getOutPlanNo());
        flightPlanAchievementDO.setAchievementId(achievement.getId());
        flightPlanAchievementDO.setAchievementName(achievement.getName());
        flightPlanAchievementDO.setAchievementType(type.getDeepCode());
        flightPlanAchievementDO.setAchievementTime(achievement.getOccurredTime());

        // 类型为video时候取downloadUrl, 其余所有都取url
        flightPlanAchievementDO.setAchievementUrl(
                StrUtil.equals(FlightAchievementTypeEnum.VIDEO.getYfCode(), achievement.getOutcomeType())
                        ? achievement.getDownloadUrl() : achievement.getUrl());

        flightPlanAchievementDO.setAchievementAddress(achievement.getOccurredSite());

        flightPlanAchievementDO.setAchievementLocation(StrUtil.isEmpty(achievement.getCenter()) ? null
                : CoordUtil.wgs84StringToGcj02Point(achievement.getCenter()));

        flightPlanAchievementDO.setAchievementLocationStr(achievement.getCenter());
        flightPlanAchievementDO.setTenantId(plan.getTenantId());

        return flightPlanAchievementDO;
    }

    private List<FlightPlanAchievementResponseVO.Achievement> queryAchievement(FlightPlanDO plan) {
        List<FlightPlanAchievementResponseVO.Achievement> achievements;
        try{
            achievements = flightClient.queryAchievementsByPlanNo(plan.getOutPlanNo());

        } catch (Exception e) {
            LogUtil.error("achievement query error, queryParam:{}", plan.getOutPlanNo(), e);
            return null;
        }
        return achievements;
    }

    private void syncLogSave(LocalDateTime current, FlightPlanDO plan) {

        try {
            transactionTemplate.executeWithoutResult(e -> {
                flightPlanSyncLogRepository.save(buildSyncLog(current, plan));

                if (current.isEqual(plan.getSyncEnd())) {
                    flightPlanRepository.update(Wrappers.<FlightPlanDO>lambdaUpdate()
                            .eq(FlightPlanDO::getId, plan.getId())
                            .set(FlightPlanDO::getSyncStatus, FlightPlanSyncStatusEnum.SYNCED.getCode())
                            .set(FlightPlanDO::getGmtModified, LocalDateTime.now()));
                }
            });
        } catch (DuplicateKeyException duplicateKeyException) {
            LogUtil.warn("当前计划ID:{}, planNo:{}, 重复保存", plan.getId(), plan.getOutPlanNo());
        } catch (Exception e) {
            LogUtil.error("当前计划ID:{}, planNo:{}, 保存同步日志并更改状态失败!", plan.getId(), plan.getOutPlanNo(), e);
        }
    }

    private static FlightPlanSyncLogDO buildSyncLog(LocalDateTime current, FlightPlanDO plan) {
        FlightPlanSyncLogDO syncLog = new FlightPlanSyncLogDO();
        syncLog.setPlanNo(plan.getOutPlanNo());
        syncLog.setSyncDate(current.toLocalDate());
        syncLog.setSyncTime(current.toLocalTime());
        syncLog.setSyncDateTime(current);
        syncLog.setSyncData(null);
        syncLog.setTenantId(plan.getTenantId());
        return syncLog;
    }

    private boolean syncDatePreCheck(LocalDateTime current, FlightPlanDO plan) {
        try {
            List<FlightPlanSyncLogDO> syncLogList = flightPlanSyncLogRepository.list(Wrappers.<FlightPlanSyncLogDO>lambdaQuery()
                    .eq(FlightPlanSyncLogDO::getPlanNo, plan.getOutPlanNo())
                    .orderByDesc(FlightPlanSyncLogDO::getSyncDateTime));

            // 当前超时, 更新同步状态, 不再执行
            if (current.isAfter(plan.getSyncEnd())) {
                flightPlanRepository.update(Wrappers.<FlightPlanDO>lambdaUpdate()
                        .eq(FlightPlanDO::getId, plan.getId())
                        .set(FlightPlanDO::getSyncStatus, CollUtil.isEmpty(syncLogList)
                                ? FlightPlanSyncStatusEnum.TERMINATED.getCode() : FlightPlanSyncStatusEnum.SYNCED.getCode()));

                return true;
            }

            return false;
        } catch (Exception e) {
            LogUtil.error("当前计划ID:{}, planNo:{}, 更新同步状态失败!", plan.getPlanId(), plan.getOutPlanNo(), e);
            return true;
        }
    }

    /**
     * 每批次查询100条
     * @param startId 开始的ID
     * @return 未同步成果的计划
     */
    private List<FlightPlanDO> getUnSyncPlanList(Long startId) {
        return flightPlanRepository.list(Wrappers.<FlightPlanDO>lambdaQuery()
                .eq(FlightPlanDO::getSyncStatus, FlightPlanSyncStatusEnum.UN_SYNC.getCode())
                .ge(ObjectUtil.isNotNull(startId), FlightPlanDO::getId, startId)
                .orderByAsc(FlightPlanDO::getId)
                .last(LIMIT_SQL));
    }

}
