package com.deepinnet.spatiotemporalplatform.skyflow.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.WeatherDeviceDO;
import com.deepinnet.spatiotemporalplatform.skyflow.convert.WeatherDeviceConvert;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.WeatherDeviceRepository;
import com.deepinnet.spatiotemporalplatform.skyflow.service.WeatherDeviceService;
import com.deepinnet.spatiotemporalplatform.vo.WeatherDeviceVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * 天气设备Service实现类
 *
 * <AUTHOR> wong
 * @create 2025/01/14
 */
@Service
@RequiredArgsConstructor
public class WeatherDeviceServiceImpl implements WeatherDeviceService {

    private final WeatherDeviceRepository weatherDeviceRepository;

    private final WeatherDeviceConvert weatherDeviceConvert;

    @Override
    public List<WeatherDeviceVO> listAllWeatherDevices() {
        LogUtil.info("[天气设备查询] 开始查询所有天气设备");

        try {
            // 查询所有天气设备，按创建时间倒序
            List<WeatherDeviceDO> weatherDeviceDOList = weatherDeviceRepository.list(
                    Wrappers.lambdaQuery(WeatherDeviceDO.class)
                            .orderByDesc(WeatherDeviceDO::getGmtCreated)
            );

            if (CollectionUtils.isEmpty(weatherDeviceDOList)) {
                LogUtil.info("[天气设备查询] 暂无天气设备数据");
                return Collections.emptyList();
            }

            // 转换为VO
            List<WeatherDeviceVO> weatherDeviceVOList = weatherDeviceConvert.toWeatherDeviceVOList(weatherDeviceDOList);

            LogUtil.info("[天气设备查询] 查询成功，共查询到 {} 条天气设备数据", weatherDeviceVOList.size());
            return weatherDeviceVOList;

        } catch (Exception e) {
            LogUtil.error("[天气设备查询] 查询天气设备失败", e);
            throw e;
        }
    }

    @Override
    public Long countWeatherDevices() {
        LogUtil.info("[天气设备统计] 开始统计当前租户的天气设备总数");

        try {
            // 统计当前租户的天气设备总数
            Long count = weatherDeviceRepository.count(
                    Wrappers.lambdaQuery(WeatherDeviceDO.class)
            );

            LogUtil.info("[天气设备统计] 统计完成，当前租户天气设备总数: {}", count);
            return count;

        } catch (Exception e) {
            LogUtil.error("[天气设备统计] 统计天气设备总数失败", e);
            throw e;
        }
    }
} 