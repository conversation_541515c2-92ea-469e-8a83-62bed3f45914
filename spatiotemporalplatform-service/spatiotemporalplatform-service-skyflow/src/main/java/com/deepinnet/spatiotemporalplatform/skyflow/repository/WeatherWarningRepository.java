package com.deepinnet.spatiotemporalplatform.skyflow.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.*;

import java.util.List;

/**
 * 天气预警Repository接口
 *
 * <AUTHOR>
 */
public interface WeatherWarningRepository extends IService<WeatherWarningDO> {

    List<IntelligenceDO> getIntelligenceList(Long startTime, Long endTime);

    List<IntelligenceDO> getHistoryIntelligenceList(Long startTime, Long endTime);
} 