package com.deepinnet.spatiotemporalplatform.skyflow.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.GdPoiDO;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
public interface GdPoiRepository extends IService<GdPoiDO> {

    /**
     * 根据区域范围与类型获取POI
     * @param region 区域范围
     * @param types 类型
     * @return POI列表
     */
    List<GdPoiDO> getPoiByRegionAndType(String region, List<String> types);
}
