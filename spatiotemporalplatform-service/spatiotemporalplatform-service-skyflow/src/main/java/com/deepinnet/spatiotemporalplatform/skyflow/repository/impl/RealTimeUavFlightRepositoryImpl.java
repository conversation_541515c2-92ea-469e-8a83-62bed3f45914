package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.RealTimeUavFlightDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.RealTimeUavFlightMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.RealTimeUavFlightRepository;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 实时无人机飞行数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
@Service
public class RealTimeUavFlightRepositoryImpl extends ServiceImpl<RealTimeUavFlightMapper, RealTimeUavFlightDO> implements RealTimeUavFlightRepository {

    @Override
    public int countDistinctFlightsFast(List<String> planIds, LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.countDistinctFlightsFast(planIds, startTime, endTime);
    }

    @Override
    public List<RealTimeUavFlightDO> queryLatestFlights(List<String> planIds, List<String> uavIds, List<String> warningPlanIds) {
        return baseMapper.queryLatestFlights(planIds, uavIds, warningPlanIds);
    }

    @Override
    public List<RealTimeUavFlightDO> queryPlanFlights(List<String> planIds, Integer pageNo, Integer pageSize) {
        return baseMapper.queryPlanFlights(planIds, pageNo, pageSize);
    }

    @Override
    public RealTimeUavFlightDO queryLatestFlightsByReportTime(LocalDateTime reportTime, String planId) {
        return baseMapper.queryLatestFlightsByReportTime(reportTime, planId);
    }

    @Override
    public List<RealTimeUavFlightDO> queryFlightPlanIdLastFlightId(List<String> planIds) {
        return baseMapper.queryFlightPlanIdLastFlightId(planIds);
    }

    @Override
    public List<RealTimeUavFlightDO> queryFlightPlanLastStatus(List<String> planIds) {
        return baseMapper.queryFlightPlanLastStatus(planIds);
    }
}
