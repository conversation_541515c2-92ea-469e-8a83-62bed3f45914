package com.deepinnet.spatiotemporalplatform.skyflow.task;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightPlanDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.RealTimeUavFlightDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.TaskFlightMilesDO;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.FlightPlanRepository;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.RealTimeUavFlightRepository;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.TaskFlightMilesRepository;
import com.deepinnet.spatiotemporalplatform.skyflow.util.beidou.HistoryAverageCalUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Profile;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *   累计飞行里程统计任务
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/5
 */
@Component
@Profile({"local", "local-inner", "test","alicloud", "demo"})
@RequiredArgsConstructor
public class FlightMilesTask {

    private final TaskFlightMilesRepository taskFlightMilesRepository;

    private final RealTimeUavFlightRepository realTimeUavFlightRepository;

    private final FlightPlanRepository flightPlanRepository;

    @Scheduled(cron = "0 0/2 * * * *")
    public void statistics() {
        LogUtil.info("[飞行概览]-[累计飞行里程]任务开始执行");

        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalDate localDate = currentDateTime.toLocalDate();

        List<String> tenantIdList = Optional.ofNullable(
                        flightPlanRepository.list(
                                Wrappers.<FlightPlanDO>lambdaQuery()
                                        .select(FlightPlanDO::getTenantId)
                                        .groupBy(FlightPlanDO::getTenantId)
                        ))
                .orElseGet(Collections::emptyList)
                .stream()
                .filter(Objects::nonNull)
                .map(FlightPlanDO::getTenantId)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(tenantIdList)) {
            LogUtil.warn("[飞行概览]-[累计飞行里程]任务暂无租户数据");
            return;
        }

        tenantIdList.forEach(tenantId -> {

            // 计算当前时间窗口
            HistoryAverageCalUtil.TimeWindow currentTimeWindow = HistoryAverageCalUtil.calculateTimeWindow(currentDateTime);

            // 获取当前飞行中的无人机里程
            BigDecimal currentFlightCount = countUniqueFlightsInTimeWindow(
                    currentTimeWindow.getStartTime(),
                    currentTimeWindow.getEndTime(),
                    tenantId
            );


            // 获取当前时间的小时和分钟，格式化为HH:mm
            String currentTimeStr = currentDateTime.format(DateTimeFormatter.ofPattern("HH:mm"));

            // 计算历史同期的数据平均值
            String historyAvgCount = calculateHistoricalAverage(currentDateTime, tenantId);

            // 保存统计结果到TaskFlightMilesDO
            TaskFlightMilesDO taskFlightMilesDO = new TaskFlightMilesDO();
            taskFlightMilesDO.setStatisticalDate(localDate);
            taskFlightMilesDO.setStatisticalTime(currentTimeStr);
            taskFlightMilesDO.setStatisticalCount(currentFlightCount.toString());
            taskFlightMilesDO.setStatisticalHistoryAvgCount(historyAvgCount);
            taskFlightMilesDO.setGmtCreated(currentDateTime);
            taskFlightMilesDO.setGmtModified(currentDateTime);
            taskFlightMilesDO.setTenantId(tenantId);

            // 保存统计结果
            try {
                taskFlightMilesRepository.save(taskFlightMilesDO);
            } catch (DuplicateKeyException duplicateKeyException) {
                LogUtil.warn("[飞行概览]-[累计飞行里程], 租户:{}, 统计数据:{}, 重复保存!", tenantId, JSONObject.toJSONString(taskFlightMilesDO));
            } catch (Exception e) {
                LogUtil.error("[飞行概览]-[累计飞行里程], 租户:{}, 统计数据:{}, 保存失败!", tenantId, JSONObject.toJSONString(taskFlightMilesDO), e);
            }
        });

    }
    
    /**
     * 计算历史同期的平均无人机飞行数量
     * 历史同期定义为：前四周同一星期几的同一时间段
     * 
     * @param currentDateTime 当前时间
     * @param tenantId 租户ID
     * @return 历史同期平均值
     */
    private String calculateHistoricalAverage(LocalDateTime currentDateTime, String tenantId) {

        // 获取当前星期几
        DayOfWeek currentDayOfWeek = currentDateTime.getDayOfWeek();
        
        // 获取当前时间（小时和分钟）
        LocalTime currentTime = currentDateTime.toLocalTime().withSecond(0).withNano(0);
        
        // 历史同期开始时间（往前推2分钟）
        LocalTime historyStartTime = currentTime.minusMinutes(2);
        
        // 历史数据列表
        List<BigDecimal> historicalCounts = new ArrayList<>();
        
        // 查询前四周同一星期几同一时间段的数据
        for (int weekOffset = 1; weekOffset <= 4; weekOffset++) {
            // 计算对应的历史日期（前 weekOffset 周的同一星期几）
            LocalDate historicalDate = getHistoricalDateForSameDayOfWeek(currentDateTime, weekOffset, currentDayOfWeek);

            // 获取历史时间段的里程
            BigDecimal historicalCount = calHistory(historicalDate, currentTime, tenantId);

            if (ObjectUtil.isNull(historicalCount)) {
                continue;
            }

            historicalCounts.add(historicalCount);
        }
        
        // 计算平均值，如果没有历史数据，返回0
        return calculateAverage(historicalCounts);
    }

    private BigDecimal calHistory(LocalDate historicalDate, LocalTime currentTime, String tenantId) {
        TaskFlightMilesDO milesDO = taskFlightMilesRepository.getOne(Wrappers.<TaskFlightMilesDO>lambdaQuery()
                .eq(TaskFlightMilesDO::getStatisticalDate, historicalDate)
                .eq(TaskFlightMilesDO::getStatisticalTime, currentTime.toString())
                .eq(TaskFlightMilesDO::getTenantId, tenantId));
        return ObjectUtil.isNull(milesDO) || StrUtil.isBlank(milesDO.getStatisticalCount()) ? null : new BigDecimal(milesDO.getStatisticalCount());
    }

    /**
     * 计算指定时间窗口内的无人机飞行里程（去重）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 无人机数量
     */
    private BigDecimal countUniqueFlightsInTimeWindow(LocalDateTime startTime, LocalDateTime endTime, String tenantId) {
        // 查询时间范围内的最新的UAV数据
        LambdaQueryWrapper<RealTimeUavFlightDO> queryWrapper = Wrappers.lambdaQuery(RealTimeUavFlightDO.class)
                .ge(RealTimeUavFlightDO::getReportTime, startTime)
                .lt(RealTimeUavFlightDO::getReportTime, endTime)
                .eq(RealTimeUavFlightDO::getTenantId, tenantId)
                .orderByDesc(RealTimeUavFlightDO::getReportTime);

        List<RealTimeUavFlightDO> flights = realTimeUavFlightRepository.list(queryWrapper);
        
        if (CollectionUtils.isEmpty(flights)) {
            return BigDecimal.ZERO;
        }
        
        // 使用Java Stream分组，获取每个plan_id的最新记录
        Map<String, RealTimeUavFlightDO> latestFlightsByPlanId = flights.stream()
                .collect(Collectors.toMap(
                        RealTimeUavFlightDO::getPlanId,
                        flight -> flight,
                        (existing, replacement) -> existing.getReportTime().isAfter(replacement.getReportTime()) ? existing : replacement
                ));

        // 使用 BigDecimal 累加每个最新记录的飞行里程
        return latestFlightsByPlanId.values().stream()
                .map(flight -> new BigDecimal(flight.getFlightMiles()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    /**
     * 计算前N周的同一星期几的日期
     *
     * @param currentDateTime 当前日期时间
     * @param weekOffset 周偏移量
     * @param targetDayOfWeek 目标星期几
     * @return 计算得到的历史日期
     */
    private LocalDate getHistoricalDateForSameDayOfWeek(LocalDateTime currentDateTime, int weekOffset, DayOfWeek targetDayOfWeek) {
        // 计算对应的历史日期（前 weekOffset 周的同一星期几）
        LocalDate historicalDate = currentDateTime.toLocalDate().minusWeeks(weekOffset);
        // 调整到同一星期几
        while (!historicalDate.getDayOfWeek().equals(targetDayOfWeek)) {
            historicalDate = historicalDate.plusDays(1);
        }
        return historicalDate;
    }
    
    /**
     * 计算整数列表的平均值
     *
     * @param numbers 整数列表
     * @return 平均值（四舍五入为整数）
     */
    private String calculateAverage(List<BigDecimal> numbers) {
        if (CollectionUtils.isEmpty(numbers)) {
            return "0";
        }

        BigDecimal total = numbers
                .stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .divide(new BigDecimal(String.valueOf(numbers.size())), 4, RoundingMode.DOWN)
                .stripTrailingZeros();

        return total.toString();
    }
}
