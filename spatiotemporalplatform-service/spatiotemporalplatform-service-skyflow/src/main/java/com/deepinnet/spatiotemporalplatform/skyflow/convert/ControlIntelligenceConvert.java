package com.deepinnet.spatiotemporalplatform.skyflow.convert;

import com.deepinnet.spatiotemporalplatform.dal.dataobject.ControlIntelligenceDO;
import com.deepinnet.spatiotemporalplatform.dto.ControlIntelligenceDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 管控情报DTO与DO转换接口
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface ControlIntelligenceConvert {

    ControlIntelligenceConvert INSTANCE = Mappers.getMapper(ControlIntelligenceConvert.class);

    /**
     * DO转DTO
     *
     * @param controlIntelligenceDO DO对象
     * @return DTO对象
     */
    ControlIntelligenceDTO toControlIntelligenceDTO(ControlIntelligenceDO controlIntelligenceDO);

    /**
     * DO列表转DTO列表
     *
     * @param controlIntelligenceDOList DO对象列表
     * @return DTO对象列表
     */
    List<ControlIntelligenceDTO> toControlIntelligenceDTOList(List<ControlIntelligenceDO> controlIntelligenceDOList);

    /**
     * DTO转DO
     *
     * @param controlIntelligenceDTO DTO对象
     * @return DO对象
     */
    ControlIntelligenceDO toControlIntelligenceDO(ControlIntelligenceDTO controlIntelligenceDTO);

    /**
     * DTO列表转DO列表
     *
     * @param controlIntelligenceDTOList DTO对象列表
     * @return DO对象列表
     */
    List<ControlIntelligenceDO> toControlIntelligenceDOList(List<ControlIntelligenceDTO> controlIntelligenceDTOList);
} 