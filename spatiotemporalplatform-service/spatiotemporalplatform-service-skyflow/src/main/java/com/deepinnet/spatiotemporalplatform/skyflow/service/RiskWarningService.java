package com.deepinnet.spatiotemporalplatform.skyflow.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.RiskWarningDO;
import com.deepinnet.spatiotemporalplatform.dal.dto.*;
import com.deepinnet.spatiotemporalplatform.dto.*;
import com.deepinnet.spatiotemporalplatform.vo.RealTimeUavFlightVO;

import java.util.List;

/**
 * 风险预警Repository接口
 *
 * <AUTHOR>
 */
public interface RiskWarningService {

    /**
     * 保存风险预警信息
     *
     * @param riskWarningDO 风险预警信息
     * @return 是否保存成功
     */
    boolean save(RiskWarningDO riskWarningDO);

    /**
     * 批量保存风险预警信息
     *
     * @param riskWarningDOList 风险预警信息列表
     * @return 是否保存成功
     */
    boolean saveBatch(List<RiskWarningDO> riskWarningDOList);

    /**
     * 根据ID查询风险预警信息
     *
     * @param id 风险预警ID
     * @return 风险预警信息
     */
    RiskWarningDTO getById(Long id);

    /**
     * 根据条件查询风险预警列表
     *
     * @param queryWrapper 查询条件
     * @return 风险预警列表
     */
    List<RiskWarningDTO> list(LambdaQueryWrapper<RiskWarningDO> queryWrapper);

    /**
     * 根据预警类型和时间范围查询风险预警
     *
     * @return 风险预警列表
     */
    List<RiskWarningDTO> listByTypeAndTimeRange(RiskWarningQueryDTO queryDTO);

    /**
     * 获取子类型数量
     *
     * @return 风险预警列表
     */
    List<SubTypeCountDTO> getSubTypeCountList(RiskWarningQueryDTO queryDTO);

    List<StatusCountDTO> getStatusCountList(RiskWarningQueryDTO queryDTO);

    /**
     * 分页查询风险预警列表
     *
     * @param queryDTO
     * @return
     */
    CommonPage<RiskWarningDTO> pageQueryRiskWarning(RiskWarningQueryDTO queryDTO);


    /**
     * 获取风险预警列表
     *
     * @param queryDTO
     * @return
     */
    List<RiskWarningDTO> getRiskWarningList(RiskWarningQueryDTO queryDTO);

    /**
     * 获取危险预警的飞行轨迹
     *
     * @param queryDTO
     * @return
     */
    List<RiskWarningPathDTO> getRiskWarningPath(RiskWarningPathQueryDTO queryDTO);

    /**
     * 获取危险预警的飞行轨迹
     *
     * @param queryDTO
     * @return
     */
    List<RiskWarningPathDTO> getHistoryRiskWarningPath(RiskWarningPathQueryDTO queryDTO);

    /**
     * 查询飞行器最新的位置
     *
     * @return
     */
    List<RealTimeUavFlightVO> getNewestFlightPosition(RiskWarningPositionQueryDTO queryDTO);

    /**
     * 根据预警状态查询风险预警
     *
     * @param warningStatus 预警状态
     * @return 风险预警列表
     */
    List<RiskWarningDTO> listByWarningStatus(String warningStatus);

    /**
     * 更新风险预警信息
     *
     * @param riskWarningDO 风险预警信息
     * @return 是否更新成功
     */
    boolean updateById(RiskWarningDO riskWarningDO);

    /**
     * 根据ID删除风险预警信息
     *
     * @param id 风险预警ID
     * @return 是否删除成功
     */
    boolean removeById(Long id);

    /**
     * 批量删除风险预警信息
     *
     * @param ids ID列表
     * @return 是否删除成功
     */
    boolean removeByIds(List<Long> ids);
} 