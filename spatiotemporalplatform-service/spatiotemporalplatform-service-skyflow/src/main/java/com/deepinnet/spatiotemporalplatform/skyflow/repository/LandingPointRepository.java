package com.deepinnet.spatiotemporalplatform.skyflow.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.LandingPointDO;

import java.util.List;

/**
 * 起降点数据Repository
 *
 * <AUTHOR>
 */
public interface LandingPointRepository extends IService<LandingPointDO> {

    /**
     * 根据外部ID查询起降点
     *
     * @param externalId 外部ID
     * @return 起降点数据
     */
    LandingPointDO getByExternalId(String externalId);

    /**
     * 根据租户ID删除起降点数据
     *
     * @param tenantId 租户ID
     * @return 删除的记录数
     */
    void deleteByTenantId(String tenantId);

    /**
     * 根据条件查询起降点列表
     *
     * @param code     编号
     * @param type     类型
     * @param name     名称
     * @param address  详细地址
     * @param teamId   团队ID
     * @param tenantId 租户ID
     * @param departId 部门ID
     * @param owner    所有人
     * @param operator 运营人
     * @param busiType 业务类型
     * @param airType  起降场类型
     * @param street   所属街道
     * @return 起降点列表
     */
    List<LandingPointDO> listByCondition(String code, String type, String name, String address,
                                         String teamId, String tenantId, String departId, String owner,
                                         String operator, String busiType, String airType, String street);
}