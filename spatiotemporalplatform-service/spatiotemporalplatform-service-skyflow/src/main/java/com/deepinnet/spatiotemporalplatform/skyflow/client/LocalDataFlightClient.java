package com.deepinnet.spatiotemporalplatform.skyflow.client;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.localdata.integration.FlightPlanClient;
import com.deepinnet.localdata.integration.model.input.FlightAchievementQueryDTO;
import com.deepinnet.localdata.integration.model.input.FlightLiveQueryDTO;
import com.deepinnet.localdata.integration.model.input.FlightPlanDetailQueryDTO;
import com.deepinnet.localdata.integration.model.output.FlightPlanAchievementResponseVO;
import com.deepinnet.localdata.integration.model.output.FlightPlanDetailResponseVO;
import com.deepinnet.localdata.integration.model.output.FlightResponseVO;
import com.deepinnet.spatiotemporalplatform.skyflow.error.BizErrorCode;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/15
 */
@Component
public class LocalDataFlightClient {

    @Resource
    private FlightPlanClient flightPlanClient;

    public FlightResponseVO queryTodayFlightPlan() {
        return flightPlanClient.queryTodayFlightPlan();
    }

    public FlightPlanDetailResponseVO queryFlightPlanDetail(String planId) {
        FlightPlanDetailQueryDTO flightPlanDetailQueryDTO = new FlightPlanDetailQueryDTO();
        flightPlanDetailQueryDTO.setPlanId(planId);

        try {
            return flightPlanClient.queryFlightPlanDetail(flightPlanDetailQueryDTO);
        } catch (Exception e) {
            LogUtil.error("queryFlightPlanDetail error, query params:{}", JSONObject.toJSONString(flightPlanDetailQueryDTO), e);
            throw new BizException(BizErrorCode.YUAN_FEI_PLAN_QUERY_ERROR.getCode(), BizErrorCode.YUAN_FEI_PLAN_QUERY_ERROR.getDesc());
        }
    }

    public String queryLiveUrl(String sn, String httpProtocol) {
        FlightLiveQueryDTO flightLiveQueryDTO = new FlightLiveQueryDTO();
        flightLiveQueryDTO.setSn(sn);
        flightLiveQueryDTO.setHttpProtocol(httpProtocol);
        try {
            return flightPlanClient.queryLiveVideoUrl(flightLiveQueryDTO);
        } catch (Exception e) {
            LogUtil.error("queryLiveUrl error, query params:{}", JSONObject.toJSONString(flightLiveQueryDTO), e);
            throw new BizException(BizErrorCode.UAV_LIVE_STREAM_QUERY_ERROR.getCode(), BizErrorCode.UAV_LIVE_STREAM_QUERY_ERROR.getDesc());
        }
    }

    public List<FlightPlanAchievementResponseVO.Achievement> queryAchievementsByPlanNo(String planNo) {
        FlightAchievementQueryDTO queryDTO = new FlightAchievementQueryDTO();
        queryDTO.setPlanNo(planNo);

        try {

            FlightPlanAchievementResponseVO achievementResult = flightPlanClient.queryAchievementsByPlanNo(queryDTO);

            if (ObjectUtil.isNull(achievementResult) || CollUtil.isEmpty(achievementResult.getAchievements())) {
                return Lists.newArrayList();
            }

            return achievementResult.getAchievements();
        }catch (Exception e) {
            LogUtil.error("queryAchievementsByPlanNo error, query params:{}", JSONObject.toJSONString(queryDTO), e);
            throw new BizException(BizErrorCode.FLIGHT_PLAN_ACHIEVEMENT_QUERY_ERROR.getCode()
                    , BizErrorCode.FLIGHT_PLAN_ACHIEVEMENT_QUERY_ERROR.getDesc());
        }
    }

}
