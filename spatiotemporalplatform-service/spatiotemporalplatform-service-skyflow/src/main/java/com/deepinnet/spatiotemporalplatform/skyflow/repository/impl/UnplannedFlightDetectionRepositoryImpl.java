package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.UnplannedFlightDetectionDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.UnplannedFlightDetectionMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.UnplannedFlightDetectionRepository;
import org.springframework.stereotype.Service;

/**
 * 未在飞行计划内的无人机检测 仓储实现
 */
@Service
public class UnplannedFlightDetectionRepositoryImpl
        extends ServiceImpl<UnplannedFlightDetectionMapper, UnplannedFlightDetectionDO>
        implements UnplannedFlightDetectionRepository {

    @Override
    public Long getUnplannedFlightCount(Long startTime, Long endTime) {
        return super.count(Wrappers.lambdaQuery(UnplannedFlightDetectionDO.class)
                .ge(UnplannedFlightDetectionDO::getReportedTime, startTime)
                .le(UnplannedFlightDetectionDO::getReportedTime, endTime));
    }
}