package com.deepinnet.spatiotemporalplatform.skyflow.service;

import com.deepinnet.spatiotemporalplatform.dto.FlightRestrictionsAOI;
import com.deepinnet.spatiotemporalplatform.dto.GroundFeatures;
import com.deepinnet.spatiotemporalplatform.dto.GroundFeaturesQueryDTO;

import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2025/1/13 10:04
 * @Description aoi接口
 */
public interface AreaOfInterestService {

    /**
     * 查询区域 AOI
     *
     * @param queryDTO
     * @return
     */
    List<FlightRestrictionsAOI> getAoi(GroundFeaturesQueryDTO queryDTO);

    /**
     * 查询地物信息
     *
     * @param queryDTO
     * @return
     */
    List<GroundFeatures> queryGroundFeatures(GroundFeaturesQueryDTO queryDTO);
}
