package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightDetectionDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.FlightDetectionMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.FlightDetectionRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 无人机数据上报表 Repository 实现类
 */
@Service
public class FlightDetectionRepositoryImpl extends ServiceImpl<FlightDetectionMapper, FlightDetectionDO> implements FlightDetectionRepository {

    @Resource
    private FlightDetectionMapper flightDetectionMapper;

    @Override
    public List<FlightDetectionDO> getUnplannedFlights(Long minId) {
        return flightDetectionMapper.getUnplannedFlights(minId);
    }

    @Override
    public List<FlightDetectionDO> getOsIdLatestFlight(List<String> osIds) {
        return flightDetectionMapper.getOsIdLatestFlight(osIds);
    }
}