package com.deepinnet.spatiotemporalplatform.skyflow.mqtt.processor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.util.PointCoordinate;
import com.deepinnet.spatiotemporalplatform.base.config.TenantConfig;
import com.deepinnet.spatiotemporalplatform.common.enums.AirspaceSourceEnum;
import com.deepinnet.spatiotemporalplatform.common.enums.YuanFeiAirSpaceRangeTypeEnum;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.AirspaceDO;
import com.deepinnet.spatiotemporalplatform.model.util.CoordinateConverter;
import com.deepinnet.spatiotemporalplatform.model.util.CoordinateUtil;
import com.deepinnet.spatiotemporalplatform.skyflow.convert.FlightConverter;
import com.deepinnet.spatiotemporalplatform.skyflow.mqtt.body.AirspaceStatusUpdateMsgBody;
import com.deepinnet.spatiotemporalplatform.skyflow.mqtt.topic.YuanFeiMqttTopicEnum;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.AirspaceRepository;
import com.deepinnet.spatiotemporalplatform.skyflow.util.beidou.WktUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;
import org.locationtech.jts.geom.Coordinate;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Creator zengjuerui
 * Date 2025-07-14
 **/

@Slf4j
@Component
@Profile("!qz-gov-inner-network")
public class AirspaceStatusUpdateMsgProcessor implements MessageBizProcessor {

    @Resource
    private AirspaceRepository airspaceRepository;
    @Resource
    private FlightConverter flightConverter;
    @Resource
    private TenantConfig tenantConfig;


    @Override
    public void handle(String topic, String message) {
        LogUtil.info("开始处理空域状态变更,消息内容:{}", message);

        // 去除外层引号
        String unescaped = message.substring(1, message.length() - 1);

        // 处理转义字符
        String cleanJson = StringEscapeUtils.unescapeJson(unescaped);

        List<AirspaceStatusUpdateMsgBody> msgBody = JSONUtil.toList(cleanJson, AirspaceStatusUpdateMsgBody.class);

        if (ObjectUtil.isNull(msgBody) || msgBody.isEmpty()) {
            LogUtil.warn("空域状态变更消息反序列化失败,消息内容:{}", message);
            return;
        }

        List<AirspaceDO> airspaceDOS = flightConverter.fromAirspaceStatusUpdateMsgBodyList(msgBody);

        List<String> codes = airspaceDOS.stream().map(AirspaceDO::getCode).collect(Collectors.toList());

        List<AirspaceDO> existed = airspaceRepository.listByCodes(codes);
        Map<String, AirspaceDO> existedMap = existed.stream().collect(Collectors.toMap(AirspaceDO::getCode, Function.identity()));

        for (AirspaceDO airspaceDO : airspaceDOS) {
            if(airspaceDO.getWkt2() != null) {
                List<Coordinate> gcj02CoordinateList = new ArrayList<>();
                for (Coordinate coordinate : airspaceDO.getWkt2().getCoordinates()) {
                    PointCoordinate pointCoordinate = CoordinateUtil.wgs84ToGcj02(String.valueOf(coordinate.x), String.valueOf(coordinate.y));
                    gcj02CoordinateList.add(new Coordinate(pointCoordinate.getLongitude(), pointCoordinate.getLatitude()));
                }

                airspaceDO.setWkt2(WktUtil.geometryFactory.createPolygon(gcj02CoordinateList.toArray(Coordinate[]::new)));

                airspaceDO.setArea(WktUtil.getAreaKm(airspaceDO.getWkt2()));
            }

            airspaceDO.setSource(AirspaceSourceEnum.HATC.getCode());
            airspaceDO.setTenantId(tenantConfig.getTenantId());
            airspaceDO.setRangeType(YuanFeiAirSpaceRangeTypeEnum.POLYGON.getCode());

            AirspaceDO old = existedMap.get(airspaceDO.getCode());

            if(old != null) {
                airspaceDO.setId(old.getId());
                airspaceDO.setGmtModified(LocalDateTime.now());
            }

            airspaceRepository.saveOrUpdate(airspaceDO);
        }

    }

    @Override
    public String getTopicPrefix() {
        return YuanFeiMqttTopicEnum.AIRSPACE_STATUS_UPDATE.getTopic();
    }
}
