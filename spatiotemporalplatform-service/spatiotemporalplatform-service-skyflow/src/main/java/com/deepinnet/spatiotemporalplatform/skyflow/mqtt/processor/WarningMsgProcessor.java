package com.deepinnet.spatiotemporalplatform.skyflow.mqtt.processor;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.*;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.spatiotemporalplatform.base.config.TenantConfig;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.RiskWarningDO;
import com.deepinnet.spatiotemporalplatform.skyflow.enums.WarningStatusEnum;
import com.deepinnet.spatiotemporalplatform.skyflow.mqtt.body.WarningMsgBody;
import com.deepinnet.spatiotemporalplatform.skyflow.mqtt.topic.YuanFeiMqttTopicEnum;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.RiskWarningRepository;
import com.deepinnet.spatiotemporalplatform.skyflow.service.FlightService;
import com.deepinnet.spatiotemporalplatform.vo.RealTimeUavFlightVO;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 低空飞行预警消息处理器
 *
 * <AUTHOR>
 * @version 2025-03-14
 */
@Component
public class WarningMsgProcessor implements MessageBizProcessor {
    @Resource
    private RiskWarningRepository riskWarningRepository;

    @Resource
    private FlightService flightService;

    @Resource
    private TenantConfig tenantConfig;

    /**
     * 预警类型常量
     */
    private static final String WARNING_TYPE_ALERT = "1";      // 预警
    private static final String WARNING_TYPE_CANCEL_ALERT = "2"; // 解除预警
    private static final String WARNING_TYPE_ALARM = "3";      // 告警
    private static final String WARNING_TYPE_CANCEL_ALARM = "4"; // 解除告警
    private static final String WARNING_END_SUFFIX = "_end"; // 解除告警


    @Override
    public void handle(String topic, String message) {
        // 处理低空飞行预警消息
        LogUtil.info("开始处理预警消息,消息内容:{}", message);

        // 反序列化消息体
        var warningMessage = JSON.parseObject(message, WarningMsgBody.class);

        // 预警消息入库
        if (warningMessage == null) {
            LogUtil.error("预警消息反序列化失败,消息内容:{}", message);
            return;
        }

        // 参数校验
        if (StrUtil.isBlank(warningMessage.getType()) || StrUtil.isBlank(warningMessage.getFlightId()) ||
                StrUtil.isBlank(warningMessage.getUavId()) ||
                StrUtil.isBlank(warningMessage.getWarningType())) {
            LogUtil.error("预警消息参数校验失败,缺少必要字段,预警消息:{}", JSON.toJSONString(warningMessage));
            return;
        }

        processWarningMessage(warningMessage);
    }

    /**
     * 处理预警消息
     */
    private void processWarningMessage(WarningMsgBody warningMessage) {
        String subType = warningMessage.getType();
        String flightId = warningMessage.getFlightId();
        String uavId = warningMessage.getUavId();
        String warningType = warningMessage.getWarningType();

        LogUtil.info("处理预警消息 - subType:{}, flightId:{}, uavId:{}, warningType:{}",
                subType, flightId, uavId, warningType);

        // super_scope-超域，super_scope_end解除超域预警
        // 解除预警的type，需要把_end去掉
        if (StrUtil.contains(subType, WARNING_END_SUFFIX)) {
            subType = subType.replace(WARNING_END_SUFFIX, "");
        }

        // 根据 subType + flightId + uavId 查询是否存在相同的预警
        RiskWarningDO existingWarning = riskWarningRepository.getByCondition(
                Wrappers.lambdaQuery(RiskWarningDO.class)
                        .eq(RiskWarningDO::getSubType, subType)
                        .eq(RiskWarningDO::getFlightId, flightId)
                        .eq(RiskWarningDO::getUavId, uavId)
                        .orderByDesc(RiskWarningDO::getGmtCreated)
                        .last("LIMIT 1")
        );

        if (existingWarning != null) {
            LogUtil.info("发现重复预警,准备更新状态 - 预警ID:{}, 当前状态:{}, 新预警类型:{}",
                    existingWarning.getId(), existingWarning.getWarningStatus(), warningType);

            // 更新现有预警的状态
            updateExistingWarning(existingWarning, warningMessage);
        } else {
            LogUtil.info("未发现重复预警,准备新建预警记录");

            // 只有预警(1)和告警(3)才新建记录，解除预警(2)和解除告警(4)如果没有对应记录则忽略
            if (WARNING_TYPE_ALERT.equals(warningType) || WARNING_TYPE_ALARM.equals(warningType)) {
                createNewWarning(warningMessage);
            } else {
                LogUtil.warn("收到解除预警/告警消息但未找到对应的预警记录,忽略处理 - subType:{}, flightId:{}, uavId:{}, warningType:{}",
                        subType, flightId, uavId, warningType);
            }
        }
    }

    /**
     * 更新现有预警状态
     */
    private void updateExistingWarning(RiskWarningDO existingWarning, WarningMsgBody warningMessage) {
        String warningType = warningMessage.getWarningType();
        String newStatus = determineWarningStatus(warningType);

        if (newStatus == null) {
            LogUtil.error("无法确定预警状态,warningType:{}", warningType);
            return;
        }

        // 如果状态没有变化，则不需要更新
        if (newStatus.equals(existingWarning.getWarningStatus())) {
            LogUtil.info("预警状态无变化,跳过更新 - 预警ID:{}, 状态:{}", existingWarning.getId(), newStatus);
            return;
        }

        // 只能单向变更，从active->end
        if (!StrUtil.equals(newStatus, WarningStatusEnum.ENDED.getCode())) {
            return;
        }

        // 查询飞行计划信息获取坐标
        RealTimeUavFlightVO latestUavFlight = flightService.queryLatestUavFlightByPlanId(warningMessage.getPlanId());
        if (latestUavFlight == null) {
            LogUtil.error("未查询到飞行计划信息,预警消息:{}", JSON.toJSONString(warningMessage));
            return;
        }

        existingWarning.setEndId(latestUavFlight.getId());
        existingWarning.setWarningStatus(newStatus);
        existingWarning.setGmtModified(LocalDateTime.now());

        // 如果是解除预警/告警，更新预警详情
        if (WARNING_TYPE_CANCEL_ALERT.equals(warningType) || WARNING_TYPE_CANCEL_ALARM.equals(warningType)) {
            existingWarning.setWarningDetails(warningMessage.getMessage());
        }

        try {
            boolean updateResult = riskWarningRepository.updateById(existingWarning);
            if (updateResult) {
                LogUtil.info("预警状态更新成功 - 预警ID:{}, 新状态:{}", existingWarning.getId(), newStatus);
            } else {
                LogUtil.error("预警状态更新失败 - 预警ID:{}, 新状态:{}", existingWarning.getId(), newStatus);
            }
        } catch (Exception e) {
            LogUtil.error("预警状态更新异常 - 预警ID:{}, 新状态:{}, 异常信息:{}",
                    existingWarning.getId(), newStatus, e.getMessage(), e);
        }
    }

    /**
     * 创建新的预警记录
     */
    private void createNewWarning(WarningMsgBody warningMessage) {
        RiskWarningDO riskWarningDO = buildRiskWarningDO(warningMessage);
        if (riskWarningDO == null) {
            LogUtil.error("构建预警消息失败,预警消息:{}", JSON.toJSONString(warningMessage));
            return;
        }

        saveRiskWarning(riskWarningDO);
    }

    /**
     * 根据 warningType 确定预警状态
     * 1-预警 -> active (预警中)
     * 2-解除预警 -> ended (预警结束)
     * 3-告警 -> active (预警中)
     * 4-解除告警 -> ended (预警结束)
     */
    private String determineWarningStatus(String warningType) {
        switch (warningType) {
            case WARNING_TYPE_ALERT:
            case WARNING_TYPE_ALARM:
                return WarningStatusEnum.ACTIVE.getCode();
            case WARNING_TYPE_CANCEL_ALERT:
            case WARNING_TYPE_CANCEL_ALARM:
                return WarningStatusEnum.ENDED.getCode();
            default:
                LogUtil.error("未知的预警类型:{}", warningType);
                return null;
        }
    }

    private void saveRiskWarning(RiskWarningDO riskWarningDO) {
        try {
            LogUtil.info("插入风险预警数据:{}", JSON.toJSONString(riskWarningDO));
            riskWarningRepository.save(riskWarningDO);
            LogUtil.info("预警消息入库成功,预警消息:{}", riskWarningDO);
        } catch (DuplicateKeyException ex) {
            LogUtil.warn("预警消息重复, data: {}", JSONObject.toJSONString(riskWarningDO));
        } catch (Exception e) {
            LogUtil.error("预警消息保存异常, data: {}, 异常信息:{}",
                    JSONObject.toJSONString(riskWarningDO), e.getMessage(), e);
        }
    }

    private RiskWarningDO buildRiskWarningDO(WarningMsgBody warningMessage) {
        // 查询飞行计划信息获取坐标
        RealTimeUavFlightVO latestUavFlight = flightService.queryLatestUavFlightByPlanId(warningMessage.getPlanId());
        if (latestUavFlight == null) {
            LogUtil.error("未查询到飞行计划信息,预警消息:{}", JSON.toJSONString(warningMessage));
            return null;
        }

        String warningStatus = determineWarningStatus(warningMessage.getWarningType());
        if (warningStatus == null) {
            LogUtil.error("无法确定预警状态,预警消息:{}", JSON.toJSONString(warningMessage));
            return null;
        }

        RiskWarningDO riskWarningDO = new RiskWarningDO();
        riskWarningDO.setWarningTime(LocalDateTime.now());
        riskWarningDO.setWarningNo(warningMessage.getId());
        riskWarningDO.setFlightId(warningMessage.getFlightId());
        riskWarningDO.setOperatingEntityName(warningMessage.getTeamName());
        riskWarningDO.setPlanId(warningMessage.getPlanId());
        riskWarningDO.setUavId(warningMessage.getUavId());
        riskWarningDO.setUserId(warningMessage.getUserId());
        riskWarningDO.setOperatingEntityId(warningMessage.getTeamId());
        riskWarningDO.setWarningType(warningMessage.getWarningType());
        riskWarningDO.setSubType(warningMessage.getType());
        riskWarningDO.setWarningDetails(warningMessage.getMessage());
        riskWarningDO.setWarningStatus(warningStatus);
        riskWarningDO.setFlightSpeed(latestUavFlight.getFlightSpeed());
        riskWarningDO.setAltitude(latestUavFlight.getElevation());
        riskWarningDO.setGmtCreated(LocalDateTime.now());
        riskWarningDO.setGmtModified(LocalDateTime.now());
        riskWarningDO.setStartId(latestUavFlight.getId());
        // 设置经纬度
        String x = latestUavFlight.getUavPosition().split(",")[0];
        String y = latestUavFlight.getUavPosition().split(",")[1];
        riskWarningDO.setLongitude(x);
        riskWarningDO.setLatitude(y);
        riskWarningDO.setTenantId(tenantConfig.getTenantId());
        riskWarningDO.setHeight(latestUavFlight.getUavAltitude());
        return riskWarningDO;
    }

    @Override
    public String getTopicPrefix() {
        return YuanFeiMqttTopicEnum.WARNING.getTopic();
    }

}
