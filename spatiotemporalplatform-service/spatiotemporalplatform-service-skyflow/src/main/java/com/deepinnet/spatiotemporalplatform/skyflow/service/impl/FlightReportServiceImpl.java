package com.deepinnet.spatiotemporalplatform.skyflow.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.util.JsonUtil;
import com.deepinnet.infra.api.dto.FileInfoDTO;
import com.deepinnet.infra.service.config.file.MinioService;
import com.deepinnet.infra.service.util.IdGenerateUtil;
import com.deepinnet.skyflow.operationcenter.enums.FlightDemandTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.MergeStatusEnum;
import com.deepinnet.skyflow.operationcenter.vo.FlightDemandVO;

import com.deepinnet.spatiotemporalplatform.base.util.ChromeDriverFactory;
import com.deepinnet.spatiotemporalplatform.skyflow.domain.*;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.Pictures;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.deepinnet.spatiotemporalplatform.base.util.ScreenshotUtil;
import com.deepinnet.spatiotemporalplatform.dto.FlightEventsDTO;
import com.deepinnet.spatiotemporalplatform.dto.FlightReportGenerateDTO;
import com.deepinnet.spatiotemporalplatform.enums.FlightEventTypeEnum;
import com.deepinnet.spatiotemporalplatform.skyflow.algorithm.EventMonitorService;
import com.deepinnet.spatiotemporalplatform.skyflow.client.FlightDemandRemoteClient;
import com.deepinnet.spatiotemporalplatform.skyflow.service.FlightReportService;
import com.deepinnet.spatiotemporalplatform.skyflow.service.FlightService;
import com.deepinnet.spatiotemporalplatform.vo.FlightPlanVO;

/**
 * Description: 飞行报告服务实现
 * Date: 2025/6/7
 * Author: lijunheng
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Profile("!qz-gov-inner-network")
public class FlightReportServiceImpl implements FlightReportService {

    private final FlightService flightService;
    private final EventMonitorService eventMonitorService;
    private final FlightDemandRemoteClient flightDemandRemoteClient;
    private final MinioService minioService;
    private final ChromeDriverFactory chromeDriverFactory;

    @Value("${word.url.front_screenshot}")
    private String frontUrl;

    @Value("${word.flight.output}")
    private String outputPath;

    @Value("${word.flight.image}")
    private String imageDir;

    @Value("${word.flight.routine.template}")
    private Resource routineTemplatePath;

    @Value("${word.flight.emergency.template}")
    private Resource emergencyTemplatePath;

    private static final String SUCCESS_WRITE_MAP = "successWriteMap";
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public FileInfoDTO generateFlightReportDocument(FlightReportGenerateDTO generateDTO) {
        LogUtil.info("开始生成飞行报告文档，请求参数：{}", generateDTO);

        FlightPlanVO flightPlanVO = flightService.queryPlanDetail(generateDTO.getPlanId());
        FlightDemandVO flightDemand = flightDemandRemoteClient.getDemandDetail(flightPlanVO.getBizNo());
        //合并需求或者被合并的原始需求不参与生成报告
        if (flightDemand.getIsMergeDemand() || flightDemand.getMergeStatus() == MergeStatusEnum.MERGED) {
            return null;
        }
        FlightDemandTypeEnum type = flightDemand.getType();

        if (Objects.equals(type, FlightDemandTypeEnum.ROUTINE_INSPECTION)) {
            FlightReportRoutineTemplateData templateData = buildRoutineTemplateData(flightPlanVO, flightDemand);
            return generateFlightReportDocument(templateData, routineTemplatePath);
        } else if (Objects.equals(type, FlightDemandTypeEnum.EMERGENCY_RESPONSE)) {
            FlightReportEmergencyTemplateData templateData = buildEmergencyTemplateData(flightPlanVO, flightDemand);
            return generateFlightReportDocument(templateData, emergencyTemplatePath);
        } else {
            throw new RuntimeException("未知的飞行任务类型");
        }
    }

    /**
     * 构建日常巡逻模板数据
     */
    private FlightReportRoutineTemplateData buildRoutineTemplateData(FlightPlanVO flightPlanVO, FlightDemandVO flightDemand) {
        LogUtil.info("开始构建日常巡逻飞行报告模板数据，计划ID：{}", flightPlanVO.getPlanId());

        FlightReportRoutineTemplateData templateData = new FlightReportRoutineTemplateData();

        // 设置公共字段
        fillCommonTemplateData(templateData, flightPlanVO, flightDemand);
        templateData.setTitleName("日常巡逻飞行任务报告");

        // 设置图片
        templateData.setPatrolAreaImageUrl(Pictures.ofLocal(getScreenShotImageUrl(getRequestUrl(flightPlanVO.getPlanId(), 1))).fitSize().create());
        templateData.setFlightTrackImageUrl(Pictures.ofLocal(getScreenShotImageUrl(getRequestUrl(flightPlanVO.getPlanId(), 2))).fitSize().create());

        return templateData;
    }

    /**
     * 构建应急处置模板数据
     */
    private FlightReportEmergencyTemplateData buildEmergencyTemplateData(FlightPlanVO flightPlanVO, FlightDemandVO flightDemand) {
        LogUtil.info("开始构建应急处置飞行报告模板数据，计划ID：{}", flightPlanVO.getPlanId());

        FlightReportEmergencyTemplateData templateData = new FlightReportEmergencyTemplateData();

        // 设置公共字段
        fillCommonTemplateData(templateData, flightPlanVO, flightDemand);
        templateData.setTitleName("应急处置飞行任务报告");

        // 设置图片
        templateData.setDestinationImageUrl(Pictures.ofLocal(getScreenShotImageUrl(getRequestUrl(flightPlanVO.getPlanId(), 1))).fitSize().create());
        templateData.setFlightTrackImageUrl(Pictures.ofLocal(getScreenShotImageUrl(getRequestUrl(flightPlanVO.getPlanId(), 2))).fitSize().create());

        return templateData;
    }

    /**
     * 填充公共的模板数据（所有继承类都有的字段）
     */
    private void fillCommonTemplateData(FlightReportBaseTemplateData templateData, FlightPlanVO flightPlanVO, FlightDemandVO flightDemand) {
        templateData.setReportCreateTime(LocalDateTime.now().format(DATE_FORMATTER));
        templateData.setTaskName(flightPlanVO.getPlanName());
        templateData.setTaskType(extractTaskType(flightDemand.getCategoryFullName()));
        templateData.setDemandName(flightDemand.getName());
        templateData.setPlanedTakeoffTime(formatDateTime(flightPlanVO.getPlanedTakeoffTime()));
        templateData.setPlanedLandingTime(formatDateTime(flightPlanVO.getPlanedLandingTime()));
        templateData.setPlanCreateTime(formatDateTime(flightPlanVO.getPlanCreateTime()));
        templateData.setCrtUserName(flightPlanVO.getCrtUserName());
        templateData.setUavId(flightPlanVO.getUavId());
        templateData.setUavModel(flightPlanVO.getUavModel());
        // 处理飞行事件
        List<FlightEventsDTO> flightEventsDTOS = eventMonitorService.queryListByFlightTaskCode(flightPlanVO.getPlanId(), List.of(flightDemand.getDemandNo()));
        if (CollectionUtil.isNotEmpty(flightEventsDTOS)) {
            Map<FlightEventTypeEnum, List<FlightEventsDTO>> eventTypeStatsMap = flightEventsDTOS.stream()
                    .collect(Collectors.groupingBy(FlightEventsDTO::getEventType));
            templateData.setFlightEventsStatList(buildFlightEventsStatList(eventTypeStatsMap));
            templateData.setEventSummary(generateEventSummaryText(eventTypeStatsMap));
        }
    }

    /**
     * 提取任务类型
     */
    private String extractTaskType(String categoryFullName) {
        if (categoryFullName == null) {
            return "";
        }
        String[] split = categoryFullName.split("/");
        return split[split.length - 1];
    }

    /**
     * 构建飞行事件统计列表
     */
    private List<FlightEventsStatTemplateData> buildFlightEventsStatList(Map<FlightEventTypeEnum, List<FlightEventsDTO>> eventTypeStatsMap) {
        AtomicInteger index = new AtomicInteger(1);
        return eventTypeStatsMap.entrySet().stream()
                .map(entry -> buildFlightEventsStatTemplateData(entry, index))
                .collect(Collectors.toList());
    }

    /**
     * 构建单个飞行事件统计模板数据
     */
    private FlightEventsStatTemplateData buildFlightEventsStatTemplateData(Map.Entry<FlightEventTypeEnum, List<FlightEventsDTO>> entry, AtomicInteger index) {
        FlightEventsStatTemplateData flightEventsStatTemplateData = new FlightEventsStatTemplateData();
        String currentIndex = "3." + index.getAndIncrement();
        flightEventsStatTemplateData.setIndex(currentIndex);
        flightEventsStatTemplateData.setEventName(entry.getKey().getName());

        AtomicInteger childIndex = new AtomicInteger(1);
        List<FlightEventsTemplateData> eventsList = entry.getValue().stream()
                .map(flightEventsDTO -> buildFlightEventsTemplateData(flightEventsDTO, currentIndex, childIndex))
                .collect(Collectors.toList());

        flightEventsStatTemplateData.setFlightEventsList(eventsList);
        return flightEventsStatTemplateData;
    }

    /**
     * 构建单个飞行事件模板数据
     */
    private FlightEventsTemplateData buildFlightEventsTemplateData(FlightEventsDTO flightEventsDTO, String parentIndex, AtomicInteger childIndex) {
        FlightEventsTemplateData flightEventsTemplateData = new FlightEventsTemplateData();
        flightEventsTemplateData.setChildIndex(parentIndex + "." + childIndex.getAndIncrement());
        flightEventsTemplateData.setEventName(flightEventsDTO.getEventName());
        flightEventsTemplateData.setEventLocation(flightEventsDTO.getEventLocation());
        flightEventsTemplateData.setDuration(secondsToMinutes(flightEventsDTO.getDuration()));

        List<String> licensePlateList = JsonUtil.parseToList(flightEventsDTO.getLicensePlate(), String.class);
        flightEventsTemplateData.setLicensePlate(CollectionUtil.isEmpty(licensePlateList) ? "-" : licensePlateList.get(0));

        List<String> evidenceImageList = JsonUtil.parseToList(flightEventsDTO.getEvidenceImages(), String.class);
        if (CollectionUtil.isNotEmpty(evidenceImageList)) {
            flightEventsTemplateData.setEvidenceImages(Pictures.ofUrl(evidenceImageList.get(0)).fitSize().create());
        }

        flightEventsTemplateData.setEventStartTime(formatDateTime(flightEventsDTO.getEventStartTime()));
        return flightEventsTemplateData;
    }

    private String getRequestUrl(String planId, int step) {
        //url里加一个免登标识
        return String.format(frontUrl, planId, step, "X-Auth-Mode", "bypass");
    }

    private double secondsToMinutes(double seconds) {
        double minutes = seconds / 60.0;
        return Math.round(minutes * 10.0) / 10.0;
    }

    public FileInfoDTO generateFlightReportDocument(Object templateData, Resource template) {
        // 生成Word文档
        byte[] documentBytes = generateWordDocument(templateData, template);

        // 上传到MinIO
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String objectName = dateStr + "/" + IdGenerateUtil.getId("flight_report") + "_飞行报告.docx";
        String contentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";

        String fileUrl = minioService.uploadBytes(documentBytes, objectName, contentType);

        LogUtil.info("飞行报告文档生成成功，文件地址：{}", fileUrl);

        return FileInfoDTO.builder()
                .url(fileUrl)
                .filename("飞行报告.docx")
                .objectName(objectName)
                .size((long) documentBytes.length)
                .contentType(contentType)
                .bucketName(minioService.getDefaultBucketName())
                .build();
    }

    private byte[] generateWordDocument(Object reportTemplateData, Resource templateResource) {
        try {
            LogUtil.info("开始生成Word文档");

            // 检查模板文件是否存在
            if (!templateResource.exists()) {
                LogUtil.error("模板文件不存在：{}", templateResource.getFilename());
                throw new RuntimeException("模板文件不存在：" + templateResource.getFilename());
            }

            // 生成临时文件路径
            String tempDir = System.getProperty("java.io.tmpdir");
            String documentName = "flight_report_" + System.currentTimeMillis() + ".docx";
            String outputFilePath = tempDir + "/" + documentName;

            XWPFTemplate template = XWPFTemplate.compile(templateResource.getInputStream()).render(reportTemplateData);

            LogUtil.info("开始写入文件：{}", outputFilePath);
            template.writeToFile(outputFilePath);

            // 读取生成的文件为字节数组
            byte[] documentBytes = Files.readAllBytes(Paths.get(outputFilePath));

            // 删除临时文件
            Files.deleteIfExists(Paths.get(outputFilePath));

            LogUtil.info("Word文档生成完成，文件大小：{} bytes", documentBytes.length);
            return documentBytes;

        } catch (Exception e) {
            LogUtil.error("生成Word文档失败，错误信息：{}", e.getMessage(), e);
            throw new RuntimeException("生成Word文档失败: " + e.getMessage(), e);
        }
    }

    private String generateEventSummaryText(Map<FlightEventTypeEnum, List<FlightEventsDTO>> eventTypeStatsMap) {

        if (MapUtil.isEmpty(eventTypeStatsMap)) {
            return "本次飞行任务未识别到任何事件。";
        }

        StringBuilder summary = new StringBuilder();

        // 添加统计信息
        summary.append("本次飞行任务共识别出 ").append((int) eventTypeStatsMap.values().stream().flatMap(List::stream).count()).append(" 个事件");

        if (!eventTypeStatsMap.isEmpty()) {
            summary.append("，其中：");
            eventTypeStatsMap.forEach((eventType, eventsDTOList) ->
                    summary.append(eventType.getName()).append(" ").append(eventsDTOList.size()).append(" 个，"));
        }

        // 移除最后一个逗号
        if (summary.toString().endsWith("，")) {
            summary.deleteCharAt(summary.length() - 1);
        }
        summary.append("。");

        return summary.toString();
    }

    /**
     * 统一的时间格式化方法，支持 Long 时间戳和 LocalDateTime
     */
    private String formatDateTime(Object dateTime) {
        if (dateTime == null) {
            return "";
        }

        if (dateTime instanceof Long) {
            Long timestamp = (Long) dateTime;
            LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
            return localDateTime.format(DATE_TIME_FORMATTER);
        } else if (dateTime instanceof LocalDateTime) {
            return ((LocalDateTime) dateTime).format(DATE_TIME_FORMATTER);
        }

        return dateTime.toString();
    }

    private String getScreenShotImageUrl(String url) {
        // 通过无头浏览器访问
        ChromeDriver webDriver = chromeDriverFactory.getNewChromeDriver();
        try {
            webDriver.get(url);
            // 多等待 5s 加载页面元素
            Thread.sleep(5000);
            String pageSource = webDriver.getPageSource();
            log.info("url:{} \n pageSource:{}", url, pageSource);
            // 等待直到元素加载完成
            // 设置显式等待，最长等待时间为10秒
            WebDriverWait wait = new WebDriverWait(webDriver, 10);
            By elementLocator = By.id(SUCCESS_WRITE_MAP);
            WebElement element = wait.until(ExpectedConditions.presenceOfElementLocated(elementLocator));

            String imagePath = imageDir + File.separator + String.format("imageDir_%s.png", System.currentTimeMillis());
            return ScreenshotUtil.createElementImage(webDriver, imagePath);
        } catch (Exception e) {
            log.error("获取图片失败,url:{}", url, e);
            throw new RuntimeException(e);
        } finally {
            if (webDriver != null) {
                webDriver.quit();
            }
        }
    }
}
