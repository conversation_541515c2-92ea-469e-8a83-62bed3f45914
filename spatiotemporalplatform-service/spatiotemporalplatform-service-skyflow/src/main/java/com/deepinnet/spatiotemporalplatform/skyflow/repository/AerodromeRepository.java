package com.deepinnet.spatiotemporalplatform.skyflow.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.AerodromeDO;
import com.deepinnet.spatiotemporalplatform.model.skyflow.LandingPoint;

import java.util.List;

/**
 * <p>
 * 机场（起降场地）信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
public interface AerodromeRepository extends IService<AerodromeDO> {

    List<LandingPoint> queryAllLandingPoint();
}
