package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.RadarDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.RadarMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.RadarRepository;

/**
 * 雷达数据仓库实现
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@Service
public class RadarRepositoryImpl extends ServiceImpl<RadarMapper, RadarDO> implements RadarRepository {

} 