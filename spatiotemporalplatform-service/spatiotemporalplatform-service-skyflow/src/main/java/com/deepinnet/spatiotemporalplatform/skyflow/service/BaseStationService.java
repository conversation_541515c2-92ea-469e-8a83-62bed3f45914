package com.deepinnet.spatiotemporalplatform.skyflow.service;

import com.deepinnet.spatiotemporalplatform.vo.BaseStationVO;

import java.util.List;

/**
 * 基站数据Service接口
 *
 * <AUTHOR>
 */
public interface BaseStationService {

    /**
     * 查询所有基站数据
     *
     * @return 基站数据列表
     */
    List<BaseStationVO> listAllBaseStations();

    /**
     * 统计基站总数
     *
     * @return 基站总数
     */
    int countBaseStations();
} 