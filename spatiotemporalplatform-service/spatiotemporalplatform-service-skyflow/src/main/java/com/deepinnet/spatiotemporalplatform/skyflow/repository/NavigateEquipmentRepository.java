package com.deepinnet.spatiotemporalplatform.skyflow.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.NavigateEquipmentDO;

import java.util.List;

/**
 * 导航设备数据Repository
 *
 * <AUTHOR>
 */
public interface NavigateEquipmentRepository extends IService<NavigateEquipmentDO> {

    /**
     * 根据外部ID查询导航设备
     *
     * @param externalId 外部ID
     * @return 导航设备数据
     */
    NavigateEquipmentDO getByExternalId(String externalId);

    /**
     * 根据租户ID删除导航设备数据
     *
     * @param tenantId 租户ID
     */
    void deleteByTenantId(String tenantId);

    /**
     * 根据条件查询导航设备列表
     *
     * @param manufacturer 厂家
     * @param deviceName 设备名称
     * @param brandModel 品牌型号
     * @param serialNo 序列号
     * @param stationType 基站类型
     * @param equipUsage 设备用途
     * @param installAdress 安装地址
     * @param tenantId 租户ID
     * @param departId 部门ID
     * @return 导航设备列表
     */
    List<NavigateEquipmentDO> listByCondition(String manufacturer, String deviceName, String brandModel,
                                            String serialNo, String stationType, String equipUsage,
                                            String installAdress, String tenantId, String departId);
}