package com.deepinnet.spatiotemporalplatform.skyflow.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.*;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.localdata.integration.model.output.*;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandMatchServiceProviderDTO;
import com.deepinnet.skyflow.operationcenter.vo.FlightDemandVO;
import com.deepinnet.spatiotemporalplatform.base.config.TenantConfig;
import com.deepinnet.spatiotemporalplatform.common.enums.YuanFeiAirSpaceRangeTypeEnum;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.*;
import com.deepinnet.spatiotemporalplatform.model.skyflow.Airspace;
import com.deepinnet.spatiotemporalplatform.skyflow.client.FlightDemandRemoteClient;
import com.deepinnet.spatiotemporalplatform.skyflow.client.LocalDataFlightClient;
import com.deepinnet.spatiotemporalplatform.skyflow.enums.FlightPlanStatusEnum;
import com.deepinnet.spatiotemporalplatform.skyflow.enums.FlightPlanSyncStatusEnum;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.*;
import com.deepinnet.spatiotemporalplatform.skyflow.service.AirspaceService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.springframework.context.annotation.Profile;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.*;

/**
 * <p>
 *    飞行计划远程拉取任务
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/15
 */

@Component
@RequiredArgsConstructor
@Profile({"local", "test", "alicloud", "demo", "sz-lg-gov"})
public class FlightPlanTask {

    private final LocalDataFlightClient flightClient;

    private final FlightPlanRepository flightPlanRepository;

    private final UavRepository uavRepository;

    private final AirspaceService airspaceService;

    private final FlightDemandRemoteClient flightDemandRemoteClient;

    private final TenantConfig tenantConfig;

    @Scheduled(cron = "0 0/1 * * * ?")
//    @Scheduled(fixedRate = 1000)
    public void pullFlightPlan() {
        LogUtil.info("========开始拉取飞行计划========");
        FlightResponseVO planResponse = flightClient.queryTodayFlightPlan();

        if (ObjectUtil.isNull(planResponse) || CollUtil.isEmpty(planResponse.getRows())) {
            LogUtil.info("========今日无飞行计划========");
            return;
        }

        // 获取当前系统里已有的飞行计划
        List<FlightPlanDO> existFlightPlanList = flightPlanRepository.list(Wrappers.<FlightPlanDO>lambdaQuery()
                .in(FlightPlanDO::getPlanId, planResponse.getRows().stream().map(FlightPlanResponseVO::getId).collect(Collectors.toList())));

        List<FlightPlanResponseVO> rows = planResponse.getRows();

        List<String> uavIds = rows.stream()
                .filter(Objects::nonNull)
                .flatMap(row -> {
                    if (row.getFlightPlanUavs() == null) {
                        return Stream.empty();
                    }
                    return row.getFlightPlanUavs().stream();
                })
                .filter(Objects::nonNull)
                .map(FlightPlanResponseVO.FlightPlanUav::getUavId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<String> uavDOIds = Lists.newArrayList();
        if (CollUtil.isNotEmpty(uavIds)) {
            uavDOIds = uavRepository.list(Wrappers.<UavDO>lambdaQuery()
                    .in(UavDO::getUavId, uavIds)).stream().map(UavDO::getUavId).collect(Collectors.toList());
        }

        List<String> existFlightPlanIds = existFlightPlanList.stream().map(FlightPlanDO::getPlanId).collect(Collectors.toList());

        Map<String, FlightPlanDO> existPlanMap = existFlightPlanList.stream().collect(Collectors.toMap(FlightPlanDO::getPlanId, Function.identity()));

        List<String> finalUavDOIds = uavDOIds;
        planResponse.getRows().forEach(e -> {

            // 查询计划详情
            FlightPlanDetailResponseVO flightPlanDetailResponseVO = flightClient.queryFlightPlanDetail(e.getId());

            if (ObjectUtil.isNull(flightPlanDetailResponseVO)) {
                LogUtil.warn("========当前飞行计划:{}, 未获取到详情========", e.getId());
                return;
            }

            FlightPlanDO flightPlanDO = buildFlightPlanDO(e, flightPlanDetailResponseVO);

            setFlightPlanSyncStatus(e, existPlanMap, flightPlanDO);


            try {
                // 计划存在则更新, 不存在则插入
                if (existFlightPlanIds.contains(e.getId())) {
                    flightPlanDO.setGmtModified(LocalDateTime.now());
                    flightPlanRepository.update(flightPlanDO, Wrappers.<FlightPlanDO>lambdaUpdate()
                            .eq(FlightPlanDO::getPlanId, e.getId()));
                } else {
                    flightPlanRepository.save(flightPlanDO);
                }
            } catch (DuplicateKeyException duplicateKeyException) {
                LogUtil.warn("========当前飞行计划:{}, 已存在, 重复插入========", e.getId());
            } catch (Exception exception) {
                LogUtil.error("========当前飞行计划:{}, 插入失败, 异常信息:{}", e.getId(), exception);
            }

            // 构建无人机数据
            UavDO uav = new UavDO();

            buildUav(flightPlanDetailResponseVO, uav, flightPlanDO);

            try {
                if (ObjectUtil.isNotNull(uav) && !finalUavDOIds.contains(uav.getUavId())) {
                    uavRepository.save(uav);
                }
            } catch (DuplicateKeyException duplicateKeyException) {
                LogUtil.warn("========当前无人机:{}, 已存在, 重复插入========", JSONObject.toJSONString(uav));
            } catch (Exception exception) {
                LogUtil.error("========当前无人机:{}, 插入失败, 异常信息:{}", e.getId(), exception);
            }

        });

    }

    private static void setFlightPlanSyncStatus(FlightPlanResponseVO e, Map<String, FlightPlanDO> existPlanMap, FlightPlanDO flightPlanDO) {
        LocalDateTime current = LocalDateTime.now();

        FlightPlanDO oldPlan = existPlanMap.get(e.getId());

        boolean needUnSync = FlightPlanStatusEnum.FLIGHT_SYNC_STATUS.contains(flightPlanDO.getStatus());

        FlightPlanSyncStatusEnum oldSyncStatus = Optional.ofNullable(oldPlan)
                .map(p -> FlightPlanSyncStatusEnum.getByCode(p.getSyncStatus()))
                .orElse(null);

        boolean isTerminal = ObjectUtil.equals(oldSyncStatus, FlightPlanSyncStatusEnum.SYNCED)
                || ObjectUtil.equals(oldSyncStatus, FlightPlanSyncStatusEnum.TERMINATED);

        FlightPlanSyncStatusEnum syncStatus = isTerminal
                ? oldSyncStatus
                    : (needUnSync ? FlightPlanSyncStatusEnum.UN_SYNC : FlightPlanSyncStatusEnum.INIT);

        flightPlanDO.setSyncStatus(syncStatus.getCode());

        LocalDateTime syncStart = Optional.ofNullable(oldPlan)
                .map(FlightPlanDO::getSyncStart)
                .orElse(syncStatus == FlightPlanSyncStatusEnum.UN_SYNC ? current : null);

        LocalDateTime syncEnd   = Optional.ofNullable(oldPlan)
                .map(FlightPlanDO::getSyncEnd)
                .orElse(syncStatus == FlightPlanSyncStatusEnum.UN_SYNC ? current.plusDays(7) : null);

        flightPlanDO.setSyncStart(syncStart);
        flightPlanDO.setSyncEnd(syncEnd);
    }

    private void buildUav(FlightPlanDetailResponseVO flightPlanDetailResponseVO, UavDO uav, FlightPlanDO flightPlanDO) {
        if (CollUtil.isNotEmpty(flightPlanDetailResponseVO.getFlightPlanUavs())) {
            FlightPlanDetailResponseVO.FlightPlanUav flightPlanUav = flightPlanDetailResponseVO.getFlightPlanUavs().get(0);


            uav.setUavId(flightPlanDO.getUavId());
            uav.setUavName(flightPlanUav.getUavName());
            uav.setAircraftType(Convert.toInt(flightPlanUav.getUavTypeMannedType()) == 1 ? "载人" : "无人驾驶");
            uav.setUsa(flightPlanUav.getReg());
            uav.setSn(flightPlanUav.getAutopilotCode());
            uav.setModel(flightPlanUav.getUavTypeName());
            uav.setTenantId(tenantConfig.getTenantId());

        }
    }

    private FlightPlanDO buildFlightPlanDO(FlightPlanResponseVO e, FlightPlanDetailResponseVO flightPlanDetailResponseVO) {
        LocalDateTime planedTakeOffTime = null;
        LocalDateTime planedLandingTime = null;
        LocalDateTime planCreateTime = null;

        // 起飞时间
        if (StrUtil.isNotBlank(e.getStartDate()) && StrUtil.isNotBlank(e.getStartTime())) {
            String startTime = e.getStartDate() + " " + e.getStartTime();
            planedTakeOffTime = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
        }

        // 降落时间
        if (StrUtil.isNotBlank(e.getEndDate()) && StrUtil.isNotBlank(e.getEndTime())) {
            String endTime = e.getEndDate() + " " + e.getEndTime();
            planedLandingTime = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
        }

        // 计划创建时间
        if (StrUtil.isNotBlank(flightPlanDetailResponseVO.getCrtTime())) {
            planCreateTime = LocalDateTime.parse(flightPlanDetailResponseVO.getCrtTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }

        FlightDemandVO demandDetail = null;

        if (StrUtil.isNotBlank(flightPlanDetailResponseVO.getRequirementNo())) {
            demandDetail = flightDemandRemoteClient.getDemandDetail(flightPlanDetailResponseVO.getRequirementNo());
        }

        FlightPlanDO flightPlanDO = new FlightPlanDO();

        // 塞入需求编号 | 场景码 | 需求名称
        if (ObjectUtil.isNotNull(demandDetail)) {
            flightPlanDO.setBizNo(flightPlanDetailResponseVO.getRequirementNo());
            flightPlanDO.setScenceCode(demandDetail.getScene().name());
            flightPlanDO.setRequirementName(demandDetail.getName());
        }
        
        flightPlanDO.setMissionId(e.getId());
        flightPlanDO.setPlanId(e.getId());
        flightPlanDO.setPlanName(flightPlanDetailResponseVO.getName());
        flightPlanDO.setPlanCreateTime(planCreateTime);
        flightPlanDO.setOutPlanNo(flightPlanDetailResponseVO.getPlanNo());

        flightPlanDO.setCrtUserId(flightPlanDetailResponseVO.getCrtUserId());
        flightPlanDO.setCrtUserName(flightPlanDetailResponseVO.getCrtUserName());
        flightPlanDO.setRelatedId(flightPlanDetailResponseVO.getRelatedId());
        flightPlanDO.setApplyType(Convert.toInt(flightPlanDetailResponseVO.getApplyType()));
        flightPlanDO.setUavId(CollUtil.isEmpty(e.getFlightPlanUavs()) ? null : e.getFlightPlanUavs().get(0).getUavId());
        if (CollUtil.isNotEmpty(flightPlanDetailResponseVO.getFlightPlanUavs())) {
            flightPlanDO.setUavModel(flightPlanDetailResponseVO.getFlightPlanUavs().get(0).getUavTypeName());
        }
        flightPlanDO.setApplyUserName(flightPlanDetailResponseVO.getUserName());
        flightPlanDO.setStatus(Convert.toInt(e.getPlanState()));
        flightPlanDO.setPlanedTakeoffTime(ObjectUtil.isNull(planedTakeOffTime) ? null : planedTakeOffTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        flightPlanDO.setPlanedLandingTime(ObjectUtil.isNull(planedLandingTime) ? null : planedLandingTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        flightPlanDO.setOperatorUser(CollUtil.isEmpty(e.getFlightPlanFlyers()) ? null : e.getFlightPlanFlyers().get(0).getFlyerName());
        // 通过详情查飞手详情
        flightPlanDO.setOperatorPhone(CollUtil.isEmpty(flightPlanDetailResponseVO.getFlightPlanFlyers()) ? null : flightPlanDetailResponseVO.getFlightPlanFlyers().get(0).getMobilePhone());
        // 通过查询计划详情
        flightPlanDO.setLandingAerodromeId(CollUtil.isEmpty(flightPlanDetailResponseVO.getLandingIds()) ? null : String.join(", ", flightPlanDetailResponseVO.getLandingIds()));
        flightPlanDO.setPlanedAltitude(CollUtil.isEmpty(flightPlanDetailResponseVO.getFlightPlanAirspaces()) ? null : Convert.toLong(flightPlanDetailResponseVO.getFlightPlanAirspaces().stream().mapToInt(FlightPlanDetailResponseVO.FlightPlanAirspace::getMaxHeight).max().orElse(0)));
        // 空域 id ，只保存航道的空域 id
        Airspace airspaceByCode = airspaceService.getAirspaceByCode(flightPlanDetailResponseVO.getAirspaceId());
        if (airspaceByCode != null && Objects.equals(airspaceByCode.getRangeType(), YuanFeiAirSpaceRangeTypeEnum.LINE.getCode())) {
            flightPlanDO.setAirspaceId(flightPlanDetailResponseVO.getAirspaceId());
        }

        FlightDemandMatchServiceProviderDTO flightDemandMatchServiceProviderDTO = null;

        if (ObjectUtil.isNotNull(demandDetail) && CollUtil.isNotEmpty(demandDetail.getServiceProviderList())) {
            flightDemandMatchServiceProviderDTO = demandDetail.getServiceProviderList().get(0);
        }

        flightPlanDO.setFlightUnit(ObjectUtil.isNull(flightDemandMatchServiceProviderDTO) ? e.getTeamName() : flightDemandMatchServiceProviderDTO.getCompanyName());
        flightPlanDO.setFlightUnitId(ObjectUtil.isNull(flightDemandMatchServiceProviderDTO) ? e.getTeamId() : flightDemandMatchServiceProviderDTO.getServiceProviderNo());
        flightPlanDO.setTenantId(tenantConfig.getTenantId());
        return flightPlanDO;
    }

}
