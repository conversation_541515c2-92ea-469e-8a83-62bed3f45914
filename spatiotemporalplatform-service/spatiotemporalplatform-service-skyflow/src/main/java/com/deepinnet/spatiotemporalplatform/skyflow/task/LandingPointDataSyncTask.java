package com.deepinnet.spatiotemporalplatform.skyflow.task;

import com.deepinnet.digitaltwin.common.util.PointCoordinate;
import com.deepinnet.localdata.integration.LandingPointClient;
import com.deepinnet.localdata.integration.model.input.LandingPointPageQueryDTO;
import com.deepinnet.localdata.integration.model.output.LandingPointDataDTO;
import com.deepinnet.localdata.integration.model.output.LandingPointPageResponseDTO;
import com.deepinnet.spatiotemporalplatform.common.enums.CoordinateReferenceSystemNameEnum;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.LandingPointDO;
import com.deepinnet.spatiotemporalplatform.model.util.CoordinateUtil;
import com.deepinnet.spatiotemporalplatform.model.util.WktUtil;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.LandingPointRepository;
import com.deepinnet.spatiotemporalplatform.base.config.TenantConfig;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.Point;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 起降点数据同步定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class LandingPointDataSyncTask {

    @Resource
    private LandingPointClient landingPointClient;

    @Resource
    private LandingPointRepository landingPointRepository;

    @Resource
    private TenantConfig tenantConfig;

    /**
     * 同步起降点数据
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 5 * 60 * 1000)
    public void syncLandingPointData() {
        log.info("开始同步起降点数据");

        try {
            // 处理每条数据的统计
            int totalNewCount = 0;
            int totalUpdateCount = 0;
            int currentPage = 1;
            int pageSize = 100; // 设置合理的分页大小
            boolean hasMoreData = true;

            while (hasMoreData) {
                // 构建查询参数
                LandingPointPageQueryDTO queryDTO = new LandingPointPageQueryDTO();
                queryDTO.setPage(currentPage);
                queryDTO.setLimit(pageSize);
                queryDTO.setCrtType("0");

                log.info("开始获取第{}页起降点数据，每页{}条", currentPage, pageSize);

                // 调用外部接口获取数据
                LandingPointPageResponseDTO response = landingPointClient.getPageLandingPoints(queryDTO);

                if (response == null || response.getData() == null || CollectionUtils.isEmpty(response.getData().getRows())) {
                    log.info("第{}页未获取到起降点数据，分页获取结束", currentPage);
                    hasMoreData = false;
                    break;
                }

                List<LandingPointDataDTO> landingPointDataList = response.getData().getRows();
                log.info("第{}页获取到起降点数据{}条", currentPage, landingPointDataList.size());

                // 处理当前页的每条数据
                int pageNewCount = 0;
                int pageUpdateCount = 0;

                for (LandingPointDataDTO dataDTO : landingPointDataList) {
                    if (dataDTO == null || !StringUtils.hasText(dataDTO.getId())) {
                        log.warn("起降点数据为空或ID为空，跳过处理");
                        continue;
                    }

                    try {
                        // 检查数据是否已存在
                        LandingPointDO existingDO = landingPointRepository.getByExternalId(dataDTO.getId());

                        if (existingDO == null) {
                            // 新增数据
                            LandingPointDO newDO = convertToLandingPointDO(dataDTO);

                            // 转换gcj02坐标
                            setPointGcj02(newDO);

                            landingPointRepository.save(newDO);
                            pageNewCount++;
                            log.debug("新增起降点数据，externalId: {}", dataDTO.getId());
                        } else {
                            // 更新数据
                            LandingPointDO updateDO = convertToLandingPointDO(dataDTO);
                            updateDO.setId(existingDO.getId());
                            updateDO.setGmtModified(LocalDateTime.now());
                            landingPointRepository.updateById(updateDO);
                            pageUpdateCount++;
                            log.debug("更新起降点数据，externalId: {}", dataDTO.getId());
                        }
                    } catch (Exception e) {
                        log.error("处理起降点数据失败，externalId: {}", dataDTO.getId(), e);
                    }
                }

                totalNewCount += pageNewCount;
                totalUpdateCount += pageUpdateCount;

                log.info("第{}页处理完成，新增{}条，更新{}条", currentPage, pageNewCount, pageUpdateCount);

                // 判断是否还有更多数据
                if (landingPointDataList.size() < pageSize) {
                    // 当前页数据量小于分页大小，说明已经是最后一页
                    hasMoreData = false;
                    log.info("已获取完所有起降点数据，总共{}页", currentPage);
                } else {
                    // 继续获取下一页
                    currentPage++;
                }
            }

            log.info("起降点数据同步完成，总共新增{}条，更新{}条", totalNewCount, totalUpdateCount);

        } catch (Exception e) {
            log.error("同步起降点数据异常", e);
        }
    }

    private static void setPointGcj02(LandingPointDO newDO) {
        // 处理坐标转换：WGS84转GCJ02
        if (StringUtils.hasText(newDO.getPoint()) && newDO.getPoint().split(",").length == 2) {
            String[] parts = newDO.getPoint().split(",");
            try {
                PointCoordinate pointCoordinate = CoordinateUtil.wgs84ToGcj02(parts[0], parts[1]);
                newDO.setPointGcj02(WktUtil.toPoint(pointCoordinate.getLongitude(), pointCoordinate.getLatitude()));
            } catch (Exception e) {
                log.error("[起降点数据同步] 坐标转换失败: {}", newDO.getPoint(), e);
            }
        }
    }

    /**
     * 将DTO转换为DO
     *
     * @param dto 起降点数据DTO
     * @return 起降点数据DO
     */
    private LandingPointDO convertToLandingPointDO(LandingPointDataDTO dto) {
        LandingPointDO landingPointDO = new LandingPointDO();

        // 将外部系统的id映射为externalId
        landingPointDO.setExternalId(dto.getId());
        landingPointDO.setCode(dto.getCode());
        landingPointDO.setType(dto.getType());
        landingPointDO.setName(dto.getName());
        landingPointDO.setAlt(dto.getAlt());
        landingPointDO.setHeight(dto.getHeight());
        landingPointDO.setRadius(dto.getRadius());
        landingPointDO.setPoint(dto.getPoint());
        landingPointDO.setAddress(dto.getAddress());
        landingPointDO.setPointOrName(dto.getPointOrName());
        landingPointDO.setTeamId(dto.getTeamId());
        landingPointDO.setCrtType(dto.getCrtType());
        landingPointDO.setCrtUserId(dto.getCrtUserId());
        landingPointDO.setCrtTime(dto.getCrtTime());
        landingPointDO.setCrtUserName(dto.getCrtUserName());
        landingPointDO.setUpdUserId(dto.getUpdUserId());
        landingPointDO.setUpdTime(dto.getUpdTime());
        landingPointDO.setUpdUserName(dto.getUpdUserName());
        landingPointDO.setRemark(dto.getRemark());
        landingPointDO.setIsDisabled(dto.getIsDisabled());
        landingPointDO.setIsDeleted(dto.getIsDeleted());
        landingPointDO.setDepartId(dto.getDepartId());
        landingPointDO.setDepartIds(dto.getDepartIds());
        landingPointDO.setExternalTenantId(dto.getTenantId());
        landingPointDO.setOwner(dto.getOwner());
        landingPointDO.setOperator(dto.getOperator());
        landingPointDO.setPhoneNumber(dto.getPhoneNumber());
        landingPointDO.setBusiType(dto.getBusiType());
        landingPointDO.setTerminalId(dto.getTerminalId());
        landingPointDO.setCameraNo(dto.getCameraNo());
        landingPointDO.setCameraUrl(dto.getCameraUrl());
        landingPointDO.setStreet(dto.getStreet());
        landingPointDO.setAccuracy(dto.getAccuracy());
        landingPointDO.setAirType(dto.getAirType());
        landingPointDO.setTenantId(tenantConfig.getTenantId());


        // 设置当前时间
        LocalDateTime now = LocalDateTime.now();
        landingPointDO.setGmtModified(now);
        
        return landingPointDO;
    }
} 