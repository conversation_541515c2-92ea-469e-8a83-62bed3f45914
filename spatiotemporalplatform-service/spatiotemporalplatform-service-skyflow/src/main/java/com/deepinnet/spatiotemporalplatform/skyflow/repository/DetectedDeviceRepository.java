package com.deepinnet.spatiotemporalplatform.skyflow.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.DetectedDeviceDO;

/**
 * 侦测定位设备数据Repository接口
 *
 * <AUTHOR>
 */
public interface DetectedDeviceRepository extends IService<DetectedDeviceDO> {
    
    /**
     * 根据外部ID查询设备数据
     *
     * @param externalId 外部ID
     * @return 设备数据
     */
    DetectedDeviceDO getByExternalId(String externalId);
} 