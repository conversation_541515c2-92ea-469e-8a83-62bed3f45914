package com.deepinnet.spatiotemporalplatform.skyflow.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightDetectionDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 无人机数据上报表 Repository 接口
 */
public interface FlightDetectionRepository extends IService<FlightDetectionDO> {

    List<FlightDetectionDO> getUnplannedFlights(Long minId);

    List<FlightDetectionDO> getOsIdLatestFlight(List<String> osIds);
}