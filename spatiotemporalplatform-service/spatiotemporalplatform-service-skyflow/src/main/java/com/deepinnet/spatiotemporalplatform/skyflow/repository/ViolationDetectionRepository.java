package com.deepinnet.spatiotemporalplatform.skyflow.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deepinnet.spatiotemporalplatform.skyflow.condition.ViolationDetectionQueryCondition;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.ViolationDetectionDO;
import com.deepinnet.spatiotemporalplatform.dto.ViolationGroupStatisticsDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 违规检测Repository接口
 *
 * <AUTHOR>
 */
public interface ViolationDetectionRepository {
    
    /**
     * 保存违规检测信息
     *
     * @param violationDetectionDO 违规检测信息
     * @return 是否保存成功
     */
    boolean save(ViolationDetectionDO violationDetectionDO);
    
    /**
     * 批量保存违规检测信息
     *
     * @param violationDetectionDOList 违规检测信息列表
     * @return 是否保存成功
     */
    boolean saveBatch(List<ViolationDetectionDO> violationDetectionDOList);
    
    /**
     * 根据ID查询违规检测信息
     *
     * @param id 违规检测ID
     * @return 违规检测信息
     */
    ViolationDetectionDO getById(Long id);
    
    /**
     * 根据条件查询违规检测列表
     *
     * @param queryWrapper 查询条件
     * @return 违规检测列表
     */
    List<ViolationDetectionDO> list(LambdaQueryWrapper<ViolationDetectionDO> queryWrapper);
    
    /**
     * 根据违规类型和时间范围查询违规检测
     *
     * @param violationType 违规类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 违规检测列表
     */
    List<ViolationDetectionDO> listByTypeAndTimeRange(String violationType, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据运营主体查询违规检测
     *
     * @param operatingEntity 运营主体
     * @return 违规检测列表
     */
    List<ViolationDetectionDO> listByOperatingEntity(String operatingEntity);
    
    /**
     * 根据飞行器SN码查询违规检测
     *
     * @param aircraftSn 飞行器SN码
     * @return 违规检测列表
     */
    List<ViolationDetectionDO> listByAircraftSn(String aircraftSn);
    
    /**
     * 更新违规检测信息
     *
     * @param violationDetectionDO 违规检测信息
     * @return 是否更新成功
     */
    boolean updateById(ViolationDetectionDO violationDetectionDO);
    
    /**
     * 根据ID删除违规检测信息
     *
     * @param id 违规检测ID
     * @return 是否删除成功
     */
    boolean removeById(Long id);
    
    /**
     * 批量删除违规检测信息
     *
     * @param ids ID列表
     * @return 是否删除成功
     */
    boolean removeByIds(List<Long> ids);

    /**
     * 根据违规检测查询条件查询违规数据
     *
     * @param queryCondition 查询条件
     * @return 违规检测列表
     */
    List<ViolationDetectionDO> listByCondition(ViolationDetectionQueryCondition queryCondition);

    /**
     * 按运营主体和违规类型分组统计违规数量（不分页）
     *
     * @param queryCondition 查询条件
     * @return 分组统计结果列表
     */
    List<ViolationGroupStatisticsDTO> countGroupByOperatingEntityAndType(ViolationDetectionQueryCondition queryCondition);
} 