package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.AirspaceDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.AirspaceMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.AirspaceRepository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 空域 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@Service
public class AirspaceRepositoryImpl extends ServiceImpl<AirspaceMapper, AirspaceDO> implements AirspaceRepository {
    @Resource
    private AirspaceMapper airspaceMapper;

    @Override
    public List<AirspaceDO> listByCodes(List<String> codes) {
        return airspaceMapper.listByCodes(codes);
    }
}