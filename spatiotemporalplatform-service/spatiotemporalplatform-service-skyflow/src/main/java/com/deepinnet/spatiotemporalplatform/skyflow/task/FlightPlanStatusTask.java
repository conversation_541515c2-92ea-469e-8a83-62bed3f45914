package com.deepinnet.spatiotemporalplatform.skyflow.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.localdata.integration.model.output.FlightPlanDetailResponseVO;
import com.deepinnet.skyflow.operationcenter.enums.FlightPlanStatusEnum;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightPlanDO;
import com.deepinnet.spatiotemporalplatform.skyflow.client.LocalDataFlightClient;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.FlightPlanRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <p>
 *    飞行计划状态变更任务
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/15
 */

@Component
@RequiredArgsConstructor
@Profile({"local", "test", "alicloud", "demo", "sz-lg-gov"})
public class FlightPlanStatusTask {

    private final LocalDataFlightClient flightClient;

    private final FlightPlanRepository flightPlanRepository;

    @PostConstruct
    public void runAtStartup() {
        CompletableFuture.runAsync(this::runOnSchedule);
    }

    @Scheduled(cron = "0 5 0 * * ?")
    public void pullFlightPlan() {

        runOnSchedule();

    }

    private void runOnSchedule() {
        List<FlightPlanDO> exectingPlanList = flightPlanRepository.list(Wrappers.<FlightPlanDO>lambdaQuery()
                .eq(FlightPlanDO::getStatus, FlightPlanStatusEnum.EXECUTING.getCode()));

        if (CollUtil.isEmpty(exectingPlanList)) {
            LogUtil.info("========暂无执行中的飞行计划========");
            return;
        }

        exectingPlanList.forEach(e -> {

            // 查询计划详情
            FlightPlanDetailResponseVO flightPlanDetailResponseVO = flightClient.queryFlightPlanDetail(e.getPlanId());

            if (ObjectUtil.isNull(flightPlanDetailResponseVO)) {
                LogUtil.warn("========当前飞行计划:{}, 未获取到详情========", e.getPlanId());
                return;
            }

            try {
                flightPlanRepository.update(Wrappers.<FlightPlanDO>lambdaUpdate()
                        .eq(FlightPlanDO::getPlanId, e.getPlanId())
                        .set(FlightPlanDO::getStatus, flightPlanDetailResponseVO.getStatus())
                        .set(FlightPlanDO::getGmtModified, LocalDateTime.now()));
            } catch (Exception exception) {
                LogUtil.error("========当前飞行计划:{}, 状态更新失败, 异常信息:{}", e.getId(), exception);
            }

        });
    }

}
