package com.deepinnet.spatiotemporalplatform.skyflow.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightPlanDO;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightDemandPlanStatDO;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightDemandPlanStatQuery;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightQuery;

import java.util.List;

/**
 * <p>
 * 飞行计划表，每个飞行任务包含多个飞行计划 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
public interface FlightPlanRepository extends IService<FlightPlanDO> {

    /**
     * 查询飞行计划
     * @param flightQuery 查询条件
     * @return 飞行计划列表
     */
    List<FlightPlanDO> listFlightPlanByCondition(FlightQuery flightQuery);

    List<FlightDemandPlanStatDO> queryFlightDemandPlanStat(FlightDemandPlanStatQuery query);

    List<String> queryHasAchievementPlan();
}
