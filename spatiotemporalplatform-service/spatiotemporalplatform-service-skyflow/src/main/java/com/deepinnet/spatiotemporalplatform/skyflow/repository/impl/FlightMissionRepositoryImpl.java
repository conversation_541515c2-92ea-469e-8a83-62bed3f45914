package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightMissionDO;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightQuery;
import com.deepinnet.spatiotemporalplatform.dal.mapper.FlightMissionMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.FlightMissionRepository;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 飞行任务表，记录飞行任务的基本信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
@Service
public class FlightMissionRepositoryImpl extends ServiceImpl<FlightMissionMapper, FlightMissionDO> implements FlightMissionRepository {

    @Override
    public List<FlightMissionDO> listFlightMissionByCondition(FlightQuery flightQuery) {
        return baseMapper.getFlightMissionByCondition(flightQuery);
    }
}
