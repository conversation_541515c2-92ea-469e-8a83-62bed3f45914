package com.deepinnet.spatiotemporalplatform.skyflow.service;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.dto.*;
import com.deepinnet.spatiotemporalplatform.model.skyflow.Airspace;
import com.deepinnet.spatiotemporalplatform.vo.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *   低空飞行服务
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/4
 */
public interface FlightService {

    /**
     * 分页查询飞行任务
     * @param flightQueryDTO 查询条件
     * @return 分页数据
     */
    @Deprecated
    CommonPage<FlightMissionVO> pageQueryMission(FlightQueryDTO flightQueryDTO);

    /**
     * 分页查询飞行计划
     * @param flightQueryDTO 查询条件
     * @return 分页数据
     */
    CommonPage<FlightPlanVO> pageQueryPlan(FlightQueryDTO flightQueryDTO);

    /**
     * 查询飞行任务详情
     * @param missionId 飞行任务id
     * @return 飞行任务详情
     */
    FlightMissionVO queryMissionDetail(String missionId);

    /**
     * 查询飞行计划详情
     * @param planId 飞行计划id
     * @return 飞行计划详情
     */
    FlightPlanVO queryPlanDetail(String planId);

    /**
     * 查询计划下的所有飞行任务
     * @param planId 计划编号
     * @return 计划下的所有飞行任务
     */
    List<RealTimeUavFlightVO> queryPlanFlight(String planId);


    /**
     * 查询今日飞行概览
     * @return 今日飞行概览
     */
    FlightTodayOverviewVO queryTodayOverview();

    /**
     * 趋势图
     * @return 类型
     */
    List queryTrend(String type);

    /**
     * 今日飞行高度架次统计
     * @return 飞行高度统计VO
     */
    List queryTodayHeightSorties(Long startTime, Long endTime);

    /**
     * 查询今日实时无人机点位
     * @return 今日实时无人机点位
     */
    List<RealTimeUavFlightVO> queryTodayRealTimeUavFlight(PositionQueryDTO queryDTO);

    /**
     * 今日无人机飞行轨迹
     * @return 今日无人机飞行轨迹
     */
    List<UavFlightTrackVO> queryTodayUavFlightTrack(PositionQueryDTO queryDTO);

    /**
     * 根据计划ID查询最新的一条飞行记录
     * @return 最新的一条飞行记录
     */
    RealTimeUavFlightVO queryLatestUavFlightByPlanId(String planId);

    /**
     * 查询直播流地址
     * @param sn 码
     * @return 直播流地址
     */
    String queryLiveUrl(String sn, String httpProtocol);

    /**
     * 查询飞行计划对应的航道
     * @param planId
     * @return
     */
    Airspace queryPlanAirway(String planId);

    /**
     * 企业飞行统计
     * @param dto 查询参数
     */
    List<FlightDailyStatisticsVO> queryFlightDailyStatistics(FlightDailyStatisticsQueryDTO dto);

    /**
     * 飞行统计概览
     * @param dto 查询参数
     */
    FlightStatisticsOverviewVO queryFlightStatisticsOverview(FlightStatisticsOverviewQueryDTO dto);

    /**
     * 根据需求编号查询飞行次数
     * @param dto 查询参数
     * @return 飞行次数
     */
    List<FlightCountVO> batchQueryFlightCount(BatchQueryPlanDTO dto);

    /**
     * 根据需求编号查询飞行计划
     * @param dto 查询参数
     * @return 飞行计划
     */
    List<FlightPlanVO> batchQueryFlightPlan(BatchQueryPlanDTO dto);

    /**
     * 批量查询起降场信息
     * @param ids 起降场id
     * @return 起降场
     */
    List<AerodromeVO> batchQueryAerodrome(List<String> ids);

    /**
     * 写入飞行报告
     * @param dto 计划编号/url
     * @return true | false
     */
    Boolean updateFlightPlan(FlightReportDTO dto);

    /**
     * 根据计划编号和发生时间查询最近一次的飞行记录
     * @param reportTime 时间
     * @param planId 计划编号
     * @return 最新一次的飞行记录
     */
    RealTimeUavFlightVO queryLatestFlightsByReportTime(LocalDateTime reportTime, String planId);

    /**
     * 返回给定类目的下一级叶子类目的需求计划统计
     * @param queryDTO
     * @return
     */
    List<FlightDemandPlanStatDTO> queryFlightDemandPlanStat(FlightDemandPlanStatQueryDTO queryDTO);

    /**
     * 获取算法视频地址
     * @param planId
     * @return
     */
    String queryAlgorithmMonitorUrl(String planId);

    /**
     * 获取计划下的架次详情
     * @param planId
     * @param flightId
     * @return
     */
    FlightPlanVO queryPlanFlightDetail(String planId, String flightId);

    /**
     * 根据计划ID查询对应的成果
     * @param planId 计划ID
     * @return 成果列表
     */
    List<FlightAchievementVO> queryAchievementList(String planId);

    /**
     * 根据计划ID查询对应日期的成果
     * @param queryDTO 查询参数
     * @return 成果
     */
    FlightAchievementVO queryAchievementCompareDate(FlightPlanAchievementQueryDTO queryDTO);
}
