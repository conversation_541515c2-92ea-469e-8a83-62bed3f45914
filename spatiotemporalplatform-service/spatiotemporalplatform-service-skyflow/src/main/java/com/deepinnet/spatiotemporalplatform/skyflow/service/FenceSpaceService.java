package com.deepinnet.spatiotemporalplatform.skyflow.service;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.dto.FenceSpacePageQueryDTO;
import com.deepinnet.spatiotemporalplatform.model.skyflow.FenceSpace;

/**
 * Description: 禁飞区/管制区服务
 * Date: 2025/3/13
 * Author: claude-ai
 */
public interface FenceSpaceService {
    
    /**
     * 创建禁飞区/管制区
     * @param fenceSpace 禁飞区/管制区信息
     * @return 创建的禁飞区/管制区编号
     */
    String createFenceSpace(FenceSpace fenceSpace);

    /**
     * 分页查询禁飞区/管制区列表
     * @param queryDTO 查询参数
     * @return 禁飞区/管制区列表
     */
    CommonPage<FenceSpace> pageQueryFenceSpace(FenceSpacePageQueryDTO queryDTO);

    /**
     * 根据编号查询禁飞区/管制区详情
     * @param fenceNum 禁飞区/管制区编号
     * @return 禁飞区/管制区详情
     */
    FenceSpace getFenceSpaceByFenceNum(String fenceNum);

} 