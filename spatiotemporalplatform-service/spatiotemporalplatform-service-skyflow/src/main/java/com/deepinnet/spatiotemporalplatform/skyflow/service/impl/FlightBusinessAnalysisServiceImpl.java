package com.deepinnet.spatiotemporalplatform.skyflow.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.localdata.integration.FlightBusinessAnalysisClient;
import com.deepinnet.localdata.integration.model.input.FlightBusinessAnalysisQueryDTO;
import com.deepinnet.localdata.integration.model.output.FlightBusinessAnalysisResponseDTO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightBusinessAnalysisDO;
import com.deepinnet.spatiotemporalplatform.dto.FlightBusinessAnalysisSpaQueryDTO;
import com.deepinnet.spatiotemporalplatform.skyflow.convert.FlightBusinessAnalysisConvert;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.FlightBusinessAnalysisRepository;
import com.deepinnet.spatiotemporalplatform.skyflow.service.FlightBusinessAnalysisService;
import com.deepinnet.spatiotemporalplatform.vo.FlightBusinessAnalysisVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 飞行业务分析Service实现类
 *
 * <AUTHOR> wong
 * @create 2025/01/14
 */
@Service
@RequiredArgsConstructor
public class FlightBusinessAnalysisServiceImpl implements FlightBusinessAnalysisService {

    private final FlightBusinessAnalysisRepository flightBusinessAnalysisRepository;
    
    private final FlightBusinessAnalysisConvert flightBusinessAnalysisConvert;

    private final FlightBusinessAnalysisClient flightBusinessAnalysisClient;

    private List<FlightBusinessAnalysisVO> analysisVOListCache = null;

    @Override
    public List<FlightBusinessAnalysisVO> listAllFlightBusinessAnalysis() {
        LogUtil.info("[飞行业务分析查询] 开始查询最新批次的飞行业务分析数据");
        
        try {
            // 首先查询最新的批次号
            FlightBusinessAnalysisDO latestBatch = flightBusinessAnalysisRepository.getOne(
                    Wrappers.lambdaQuery(FlightBusinessAnalysisDO.class)
                            .orderByDesc(FlightBusinessAnalysisDO::getBatchNo)
                            .last("LIMIT 1")
            );
            
            if (latestBatch == null) {
                LogUtil.info("[飞行业务分析查询] 暂无飞行业务分析数据");
                return Collections.emptyList();
            }
            
            Long latestBatchNo = latestBatch.getBatchNo();
            LogUtil.info("[飞行业务分析查询] 找到最新批次号: {}", latestBatchNo);
            
            // 查询最新批次的所有数据
            List<FlightBusinessAnalysisDO> analysisDOList = flightBusinessAnalysisRepository.list(
                    Wrappers.lambdaQuery(FlightBusinessAnalysisDO.class)
                            .eq(FlightBusinessAnalysisDO::getBatchNo, latestBatchNo)
                            .orderByDesc(FlightBusinessAnalysisDO::getId)
            );
            
            if (CollectionUtils.isEmpty(analysisDOList)) {
                LogUtil.info("[飞行业务分析查询] 最新批次暂无数据");
                return Collections.emptyList();
            }
            
            // 转换为VO
            List<FlightBusinessAnalysisVO> analysisVOList = flightBusinessAnalysisConvert.toFlightBusinessAnalysisVOList(analysisDOList);
            
            return analysisVOList;
            
        } catch (Exception e) {
            LogUtil.error("[飞行业务分析查询] 查询最新批次飞行业务分析数据失败", e);
            throw e;
        }
    }

    @Override
    public List<FlightBusinessAnalysisVO> listSpecificFlightBusinessAnalysis(FlightBusinessAnalysisSpaQueryDTO queryDTO) {

        FlightBusinessAnalysisQueryDTO flightBusinessAnalysisQueryDTO = new FlightBusinessAnalysisQueryDTO();
        flightBusinessAnalysisQueryDTO.setStartTime(queryDTO.getStartTime());
        flightBusinessAnalysisQueryDTO.setEndTime(queryDTO.getEndTime());
        FlightBusinessAnalysisResponseDTO flightBusinessAnalysis = flightBusinessAnalysisClient.getFlightBusinessAnalysis(flightBusinessAnalysisQueryDTO);
        if (flightBusinessAnalysis == null|| flightBusinessAnalysis.getData()==null) {
            LogUtil.error("外部系统返回飞行业务分析响应为空");
            return null;
        }
        if (flightBusinessAnalysis.getStatus() != 200) {
            LogUtil.error("调用外部飞行业务分析接口失败，status={}", flightBusinessAnalysis.getStatus());
            return null;
        }

        List<FlightBusinessAnalysisVO> flightBusinessAnalysisVOS = flightBusinessAnalysisConvert.dtoToFlightBusinessAnalysisVOList(flightBusinessAnalysis.getData().getBusinessTypes());
        if (CollectionUtils.isEmpty(flightBusinessAnalysisVOS)) {
            LogUtil.info("没有符合条件的数据,response:{}", JSONUtil.toJsonStr(flightBusinessAnalysis));
        }
        return flightBusinessAnalysisVOS;
    }
} 