package com.deepinnet.spatiotemporalplatform.skyflow.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.GdStaticSurfaceObstacleDO;
import org.locationtech.jts.geom.Point;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
public interface GdStaticSurfaceObstacleRepository extends IService<GdStaticSurfaceObstacleDO> {

    List<GdStaticSurfaceObstacleDO> getStaticSurfaceObstacles(List<String> bd2DGridLocationCodes, List<Point> pointList, String region);

}
