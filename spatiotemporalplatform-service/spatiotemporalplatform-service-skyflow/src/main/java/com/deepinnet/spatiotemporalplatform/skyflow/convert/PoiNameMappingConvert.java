package com.deepinnet.spatiotemporalplatform.skyflow.convert;

import com.deepinnet.spatiotemporalplatform.dal.dataobject.PoiNameMappingDO;
import com.deepinnet.spatiotemporalplatform.model.skyflow.PoiNameMapping;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * PoiNameMappingConvert
 * Author: chenkaiyang
 * Date: 2025/3/21
 */
@Mapper(componentModel = "spring")
public interface PoiNameMappingConvert {
    List<PoiNameMapping> toDomainList(List<PoiNameMappingDO> records);
}
