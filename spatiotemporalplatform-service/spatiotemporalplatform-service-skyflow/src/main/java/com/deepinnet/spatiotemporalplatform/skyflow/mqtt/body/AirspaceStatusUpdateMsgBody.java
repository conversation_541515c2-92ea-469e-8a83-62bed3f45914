package com.deepinnet.spatiotemporalplatform.skyflow.mqtt.body;

import lombok.Data;

/**
 * C<PERSON>
 * Date 2025-07-14
 **/

@Data
public class AirspaceStatusUpdateMsgBody {

    //目标空域标识
    private String asid;

    //0预激活，1激活，2预释放，3空闲，4回收5管制，6监视，7报告
    private Integer status;

    // 区域边界点序列wkt
    private String boundary;

    //区域的顶高
    private Integer top;

    // 区域的底高
    private Integer bot;


}
