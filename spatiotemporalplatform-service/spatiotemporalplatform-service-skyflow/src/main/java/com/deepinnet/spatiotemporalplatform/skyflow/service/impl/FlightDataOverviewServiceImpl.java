package com.deepinnet.spatiotemporalplatform.skyflow.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightDataOverviewDO;
import com.deepinnet.spatiotemporalplatform.skyflow.convert.FlightDataOverviewConvert;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.FlightDataOverviewRepository;
import com.deepinnet.spatiotemporalplatform.skyflow.service.FlightDataOverviewService;
import com.deepinnet.spatiotemporalplatform.vo.FlightDataOverviewVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 飞行数据概览Service实现类
 *
 * <AUTHOR> wong
 * @create 2025/01/14
 */
@Service
@RequiredArgsConstructor
public class FlightDataOverviewServiceImpl implements FlightDataOverviewService {

    private final FlightDataOverviewRepository flightDataOverviewRepository;
    
    private final FlightDataOverviewConvert flightDataOverviewConvert;

    @Override
    public FlightDataOverviewVO getLatestFlightDataOverview() {
        LogUtil.info("[飞行数据概览查询] 开始查询最新的飞行数据概览");
        
        try {
            // 根据batchNo倒序查询最新的一条记录
            FlightDataOverviewDO latestOverview = flightDataOverviewRepository.getOne(
                    Wrappers.lambdaQuery(FlightDataOverviewDO.class)
                            .orderByDesc(FlightDataOverviewDO::getBatchNo)
                            .last("limit 1")
            );
            
            if (latestOverview == null) {
                LogUtil.info("[飞行数据概览查询] 暂无飞行数据概览");
                return null;
            }
            
            // 转换为VO
            FlightDataOverviewVO overviewVO = flightDataOverviewConvert.toFlightDataOverviewVO(latestOverview);
            
            LogUtil.info("[飞行数据概览查询] 查询成功，批次号: {}", latestOverview.getBatchNo());
            return overviewVO;
            
        } catch (Exception e) {
            LogUtil.error("[飞行数据概览查询] 查询最新飞行数据概览失败", e);
            throw e;
        }
    }
} 