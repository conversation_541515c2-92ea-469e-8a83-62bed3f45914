package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.WeatherGridDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.WeatherGridMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.condition.WeatherGridQueryCondition;
import com.deepinnet.spatiotemporalplatform.skyflow.error.BizErrorCode;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.WeatherGridRepository;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 气象网格表 仓储实现
 */
@Service
public class WeatherGridRepositoryImpl extends ServiceImpl<WeatherGridMapper, WeatherGridDO> implements WeatherGridRepository {

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    @Override
    public int upsertBatch(List<WeatherGridDO> list) {
        if(CollectionUtils.isEmpty(list)) {
            return 0;
        }

        return getBaseMapper().upsertBatch(list);
    }

    @Override
    public List<WeatherGridDO> listByCondition(WeatherGridQueryCondition condition) {
        return getBaseMapper().list(condition.getFactor().getCode(), condition.getBounds());
    }
}