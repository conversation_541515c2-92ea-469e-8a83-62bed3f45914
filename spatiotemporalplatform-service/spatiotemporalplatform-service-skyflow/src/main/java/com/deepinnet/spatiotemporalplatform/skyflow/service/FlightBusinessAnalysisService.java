package com.deepinnet.spatiotemporalplatform.skyflow.service;

import com.deepinnet.spatiotemporalplatform.dto.FlightBusinessAnalysisSpaQueryDTO;
import com.deepinnet.spatiotemporalplatform.vo.FlightBusinessAnalysisVO;

import java.util.List;

/**
 * 飞行业务分析Service接口
 *
 * <AUTHOR> wong
 * @create 2025/01/14
 */
public interface FlightBusinessAnalysisService {

    /**
     * 查询所有飞行业务分析数据
     *
     * @return 飞行业务分析数据列表
     */
    List<FlightBusinessAnalysisVO> listAllFlightBusinessAnalysis();

    /**
     * 查询指定时间段的飞行业务分析数据
     * @param queryDTO
     * @return
     */
    List<FlightBusinessAnalysisVO> listSpecificFlightBusinessAnalysis(FlightBusinessAnalysisSpaQueryDTO queryDTO);
}