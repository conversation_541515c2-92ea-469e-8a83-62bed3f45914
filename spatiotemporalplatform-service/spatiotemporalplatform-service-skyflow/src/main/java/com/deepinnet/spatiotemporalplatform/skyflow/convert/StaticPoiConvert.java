package com.deepinnet.spatiotemporalplatform.skyflow.convert;

import com.deepinnet.spatiotemporalplatform.dal.dataobject.StaticPoiDO;
import com.deepinnet.spatiotemporalplatform.model.staticpoi.StaticPoi;
import org.springframework.util.CollectionUtils;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 静态核心POI对象转换工具类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-10
 */
public class StaticPoiConvert {

    /**
     * DO 转领域模型
     */
    public static StaticPoi toModel(StaticPoiDO staticPoiDO) {
        if (staticPoiDO == null) {
            return null;
        }

        StaticPoi model = new StaticPoi();
        model.setId(staticPoiDO.getId());
        model.setPoiId(staticPoiDO.getPoiId());
        model.setPoiName(staticPoiDO.getPoiName());
        model.setLongitude(staticPoiDO.getLongitude());
        model.setLatitude(staticPoiDO.getLatitude());
        model.setCoordinateSystem(staticPoiDO.getCoordinateSystem());
        model.setGmtCreated(staticPoiDO.getGmtCreated());
        model.setGmtModified(staticPoiDO.getGmtModified());
        return model;
    }

    /**
     * DO 列表转领域模型列表
     */
    public static List<StaticPoi> toModelList(List<StaticPoiDO> staticPoiDOList) {
        if (CollectionUtils.isEmpty(staticPoiDOList)) {
            return Collections.emptyList();
        }

        return staticPoiDOList.stream()
                .map(StaticPoiConvert::toModel)
                .collect(Collectors.toList());
    }

    /**
     * 领域模型转 DO
     */
    public static StaticPoiDO toDO(StaticPoi staticPoi) {
        if (staticPoi == null) {
            return null;
        }

        StaticPoiDO staticPoiDO = new StaticPoiDO();
        staticPoiDO.setId(staticPoi.getId());
        staticPoiDO.setPoiId(staticPoi.getPoiId());
        staticPoiDO.setPoiName(staticPoi.getPoiName());
        staticPoiDO.setLongitude(staticPoi.getLongitude());
        staticPoiDO.setLatitude(staticPoi.getLatitude());
        staticPoiDO.setCoordinateSystem(staticPoi.getCoordinateSystem());
        return staticPoiDO;
    }
} 