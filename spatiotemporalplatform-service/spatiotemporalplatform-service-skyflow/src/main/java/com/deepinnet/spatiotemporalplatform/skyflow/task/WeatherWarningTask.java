package com.deepinnet.spatiotemporalplatform.skyflow.task;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.*;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.base.config.TenantConfig;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.WeatherWarningDataRepository;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.*;
import com.deepinnet.spatiotemporalplatform.model.skyflow.*;
import com.deepinnet.spatiotemporalplatform.skyflow.enums.*;
import com.deepinnet.spatiotemporalplatform.skyflow.error.BizErrorCode;
import com.deepinnet.spatiotemporalplatform.skyflow.flush.*;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.*;
import com.deepinnet.spatiotemporalplatform.skyflow.util.WktGridUtil;
import com.deepinnet.spatiotemporalplatform.skyflow.util.beidou.*;
import com.deepinnet.spatiotemporalplatform.skyflow.util.weather.*;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.*;
import org.apache.http.client.methods.*;
import org.apache.http.impl.client.*;
import org.locationtech.jts.geom.Polygon;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.*;
import java.io.*;
import java.nio.file.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;

/**
 * <AUTHOR> wong
 * @create 2025/3/10 17:51
 * @Description 天气预警定时任务
 */
@Component
@Profile({"local", "test", "alicloud", "demo", "sz-lg-gov"})
public class WeatherWarningTask {

    @Resource
    private WeatherWarningRepository weatherWarningRepository;

    @Resource
    private AirspaceRepository airspaceRepository;

    @Resource
    private CommonDataService commonDataService;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private WeatherWarningDataRepository weatherWarningDataRepository;

    @Value("${file.attachment.dir}")
    private String weatherDataFilePath;

    @Value("${file.attachment.url}")
    private String urlPrefix;

    @Value("${weather.node.url}")
    private String weatherNodeUrl;

    @Resource
    private TenantConfig tenantConfig;

    // private ExecutorService downloadExecutor;

    private static final double GCJ02_EARTH_RADIUS = 6378245.0;

//    @PostConstruct
//    public void init() {
//        // 初始化线程池用于下载气象数据文件
//        //downloadExecutor = Executors.newFixedThreadPool(5);
//
//        // 确保目录存在
//        try {
//            if (!Files.exists(Paths.get(weatherDataFilePath))) {
//                Files.createDirectories(Paths.get(weatherDataFilePath));
//            }
//            LogUtil.info("气象数据文件保存目录创建成功: {}", weatherDataFilePath);
//        } catch (IOException e) {
//            LogUtil.error("创建气象数据文件保存目录失败: {}", weatherDataFilePath, e);
//        }
//    }

    /**
     * 天气预警定时任务
     */
    @Scheduled(fixedRate = 30, timeUnit = TimeUnit.MINUTES)
    public void process() {
        List<AirspaceDO> airspaceDOs = airspaceRepository.list();
        if (CollectionUtils.isEmpty(airspaceDOs)) {
            LogUtil.info("没有空域数据，任务终止.");
            return;
        }

        List<WeatherWarningDO> weatherWarningDOList = weatherWarningRepository.list(Wrappers.lambdaQuery(WeatherWarningDO.class)
                .ge(WeatherWarningDO::getWarningTime, TimeUtil.getStartOfDay())
                .le(WeatherWarningDO::getWarningTime, TimeUtil.getEndOfDay()));

        // 关闭昨天所有的天气预警
        closeYesterdayWeatherWarnings(airspaceDOs);

        // 依次处理每个空域
        for (AirspaceDO airSpace : airspaceDOs) {
            try {
                processAirspace(airSpace, weatherWarningDOList);
            } catch (Exception e) {
                LogUtil.error("处理空域[{}]天气数据异常，跳过该空域", airSpace.getCode(), e);
            }
        }
    }

    /**
     * 处理单个空域的天气数据
     *
     * @param airSpace             空域信息
     * @param weatherWarningDOList 现有预警列表
     */
    private void processAirspace(AirspaceDO airSpace, List<WeatherWarningDO> weatherWarningDOList) {
        // 初始化预警列表
        List<WeatherWarningDO> newWeatherWarnings = Lists.newArrayList();
        List<WeatherWarningDO> updateWeatherWarnings = Lists.newArrayList();

        // 使用WktGridUtil拆分空域的WKT为较小的网格
        String airspaceWkt = "POLYGON((114.23853060366812 22.68965680603892,114.23852274478584 22.689653294825156,114.23564171058463 22.68839546916248,114.2355481550816 22.688364989081656,114.23256424356266 22.687700472059607,114.23247426520821 22.687688538798447,114.23238346536742 22.687692351069902,114.22927093558472 22.688095810764338,114.22919141924079 22.6881125205373,114.22911602891544 22.68814132220172,114.22904688369137 22.688181406194914,114.22644880779052 22.689985081827757,114.22642093410619 22.690005961218706,114.22416079054501 22.691830512114972,114.2215662759511 22.69384496586063,114.2215354670953 22.693871049782658,114.21909173837517 22.696125554622256,114.21902445116247 22.696203122142744,114.21897713684318 22.696292493806602,114.21799964020992 22.698808000322416,114.21797835700238 22.698881870300042,114.21797082205293 22.698958013944555,114.21794666028907 22.70228830269691,114.21792943188305 22.704663593769396,114.2179381381428 22.704751681417747,114.21796519955151 22.704836500615283,114.21800957618626 22.70491479180196,114.2180695627011 22.704983546280037,114.218142853859 22.70504012183837,114.21822663311916 22.70508234429341,114.2183176808755 22.705108591044375,114.21841249818677 22.70511785343126,114.21850744124298 22.705109775498894,114.21859886140014 22.70508466767716,114.21868324540111 22.705043494851406,114.21875735039279 22.704987839281593,114.21881832855082 22.704919839795163,114.21886383652146 22.70484210959081,114.21889212547507 22.704757635812207,114.21890210831003 22.704669664751382,114.21891931991125 22.702294374825847,114.21894289800348 22.699041381673723,114.21985504612066 22.69669400626386,114.22220645332622 22.69452466512015,114.22478854872419 22.69251984886882,114.22479565504231 22.692514222607148,114.2270458052509 22.690697734666646,114.22952937587688 22.688973551215405,114.23242681640515 22.688597973913215,114.23527272285273 22.68923175888835,114.238104727451 22.690468181847663,114.24090470034514 22.691747957862198,114.24099393911729 22.691779079560053,114.24108800663612 22.691793449067788,114.24118328790728 22.691790514164765,114.24127612128935 22.691770387635238,114.2413629392141 22.691733842934457,114.24144040529119 22.69168228446468,114.24150554252778 22.69161769360318,114.24155584773497 22.69154255255692,114.24158938772418 22.69145974897025,114.24160487359659 22.69137246495185,114.24160171027118 22.691284054786117,114.24158001934833 22.69119791602856,114.24154063443152 22.691117358939298,114.24148506908716 22.69104547927223,114.24141545867519 22.690985039308426,114.24133447828618 22.69093836170544,114.23853060366812 22.68965680603892))";
        List<String> gridWktList = WktGridUtil.splitIntoGrids(airspaceWkt, 1.0);
        LogUtil.info("空域[{}]拆分为{}个网格", airSpace.getCode(), gridWktList.size());

        if (CollectionUtils.isEmpty(gridWktList)) {
            LogUtil.warn("空域[{}]拆分网格为空，跳过处理", airSpace.getCode());
            return;
        }

        // 处理所有网格，收集气象数据
        WeatherDataSummary dataSummary = processWeatherGrids(airSpace, gridWktList);
        if (dataSummary == null) {
            LogUtil.warn("空域[{}]气象数据处理失败，跳过后续处理", airSpace.getCode());
            return;
        }

        // 根据气象数据构建预警
        buildWarnings(airSpace, weatherWarningDOList, dataSummary, newWeatherWarnings, updateWeatherWarnings);

        // 持久化预警数据
        persistWarnings(dataSummary.getWeatherDataFileName(), newWeatherWarnings, updateWeatherWarnings);
    }

    /**
     * 处理所有网格，收集气象数据
     *
     * @param airSpace    空域信息
     * @param gridWktList 网格WKT列表
     * @return 气象数据汇总
     */
    private WeatherDataSummary processWeatherGrids(AirspaceDO airSpace, List<String> gridWktList) {
        WeatherDataSummary summary = new WeatherDataSummary();
        boolean hasValidData = false;

        final int totalGrids = gridWktList.size();
        List<String> allFileNames = new ArrayList<>();

        // 生成时间戳和日期
        long timestamp = System.currentTimeMillis();

        // 循环处理每个网格
        for (int i = 0; i < gridWktList.size(); i++) {
            final String gridWkt = gridWktList.get(i);

            try {
                String weatherData = invokeRemoteGetWeatherData(gridWkt);
                if (StringUtils.isBlank(weatherData)) {
                    LogUtil.info("高德返回气象数据响应为空，入参为：{}", JSONUtil.toJsonStr(gridWkt));
                    continue;
                }
                JSONObject jsonObject = JSONUtil.parseObj(weatherData);
                String dataUrl = (String) jsonObject.get("url");
                Map<String, LocationWeatherData> weatherDataMap = downloadAndDeserializeWeatherData(dataUrl);
                LogUtil.info("气象定时任务流式下载并反序列化成功，网格{}：{}", i, gridWkt.substring(0, Math.min(50, gridWkt.length())) + "...");

                // 处理网格气象数据，更新汇总信息
                if (processWeatherData(airSpace, weatherDataMap, summary)) {
                    hasValidData = true;

                    // 为当前网格生成4种类型的气象数据文件
                    List<String> gridFileNames = generateWeatherDataFilesForSingleGrid(airSpace, weatherDataMap, totalGrids, i, summary, timestamp);
                    allFileNames.addAll(gridFileNames);
                }

            } catch (Exception e) {
                LogUtil.error("处理网格[{}]气象数据异常", gridWkt.substring(0, Math.min(30, gridWkt.length())), e);
                // 继续处理下一个网格
            }
        }

        if (hasValidData) {
            LogUtil.info("空域[{}]气象数据处理完成，最大降雨强度:{}mm/h，最高温度:{}℃，最低温度:{}℃，最大风速:{}m/s，共生成{}个文件",
                    airSpace.getCode(), summary.maxRainIntensity, summary.maxTemperature,
                    summary.minTemperature, summary.maxWindSpeed, allFileNames.size());

            summary.setWeatherDataFileName(allFileNames);
            return summary;
        } else {
            LogUtil.warn("空域[{}]未获取到有效气象数据", airSpace.getCode());
            return null;
        }
    }

    /**
     * 为单个网格生成4种类型的气象数据文件
     *
     * @param airSpace       空域信息
     * @param weatherDataMap 单个网格的气象数据
     * @param totalGrids     总网格数
     * @param gridIndex      当前网格索引
     * @param summary        气象数据汇总
     * @return 生成的文件名列表
     */
    private List<String> generateWeatherDataFilesForSingleGrid(AirspaceDO airSpace, Map<String, LocationWeatherData> weatherDataMap,
                                                               int totalGrids, int gridIndex, WeatherDataSummary summary, Long timestamp) {
        List<String> fileNames = new ArrayList<>();
        try {
            // 获取网格中心点坐标
            Set<String> gridCodes = weatherDataMap.keySet();
            Map<String, GridCenterCoordinate> gridCenterMap = WeatherDataProcessUtil.getGridCenterFromNode(weatherNodeUrl, gridCodes);

            String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());

            // 使用工具类生成标准气象文件（温度、降雨、风力）
//            List<String> standardFileNames = WeatherDataProcessUtil.generateStandardWeatherFiles(
//                    airSpace.getCode(), weatherDataFilePath, urlPrefix,
//                    weatherDataMap, gridCenterMap,
//                    totalGrids, gridIndex, timestamp, dateStr,
//                    airSpace.getMaxHeight());
//            fileNames.addAll(standardFileNames);

            // 4.生成预警文件
            String warningFileName = WeatherDataProcessUtil.generateFileName(airSpace.getCode(), "预警", dateStr, totalGrids, gridIndex, timestamp);
            generateWarningFile(warningFileName, airSpace, weatherDataMap, gridCenterMap, summary);
            fileNames.add(warningFileName);

            LogUtil.info("成功为网格{}生成{}个气象数据文件", gridIndex, fileNames.size());
        } catch (Exception e) {
            LogUtil.error("为网格{}生成气象数据文件失败", gridIndex, e);
            throw e;
        }

        return fileNames;
    }


    /**
     * 生成预警文件
     *
     * @param fileName          文件名
     * @param airSpace          空域信息
     * @param allWeatherDataMap 所有气象数据
     * @param gridCenterMap     网格中心点坐标
     * @param summary           气象数据汇总
     */
    private void generateWarningFile(String fileName, AirspaceDO airSpace, Map<String, LocationWeatherData> allWeatherDataMap,
                                     Map<String, GridCenterCoordinate> gridCenterMap, WeatherDataSummary summary) {
        try {
            JSONObject warningData = new JSONObject();
            warningData.set("name", "meteorology");

            JSONObject data = new JSONObject();

            // 计算空域中心点
            JSONObject center = new JSONObject();
            // 这里可以根据实际需求计算空域中心点，暂时使用第一个网格的坐标
            if (!gridCenterMap.isEmpty()) {
                GridCenterCoordinate firstCenter = gridCenterMap.values().iterator().next();
                center.set("lng", firstCenter.getLngDegree());
                center.set("lat", firstCenter.getLatDegree());
                center.set("alt", airSpace.getMaxHeight());
            }
            data.set("center", center);

            JSONArray pointArray = new JSONArray();

            for (Map.Entry<String, LocationWeatherData> entry : allWeatherDataMap.entrySet()) {
                String locationCode = entry.getKey();
                LocationWeatherData locationData = entry.getValue();

                if (locationData != null && locationData.getBaseInfo() != null && gridCenterMap.containsKey(locationCode)) {
                    GridCenterCoordinate gridCenter = gridCenterMap.get(locationCode);

                    JSONObject pointData = new JSONObject();
                    JSONObject location = new JSONObject();
                    location.set("lng", gridCenter.getLngDegree());
                    location.set("lat", gridCenter.getLatDegree());
                    location.set("code", locationCode);
                    location.set("alt", airSpace.getMaxHeight());
                    pointData.set("location", location);

                    // 判断是否有预警 (根据阈值判断)
                    int meteorologyFlow = 0;
                    if (hasWarning(locationData, airSpace)) {
                        meteorologyFlow = 1;
                    }
                    pointData.set("meteorologyflow", meteorologyFlow);

                    pointArray.add(pointData);
                }
            }

            data.set("point", pointArray);
            warningData.set("data", data);

            // 保存到文件
            File file = new File(weatherDataFilePath, fileName);
            Files.write(file.toPath(), warningData.toString().getBytes());
            LogUtil.info("预警数据文件已保存: {}", file.getAbsolutePath());

        } catch (Exception e) {
            LogUtil.error("生成预警文件失败: {}", fileName, e);
        }
    }

    /**
     * 获取风向
     *
     * @param airSpace     空域信息
     * @param locationData 位置气象数据
     * @return 风向角度
     */
    private double getWindDirection(AirspaceDO airSpace, LocationWeatherData locationData) {
        double maxHeight = airSpace.getMaxHeight();
        double minHeight = airSpace.getMinHeight();

        double minAltitude;
        double maxAltitude;

        if (Math.floor(minHeight / 10.0) == Math.floor(maxHeight / 10.0)) {
            minAltitude = maxAltitude = Math.ceil(maxHeight / 10.0) * 10;
        } else {
            minAltitude = Math.floor(minHeight / 10.0) * 10;
            maxAltitude = Math.ceil(maxHeight / 10.0) * 10;
        }

        // 从符合条件的天气层中获取风向，取第一个有效值
        return locationData.getWeatherLayers().stream()
                .filter(layer -> layer.getAltitude() >= minAltitude && layer.getAltitude() <= maxAltitude)
                .mapToDouble(WeatherLayer::getWindDirection)
                .findFirst()
                .orElse(0.0);
    }

    /**
     * 判断是否有预警
     *
     * @param locationData 位置气象数据
     * @param airSpace     空域信息
     * @return 是否有预警
     */
    private boolean hasWarning(LocationWeatherData locationData, AirspaceDO airSpace) {
        if (locationData.getBaseInfo() == null) {
            return false;
        }

        // 降雨预警
        if (locationData.getBaseInfo().getPrecipitationIntensity() > 50.0) {
            return true;
        }

        // 温度预警
        double temperature = locationData.getBaseInfo().getTemperature();
        if (temperature > 50.0 || temperature < -50.0) {
            return true;
        }

        // 风力预警
        Double windSpeed = calculateMaxWindSpeed(airSpace, locationData);
        if (windSpeed != null && windSpeed > 20) {
            return true;
        }

        return false;
    }

    /**
     * 处理单个网格的气象数据，更新气象数据汇总
     *
     * @param airSpace       空域信息
     * @param weatherDataMap 气象数据映射
     * @param summary        气象数据汇总
     * @return 是否成功处理
     */
    private boolean processWeatherData(AirspaceDO airSpace, Map<String, LocationWeatherData> weatherDataMap, WeatherDataSummary summary) {
        if (MapUtil.isEmpty(weatherDataMap)) {
            return false;
        }

        boolean processedAnyData = false;

        for (Map.Entry<String, LocationWeatherData> entry : weatherDataMap.entrySet()) {
            String locationCode = entry.getKey();
            LocationWeatherData locationData = entry.getValue();
            if (locationData == null || locationData.getBaseInfo() == null) {
                LogUtil.warn("当前网格={}，对应的基础地理信息为空", locationCode);
                continue;
            }

            // 根据空域的高度，计算出来需要哪些网格的风力数据
            Double windSpeed = calculateMaxWindSpeed(airSpace, locationData);
            if (windSpeed == null) {
                continue;
            }

            double rainIntensity = locationData.getBaseInfo().getPrecipitationIntensity();
            double temperature = locationData.getBaseInfo().getTemperature();

            // 记录最大降雨强度
            if (rainIntensity > summary.maxRainIntensity) {
                summary.maxRainIntensity = rainIntensity;
            }
            // 记录最高温度
            if (temperature > summary.maxTemperature) {
                summary.maxTemperature = temperature;
            }
            // 记录最低温度
            if (temperature < summary.minTemperature) {
                summary.minTemperature = temperature;
            }
            // 记录最大风速
            if (windSpeed > summary.maxWindSpeed) {
                summary.maxWindSpeed = windSpeed;
            }

            processedAnyData = true;
        }

        return processedAnyData;
    }

    /**
     * 根据气象数据构建预警
     *
     * @param airSpace              空域信息
     * @param weatherWarningDOList  现有预警列表
     * @param summary               气象数据汇总
     * @param newWeatherWarnings    新增预警列表
     * @param updateWeatherWarnings 更新预警列表
     */
    private void buildWarnings(AirspaceDO airSpace, List<WeatherWarningDO> weatherWarningDOList,
                               WeatherDataSummary summary, List<WeatherWarningDO> newWeatherWarnings,
                               List<WeatherWarningDO> updateWeatherWarnings) {
        // 降雨预警
        boolean gdExistHeavyRainLocations = false;
        String rainfallWarningContent = null;
        if (summary.maxRainIntensity > 50.0) {
            gdExistHeavyRainLocations = true;
            rainfallWarningContent = StringUtils.replace("降雨量为%smm/h，大于50mm/h", "%s", String.valueOf(summary.maxRainIntensity));
        }

        // 温度预警
        boolean gdExistTemperatureExceedThreshold = false;
        String temperatureWarningContent = null;
        if (summary.maxTemperature > 50.0) {
            gdExistTemperatureExceedThreshold = true;
            temperatureWarningContent = StringUtils.replace("温度为%s℃，大于50℃", "%s", String.valueOf(summary.maxTemperature));
        } else if (summary.minTemperature < -50.0) {
            gdExistTemperatureExceedThreshold = true;
            temperatureWarningContent = StringUtils.replace("温度为%s℃，小于-50℃", "%s", String.valueOf(summary.minTemperature));
        }

        // 风力预警
        boolean gdExistWindExceedThreshold = false;
        String windWarningContent = null;
        if (summary.maxWindSpeed > 20) {
            gdExistWindExceedThreshold = true;
            windWarningContent = StringUtils.replace("风速为%sm/s，大于20m/s", "%s", String.valueOf(summary.maxWindSpeed));
        }

        // 构建预警
        buildNeedSaveOrUpdateWeatherWarnings(weatherWarningDOList, gdExistHeavyRainLocations, airSpace,
                WeatherWarningTypeEnum.RAINFALL, rainfallWarningContent, newWeatherWarnings, updateWeatherWarnings);

        buildNeedSaveOrUpdateWeatherWarnings(weatherWarningDOList, gdExistTemperatureExceedThreshold, airSpace,
                WeatherWarningTypeEnum.TEMPERATURE, temperatureWarningContent, newWeatherWarnings, updateWeatherWarnings);

        buildNeedSaveOrUpdateWeatherWarnings(weatherWarningDOList, gdExistWindExceedThreshold, airSpace,
                WeatherWarningTypeEnum.WIND_SPEED, windWarningContent, newWeatherWarnings, updateWeatherWarnings);
    }

    /**
     * 持久化预警数据
     *
     * @param newWeatherWarnings    新增预警列表
     * @param updateWeatherWarnings 更新预警列表
     */
    private void persistWarnings(List<String> fileNames, List<WeatherWarningDO> newWeatherWarnings, List<WeatherWarningDO> updateWeatherWarnings) {
        transactionTemplate.executeWithoutResult(action -> {
            if (CollectionUtils.isNotEmpty(newWeatherWarnings)) {
                List<WeatherWarningDataDO> weatherWarningDataDOs = Lists.newArrayList();
                newWeatherWarnings.forEach(e -> {
                    List<String> urls = buildFileUrl(fileNames);
                    WeatherWarningDataDO weatherWarningDataDO = buildWeatherWarningRequest(e, JSONUtil.toJsonStr(urls));
                    weatherWarningDataDOs.add(weatherWarningDataDO);
                });

                weatherWarningDataRepository.saveBatch(weatherWarningDataDOs);
                weatherWarningRepository.saveBatch(newWeatherWarnings);
            }

            if (CollectionUtils.isNotEmpty(updateWeatherWarnings)) {
                weatherWarningRepository.updateBatchById(updateWeatherWarnings);
            }
        });
    }

    private List<String> buildFileUrl(List<String> fileNames) {
        List<String> urls = Lists.newArrayList();
        fileNames.forEach(name -> {
            String url = urlPrefix + name;
            urls.add(url);
        });
        return urls;
    }

    /**
     * 使用流式处理下载、解压和反序列化天气数据
     *
     * @param url 数据下载URL
     * @return 反序列化后的天气数据
     */
    private Map<String, LocationWeatherData> downloadAndDeserializeWeatherData(String url) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet httpGet = new HttpGet(url);
            try (CloseableHttpResponse response = httpClient.execute(httpGet);
                 InputStream inputStream = response.getEntity().getContent();
                 BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream);
                 GZIPInputStream gzipInputStream = new GZIPInputStream(bufferedInputStream)) {

                // 直接从流中反序列化，而不是先保存到磁盘
                return WeatherDataProcessUtil.deserializeDataFromStream(gzipInputStream);
            }
        } catch (IOException e) {
            LogUtil.error("下载或解析气象数据失败", e);
            throw new BizException(BizErrorCode.INVOKE_WEATHER_CLIENT_ERROR.getCode(),
                    BizErrorCode.INVOKE_WEATHER_CLIENT_ERROR.getDesc());
        }
    }

    private void closeYesterdayWeatherWarnings(List<AirspaceDO> airspaceDOs) {
        List<String> airSpaceCodes = airspaceDOs.stream()
                .map(AirspaceDO::getCode)
                .collect(Collectors.toList());

        weatherWarningRepository.update(Wrappers.lambdaUpdate(WeatherWarningDO.class)
                .in(WeatherWarningDO::getAirspaceCode, airSpaceCodes)
                .ge(WeatherWarningDO::getWarningTime, TimeUtil.getStartOfYesterday())
                .le(WeatherWarningDO::getWarningTime, TimeUtil.getEndOfYesterday())
                .set(WeatherWarningDO::getStatus, WarningStatusEnum.ENDED.getCode()));
    }

    private WeatherWarningDataDO buildWeatherWarningRequest(WeatherWarningDO e, String url) {
        WeatherWarningDataDO weatherWarningDataDO = new WeatherWarningDataDO();
        weatherWarningDataDO.setWarningNo(e.getWarningNo());
        weatherWarningDataDO.setUrl(url);
        weatherWarningDataDO.setRequestTime(new Date());
        weatherWarningDataDO.setGmtCreated(new Date());
        weatherWarningDataDO.setGmtModified(new Date());
        return weatherWarningDataDO;
    }

    private String invokeRemoteGetWeatherData(String wkt) {
        WeatherGridPostBody queryPostBody = new WeatherGridPostBody();
        queryPostBody.setBounds(wkt);
        queryPostBody.setLevel(7);
        Result<WeatherGridQueryResponse> result = queryGdWeatherGridData(queryPostBody);
        if (!result.isSuccess()) {
            LogUtil.error("请求气象网格数据失败，入参为：{}", JSONUtil.toJsonStr(queryPostBody));
            throw new BizException(BizErrorCode.INVOKE_WEATHER_CLIENT_ERROR.getCode(), BizErrorCode.INVOKE_WEATHER_CLIENT_ERROR.getDesc());
        }

        WeatherGridQueryResponse response = result.getData();
        if (ObjectUtils.isEmpty(response) || StringUtils.isBlank(response.getData())) {
            LogUtil.warn("请求气象数据，返回的结果为空，入参为：{}", JSONUtil.toJsonStr(queryPostBody));
            return null;
        }

        return response.getData();
    }

    private Double calculateMaxWindSpeed(AirspaceDO airSpace, LocationWeatherData locationData) {
        double maxHeight = airSpace.getMaxHeight();
        double minHeight = airSpace.getMinHeight();

        // 计算出高度区间
        double minAltitude;
        double maxAltitude;

        // 特殊情况判断: 当两个高度落在同一个高度区间时，取向上取整高度
        if (Math.floor(minHeight / 10.0) == Math.floor(maxHeight / 10.0)) {
            minAltitude = maxAltitude = Math.ceil(maxHeight / 10.0) * 10;
        } else {
            // 向下取整
            minAltitude = Math.floor(minHeight / 10.0) * 10;
            // 向上取整
            maxAltitude = Math.ceil(maxHeight / 10.0) * 10;
        }

        // 筛选符合条件的WeatherLayer
        // 如果没有找到数据，则抛异常
        OptionalDouble maxWindOption = locationData.getWeatherLayers().stream()
                .filter(layer -> layer.getAltitude() >= minAltitude && layer.getAltitude() <= maxAltitude)
                .mapToDouble(WeatherLayer::getHorizontalWindSpeed)
                .max();

        boolean isPresent = maxWindOption.isPresent();
        if (isPresent) {
            return maxWindOption.getAsDouble();
        }

        LogUtil.error("当前计算最大风速失败，数据缺失，airSpace={}，数据为：{}", airSpace.getCode(), locationData.getLocationCode());
        return null;
    }

    private Map<String, Polygon> generateGridPolygon(Map<String, LocationWeatherData> weatherDataMap) {
        // 请求node服务获取网格的wkt，后面需要做空间计算
        Set<String> gridCodeSet = weatherDataMap.keySet();
        List<String> gridCodes = new ArrayList<>(gridCodeSet);
        // 获取网格中心点
        Map<String, GridCenterCoordinate> gridCenterFromNode = GenerateGridPolygonService.getGridCenterFromNode(gridCodes);
        // 生成网格的Polygon对象
        return GenerateGridPolygonService.generatePolygonGcj02(gridCenterFromNode);
    }

    private void buildNeedSaveOrUpdateWeatherWarnings(List<WeatherWarningDO> weatherWarningDOList, boolean gdExistWarning, AirspaceDO airspace,
                                                      WeatherWarningTypeEnum typeEnum, String warningDesc, List<WeatherWarningDO> newWeatherWarnings, List<WeatherWarningDO> updateWeatherWarnings) {

        List<WeatherWarningDO> weatherWarningList = weatherWarningDOList.stream()
                .filter(w -> StringUtils.equals(w.getAirspaceCode(), airspace.getCode()) && Objects.equals(w.getType(), typeEnum.getCode()))
                .sorted(Comparator.comparing(WeatherWarningDO::getWarningTime).reversed())
                .collect(Collectors.toList());
        WeatherWarningDO weatherWarning = null;
        boolean existWarning = !CollectionUtils.isEmpty(weatherWarningList);
        if (existWarning) {
            weatherWarning = weatherWarningList.get(0);
        }

        // 高德数据的降雨量超过阈值
        if (gdExistWarning) {
            // 当前航道不存在降雨量报警，直接新增气象预警
            if (!existWarning) {
                WeatherWarningDO weatherWarningDO = buildWeatherWarning(airspace.getCode(), airspace.getName(), typeEnum.getCode(), WeatherWarningStatusEnum.WARNING.getCode(), warningDesc);
                newWeatherWarnings.add(weatherWarningDO);
            } else {
                // 当前航道已经存在报警，且是预警中，这次还是预警了，更新预警内容
                if (StringUtils.equals(weatherWarning.getStatus(), WeatherWarningStatusEnum.WARNING.getCode())) {
                    weatherWarning.setContent(warningDesc);
                    weatherWarning.setTitle(warningDesc);
                    updateWeatherWarnings.add(weatherWarning);
                }

                // 当前航道已经存在报警，且是预警结束，新增一条
                if (StringUtils.equals(weatherWarning.getStatus(), WeatherWarningStatusEnum.ENDED.getCode())) {
                    WeatherWarningDO weatherWarningDO = buildWeatherWarning(airspace.getCode(), airspace.getName(), typeEnum.getCode(), WeatherWarningStatusEnum.WARNING.getCode(), warningDesc);
                    newWeatherWarnings.add(weatherWarningDO);
                }
            }
        } else {
            // 高德降雨量没超过阈值但是当前航道存在预警，应该结束掉该预警
            if (existWarning) {
                weatherWarning.setStatus(WeatherWarningStatusEnum.ENDED.getCode());
                updateWeatherWarnings.add(weatherWarning);
            }
        }
    }

    private WeatherWarningDO buildWeatherWarning(String airWayCode, String airwayName, String type, String status, String content) {
        WeatherWarningDO weatherWarningDO = new WeatherWarningDO();
        weatherWarningDO.setWarningNo(IdUtil.getSnowflakeNextIdStr());
        weatherWarningDO.setAirspaceCode(airWayCode);
        weatherWarningDO.setAirspaceName(airwayName);
        weatherWarningDO.setType(type);
        weatherWarningDO.setWarningTime(System.currentTimeMillis());
        weatherWarningDO.setStatus(status);
        weatherWarningDO.setContent(content);
        weatherWarningDO.setTitle(content);
        weatherWarningDO.setTenantId(tenantConfig.getTenantId());
        return weatherWarningDO;
    }

    // 根据 GCJ02 近似，把指定米数转换成"纬度"角度差
    private static double metersTogetLatDegreesGcj02(double meters) {
        return meters / (GCJ02_EARTH_RADIUS * Math.PI / 180.0);
    }

    // 根据 GCJ02 近似，把指定米数转换成"经度"角度差
    // 注意要乘以 cos(lat)，lat 用弧度
    private static double metersToLngDegreesGcj02(double meters, double getLatDegree) {
        double rad = Math.toRadians(getLatDegree);
        return meters / (GCJ02_EARTH_RADIUS * Math.PI / 180.0 * Math.cos(rad));
    }

    public static List<AirspaceDO> buildMockAirSpace() {
        List<AirspaceDO> airSpaceList = Lists.newArrayList();
        AirspaceDO airSpace1 = new AirspaceDO();
        airSpace1.setName("沈阳周边");
        airSpace1.setCode("KY20231123102");
        airSpace1.setMaxHeight(105);
        airSpace1.setMinHeight(15);

        AirspaceDO airSpace2 = new AirspaceDO();
        airSpace2.setName("法库空域1");
        airSpace2.setCode("KY20231124011");
        airSpace2.setMaxHeight(200);
        airSpace2.setMinHeight(15);

        airSpaceList.add(airSpace1);
        airSpaceList.add(airSpace2);
        return airSpaceList;
    }

    private Result<WeatherGridQueryResponse> queryGdWeatherGridData(WeatherGridPostBody weatherGridPostBody) {
        WeatherGridRequest request = new WeatherGridRequest();
        request.setPostBody(weatherGridPostBody);
        WeatherGridUrlParams weatherGridUrlParams = new WeatherGridUrlParams();
        request.setUrlParams(weatherGridUrlParams);
        return Result.success((WeatherGridQueryResponse) commonDataService.fetchData(request));
    }

    /**
     * 气象数据汇总类，用于记录气象极值
     */
    @Data
    private static class WeatherDataSummary {
        double maxRainIntensity = 0;
        double maxTemperature = Double.MIN_VALUE;
        double minTemperature = Double.MAX_VALUE;
        double maxWindSpeed = 0;
        List<String> weatherDataFileName;
    }


}
