package com.deepinnet.spatiotemporalplatform.skyflow.service;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.dto.RadarPageQueryDTO;
import com.deepinnet.spatiotemporalplatform.model.skyflow.Radar;

/**
 * Description: 雷达服务
 * Date: 2025/3/13
 * Author: claude-ai
 */
public interface RadarService {
    
    /**
     * 创建雷达
     * @param radar 雷达信息
     * @return 创建的雷达代码
     */
    String createRadar(Radar radar);

    /**
     * 分页查询雷达列表
     * @param queryDTO 查询参数
     * @return 雷达列表
     */
    CommonPage<Radar> pageQueryRadar(RadarPageQueryDTO queryDTO);

    /**
     * 根据代码查询雷达详情
     * @param code 雷达代码
     * @return 雷达详情
     */
    Radar getRadarByCode(String code);
} 