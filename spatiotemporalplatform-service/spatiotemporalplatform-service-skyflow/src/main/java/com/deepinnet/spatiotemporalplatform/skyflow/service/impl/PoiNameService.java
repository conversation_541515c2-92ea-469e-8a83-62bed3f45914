package com.deepinnet.spatiotemporalplatform.skyflow.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.PoiNameMappingService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.AirspaceDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.PoiNameMappingDO;
import com.deepinnet.spatiotemporalplatform.dto.PoiNameMappingPageQueryDTO;
import com.deepinnet.spatiotemporalplatform.model.skyflow.PoiNameMapping;
import com.deepinnet.spatiotemporalplatform.skyflow.convert.PoiNameMappingConvert;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * PoiNameService
 * Author: chenkaiyang
 * Date: 2025/3/21
 */
@Service
public class PoiNameService {
    @Resource
    private PoiNameMappingService poiNameMappingService;
    @Resource
    private PoiNameMappingConvert poiNameMappingConvert;

    // 分页获取列表
    public CommonPage<PoiNameMapping> pageQueryPoiNameMapping(PoiNameMappingPageQueryDTO queryDTO) {
        // 创建分页对象
        // 执行分页查询
        Page<PoiNameMappingDO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        page = poiNameMappingService.page(page);

        // 转换为通用分页对象
        List<PoiNameMappingDO> records = page.getRecords();
        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), (int) page.getPages(), page.getTotal(), poiNameMappingConvert.toDomainList(records));
    }
}
