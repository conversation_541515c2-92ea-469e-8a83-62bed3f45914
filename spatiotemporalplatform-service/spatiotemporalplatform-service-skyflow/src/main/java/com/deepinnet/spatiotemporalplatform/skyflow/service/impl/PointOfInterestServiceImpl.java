package com.deepinnet.spatiotemporalplatform.skyflow.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.GdStaticSurfaceObstacleDO;
import com.deepinnet.spatiotemporalplatform.dto.*;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.GdStaticSurfaceObstacleRepository;
import com.deepinnet.spatiotemporalplatform.skyflow.service.PointOfInterestService;
import com.deepinnet.spatiotemporalplatform.skyflow.util.beidou.JsonUtil;
import com.deepinnet.spatiotemporalplatform.skyflow.util.beidou.WktUtil;
import com.deepinnet.spatiotemporalplatform.model.tour.openapi.*;
import com.google.common.collect.Lists;
import org.locationtech.jts.geom.Point;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *  service 接口实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Service
public class PointOfInterestServiceImpl implements PointOfInterestService {

    @Resource
    private CommonDataService commonDataService;

    @Resource
    private GdStaticSurfaceObstacleRepository gdStaticSurfaceObstacleRepository;

    @Override
    public List<Poi> getPoi(POIQueryDTO dto) {
        Assert.notNull(dto, "dto must not be null");
        Assert.hasText(dto.getLocation(), "location must not be empty");

        return queryGdPoi(dto);
    }

    @Override
    public List<StaticSurfaceObstacle> getStaticObstacle(StaticSurfaceObstacleQueryDTO dto) {
        Assert.notNull(dto, "dto must not be null");
        Assert.isTrue(!(StrUtil.isEmpty(dto.getRegion()) && StrUtil.isEmpty(dto.getBd2DGridLocationCode()) && ObjectUtil.isNull(dto.getCoordinates())), "param error");

        Point point = buildPoint(dto.getCoordinates());

        String geoJson = StrUtil.isEmpty(dto.getRegion()) ? null : JsonUtil.extractGeometry(dto.getRegion());

        List<GdStaticSurfaceObstacleDO> staticSurfaceObstacles = gdStaticSurfaceObstacleRepository.getStaticSurfaceObstacles(StrUtil.isEmpty(dto.getBd2DGridLocationCode()) ? null : Lists.newArrayList(dto.getBd2DGridLocationCode()), ObjectUtil.isNull(point) ? null : Lists.newArrayList(point), geoJson);

        return staticSurfaceObstacles.stream().map(e -> StaticSurfaceObstacle.builder()
                .region(WktUtil.toWkt(e.getGridGeometry()))
                .bd2DGridLocationCode(e.getGridCode())
                .verticalObstacleMarker(JSONArray.parseArray(e.getStaticObstacle(), Integer.class)).build()).collect(Collectors.toList());
    }

    @Override
    public List<Poi> getPolygonPoi(POIPolygonQueryDTO dto) {
        Assert.notNull(dto, "dto must not be null");
        Assert.hasText(dto.getRegion(), "region must not be empty");

        List<String> points = JsonUtil.extractCoordinates(dto.getRegion());

        return queryGdPolygonPoi(points, dto);
    }

    private List<Poi> queryGdPolygonPoi(List<String> points, POIPolygonQueryDTO dto) {
        PolygonPoiRequest polygonPoiRequest = new PolygonPoiRequest();
        PolygonPoiParam param = new PolygonPoiParam();
        param.setPolygon(String.join("|", points));
        param.setTypes(CollUtil.isNotEmpty(dto.getTypes()) ? String.join("|", dto.getTypes()) : null);
        param.setPage_num(dto.getPageNum());
        param.setPage_size(dto.getPageSize());
        param.setIsPersistence(false);

        polygonPoiRequest.setPolygonPoiParam(param);

        PoiResponse poi = (PoiResponse) commonDataService.fetchData(polygonPoiRequest);

        List<GdPoi> pois = poi.getPois();

        if (CollUtil.isEmpty(pois)) {
            return Collections.emptyList();
        }

        return pois.stream().map(e -> Poi.builder()
                .id(e.getId())
                .name(e.getName())
                .type(e.getType())
                .typeCode(e.getTypecode())
                .location(e.getLocation())
                .address(e.getAddress()).build()).collect(Collectors.toList());

    }

    private Point buildPoint(Coordinates coordinates) {
        if (ObjectUtil.isNull(coordinates)) {
            return null;
        }
        return WktUtil.toPoint(coordinates.getLongitude(), coordinates.getLatitude());
    }

    private List<Point> buildPointList(List<Coordinates> coordinates) {
        if (CollUtil.isEmpty(coordinates)) {
            return Collections.emptyList();
        }

        return coordinates.stream().map(coordinate -> WktUtil.toPoint(coordinate.getLongitude(), coordinate.getLatitude())).collect(Collectors.toList());
    }

    private List<Poi> queryGdPoi(POIQueryDTO dto) {
        PoiRequest poiRequest = new PoiRequest();
        PoiParam poiParam = new PoiParam();
        poiParam.setLocation(dto.getLocation());
        poiParam.setRadius(dto.getRadius());
        poiParam.setTypes(CollUtil.isNotEmpty(dto.getTypes()) ? String.join("|", dto.getTypes()) : null);
        poiParam.setPage_num(dto.getPageNum());
        poiParam.setPage_size(dto.getPageSize());
        poiParam.setIsPersistence(false);

        poiRequest.setUrlParams(poiParam);

        PoiResponse poi = (PoiResponse) commonDataService.fetchData(poiRequest);

        List<GdPoi> pois = poi.getPois();

        if (CollUtil.isEmpty(pois)) {
            return Collections.emptyList();
        }

        return pois.stream().map(e -> Poi.builder()
                .id(e.getId())
                .name(e.getName())
                .type(e.getType())
                .typeCode(e.getTypecode())
                .location(e.getLocation())
                .address(e.getAddress()).build()).collect(Collectors.toList());

    }
}
