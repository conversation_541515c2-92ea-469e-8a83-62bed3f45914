package com.deepinnet.spatiotemporalplatform.skyflow.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.vo.FlightDemandVO;
import com.deepinnet.spatiotemporalplatform.base.infra.DictionaryItemService;
import com.deepinnet.spatiotemporalplatform.common.enums.YuanFeiAirSpaceRangeTypeEnum;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.FlightDailyStatisticsRepository;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.FlightPlanAchievementRepository;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.FlightRecordRepository;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.*;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightDemandPlanStatDO;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightDemandPlanStatQuery;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightQuery;
import com.deepinnet.spatiotemporalplatform.dto.*;
import com.deepinnet.spatiotemporalplatform.enums.FlightQueryFiledEnum;
import com.deepinnet.spatiotemporalplatform.enums.FlightQuerySortTypeEnum;
import com.deepinnet.spatiotemporalplatform.model.skyflow.Airspace;
import com.deepinnet.spatiotemporalplatform.skyflow.client.FlightDemandRemoteClient;
import com.deepinnet.spatiotemporalplatform.skyflow.client.LocalDataFlightClient;
import com.deepinnet.spatiotemporalplatform.skyflow.convert.FlightConverter;

import com.deepinnet.spatiotemporalplatform.skyflow.enums.FlightPlanStatusEnum;
import com.deepinnet.spatiotemporalplatform.skyflow.enums.FlightStatusEnum;
import com.deepinnet.spatiotemporalplatform.skyflow.handler.TodayFlightOverviewFactory;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.*;
import com.deepinnet.spatiotemporalplatform.skyflow.service.AirspaceService;
import com.deepinnet.spatiotemporalplatform.skyflow.service.FlightService;
import com.deepinnet.spatiotemporalplatform.skyflow.util.beidou.TimeUtil;
import com.deepinnet.spatiotemporalplatform.vo.*;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.deepinnet.spatiotemporalplatform.enums.FlightEventsStatStartTimeQueryType.PLAN_PLANED_TAKEOFF_TIME;

/**
 * <p>
 * 低空飞行服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/4
 */

@Service
@RequiredArgsConstructor
public class FlightServiceImpl implements FlightService {

    private final FlightMissionRepository flightMissionRepository;

    private final FlightPlanRepository flightPlanRepository;

    private final RealTimeUavFlightRepository realTimeUavFlightRepository;

    private final AerodromeRepository aerodromeRepository;

    private final UavRepository uavRepository;

    private final TaskInFlightRepository taskInFlightRepository;

    private final TaskFlightMilesRepository taskFlightMilesRepository;

    private final TaskFlightSortiesRepository taskFlightSortiesRepository;

    private final TaskFlightWarningsRepository taskFlightWarningsRepository;

    private final FlightConverter flightConverter;

    private final TodayFlightOverviewFactory todayFlightOverviewFactory;

    private final RiskWarningRepository riskWarningRepository;

    private final LocalDataFlightClient flightClient;

    private final AirspaceService airspaceService;

    private final FlightDailyStatisticsRepository flightDailyStatisticsRepository;

    private final UnplannedFlightDetectionRepository unplannedFlightDetectionRepository;

    private final FlightRecordRepository flightRecordRepository;

    private final DictionaryItemService dictionaryItemService;

    private final FlightDemandRemoteClient flightDemandRemoteClient;

    private final FlightPlanAchievementRepository achievementRepository;

    @Override
    public CommonPage<FlightMissionVO> pageQueryMission(FlightQueryDTO flightQueryDTO) {

        Page<Object> page = PageHelper.startPage(flightQueryDTO.getPageNum(), flightQueryDTO.getPageSize());

        List<FlightMissionDO> flightMissionList = flightMissionRepository.listFlightMissionByCondition(flightConverter.toFlightQuery(flightQueryDTO));

        return CommonPage.buildPage(flightQueryDTO.getPageNum(), flightQueryDTO.getPageSize(), page.getPages(), page.getTotal(), flightConverter.toFlightMissionVOList(flightMissionList));
    }

    @Override
    public CommonPage<FlightPlanVO> pageQueryPlan(FlightQueryDTO flightQueryDTO) {

        Page<Object> page = PageHelper.startPage(flightQueryDTO.getPageNum(), flightQueryDTO.getPageSize());

        List<FlightPlanDO> flightPlanList = flightPlanRepository.listFlightPlanByCondition(flightConverter.toFlightQuery(flightQueryDTO));

        if (CollUtil.isEmpty(flightPlanList)) {
            return CommonPage.buildPage(flightQueryDTO.getPageNum(), flightQueryDTO.getPageSize(), page.getPages(), page.getTotal(), Lists.newArrayList());
        }

        List<String> planIds = flightPlanList.stream().map(FlightPlanDO::getPlanId).collect(Collectors.toList());
        List<RealTimeUavFlightDO> uavStatus = realTimeUavFlightRepository.queryFlightPlanLastStatus(planIds);

        List<FlightPlanVO> flightPlanVOList = flightConverter.toFlightPlanVOList(flightPlanList);

        if (CollUtil.isEmpty(uavStatus)) {
            return CommonPage.buildPage(flightQueryDTO.getPageNum(), flightQueryDTO.getPageSize(), page.getPages(), page.getTotal(), flightPlanVOList);
        }

        Map<String, RealTimeUavFlightDO> planUavStatusMap = uavStatus.stream()
                .filter(a -> a.getFlightId() != null)
                .collect(Collectors.toMap(RealTimeUavFlightDO::getPlanId, Function.identity()));

        flightPlanVOList.forEach(e -> {
            RealTimeUavFlightDO realTimeUavFlightDO = planUavStatusMap.get(e.getPlanId());

            FlightStatusEnum uavVOStatus = getFlightStatusEnum(realTimeUavFlightDO);
            e.setUavStatus(uavVOStatus == null ? null : uavVOStatus.getCode());

        });

        return CommonPage.buildPage(flightQueryDTO.getPageNum(), flightQueryDTO.getPageSize(), page.getPages(), page.getTotal(), flightPlanVOList);
    }

    private static FlightStatusEnum getFlightStatusEnum(RealTimeUavFlightDO realTimeUavFlightDO) {
        String planUavStatus = Optional.ofNullable(realTimeUavFlightDO).map(RealTimeUavFlightDO::getStatus).orElse(null);
        LocalDateTime reportTime = Optional.ofNullable(realTimeUavFlightDO).map(RealTimeUavFlightDO::getReportTime).orElse(null);
        FlightStatusEnum uavVOStatus = FlightStatusEnum.getVOStatus(planUavStatus, reportTime);
        return uavVOStatus;
    }

    @Override
    public FlightMissionVO queryMissionDetail(String missionId) {
        Assert.hasText(missionId, "missionId not be null!");

        // 获取飞行任务信息
        FlightMissionDO missionDO = getFlightMissionByMissionId(missionId);
        if (ObjectUtil.isNull(missionDO)) {
            LogUtil.warn("飞行任务ID:{}, 不存在!", missionId);
            return null;
        }

        FlightMissionVO missionVO = flightConverter.toFlightMissionVO(missionDO);

        // 查询该任务的所有飞行计划
        List<FlightPlanDO> flightPlanList = getFlightPlansByMissionId(missionDO);

        if (CollUtil.isEmpty(flightPlanList)) {
            LogUtil.warn("飞行任务ID:{}, 不存在任何飞行计划!", missionId);
            return missionVO;
        }

        // 获取所有飞行计划ID、无人机ID和机场ID
        List<String> planIds = flightPlanList.stream()
                .map(FlightPlanDO::getPlanId)
                .collect(Collectors.toList());

        List<String> uavIds = flightPlanList.stream()
                .map(FlightPlanDO::getUavId)
                .filter(StrUtil::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());

        List<String> aerodromeIds = flightPlanList.stream()
                .flatMap(flightPlan -> Stream.of(flightPlan.getLandingAerodromeId().split(",")))
                .map(String::trim)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询无人机信息
        Map<String, UavDO> uavMap = CollUtil.isEmpty(uavIds) ? new HashMap<>() :
                uavRepository.list(Wrappers.lambdaQuery(UavDO.class)
                                .in(UavDO::getUavId, uavIds))
                        .stream()
                        .collect(Collectors.toMap(UavDO::getUavId, Function.identity(), (v1, v2) -> v1));

        // 批量查询机场信息
        Map<String, AerodromeDO> aerodromeMap = CollUtil.isEmpty(aerodromeIds) ? new HashMap<>() :
                aerodromeRepository.list(Wrappers.lambdaQuery(AerodromeDO.class)
                                .in(AerodromeDO::getUavPortId, aerodromeIds))
                        .stream()
                        .collect(Collectors.toMap(AerodromeDO::getUavPortId, Function.identity(), (v1, v2) -> v1));

        // 批量查询实时飞行数据
        Map<String, List<RealTimeUavFlightDO>> realTimeFlightMap = CollUtil.isEmpty(planIds) ? new HashMap<>() :
                realTimeUavFlightRepository.list(Wrappers.lambdaQuery(RealTimeUavFlightDO.class)
                                .in(RealTimeUavFlightDO::getPlanId, planIds))
                        .stream()
                        .collect(Collectors.groupingBy(RealTimeUavFlightDO::getPlanId));

        // 批量查询飞行记录
        Map<String, FlightRecordDO> flightRecordMap = CollUtil.isEmpty(planIds) ? new HashMap<>() :
                flightRecordRepository.list(Wrappers.lambdaQuery(FlightRecordDO.class)
                                .in(FlightRecordDO::getPlanId, planIds))
                        .stream().collect(Collectors.toMap(FlightRecordDO::getPlanId, Function.identity()));

        // 单次循环组装所有数据
        List<FlightPlanVO> flightPlanVOList = flightPlanList.stream()
                .map(planDO -> {
                    // 基础转换
                    FlightPlanVO planVO = flightConverter.toFlightPlanVO(planDO);

                    // 设置无人机信息
                    if (StrUtil.isNotEmpty(planDO.getUavId()) && uavMap.containsKey(planDO.getUavId())) {
                        planVO.setUavInfo(flightConverter.toUavVO(uavMap.get(planDO.getUavId())));
                    }

                    // 设置机场信息
                    setAerodromeInfo(aerodromeMap, planVO);

                    // 设置实时飞行数据
                    List<RealTimeUavFlightDO> realTimeFlights = realTimeFlightMap.getOrDefault(planDO.getPlanId(), Lists.newArrayList());
                    if (CollUtil.isNotEmpty(realTimeFlights)) {
                        planVO.setRealtimeUavInfoList(flightConverter.toRealTimeUavFlightVOList(realTimeFlights));
                    }

                    FlightRecordDO flightRecordDO = flightRecordMap.get(planDO.getPlanId());
                    if (ObjectUtil.isNotNull(flightRecordDO)) {
                        planVO.setFlightRecord(flightConverter.toFlightRecordVO(flightRecordDO));
                    }

                    return planVO;
                })
                .collect(Collectors.toList());

        missionVO.setFlightPlanList(flightPlanVOList);

        return missionVO;
    }

    @Override
    public FlightPlanVO queryPlanDetail(String planId) {
        Assert.hasText(planId, "planId not be null!");

        // 获取飞行任务信息
        FlightPlanDO planDO = getFlightPlanByMissionId(planId);
        if (ObjectUtil.isNull(planDO)) {
            LogUtil.warn("飞行计划ID:{}, 不存在!", planId);
            return null;
        }

        // 查询当前飞行计划的最新flight_id 的 飞行信息
        List<RealTimeUavFlightDO> flightId = realTimeUavFlightRepository.queryFlightPlanIdLastFlightId(Lists.newArrayList(planId));

        return getFlightPlanVO(planId, planDO, flightId);
    }

    private FlightPlanVO getFlightPlanVO(String planId, FlightPlanDO planDO, List<RealTimeUavFlightDO> realTimeUavFlightDOS) {

        String flightId = Optional.ofNullable(realTimeUavFlightDOS)
                .filter(CollUtil::isNotEmpty)
                .map(list -> list.get(0).getFlightId())
                .orElse(null);

        return getFlightPlanVOByPlanAndFlight(planId, planDO, flightId);
    }

    private FlightPlanVO getFlightPlanVOByPlanAndFlight(String planId, FlightPlanDO planDO, String flightId) {
        FlightPlanVO flightPlanVO = flightConverter.toFlightPlanVO(planDO);


        List<String> aerodromeIds = Optional.ofNullable(flightPlanVO.getLandingAerodromeId())
                .map(id -> Stream.of(id.split(","))
                        .map(String::trim)
                        .distinct()
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());

        List<RealTimeUavFlightDO> realTimeUavFlightDOList = Lists.newArrayList();

        if (StringUtils.isNotEmpty(flightId)) {
            // 查询实时飞行信息
            realTimeUavFlightDOList = realTimeUavFlightRepository.list(Wrappers.lambdaQuery(RealTimeUavFlightDO.class)
                    .eq(RealTimeUavFlightDO::getPlanId, planId)
                    .eq(RealTimeUavFlightDO::getFlightId, flightId)
                    .orderByAsc(RealTimeUavFlightDO::getReportTime));
        }

        // 查询无人机信息
        UavDO uavDO = uavRepository.getOne(Wrappers.<UavDO>lambdaQuery()
                .eq(UavDO::getUavId, planDO.getUavId()));

        // 批量查询机场信息
        Map<String, AerodromeDO> aerodromeMap = CollUtil.isEmpty(aerodromeIds) ? new HashMap<>() :
                aerodromeRepository.list(Wrappers.lambdaQuery(AerodromeDO.class)
                                .in(AerodromeDO::getUavPortId, aerodromeIds))
                        .stream()
                        .collect(Collectors.toMap(AerodromeDO::getUavPortId, Function.identity(), (v1, v2) -> v1));

        if (CollUtil.isNotEmpty(realTimeUavFlightDOList)) {
            List<RealTimeUavFlightVO> realTimeUavFlightVOList = flightConverter.toRealTimeUavFlightVOList(realTimeUavFlightDOList)
                    .stream().filter(realTimeUavFlightVO -> realTimeUavFlightVO.getFlightId() != null).collect(Collectors.toList());
            // 设置最新的一条的展示状态
            if (CollUtil.isNotEmpty(realTimeUavFlightVOList)) {
                RealTimeUavFlightVO latestRealTimeUavFlightVO = realTimeUavFlightVOList.get(realTimeUavFlightVOList.size() - 1);
                FlightStatusEnum voStatus = FlightStatusEnum.getVOStatus(latestRealTimeUavFlightVO.getStatus(), latestRealTimeUavFlightVO.getReportTime());
                latestRealTimeUavFlightVO.setStatus(voStatus == null ? null : voStatus.getCode());
                flightPlanVO.setRealtimeUavInfoList(realTimeUavFlightVOList);
            }
        }

        if (ObjectUtil.isNotNull(uavDO)) {
            flightPlanVO.setUavInfo(flightConverter.toUavVO(uavDO));
        }

        FlightRecordDO record = getFlightRecordDOByPlanId(planId);

        if (ObjectUtil.isNotNull(record)) {
            flightPlanVO.setFlightRecord(flightConverter.toFlightRecordVO(record));
        }

        // 设置机场信息
        setAerodromeInfo(aerodromeMap, flightPlanVO);

        return flightPlanVO;
    }

    private FlightRecordDO getFlightRecordDOByPlanId(String planId) {
        FlightRecordDO record = flightRecordRepository.getOne(Wrappers.<FlightRecordDO>lambdaQuery()
                .eq(FlightRecordDO::getPlanId, planId));
        return record;
    }

    @Override
    public List<RealTimeUavFlightVO> queryPlanFlight(String planId) {
        Assert.hasText(planId, "planId not be null!");

        // 获取飞行任务信息
        FlightPlanDO planDO = getFlightPlanByMissionId(planId);
        if (ObjectUtil.isNull(planDO)) {
            LogUtil.warn("飞行计划ID:{}, 不存在!", planId);
            return null;
        }

        // 查询当前飞行计划的最新flight_id 的 飞行信息
        // 默认为1000
        List<RealTimeUavFlightDO> realTimeUavFlightDOList = realTimeUavFlightRepository.queryPlanFlights(Lists.newArrayList(planId), 1, 200);

        return realTimeUavFlightDOList.stream()
                .filter(flight -> flight.getFlightId() != null)
                .map(flightConverter::toRealTimeUavFlightVO)
                .sorted((o1, o2) -> o2.getReportTime().compareTo(o1.getReportTime()))
                .collect(Collectors.toList());

    }

    private void setAerodromeInfo(Map<String, AerodromeDO> aerodromeMap, FlightPlanVO flightPlanVO) {
        flightPlanVO.setLandingAerodrome(flightConverter.toListAerodromeVO(new ArrayList<>(aerodromeMap.values())));
    }

    @Override
    public FlightTodayOverviewVO queryTodayOverview() {
        LocalDate today = LocalDate.now();

        TaskInFlightDO inFlightDO = taskInFlightRepository.getOne(
                createQueryWrapper(TaskInFlightDO::getStatisticalDate, TaskInFlightDO::getGmtCreated, today)
        );
        TaskFlightMilesDO flightMilesDO = taskFlightMilesRepository.getOne(
                createQueryWrapper(TaskFlightMilesDO::getStatisticalDate, TaskFlightMilesDO::getGmtCreated, today)
        );
        TaskFlightSortiesDO flightSortiesDO = taskFlightSortiesRepository.getOne(
                createQueryWrapper(TaskFlightSortiesDO::getStatisticalDate, TaskFlightSortiesDO::getGmtCreated, today)
        );
        TaskFlightWarningsDO flightWarningDO = taskFlightWarningsRepository.getOne(
                createQueryWrapper(TaskFlightWarningsDO::getStatisticalDate, TaskFlightWarningsDO::getGmtCreated, today)
        );

        // 今日飞行计划总数
        Long flightPlanCount = flightPlanRepository.count(
                Wrappers.lambdaQuery(FlightPlanDO.class)
                        .in(FlightPlanDO::getStatus, FlightPlanStatusEnum.FLIGHT_STATUS)
                        .and(wrapper ->
                                wrapper.apply("DATE(to_timestamp(planed_takeoff_time / 1000)) = CURRENT_DATE")
                                        .or()
                                        .apply("DATE(to_timestamp(planed_landing_time / 1000)) = CURRENT_DATE")
                        )
        );
        // 今日黑飞探测总数

        Long unplannedFlightsCount = unplannedFlightDetectionRepository.count(Wrappers.lambdaQuery(UnplannedFlightDetectionDO.class)
                .ge(UnplannedFlightDetectionDO::getReportedTime, TimeUtil.getStartOfDay())
                .le(UnplannedFlightDetectionDO::getReportedTime, TimeUtil.getEndOfDay()));

        FlightTodayOverviewVO overview = new FlightTodayOverviewVO();

        // 今日飞行计划
        overview.setTodayFlightPlan(buildFlightStat(
                flightPlanCount.intValue(),
                flightPlanCount.intValue() / 2.2
        ));

        // 今日黑飞探测
        overview.setTodayFlightDetect(buildFlightStat(
                unplannedFlightsCount.intValue(),
                unplannedFlightsCount.intValue() / 1.5
        ));

        if (ObjectUtil.isNotNull(inFlightDO)) {
            overview.setInFlightCount(buildFlightStat(
                    inFlightDO.getStatisticalCount(),
                    inFlightDO.getStatisticalHistoryAvgCount()
            ));
        }
        if (ObjectUtil.isNotNull(flightMilesDO)) {
            overview.setTotalDistance(buildFlightStat(
                    flightMilesDO.getStatisticalCount(),
                    flightMilesDO.getStatisticalHistoryAvgCount()
            ));
        }
        if (ObjectUtil.isNotNull(flightSortiesDO)) {
            overview.setTotalFlights(buildFlightStat(
                    flightSortiesDO.getStatisticalCount(),
                    flightSortiesDO.getStatisticalHistoryAvgCount()
            ));
        }
        if (ObjectUtil.isNotNull(flightWarningDO)) {
            overview.setTotalWarnings(buildFlightStat(
                    flightWarningDO.getStatisticalCount(),
                    flightWarningDO.getStatisticalHistoryAvgCount()
            ));
        }

        return overview;
    }

    @Override
    public List queryTrend(String type) {
        return todayFlightOverviewFactory.handle(type);
    }

    @Override
    public List queryTodayHeightSorties(Long startTime, Long endTime) {
        FlightQuery flightQuery = new FlightQuery();
        flightQuery.setStartTime(startTime);
        flightQuery.setEndTime(endTime);
        List<FlightPlanDO> flightPlanDOList = flightPlanRepository.listFlightPlanByCondition(flightQuery);

        if (CollUtil.isEmpty(flightPlanDOList)) {
            return Lists.newArrayList();
        }

        Map<String, Long> altitudeRanges = flightPlanDOList.stream().filter(e -> ObjectUtil.isNotNull(e.getPlanedAltitude()))
                .collect(Collectors.groupingBy(fp -> getAltitudeCategory(fp.getPlanedAltitude()), Collectors.counting()));

        List<TrendVO<String, Long>> trendVOList = Lists.newArrayList();

        altitudeRanges.forEach((key, value) -> trendVOList.add(new TrendVO<>(key, value, null, null)));

        return trendVOList;
    }

    @Override
    public List<RealTimeUavFlightVO> queryTodayRealTimeUavFlight(PositionQueryDTO queryDTO) {

        Assert.notNull(queryDTO, "PositionQueryDTO is null!");
        Assert.notNull(queryDTO.getStatus(), "status is null!");

        if (ObjectUtil.isNull(FlightPlanStatusEnum.getByCode(queryDTO.getStatus()))) {
            LogUtil.warn("未匹配到对应的飞行状态:{}", queryDTO.getStatus());
            return Lists.newArrayList();
        }

        List<FlightPlanDO> flightPlans = flightPlanRepository.list(Wrappers.<FlightPlanDO>lambdaQuery()
                .eq(FlightPlanDO::getStatus, queryDTO.getStatus())
                .eq(StrUtil.isNotBlank(queryDTO.getPlanId()), FlightPlanDO::getPlanId, queryDTO.getPlanId())
                .in(CollectionUtils.isNotEmpty(queryDTO.getPlanIds()), FlightPlanDO::getPlanId, queryDTO.getPlanIds()));

        if (CollUtil.isEmpty(flightPlans)) {
            return Lists.newArrayList();
        }

        LocalDate today = LocalDate.now();
        LocalDateTime startOfDay = today.atStartOfDay();
        LocalDateTime endOfDay = today.atTime(23, 59, 59);

        List<RiskWarningDO> riskWarningList = riskWarningRepository.list(Wrappers.<RiskWarningDO>lambdaQuery()
                .ge(RiskWarningDO::getWarningTime, startOfDay)
                .le(RiskWarningDO::getWarningTime, endOfDay));

        // 填充今天风险预警的planIds
        List<String> warningPlanIds = Lists.newArrayList();
        if (CollUtil.isNotEmpty(riskWarningList)) {
            warningPlanIds = riskWarningList.stream().map(RiskWarningDO::getPlanId).collect(Collectors.toList());
        }

        List<String> planIds = flightPlans.stream().map(FlightPlanDO::getPlanId).collect(Collectors.toList());

        List<String> needPlanIds = new ArrayList<>(planIds);
        if (CollectionUtils.isNotEmpty(warningPlanIds)) {
            warningPlanIds.forEach(needPlanIds::remove);
        }

        if (CollectionUtils.isEmpty(needPlanIds)) {
            return Lists.newArrayList();
        }

        List<RealTimeUavFlightDO> latestFlights = realTimeUavFlightRepository.queryLatestFlights(needPlanIds, Lists.newArrayList(), warningPlanIds);

        if (CollUtil.isEmpty(latestFlights)) {
            return Lists.newArrayList();
        }

        Map<String, FlightPlanDO> planMap = flightPlans.stream().collect(Collectors.toMap(FlightPlanDO::getPlanId, Function.identity()));

        List<RealTimeUavFlightVO> realTimeUavFlightVOList = flightConverter.toRealTimeUavFlightVOList(latestFlights);

        // 获取需求信息,planId和需求的映射
        HashMap<String, FlightDemandVO> planId2DemandMap = getFlightDemandVOHashMap(flightPlans);

        realTimeUavFlightVOList.forEach(e -> {
            FlightPlanDO flightPlanDO = planMap.get(e.getPlanId());

            if (ObjectUtil.isNotNull(flightPlanDO)) {
                e.setFlightUnit(flightPlanDO.getFlightUnit());
                e.setPlanName(flightPlanDO.getPlanName());
            }
            String categoryFullName = Optional.ofNullable(planId2DemandMap.get(e.getPlanId())).map(FlightDemandVO::getCategoryFullName).orElse(null);
            e.setCategoryFullName(categoryFullName);
            e.setCategoryNo(Optional.ofNullable(planId2DemandMap.get(e.getPlanId())).map(FlightDemandVO::getCategoryNo).orElse(null));
            e.setRootCategoryFullName(Optional.ofNullable(categoryFullName).map(a -> a.split("/")[0]).orElse(null));
        });

        return realTimeUavFlightVOList;
    }

    private HashMap<String, FlightDemandVO> getFlightDemandVOHashMap(List<FlightPlanDO> flightPlans) {
        HashMap<String, FlightDemandVO> planId2DemandMap = new HashMap<>();

        List<String> demandNoList = flightPlans.stream().map(FlightPlanDO::getBizNo).filter(StrUtil::isNotBlank).collect(Collectors.toList());

        if (CollUtil.isEmpty(demandNoList)) {
            return planId2DemandMap;
        }

        List<FlightDemandVO> flightDemandByDemandNoList = flightDemandRemoteClient.getFlightDemandByDemandNoList(demandNoList);
        Map<String, FlightDemandVO> demandMap = flightDemandByDemandNoList.stream().collect(Collectors.toMap(FlightDemandVO::getDemandNo, Function.identity()));

        for (FlightPlanDO flightPlan : flightPlans) {
            planId2DemandMap.put(flightPlan.getPlanId(), demandMap.get(flightPlan.getBizNo()));
        }
        return planId2DemandMap;
    }

    @Override
    public List<UavFlightTrackVO> queryTodayUavFlightTrack(PositionQueryDTO queryDTO) {

        Assert.notNull(queryDTO, "PositionQueryDTO is null!");
        Assert.isTrue(Objects.nonNull(queryDTO.getStatus())
                || StrUtil.isNotBlank(queryDTO.getPlanId())
                || CollectionUtils.isNotEmpty(queryDTO.getPlanIds()), "param is null!");

        if (ObjectUtil.isNull(FlightPlanStatusEnum.getByCode(queryDTO.getStatus()))) {
            LogUtil.warn("未匹配到对应的飞行状态:{}", queryDTO.getStatus());
            return Lists.newArrayList();
        }

        List<FlightPlanDO> flightPlans = flightPlanRepository.list(Wrappers.<FlightPlanDO>lambdaQuery()
                .eq(Objects.nonNull(queryDTO.getStatus()), FlightPlanDO::getStatus, queryDTO.getStatus())
                .eq(StrUtil.isNotBlank(queryDTO.getPlanId()), FlightPlanDO::getPlanId, queryDTO.getPlanId())
                .in(CollectionUtils.isNotEmpty(queryDTO.getPlanIds()), FlightPlanDO::getPlanId, queryDTO.getPlanIds()));

        if (CollUtil.isEmpty(flightPlans)) {
            return Lists.newArrayList();
        }

        Map<String, FlightPlanDO> planDOMap = flightPlans.stream().collect(Collectors.toMap(FlightPlanDO::getPlanId, Function.identity(), (a, b) -> a));

        LocalDate today = LocalDate.now();
        LocalDateTime startOfDay = today.atStartOfDay();
        LocalDateTime endOfDay = today.atTime(23, 59, 59);

        List<RiskWarningDO> riskWarningList = riskWarningRepository.list(Wrappers.<RiskWarningDO>lambdaQuery()
                .ge(RiskWarningDO::getWarningTime, startOfDay)
                .le(RiskWarningDO::getWarningTime, endOfDay));


        // 预警架次列表
        List<String> warningFlightIds = riskWarningList.stream().map(RiskWarningDO::getFlightId).collect(Collectors.toList());

        List<String> planIds = flightPlans.stream().map(FlightPlanDO::getPlanId).collect(Collectors.toList());

        // 查询这些计划最新的飞行架次
        List<RealTimeUavFlightDO> lastFlightIds = realTimeUavFlightRepository.queryFlightPlanIdLastFlightId(planIds);

        if (CollUtil.isEmpty(lastFlightIds)) {
            LogUtil.warn("计划ID:{}, 暂无对应的飞行架次", planIds);
            return Lists.newArrayList();
        }

        Set<String> needPlanIds = new HashSet<>(planIds);
        Set<String> needFlightIds = new HashSet<>(lastFlightIds.stream().map(RealTimeUavFlightDO::getFlightId).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(warningFlightIds)) {
            warningFlightIds.forEach(needFlightIds::remove);
        }

        if (CollectionUtils.isEmpty(needPlanIds)) {
            return Lists.newArrayList();
        }

        long current = System.currentTimeMillis();

        List<RealTimeUavFlightDO> realTimeUavFlightList = realTimeUavFlightRepository.list(Wrappers.<RealTimeUavFlightDO>lambdaQuery()
                .in(RealTimeUavFlightDO::getPlanId, needPlanIds)
                .in(RealTimeUavFlightDO::getStatus, FlightStatusEnum.CONTROL_FLIGHT_STATUS)
                .in(RealTimeUavFlightDO::getFlightId, needFlightIds));

        LogUtil.info("实时点位查询耗时:{}", (System.currentTimeMillis() - current));

        if (CollUtil.isEmpty(realTimeUavFlightList)) {
            return Lists.newArrayList();
        }

        List<RealTimeUavFlightVO> realTimeUavFlightVOList = flightConverter.toRealTimeUavFlightVOList(realTimeUavFlightList);

        Map<String, List<RealTimeUavFlightVO>> planMap = realTimeUavFlightVOList.stream()
                .collect(Collectors.groupingBy(
                        RealTimeUavFlightVO::getPlanId,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.stream()
                                        .sorted(Comparator.comparingLong(RealTimeUavFlightVO::getReportTime))
                                        .collect(Collectors.toList())
                        )
                ));
        List<String> demandNoList = flightPlans.stream().map(FlightPlanDO::getBizNo).filter(StrUtil::isNotBlank).collect(Collectors.toList());

        if (CollUtil.isEmpty(demandNoList)) {
            return planMap.entrySet().stream().map(e -> UavFlightTrackVO.builder()
                    .planId(e.getKey())
                    .uavFlightList(e.getValue()).build()).collect(Collectors.toList());
        }

        List<FlightDemandVO> demandList = flightDemandRemoteClient.getFlightDemandByDemandNoList(demandNoList);
        Map<String, FlightDemandVO> demandMap = demandList.stream().collect(Collectors.toMap(FlightDemandVO::getDemandNo, Function.identity(), (a, b) -> a));

        return planMap.entrySet().stream().map(e -> UavFlightTrackVO.builder()
                .planId(e.getKey())
                .categoryNo(
                        Optional.ofNullable(e.getKey())
                                .map(planDOMap::get)
                                .map(FlightPlanDO::getBizNo)
                                .map(demandMap::get)
                                .map(FlightDemandVO::getCategoryNo)
                                .orElse(null)
                )
                .categoryFullName(
                        Optional.ofNullable(e.getKey())
                                .map(planDOMap::get)
                                .map(FlightPlanDO::getBizNo)
                                .map(demandMap::get)
                                .map(FlightDemandVO::getCategoryFullName)
                                .orElse(null)
                )
                .rootCategoryFullName(
                        Optional.ofNullable(e.getKey())
                                .map(planDOMap::get)
                                .map(FlightPlanDO::getBizNo)
                                .map(demandMap::get)
                                .map(FlightDemandVO::getCategoryFullName)
                                // todo 目前由前端定义，后续迭代改为后端定义类目结构为：一级类目/二级类目
                                .map(a -> a.split("/")[0])
                                .orElse(null)
                )
                .uavFlightList(e.getValue()).build()).collect(Collectors.toList());
    }

    @Override
    public RealTimeUavFlightVO queryLatestUavFlightByPlanId(String planId) {
        return flightConverter.toRealTimeUavFlightVO(realTimeUavFlightRepository.getOne(Wrappers.<RealTimeUavFlightDO>lambdaQuery()
                .eq(RealTimeUavFlightDO::getPlanId, planId)
                .orderByDesc(RealTimeUavFlightDO::getReportTime)
                .last("limit 1")));

    }

    @Override
    public String queryLiveUrl(String sn, String httpProtocol) {
        return flightClient.queryLiveUrl(sn, httpProtocol);
    }

    @Override
    public Airspace queryPlanAirway(String planId) {
        FlightPlanDO flightPlanDO = flightPlanRepository.getOne(Wrappers.<FlightPlanDO>lambdaQuery()
                .eq(FlightPlanDO::getPlanId, planId));
        if (ObjectUtil.isNull(flightPlanDO)) {
            LogUtil.error("查询不到对应的飞行计划, planId:{}", planId);
            return null;
        }

        Airspace airspaceByAirspaceId = airspaceService.getAirspaceByAirspaceId(flightPlanDO.getAirspaceId());

        if (ObjectUtil.isNull(airspaceByAirspaceId)) {
            LogUtil.error("查询不到对应的空域, airspaceId:{}", flightPlanDO.getAirspaceId());
            return null;
        }

        if (!Objects.equals(airspaceByAirspaceId.getRangeType(), YuanFeiAirSpaceRangeTypeEnum.LINE.getCode())) {
            LogUtil.error("查询不到对应的空域, 空域类型为:{}", airspaceByAirspaceId.getRangeType());
            return null;
        }

        return airspaceByAirspaceId;
    }

    @Override
    public List<FlightDailyStatisticsVO> queryFlightDailyStatistics(FlightDailyStatisticsQueryDTO dto) {

        Assert.notNull(dto, "query dto is null!");
        Assert.notNull(dto.getStartTime(), "startTime is null!");
        Assert.notNull(dto.getEndTime(), "endTime is null!");

        LambdaQueryWrapper<FlightDailyStatisticsDO> queryWrapper = Wrappers.<FlightDailyStatisticsDO>lambdaQuery()
                .between(FlightDailyStatisticsDO::getTimestamp, dto.getStartTime(), dto.getEndTime());

        List<FlightDailyStatisticsDO> dailyStatisticsDOList = flightDailyStatisticsRepository.list(queryWrapper);

        if (CollUtil.isEmpty(dailyStatisticsDOList)) {
            return Lists.newArrayList();
        }

        List<FlightDailyStatisticsVO> flightDailyStatisticsVOList = flightConverter.toFlightDailyStatisticsVOList(dailyStatisticsDOList);

        return aggregateByFlightUnit(flightDailyStatisticsVOList, dto);
    }

    @Override
    public FlightStatisticsOverviewVO queryFlightStatisticsOverview(FlightStatisticsOverviewQueryDTO dto) {

        Assert.notNull(dto, "query dto is null!");
        Assert.notNull(dto.getStartTime(), "startTime is null!");
        Assert.notNull(dto.getEndTime(), "endTime is null!");

        List<FlightDailyStatisticsVO> flightDailyStatisticsVOS = queryFlightDailyStatistics(FlightDailyStatisticsQueryDTO.builder()
                .startTime(dto.getStartTime())
                .endTime(dto.getEndTime()).build());

        FlightStatisticsOverviewVO resultVO = FlightStatisticsOverviewVO.builder()
                .flightCount(0)
                .flightMiles("0").build();

        if (CollUtil.isNotEmpty(flightDailyStatisticsVOS)) {
            int totalFlightCount = flightDailyStatisticsVOS.stream()
                    .mapToInt(stat -> stat.getFlightCount() != null ? stat.getFlightCount() : 0)
                    .sum();

            BigDecimal totalFlightMiles = flightDailyStatisticsVOS.stream()
                    .map(stat -> {
                        if (stat.getFlightMiles() == null || stat.getFlightMiles().isEmpty()) {
                            return BigDecimal.ZERO;
                        }
                        try {
                            return new BigDecimal(stat.getFlightMiles());
                        } catch (NumberFormatException e) {
                            return BigDecimal.ZERO;
                        }
                    })
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            resultVO.setFlightCount(totalFlightCount);
            resultVO.setFlightMiles(totalFlightMiles.toString());
        }

        Long unplannedFlightCount = unplannedFlightDetectionRepository.getUnplannedFlightCount(dto.getStartTime(), dto.getEndTime());

        Long riskWarningCount = riskWarningRepository.getRiskWarningCount(dto.getStartTime(), dto.getEndTime());

        resultVO.setFlightDetectionCount(unplannedFlightCount);

        resultVO.setFlightWarningCount(riskWarningCount);

        return resultVO;
    }

    public List<FlightDailyStatisticsVO> aggregateByFlightUnit(List<FlightDailyStatisticsVO> dailyStatisticsList, FlightDailyStatisticsQueryDTO dto) {
        Map<String, List<FlightDailyStatisticsVO>> groupedByFlightUnit = dailyStatisticsList.stream()
                .collect(Collectors.groupingBy(FlightDailyStatisticsVO::getFlightUnitId));

        List<FlightDailyStatisticsVO> aggregatedResults = new ArrayList<>();

        groupedByFlightUnit.forEach((flightUnitId, statistics) -> {
            String flightUnit = statistics.get(0).getFlightUnit();

            Integer totalFlightCount = statistics.stream()
                    .mapToInt(stat -> stat.getFlightCount() != null ? stat.getFlightCount() : 0)
                    .sum();

            Integer totalFlightDuration = statistics.stream()
                    .mapToInt(stat -> stat.getFlightDuration() != null ? stat.getFlightDuration() : 0)
                    .sum();

            BigDecimal totalFlightMiles = statistics.stream()
                    .map(stat -> {
                        if (stat.getFlightMiles() == null || stat.getFlightMiles().isEmpty()) {
                            return BigDecimal.ZERO;
                        }
                        try {
                            return new BigDecimal(stat.getFlightMiles());
                        } catch (NumberFormatException e) {
                            return BigDecimal.ZERO;
                        }
                    })
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            FlightDailyStatisticsVO aggregatedStat = FlightDailyStatisticsVO.builder()
                    .flightUnitId(flightUnitId)
                    .flightUnit(flightUnit)
                    .flightCount(totalFlightCount)
                    .flightDuration(totalFlightDuration)
                    .flightMiles(totalFlightMiles.toString())
                    .build();

            aggregatedResults.add(aggregatedStat);
        });

        FlightQueryFiledEnum queryFieldEnum = FlightQueryFiledEnum.getEnumByCode(dto.getSortField());
        FlightQuerySortTypeEnum querySortTypeEnum = FlightQuerySortTypeEnum.getByCode(dto.getSortType());

        Comparator<FlightDailyStatisticsVO> comparator = null;

        switch (queryFieldEnum) {
            case FLIGHT_COUNT:
                comparator = Comparator.comparing(FlightDailyStatisticsVO::getFlightCount);
                break;
            case FLIGHT_DURATION:
                comparator = Comparator.comparing(FlightDailyStatisticsVO::getFlightDuration);
                break;
            case FLIGHT_MILES:
                comparator = Comparator.comparing(FlightDailyStatisticsVO::getFlightMiles, Comparator.nullsLast(Comparator.comparing(BigDecimal::new)));
                break;
        }

        aggregatedResults.sort(ObjectUtil.equals(querySortTypeEnum, FlightQuerySortTypeEnum.DESC) ? comparator.reversed() : comparator);

        return aggregatedResults;
    }

    @Override
    public List<FlightCountVO> batchQueryFlightCount(BatchQueryPlanDTO dto) {

        Assert.notNull(dto, "查询参数不能为空");

        if (CollUtil.isEmpty(dto.getBizNo())) {
            return Lists.newArrayList();
        }

        List<FlightPlanDO> flightPlanList = flightPlanRepository.list(Wrappers.<FlightPlanDO>lambdaQuery()
                .in(FlightPlanDO::getBizNo, dto.getBizNo())
                .in(FlightPlanDO::getApplyType, Lists.newArrayList(2, 3))
                .in(FlightPlanDO::getStatus, Lists.newArrayList(FlightPlanStatusEnum.EXECUTING.getCode(), FlightPlanStatusEnum.COMPLETED.getCode()))
                .eq(StrUtil.isNotBlank(dto.getTenantId()), FlightPlanDO::getTenantId, dto.getTenantId()));

        if (CollUtil.isEmpty(flightPlanList)) {
            return Lists.newArrayList();
        }

        return flightPlanList.stream()
                .collect(Collectors.groupingBy(FlightPlanDO::getBizNo))
                .entrySet().stream()
                .map(entry -> FlightCountVO.builder().bizNo(entry.getKey()).count(entry.getValue().size()).build())
                .collect(Collectors.toList());
    }

    @Override
    public List<FlightPlanVO> batchQueryFlightPlan(BatchQueryPlanDTO dto) {

        Assert.notNull(dto, "查询参数不能为空");

        if (CollUtil.isEmpty(dto.getBizNo())) {
            return Lists.newArrayList();
        }

        List<FlightPlanDO> flightPlanDOList = flightPlanRepository.list(Wrappers.<FlightPlanDO>lambdaQuery()
                .in(FlightPlanDO::getBizNo, dto.getBizNo())
                .eq(FlightPlanDO::getTenantId, dto.getTenantId()));

        return flightConverter.toFlightPlanVOList(flightPlanDOList);
    }

    @Override
    public List<AerodromeVO> batchQueryAerodrome(List<String> ids) {

        Assert.notEmpty(ids, "起降场ID不能为空");

        List<AerodromeDO> list = aerodromeRepository.list(Wrappers.lambdaQuery(AerodromeDO.class)
                .in(AerodromeDO::getUavPortId, ids));

        return flightConverter.toListAerodromeVO(list);
    }

    @Override
    public Boolean updateFlightPlan(FlightReportDTO dto) {
        Assert.notNull(dto, "参数不能为空!");
        Assert.hasText(dto.getPlanId(), "计划编号不能为空!");
        Assert.hasText(dto.getFlightReport(), "飞行报告不能为空!");

        FlightPlanDO plan = flightPlanRepository.getOne(Wrappers.<FlightPlanDO>lambdaQuery()
                .eq(FlightPlanDO::getPlanId, dto.getPlanId()));

        if (ObjectUtil.isNull(plan)) {
            LogUtil.error("当前计划:{}, 不存在!", dto.getPlanId());
            return false;
        }

        return flightPlanRepository.update(Wrappers.<FlightPlanDO>lambdaUpdate()
                .eq(FlightPlanDO::getPlanId, dto.getPlanId())
                .set(FlightPlanDO::getFlightReport, dto.getFlightReport())
                .set(FlightPlanDO::getGmtModified, LocalDateTime.now()));
    }

    @Override
    public RealTimeUavFlightVO queryLatestFlightsByReportTime(LocalDateTime reportTime, String planId) {
        RealTimeUavFlightDO realTimeUavFlightDO = realTimeUavFlightRepository.queryLatestFlightsByReportTime(reportTime, planId);
        return flightConverter.toRealTimeUavFlightVO(realTimeUavFlightDO);
    }

    @Override
    public List<FlightDemandPlanStatDTO> queryFlightDemandPlanStat(FlightDemandPlanStatQueryDTO queryDTO) {

        Map<String, DictionaryItemTreeDTO> leafNodesMap = new HashMap<>();
        if(CollectionUtils.isEmpty(queryDTO.getCategoryCodeList())) {
            //查找叶子类目的集合
            List<DictionaryTreeDTO> dictionaryTreeDTOS = dictionaryItemService.getDictionaryTreeByTypeAndTenantId(queryDTO.getCategoryType(), queryDTO.getTenantId());
            if (CollectionUtils.isEmpty(dictionaryTreeDTOS)) {
                return new ArrayList<>();
            }
            List<DictionaryItemTreeDTO> leafNodesCollection = new ArrayList<>();
            getLeafNodes(leafNodesCollection, dictionaryTreeDTOS.get(0).getItems());
            leafNodesMap = leafNodesCollection.stream().collect(Collectors.toMap(DictionaryItemTreeDTO::getCode, Function.identity()));
            List<String> childCategoryCodeList = new ArrayList<>(leafNodesMap.keySet());
            if (CollectionUtil.isEmpty(childCategoryCodeList)) {
                //不存在子类目是返回空集合
                return new ArrayList<>();
            }

            queryDTO.setCategoryCodeList(childCategoryCodeList);
        }

        FlightDemandPlanStatQuery queryDO = new FlightDemandPlanStatQuery();
        queryDO.setStartTime(queryDTO.getStartTime());
        queryDO.setDemandScene(queryDTO.getScene());
        queryDO.setCategoryCodeList(queryDTO.getCategoryCodeList());
        queryDO.setDemandType(queryDTO.getDemandType());
        queryDO.setUserNo(queryDTO.getUserNo());
        queryDO.setTenantId(queryDTO.getTenantId());
        queryDO.setAccount(queryDTO.getAccount());
        queryDO.setUserNoList(queryDTO.getUserNoList());
        queryDO.setStartTimeType(Optional.ofNullable(queryDTO.getStartTimeType()).map(Enum::name).orElse(PLAN_PLANED_TAKEOFF_TIME.name()));
        List<FlightDemandPlanStatDO> flightDemandPlanStatDOList = flightPlanRepository.queryFlightDemandPlanStat(queryDO);
        List<FlightDemandPlanStatDTO> flightDemandPlanStatDTOList = flightConverter.toFlightDemandPlanStatDTOList(flightDemandPlanStatDOList);

        if(MapUtils.isNotEmpty(leafNodesMap)) {
            for (FlightDemandPlanStatDTO flightDemandPlanStatDTO : flightDemandPlanStatDTOList) {
                flightDemandPlanStatDTO.setDemandCategoryName(leafNodesMap.get(flightDemandPlanStatDTO.getDemandCategoryCode()).getName());
            }
        }

        return flightDemandPlanStatDTOList;
    }

    @Override
    public String queryAlgorithmMonitorUrl(String planId) {
        FlightPlanDO plan = flightPlanRepository.getOne(Wrappers.<FlightPlanDO>lambdaQuery()
                .eq(FlightPlanDO::getPlanId, planId));
        return plan.getMonitorUrl();
    }

    @Override
    public FlightPlanVO queryPlanFlightDetail(String planId, String flightId) {

        FlightPlanDO flightPlan = getFlightPlanByMissionId(planId);
        if (ObjectUtil.isNull(flightPlan)) {
            LogUtil.error("当前计划:{}, 不存在!", planId);
            throw new BizException("当前计划不存在!");
        }

        FlightPlanVO flightPlanVOByPlanAndFlight = getFlightPlanVOByPlanAndFlight(planId, flightPlan, flightId);
        if (CollectionUtil.isEmpty(flightPlanVOByPlanAndFlight.getRealtimeUavInfoList())) {
            LogUtil.error("当前计划:{}架次:{}, 不存在!", planId, flightId);
            return null;
        }
        return flightPlanVOByPlanAndFlight;
    }

    @Override
    public List<FlightAchievementVO> queryAchievementList(String planId) {

        Assert.hasText(planId, "planId not be null!");

        FlightPlanDO planDO = getFlightPlanByMissionId(planId);

        if (ObjectUtil.isNull(planDO)) {
            return Lists.newArrayList();
        }

        List<FlightPlanAchievementDO> achievementList = achievementRepository.list(Wrappers.<FlightPlanAchievementDO>lambdaQuery()
                .eq(FlightPlanAchievementDO::getPlanNo, planDO.getOutPlanNo()));

        if (CollUtil.isEmpty(achievementList)) {
            return Lists.newArrayList();
        }

        List<FlightAchievementVO> result = flightConverter.toFlightAchievementVOList(achievementList);

        result.forEach(achievement -> achievement.setPlanId(planId));

        return result;
    }

    @Override
    public FlightAchievementVO queryAchievementCompareDate(FlightPlanAchievementQueryDTO queryDTO) {

        Assert.notNull(queryDTO, "queryDTO must not be null!");
        Assert.hasText(queryDTO.getPlanId(), "planId not be null!");
        Assert.notNull(queryDTO.getAchievementTypeEnum(), "achievementType not be null!");

        FlightPlanDO planDO = getFlightPlanByMissionId(queryDTO.getPlanId());

        if (ObjectUtil.isNull(planDO)) {
            return null;
        }

        FlightPlanAchievementDO currentAchievement = achievementRepository.getOne(Wrappers.<FlightPlanAchievementDO>lambdaQuery()
                .eq(FlightPlanAchievementDO::getPlanNo, planDO.getOutPlanNo())
                .eq(FlightPlanAchievementDO::getAchievementType, queryDTO.getAchievementTypeEnum().getDeepCode()));

        if (ObjectUtil.isNull(currentAchievement)) {
            LogUtil.warn("当前计划编号:{}, 暂无成此类型:{}, 成果", planDO.getPlanId(), queryDTO.getAchievementTypeEnum().getDeepCode());
            return null;
        }

        if (StrUtil.isEmpty(currentAchievement.getAchievementLocationStr())) {
            LogUtil.warn("当前计划编号:{}, 成果ID:{}, 暂无位置信息", planDO.getPlanId(), currentAchievement.getAchievementId());
            return null;
        }

        // 查询当前类型成果日期数据
        List<FlightPlanAchievementDO> nearAchievementDateList = achievementRepository.getAchievementList(
                ObjectUtil.isNull(currentAchievement.getAchievementTime()) ? null : currentAchievement.getAchievementTime().toLocalDate()
                , queryDTO.getAchievementTypeEnum().getDeepCode()
                , currentAchievement.getAchievementLocationStr());

        // 查询当前类型成果对应日期的上次成果
        FlightPlanAchievementDO lastAchievement = achievementRepository.getLastAchievement(currentAchievement.getPlanNo()
                , queryDTO.getCompareDate()
                , ObjectUtil.isNull(currentAchievement.getAchievementTime()) ? null : currentAchievement.getAchievementTime().toLocalDate()
                , queryDTO.getAchievementTypeEnum().getDeepCode()
                , currentAchievement.getAchievementLocationStr());

        if (ObjectUtil.isNull(lastAchievement)) {
            LogUtil.warn("当前计划编号:{}, 当前成果ID:{}, 未查询到上一次成果类型:{}, 的成果信息"
                    , planDO.getPlanId()
                    , currentAchievement.getAchievementId()
                    , queryDTO.getAchievementTypeEnum().getDeepCode());
            return null;
        }

        FlightAchievementVO flightAchievementVO = flightConverter.toFlightAchievementVO(lastAchievement);
        flightAchievementVO.setPlanId(queryDTO.getPlanId());

        if (CollUtil.isNotEmpty(nearAchievementDateList)) {
            flightAchievementVO.setAvailableCompareDate(nearAchievementDateList.stream()
                    .filter(e -> ObjectUtil.isNotNull(e.getAchievementTime()))
                    .map(e -> e.getAchievementTime().toLocalDate())
                    .distinct()
                    .sorted()
                    .collect(Collectors.toList()));
        }

        return flightAchievementVO;
    }

    /**
     * 获取叶子类目
     *
     * @param nodes
     * @return
     */
    private void getLeafNodes(List<DictionaryItemTreeDTO> leafNodesCollection, List<DictionaryItemTreeDTO> nodes) {
        if (CollectionUtils.isEmpty(nodes)) {
            return;
        }
        for (DictionaryItemTreeDTO node : nodes) {
            List<DictionaryItemTreeDTO> children = node.getChildren();
            if (CollectionUtils.isEmpty(children)) {
                leafNodesCollection.add(node);
            } else {
                getLeafNodes(leafNodesCollection, children);
            }
        }
    }

    private static String getAltitudeCategory(long altitude) {
        if (altitude <= 120) {
            return "120米以下";
        } else if (altitude <= 300) {
            return "120-300";
        } else if (altitude <= 600) {
            return "300-600";
        } else {
            return "600米以上";
        }
    }

    /**
     * 公共方法：构造查询条件，封装了根据统计日期过滤、根据创建时间倒序排序并限制返回一条数据的逻辑
     *
     * @param dateGetter    用于获取统计日期的属性方法引用
     * @param createdGetter 用于获取创建时间的属性方法引用
     * @param today         查询条件中的日期值
     * @param <T>           对应的实体类型
     * @return 封装好的 LambdaQueryWrapper
     */
    private <T> LambdaQueryWrapper<T> createQueryWrapper(SFunction<T, LocalDate> dateGetter,
                                                         SFunction<T, ?> createdGetter,
                                                         LocalDate today) {
        return Wrappers.<T>lambdaQuery()
                .eq(dateGetter, today)
                .orderByDesc(createdGetter)
                .last("limit 1");
    }

    /**
     * 公共方法：构造 FlightStat 对象并计算变化率
     *
     * @param statisticalCount           当前统计数值（例如：飞行中数量、里程、架次、预警数）
     * @param statisticalHistoryAvgCount 历史平均数值
     * @param <T>                        统计数值的类型
     * @return 包含当前数值和变化率的 FlightStat 对象
     */
    private <T> FlightStat<T> buildFlightStat(T statisticalCount, Object statisticalHistoryAvgCount) {
        FlightStat<T> stat = new FlightStat<>();
        stat.setT(statisticalCount);

        BigDecimal current = new BigDecimal(statisticalCount.toString());
        BigDecimal historyAvg = new BigDecimal(statisticalHistoryAvgCount.toString());

        // 如果历史平均值为0，则变化率设置为null，否则计算变化率
        stat.setChangeRate(historyAvg.compareTo(BigDecimal.ZERO) == 0
                ? null
                : current.subtract(historyAvg).divide(historyAvg, 4, RoundingMode.DOWN).toString());
        return stat;
    }

    private List<FlightPlanDO> getFlightPlansByMissionId(FlightMissionDO missionDO) {
        return flightPlanRepository.list(
                Wrappers.lambdaQuery(FlightPlanDO.class)
                        .eq(FlightPlanDO::getMissionId, missionDO.getMissionId())
                        .orderByDesc(FlightPlanDO::getGmtCreated));
    }

    private FlightMissionDO getFlightMissionByMissionId(String missionId) {
        return flightMissionRepository.getOne(
                Wrappers.lambdaQuery(FlightMissionDO.class)
                        .eq(FlightMissionDO::getMissionId, missionId));
    }

    private FlightPlanDO getFlightPlanByMissionId(String planId) {
        return flightPlanRepository.getOne(
                Wrappers.lambdaQuery(FlightPlanDO.class)
                        .eq(FlightPlanDO::getPlanId, planId));
    }
}
