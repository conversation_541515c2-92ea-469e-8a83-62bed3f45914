package com.deepinnet.spatiotemporalplatform.skyflow.enums;

public enum FlightViolationType {
    DEVIATE_ROUTE("deviate_route", "偏离航道"),
    ENTER_NO_FLY_ZONE("enter_no_fly_zone", "侵入禁飞区"),
    EXCEED_ALTITUDE("exceed_altitude", "超高飞行");

    private final String code;
    private final String description;

    FlightViolationType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 通过 code 获取枚举
    public static FlightViolationType fromCode(String code) {
        for (FlightViolationType type : FlightViolationType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的违规类型 code: " + code);
    }
}