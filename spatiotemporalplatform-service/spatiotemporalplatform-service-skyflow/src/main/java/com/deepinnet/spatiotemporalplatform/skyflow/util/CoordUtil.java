package com.deepinnet.spatiotemporalplatform.skyflow.util;

import cn.hutool.core.util.StrUtil;
import org.locationtech.jts.geom.*;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/8/8
 */
public class CoordUtil {

    private static final double PI = Math.PI;
    // <PERSON><PERSON><PERSON> 椭球
    private static final double A = 6378245.0;
    private static final double EE = 0.00669342162296594323;

    /** 入口：把 "lon,lat" 的 WGS84 坐标转为 JTS 的 GCJ02 Point */
    public static Point wgs84StringToGcj02Point(String lonLat) {
        if (StrUtil.isBlank(lonLat)) {
            return null;
        }

        GeometryFactory gf = new GeometryFactory(new PrecisionModel(), 0);

        String[] parts = lonLat.split(",");
        double wgsLon = Double.parseDouble(parts[0].trim());
        double wgsLat = Double.parseDouble(parts[1].trim());

        double[] gcj = wgs84ToGcj02(wgsLat, wgsLon);
        // 保留6位小数（可选）
        double gcjLon = scale6(gcj[1]);
        double gcjLat = scale6(gcj[0]);

        CoordinateSequence seq = gf.getCoordinateSequenceFactory()
                .create(new Coordinate[]{ new Coordinate(gcjLon, gcjLat) });
        return new Point(seq, gf);
    }

    /** WGS84 -> GCJ02；如果不在大陆范围直接原样返回 */
    public static double[] wgs84ToGcj02(double lat, double lon) {
        if (outOfChina(lat, lon)) {
            return new double[]{ lat, lon };
        }
        double dLat = transformLat(lon - 105.0, lat - 35.0);
        double dLon = transformLon(lon - 105.0, lat - 35.0);
        double radLat = lat / 180.0 * PI;
        double magic = Math.sin(radLat);
        magic = 1 - EE * magic * magic;
        double sqrtMagic = Math.sqrt(magic);
        dLat = (dLat * 180.0) / ((A * (1 - EE)) / (magic * sqrtMagic) * PI);
        dLon = (dLon * 180.0) / (A / sqrtMagic * Math.cos(radLat) * PI);
        double mgLat = lat + dLat;
        double mgLon = lon + dLon;
        return new double[]{ mgLat, mgLon };
    }

    private static boolean outOfChina(double lat, double lon) {
        // 简单包围盒：GCJ02 只在中国大陆有效（不含港澳台）
        return lon < 72.004 || lon > 137.8347 || lat < 0.8293 || lat > 55.8271;
    }

    private static double transformLat(double x, double y) {
        double ret = -100.0 + 2.0*x + 3.0*y + 0.2*y*y + 0.1*x*y + 0.2*Math.sqrt(Math.abs(x));
        ret += (20.0*Math.sin(6.0*x*PI) + 20.0*Math.sin(2.0*x*PI)) * 2.0 / 3.0;
        ret += (20.0*Math.sin(y*PI) + 40.0*Math.sin(y/3.0*PI)) * 2.0 / 3.0;
        ret += (160.0*Math.sin(y/12.0*PI) + 320*Math.sin(y*PI/30.0)) * 2.0 / 3.0;
        return ret;
    }

    private static double transformLon(double x, double y) {
        double ret = 300.0 + x + 2.0*y + 0.1*x*x + 0.1*x*y + 0.1*Math.sqrt(Math.abs(x));
        ret += (20.0*Math.sin(6.0*x*PI) + 20.0*Math.sin(2.0*x*PI)) * 2.0 / 3.0;
        ret += (20.0*Math.sin(x*PI) + 40.0*Math.sin(x/3.0*PI)) * 2.0 / 3.0;
        ret += (150.0*Math.sin(x/12.0*PI) + 300.0*Math.sin(x/30.0*PI)) * 2.0 / 3.0;
        return ret;
    }

    private static double scale6(double v) {
        return new BigDecimal(v).setScale(6, RoundingMode.DOWN).doubleValue();
    }

}
