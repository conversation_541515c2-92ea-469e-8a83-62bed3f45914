package com.deepinnet.spatiotemporalplatform.skyflow.task;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.localdata.integration.SurveillanceClient;
import com.deepinnet.localdata.integration.model.input.SurveillanceQueryDTO;
import com.deepinnet.localdata.integration.model.output.*;
import com.deepinnet.spatiotemporalplatform.base.config.TenantConfig;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightDetectionDO;
import com.deepinnet.spatiotemporalplatform.skyflow.convert.FlightDetectionConverter;
import com.deepinnet.spatiotemporalplatform.skyflow.enums.BigTypeEnums;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.FlightDetectionRepository;
import com.deepinnet.spatiotemporalplatform.skyflow.util.beidou.WktUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wong
 * @create 2025/3/6 15:52
 * @Description
 */
@Component
@RequiredArgsConstructor
@Profile({"local", "test", "alicloud", "demo", "sz-lg-gov"})
public class FlightDetectionTask {

    private final FlightDetectionRepository flightDetectionRepository;

    private final SurveillanceClient surveillanceClient;

    private final FlightDetectionConverter flightDetectionConverter;

    private final TenantConfig tenantConfig;

    private static final Long FIVE_MINUTES_MILLIS = TimeUnit.MINUTES.toMillis(5);

    //@Scheduled(cron = "0 0/5 * * * *")
    @Scheduled(fixedRate = 5, timeUnit = TimeUnit.SECONDS)
    public void process() {
        SurveillanceQueryDTO queryDTO = new SurveillanceQueryDTO();
        queryDTO.setCenterLatitude("22.72");
        queryDTO.setCenterLongitude("114.25");
//        queryDTO.setCenterLatitude("22.97827");
//        queryDTO.setCenterLongitude("113.96622");
        queryDTO.setRadius(10000);
        SurveillanceResponseDTO surveillanceResponseDTO = surveillanceClient.querySurveillanceData(queryDTO);

        LogUtil.info("【黑飞数据】从鸢飞查询到飞行数据：{}", JSONUtil.toJsonStr(surveillanceResponseDTO));
        if (surveillanceResponseDTO == null) {
            LogUtil.info("[雷达监测飞行数据定时任务]鸢飞响应为空，入参为：{}", JSONUtil.toJsonStr(queryDTO));
            return;
        }

        if (surveillanceResponseDTO.getStatus() != 200) {
            LogUtil.info("[雷达监测飞行数据定时任务]调用鸢飞失败，入参为：{},code={},msg={}", JSONUtil.toJsonStr(queryDTO), surveillanceResponseDTO.getStatus(), surveillanceResponseDTO.getMessage());
            return;
        }

        if (CollectionUtils.isEmpty(surveillanceResponseDTO.getData())) {
            LogUtil.info("[雷达监测飞行数据定时任务]鸢飞飞行数据返回空，入参为：{},code={},msg={}", JSONUtil.toJsonStr(queryDTO), surveillanceResponseDTO.getStatus(), surveillanceResponseDTO.getMessage());
            return;
        }

        // 过滤需要新增的数据
        List<FlightDetectionDO> needInsertFlightDetectionList = filterNeedInsertFlightDetectionList(surveillanceResponseDTO);

        // 批量保存新的数据
        if (CollectionUtils.isNotEmpty(needInsertFlightDetectionList)) {
            flightDetectionRepository.saveBatch(needInsertFlightDetectionList);
            LogUtil.info("[雷达监测飞行数据定时任务]执行成功，共保存{}条数据", needInsertFlightDetectionList.size());
        }
    }

    private List<FlightDetectionDO> filterNeedInsertFlightDetectionList(SurveillanceResponseDTO flightDetectionResponse) {
        List<SurveillanceDataDTO> flightDetectionList = flightDetectionResponse.getData();
        List<FlightDetectionDO> flightDetectionDOList = flightDetectionConverter.toFlightDetectionDOList(flightDetectionList);
        if (CollectionUtils.isEmpty(flightDetectionDOList)) {
            return null;
        }

        flightDetectionDOList.removeIf(e -> !(StrUtil.equals(BigTypeEnums.BIRD.getCode(), e.getBigType()) || StrUtil.equals(BigTypeEnums.UAV.getCode(), e.getBigType())));
        if (CollectionUtils.isEmpty(flightDetectionDOList)) {
            return null;
        }

        List<String> osiIds = flightDetectionDOList.stream()
                .distinct()
                .map(FlightDetectionDO::getOsid)
                .collect(Collectors.toList());
        flightDetectionDOList.forEach(flightDetectionDO -> flightDetectionDO.setTenantId(tenantConfig.getTenantId()));

        List<FlightDetectionDO> existFlightDetectionList = flightDetectionRepository.list(Wrappers.lambdaQuery(FlightDetectionDO.class)
                .in(FlightDetectionDO::getOsid, osiIds)
                .orderByDesc(FlightDetectionDO::getTime)
                // 这里需要控制范围，不然容易全表扫描，现在这里只查询5分钟之内的数据有没有重复
                .ge(FlightDetectionDO::getTime, System.currentTimeMillis() - FIVE_MINUTES_MILLIS)
                .le(FlightDetectionDO::getTime, System.currentTimeMillis()));

        // 构建 Map<String, Set<Long>>，存储 osId 对应的 time 集合
        Map<String, Set<Long>> existFlightDetectionMap = existFlightDetectionList.stream()
                .collect(Collectors.groupingBy(
                        FlightDetectionDO::getOsid,
                        Collectors.mapping(FlightDetectionDO::getTime, Collectors.toSet())
                ));

        // 过滤掉已经存在的 osId + time 组合
        List<FlightDetectionDO> needInsertFlightDetectionList = flightDetectionDOList.stream()
                .filter(flightDetectionDO -> {
                    Set<Long> existTimes = existFlightDetectionMap.get(flightDetectionDO.getOsid());
                    return existTimes == null || !existTimes.contains(flightDetectionDO.getTime());
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(needInsertFlightDetectionList)) {
            needInsertFlightDetectionList.forEach(flightDetectionDO -> flightDetectionDO.setCoordinate(WktUtil.toPoint(flightDetectionDO.getLongitude(), flightDetectionDO.getLatitude())));
        }

        return needInsertFlightDetectionList;
    }
}
