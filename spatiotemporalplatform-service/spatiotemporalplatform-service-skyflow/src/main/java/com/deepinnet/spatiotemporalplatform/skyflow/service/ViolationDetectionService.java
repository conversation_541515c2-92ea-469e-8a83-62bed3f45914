package com.deepinnet.spatiotemporalplatform.skyflow.service;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.dto.ViolationDetectionDTO;
import com.deepinnet.spatiotemporalplatform.dto.ViolationDetectionQueryDTO;
import com.deepinnet.spatiotemporalplatform.dto.ViolationGroupStatisticsDTO;

import java.util.List;

/**
 * 违规检测Service接口
 *
 * <AUTHOR>
 */
public interface ViolationDetectionService {

    /**
     * 按运营主体和违规类型分组统计并分页
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    List<ViolationGroupStatisticsDTO> groupByOperatingEntityAndType(ViolationDetectionQueryDTO queryDTO);

    /**
     * 分页查询违规检测数据
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    CommonPage<ViolationDetectionDTO> pageQueryDetail(ViolationDetectionQueryDTO queryDTO);
} 