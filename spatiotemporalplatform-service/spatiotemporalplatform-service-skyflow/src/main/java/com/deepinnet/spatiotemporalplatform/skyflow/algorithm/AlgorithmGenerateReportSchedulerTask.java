package com.deepinnet.spatiotemporalplatform.skyflow.algorithm;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.infra.api.dto.FileInfoDTO;
import com.deepinnet.spatiotemporalplatform.common.ScheduleTaskExecService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightPlanDO;
import com.deepinnet.spatiotemporalplatform.dto.FlightReportGenerateDTO;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.FlightPlanRepository;
import com.deepinnet.spatiotemporalplatform.skyflow.service.FlightReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 算法侧数据监听和转换任务
 * Description: 监听算法侧的事件数据变更，将其转换为EventMonitorService支持的数据格式
 * Date: 2025/5/30
 * Author: lijunheng
 */
@Slf4j
@Component
@Profile({"local", "test", "alicloud", "sz-lg-gov"})
public class AlgorithmGenerateReportSchedulerTask implements ScheduleTaskExecService {

    @Resource
    private FlightPlanRepository flightPlanRepository;

    @Resource
    private FlightReportService flightReportService;

    //每天凌晨3点执行
    @Override
    @Scheduled(cron = "0 0 3 * * ? ")
    public void execute() {
        List<String> planIdList = flightPlanRepository.queryHasAchievementPlan();

        planIdList.parallelStream().forEach(planId -> {
            FlightReportGenerateDTO generateDTO = new FlightReportGenerateDTO();
            generateDTO.setPlanId(planId);
            FileInfoDTO fileInfoDTO = flightReportService.generateFlightReportDocument(generateDTO);
            if (fileInfoDTO != null) {
                    flightPlanRepository.update(Wrappers.<FlightPlanDO>lambdaUpdate()
                            .eq(FlightPlanDO::getPlanId, planId)
                            .set(FlightPlanDO::getFlightReport, fileInfoDTO.getUrl())
                            .set(FlightPlanDO::getGmtModified, LocalDateTime.now()));
            }
        });
    }
}
