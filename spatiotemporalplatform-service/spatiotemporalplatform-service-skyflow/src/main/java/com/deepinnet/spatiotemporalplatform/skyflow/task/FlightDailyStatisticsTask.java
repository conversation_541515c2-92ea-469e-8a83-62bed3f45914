package com.deepinnet.spatiotemporalplatform.skyflow.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.FlightDailyStatisticsRepository;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightDailyStatisticsDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightPlanDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.RealTimeUavFlightDO;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightQuery;
import com.deepinnet.spatiotemporalplatform.skyflow.enums.FlightPlanStatusEnum;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.FlightPlanRepository;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.RealTimeUavFlightRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Profile;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *   每日飞行统计
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/10
 */
@Component
@Profile({"local", "test", "alicloud", "demo", "sz-lg-gov"})
@RequiredArgsConstructor
public class FlightDailyStatisticsTask {

    private final FlightPlanRepository flightPlanRepository;

    private final RealTimeUavFlightRepository realTimeUavFlightRepository;

    private final FlightDailyStatisticsRepository flightDailyStatisticsRepository;

    @Scheduled(cron = "0 0 0 * * *")
    public void statistics() {
        LogUtil.info("========[飞行统计]-企业飞行统计开始执行========");

        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalDateTime yesterday = currentDateTime.minusDays(1);
        LocalDate localDate = yesterday.toLocalDate();

        LocalDateTime yesterdayStartDateTime = yesterday.withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime yesterdayEndDateTime = yesterday.withHour(23).withMinute(59).withSecond(59).withNano(0);

        long yesterdayStartEpochMilli = yesterdayStartDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        long yesterdayEndEpochMilli = yesterdayEndDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();

        List<FlightPlanDO> flightPlanList = queryYesterdayFlightPlans(yesterdayStartEpochMilli, yesterdayEndEpochMilli);

        if (CollUtil.isEmpty(flightPlanList)) {
            LogUtil.info("========[飞行统计]-企业飞行统计结束，未查询到:{}, 日期的飞行计划========", localDate.toString());
            return;
        }

        List<String> planIds = flightPlanList.stream().map(FlightPlanDO::getPlanId).collect(Collectors.toList());

        List<RealTimeUavFlightDO> realTimeUavFlightList = realTimeUavFlightRepository.list(Wrappers.<RealTimeUavFlightDO>lambdaQuery()
                .in(RealTimeUavFlightDO::getPlanId, planIds));

        if (CollUtil.isEmpty(realTimeUavFlightList)) {
            LogUtil.info("========[飞行统计]-企业飞行统计结束，未查询到:{}, 日期的实时飞行数据========", localDate.toString());
            return;
        }

        Map<String, List<RealTimeUavFlightDO>> planRealTimeUavFlightMap = realTimeUavFlightList.stream()
                .collect(Collectors.groupingBy(
                        RealTimeUavFlightDO::getPlanId,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.stream()
                                        .sorted(Comparator.comparing(RealTimeUavFlightDO::getReportTime))
                                        .collect(Collectors.toList())
                        )
                ));

        Map<String, List<FlightPlanDO>> flightUnitPlanMap = flightPlanList.stream().collect(Collectors.groupingBy(FlightPlanDO::getFlightUnitId));

        calculateEveryFlightUnit(localDate, yesterdayStartEpochMilli, flightUnitPlanMap, planRealTimeUavFlightMap);

        LogUtil.info("========[飞行统计]-企业飞行统计结束执行========");

    }

    private void calculateEveryFlightUnit(LocalDate yesterdayDate, long yesterdayStartEpochMilli, Map<String, List<FlightPlanDO>> flightUnitPlanMap, Map<String, List<RealTimeUavFlightDO>> planRealTimeUavFlightMap) {
        flightUnitPlanMap.forEach((flightUnitId, flightPlans) -> {
            int flightCount = flightPlans.size();

            BigDecimal totalFlightMiles = BigDecimal.ZERO;
            BigDecimal totalFlightDuration = BigDecimal.ZERO;

            for (FlightPlanDO flightPlan : flightPlans) {
                String planId = flightPlan.getPlanId();
                List<RealTimeUavFlightDO> flightDataList = planRealTimeUavFlightMap.getOrDefault(planId, Collections.emptyList());

                if (!flightDataList.isEmpty()) {
                    RealTimeUavFlightDO firstData = flightDataList.get(0);
                    RealTimeUavFlightDO lastData = flightDataList.size() > 1 ?
                            flightDataList.get(flightDataList.size() - 1) : firstData;

                    BigDecimal firstMiles = parseBigDecimal(firstData.getFlightMiles());
                    BigDecimal lastMiles = parseBigDecimal(lastData.getFlightMiles());
                    BigDecimal firstDuration = parseBigDecimal(firstData.getFlightDuration());
                    BigDecimal lastDuration = parseBigDecimal(lastData.getFlightDuration());

                    BigDecimal planMiles = flightDataList.size() > 1 ?
                            lastMiles.subtract(firstMiles) : lastMiles;
                    BigDecimal planDuration = flightDataList.size() > 1 ?
                            lastDuration.subtract(firstDuration) : lastDuration;

                    totalFlightMiles = totalFlightMiles.add(planMiles);
                    totalFlightDuration = totalFlightDuration.add(planDuration);
                }
            }

            String totalFlightMilesStr = totalFlightMiles.toString();
            String totalFlightDurationStr = totalFlightDuration.toString();

            FlightDailyStatisticsDO flightDailyStatisticsDO = buildFlightDailyStatisticsDO(yesterdayDate, yesterdayStartEpochMilli, flightUnitId, flightPlans, flightCount, totalFlightDurationStr, totalFlightMilesStr);

            saveFlightDailyStatistics(flightDailyStatisticsDO);
        });
    }

    private void saveFlightDailyStatistics(FlightDailyStatisticsDO flightDailyStatisticsDO) {
        try {
            flightDailyStatisticsRepository.save(flightDailyStatisticsDO);
        } catch (DuplicateKeyException duplicateKeyException) {
            LogUtil.warn("========企业飞行统计数据:{}, 已存在, 重复插入========", JSONObject.toJSONString(flightDailyStatisticsDO));
        } catch (Exception e) {
            LogUtil.error("========企业飞行统计数据:{}, 插入失败, 异常信息:{}", e);
        }
    }

    private static FlightDailyStatisticsDO buildFlightDailyStatisticsDO(LocalDate yesterdayDate, long yesterdayStartEpochMilli, String flightUnitId, List<FlightPlanDO> flightPlans, int flightCount, String totalFlightDurationStr, String totalFlightMilesStr) {
        FlightDailyStatisticsDO flightDailyStatisticsDO = new FlightDailyStatisticsDO();
        flightDailyStatisticsDO.setFlightUnitId(flightUnitId);
        flightDailyStatisticsDO.setFlightUnit(flightPlans.get(0).getFlightUnit());
        flightDailyStatisticsDO.setFlightCount(flightCount);
        flightDailyStatisticsDO.setFlightDuration(Convert.toInt(totalFlightDurationStr));
        flightDailyStatisticsDO.setFlightMiles(totalFlightMilesStr);
        flightDailyStatisticsDO.setDate(yesterdayDate.toString());
        flightDailyStatisticsDO.setTimestamp(yesterdayStartEpochMilli);
        return flightDailyStatisticsDO;
    }

    private List<FlightPlanDO> queryYesterdayFlightPlans(long yesterdayStartEpochMilli, long yesterdayEndEpochMilli) {
        FlightQuery flightQuery = new FlightQuery();
        flightQuery.setStartTime(yesterdayStartEpochMilli);
        flightQuery.setEndTime(yesterdayEndEpochMilli);
        flightQuery.setStatus(FlightPlanStatusEnum.FLIGHT_STATUS);
        return flightPlanRepository.listFlightPlanByCondition(flightQuery);
    }

    // 安全解析字符串为BigDecimal的辅助方法
    private BigDecimal parseBigDecimal(String value) {
        if (value == null || value.trim().isEmpty()) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(value);
        } catch (NumberFormatException e) {
            LogUtil.warn("========[飞行统计]-解析数值异常: {}========", value);
            return BigDecimal.ZERO;
        }
    }

}
