package com.deepinnet.spatiotemporalplatform.skyflow.mqtt;

import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.spatiotemporalplatform.skyflow.mqtt.processor.MessageBizProcessor;
import org.springframework.context.annotation.Profile;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2025/3/5 16:00
 * @Description
 */
@Component
@Profile("!qz-gov-inner-network")

public class YuanFeiMessageHandler implements MessageHandler {

    /**
     * 实时飞行数据MQTT消息主题
     */
    // 鸢飞正常无人机地址
//    public static final String REALTIME_FLY_DATA_TOPIC = "/anyang/realtime/fly/uav/#";

    // 鸢飞压测无人机地址
    public static final String REALTIME_FLY_DATA_TOPIC = "/trajectory/point/#";

    /**
     * 预警MQTT消息主题
     */
    public static final String WARNING_TOPIC = "/jtgk/warning/#";

    public static final String AIRSPACE_STATUS_UPDATE_TOPIC = "/hatc/airspace/AirspaceStatusUpdateMessage";


    @Resource
    private List<MessageBizProcessor> messageBizHandlers;

    @Override
    public void handleMessage(Message<?> message) {
        String topic = (String) message.getHeaders().get("mqtt_receivedTopic");
        String payload = message.getPayload().toString();
        LogUtil.info("received mqtt message success, topic={}", topic);

        // 根据topic和payload处理消息
        for (MessageBizProcessor handler : messageBizHandlers) {
            if (topic.startsWith(handler.getTopicPrefix())) {
                handler.handle(topic, payload);
            }
        }
    }
}
