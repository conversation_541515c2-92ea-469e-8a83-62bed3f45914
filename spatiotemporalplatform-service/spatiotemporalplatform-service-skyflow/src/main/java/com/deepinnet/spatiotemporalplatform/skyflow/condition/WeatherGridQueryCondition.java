package com.deepinnet.spatiotemporalplatform.skyflow.condition;

import com.deepinnet.spatiotemporalplatform.enums.WeatherGridFactor;
import com.deepinnet.spatiotemporalplatform.model.common.PageQuery;
import lombok.Data;
import org.locationtech.jts.geom.Polygon;

/**
 * <PERSON><PERSON> z<PERSON>
 * Date 2025-04-21
 **/

@Data
public class WeatherGridQueryCondition extends PageQuery {

    private Polygon bounds;

    private WeatherGridFactor factor;

    private Integer windForceAltitude;
}
