package com.deepinnet.spatiotemporalplatform.skyflow.mqtt.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.infra.api.dto.FileInfoDTO;
import com.deepinnet.localdata.integration.error.RepeatExecException;
import com.deepinnet.localdata.integration.model.outsidebean.FlightMonitorTaskResp;
import com.deepinnet.skyflow.operationcenter.dto.FlightOrderProductUsageDTO;
import com.deepinnet.spatiotemporalplatform.base.config.TenantConfig;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.FlightRecordRepository;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightPlanDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightRecordDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.RealTimeUavFlightDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.UavRealtimeMqRecordDO;
import com.deepinnet.spatiotemporalplatform.dto.FlightAlgorithmMonitorCreateDTO;
import com.deepinnet.spatiotemporalplatform.dto.FlightReportGenerateDTO;
import com.deepinnet.spatiotemporalplatform.dto.FlightTaskNotifyDTO;
import com.deepinnet.spatiotemporalplatform.enums.FlightEventTypeEnum;
import com.deepinnet.spatiotemporalplatform.model.util.CoordinateUtil;
import com.deepinnet.spatiotemporalplatform.skyflow.client.FlightOrderRemoteClient;
import com.deepinnet.spatiotemporalplatform.skyflow.client.FlightTaskNotifyClient;
import com.deepinnet.spatiotemporalplatform.skyflow.convert.FlightConverter;
import com.deepinnet.spatiotemporalplatform.skyflow.enums.FlightStatusEnum;
import com.deepinnet.spatiotemporalplatform.skyflow.error.BizErrorCode;
import com.deepinnet.spatiotemporalplatform.skyflow.mqtt.body.RealTimeFlyDataMsgBody;
import com.deepinnet.spatiotemporalplatform.skyflow.mqtt.topic.YuanFeiMqttTopicEnum;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.FlightPlanRepository;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.RealTimeUavFlightRepository;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.UavRealtimeMqRecordRepository;
import com.deepinnet.spatiotemporalplatform.skyflow.service.FlightReportService;
import com.deepinnet.spatiotemporalplatform.skyflow.service.FlightService;
import com.deepinnet.spatiotemporalplatform.vo.FlightPlanVO;
import com.deepinnet.spatiotemporalplatform.vo.RealTimeUavFlightVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 实时飞行数据消息处理器
 *
 * <AUTHOR>
 * @version 2025-03-15
 */
@Slf4j
@Component
@Profile("!qz-gov-inner-network")

public class RealTimeFlyDataMsgProcessor implements MessageBizProcessor {

    @Value("${inner-api.algorithm.monitor-video.url}")
    private String algorithmMonitorVideoUrl;

    @Resource
    private RealTimeUavFlightRepository realTimeUavFlightRepository;

    @Resource
    private UavRealtimeMqRecordRepository uavRealtimeMqRecordRepository;

    @Resource
    private FlightConverter flightConverter;

    @Resource
    private FlightRecordRepository flightRecordRepository;

    @Resource
    private FlightOrderRemoteClient flightOrderRemoteClient;

    @Resource
    private FlightTaskNotifyClient flightTaskNotifyClient;

    @Resource
    private FlightService flightService;

    @Resource
    private FlightPlanRepository flightPlanRepository;

    @Resource
    private TenantConfig tenantConfig;

    @Resource
    private FlightReportService flightReportService;

    @Override
    public void handle(String topic, String message) {
        // 处理低空飞行实时数据消息
        LogUtil.info("开始处理实时飞行数据消息,消息内容:{}", message);

        // 反序列化消息体
        var flyDataMsgBody = JSON.parseObject(message, RealTimeFlyDataMsgBody.class);

        if (ObjectUtil.isNull(flyDataMsgBody)) {
            LogUtil.warn("消息反序列化失败,消息内容:{}", message);
            return;
        }

        saveMqData(flyDataMsgBody);

        FlightPlanVO flightPlanVO = flightService.queryPlanDetail(flyDataMsgBody.getPlanId());

        if (ObjectUtil.isNull(flightPlanVO)) {
            LogUtil.warn("消息中包含的飞行计划不存在, planId:{}", flyDataMsgBody.getPlanId());
            return;
        }

        var realTimeUavFlightDO = saveFlightUavRealTimeData(flyDataMsgBody);

        List<RealTimeUavFlightVO> realTimeUavFlightList = flightPlanVO.getRealtimeUavInfoList();

        saveFlightRecord(realTimeUavFlightList, flightPlanVO, realTimeUavFlightDO);

        // 首次有飞行数据时推动该订单的用量
        if (StrUtil.isNotBlank(flightPlanVO.getBizNo()) && CollUtil.isEmpty(realTimeUavFlightList)) {
            LogUtil.info("飞行计划:{}, 需求编号:{}, 增加订单用量", flightPlanVO.getPlanId(), flightPlanVO.getBizNo());
            FlightOrderProductUsageDTO flightOrderProductUsageDTO = new FlightOrderProductUsageDTO();
            flightOrderProductUsageDTO.setRequirementNo(flightPlanVO.getBizNo());

            flightOrderRemoteClient.orderProductUsage(flightOrderProductUsageDTO);
        }

        // 通知算法对视频进行分析
        String sn = ObjectUtil.isNotNull(flightPlanVO.getUavInfo()) ? flightPlanVO.getUavInfo().getSn() : null;

        String liveUrl = StrUtil.isNotBlank(sn) ? flightService.queryLiveUrl(sn, "https") : null;

        // notify remote client
        notifyTaskStartOrEnd(flightPlanVO, liveUrl, realTimeUavFlightDO);

        LogUtil.info("实时飞行消息入库成功,实时飞行消息:{}", JSONObject.toJSONString(realTimeUavFlightDO));
    }

    private RealTimeUavFlightDO saveFlightUavRealTimeData(RealTimeFlyDataMsgBody flyDataMsgBody) {
        // 预警消息入库
        var realTimeUavFlightDO = buildRealTimeUavFlightDO(flyDataMsgBody);


        // 保存实时飞行数据
        safeSave(
                () -> realTimeUavFlightRepository.save(realTimeUavFlightDO),
                realTimeUavFlightDO,
                "实时飞行记录已存在, data: {}",
                "实时飞行数据保存失败, data: {}",
                BizErrorCode.REAL_TIME_FLIGHT_SAVE_ERROR
        );
        return realTimeUavFlightDO;
    }

    private void saveMqData(RealTimeFlyDataMsgBody flyDataMsgBody) {
        // 转换后的数据对象，避免重复转换
        UavRealtimeMqRecordDO recordDO = flightConverter.toUavRealtimeMqRecordDO(flyDataMsgBody);

        // 保存原始数据
        safeSave(
                () -> uavRealtimeMqRecordRepository.save(recordDO),
                recordDO,
                "消息中包含的实时飞行数据已存在, data: {}",
                "消息入库失败, data: {}",
                BizErrorCode.MESSAGE_SAVE_ERROR
        );
    }

    private void saveFlightRecord(List<RealTimeUavFlightVO> realTimeUavFlightList, FlightPlanVO flightPlanVO, RealTimeUavFlightDO realTimeUavFlightDO) {
        Long groundTime = null;
        Long landTime = null;
        long reportTime = realTimeUavFlightDO.getReportTime().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        String currentStatus = realTimeUavFlightDO.getStatus();

        List<RealTimeUavFlightVO> safeList = realTimeUavFlightList == null ? Collections.emptyList() : realTimeUavFlightList;

        if (safeList.isEmpty()) {
            if (StrUtil.equals(currentStatus, FlightStatusEnum.GROUND.getCode())) {
                groundTime = reportTime;
            } else {
                if (StrUtil.equals(currentStatus, FlightStatusEnum.LAND.getCode())) {
                    landTime = reportTime;
                }
            }
        } else {
            Optional<Long> firstGround = safeList.stream()
                    .filter(r -> FlightStatusEnum.GROUND.getCode().equals(r.getStatus()))
                    .map(RealTimeUavFlightVO::getReportTime)
                    .min(Long::compare);

            Optional<Long> firstLandTs = safeList.stream()
                    .filter(r -> FlightStatusEnum.LAND.getCode().equals(r.getStatus()))
                    .map(RealTimeUavFlightVO::getReportTime)
                    .min(Long::compare);

            if (StrUtil.equals(currentStatus, FlightStatusEnum.GROUND.getCode())) {
                groundTime = firstGround.orElse(reportTime);
            }

            if (StrUtil.equals(currentStatus, FlightStatusEnum.LAND.getCode())) {
                landTime = firstLandTs.orElse(reportTime);
            }
        }

        FlightRecordDO recordDO = flightRecordRepository.getOne(Wrappers.<FlightRecordDO>lambdaQuery()
                .eq(FlightRecordDO::getPlanId, flightPlanVO.getPlanId()));

        FlightRecordDO record = new FlightRecordDO();
        record.setPlanId(flightPlanVO.getPlanId());
        record.setTakeoffTime(groundTime);
        record.setLandingTime(landTime);
        record.setGmtModified(LocalDateTime.now());
        record.setTenantId(tenantConfig.getTenantId());

        safeSave(
                () -> {
                    if (ObjectUtil.isNull(recordDO)) {
                        flightRecordRepository.save(record);
                    } else {
                        flightRecordRepository.update(record, Wrappers.<FlightRecordDO>lambdaUpdate()
                                .eq(FlightRecordDO::getPlanId, flightPlanVO.getPlanId()));
                    }
                },
                record,
                "飞行事件记录已存在, data: {}",
                "飞行事件记录保存失败, data: {}",
                BizErrorCode.FLIGHT_RECORD_SAVE_ERROR
        );
    }

    private void notifyTaskStartOrEnd(FlightPlanVO flightPlanVO, String liveUrl, RealTimeUavFlightDO realTimeUavFlightDO) {
        try {
            if (StrUtil.isNotBlank(flightPlanVO.getBizNo())
                    && StrUtil.isNotBlank(liveUrl)
                    && FlightStatusEnum.GROUND.getCode().equals(realTimeUavFlightDO.getStatus())) {

                FlightAlgorithmMonitorCreateDTO flightAlgorithmMonitorCreateDTO = new FlightAlgorithmMonitorCreateDTO();
                flightAlgorithmMonitorCreateDTO.setFlightTaskId(flightPlanVO.getPlanId());
                flightAlgorithmMonitorCreateDTO.setVideoUrl(liveUrl);
                List<FlightEventTypeEnum> eventTypeList = new ArrayList<>();
                eventTypeList.add(FlightEventTypeEnum.PARKING_VIOLATION);
                flightAlgorithmMonitorCreateDTO.setEventTypeList(eventTypeList);

                FlightMonitorTaskResp flightMonitorTaskResp = flightTaskNotifyClient.startFlightTask(flightAlgorithmMonitorCreateDTO);
                //算法任务启动时会返回一个检测视频流地址，需要保存到计划表里，因为计划和飞行记录是一一对应的
                String monitorUrl = String.format("%s/live/%s_alg/%s", algorithmMonitorVideoUrl, flightMonitorTaskResp.getId(), "hls.m3u8");
                flightPlanRepository.update(Wrappers.<FlightPlanDO>lambdaUpdate()
                        .eq(FlightPlanDO::getPlanId, flightPlanVO.getPlanId())
                        .set(FlightPlanDO::getMonitorUrl, monitorUrl)
                        .set(FlightPlanDO::getGmtModified, LocalDateTime.now()));
            }

            if (StrUtil.isNotBlank(flightPlanVO.getBizNo())
                    && FlightStatusEnum.LAND.getCode().equals(realTimeUavFlightDO.getStatus())) {

                FlightTaskNotifyDTO flightTaskNotifyDTO = new FlightTaskNotifyDTO();
                flightTaskNotifyDTO.setFlightTaskId(flightPlanVO.getPlanId());

                flightTaskNotifyClient.endFlightTask(flightTaskNotifyDTO);

                //通知完成后生成飞行报告
                FlightReportGenerateDTO generateDTO = new FlightReportGenerateDTO();
                generateDTO.setPlanId(flightPlanVO.getPlanId());
                FileInfoDTO fileInfoDTO = flightReportService.generateFlightReportDocument(generateDTO);
                if (fileInfoDTO != null) {
                    flightPlanRepository.update(Wrappers.<FlightPlanDO>lambdaUpdate()
                            .eq(FlightPlanDO::getPlanId, flightPlanVO.getPlanId())
                            .set(FlightPlanDO::getFlightReport, fileInfoDTO.getUrl())
                            .set(FlightPlanDO::getGmtModified, LocalDateTime.now()));
                }
            }
        } catch (RepeatExecException e) {
            log.warn("重复请求, data: {}", e.getMessage(), e);
        }
    }

    private RealTimeUavFlightDO buildRealTimeUavFlightDO(RealTimeFlyDataMsgBody flyDataMsgBody) {
        RealTimeUavFlightDO realTimeUavFlightDO = new RealTimeUavFlightDO();
        realTimeUavFlightDO.setPlanId(flyDataMsgBody.getPlanId());
        realTimeUavFlightDO.setUavId(flyDataMsgBody.getUavId());
        realTimeUavFlightDO.setFlightId(flyDataMsgBody.getFlightId());
        realTimeUavFlightDO.setStatus(ObjectUtil.isNull(FlightStatusEnum.getByStateCode(flyDataMsgBody.getState())) ? null : FlightStatusEnum.getByStateCode(flyDataMsgBody.getState()).getCode());
        // 原始数据，鸢飞×100返回的
        if (ObjectUtil.isNotNull(flyDataMsgBody)) {
            BigDecimal sourceSpeed = new BigDecimal(String.valueOf(flyDataMsgBody.getSpeed()));
            BigDecimal targetSpeed = sourceSpeed.divide(new BigDecimal("100"), 4, RoundingMode.DOWN).multiply(new BigDecimal("3.6"));
            realTimeUavFlightDO.setFlightSpeed(targetSpeed.setScale(1, RoundingMode.DOWN).stripTrailingZeros().toString());
        }
        realTimeUavFlightDO.setUavPosition(buildUavPosition(flyDataMsgBody.getLon(), flyDataMsgBody.getLat()));
        // 高度单位是米，鸢飞乘以1000返回的
        realTimeUavFlightDO.setUavAltitude(String.valueOf(flyDataMsgBody.getHeight() / 1000));
        realTimeUavFlightDO.setElevation(String.valueOf(flyDataMsgBody.getAlt() / 1000));
        realTimeUavFlightDO.setFlightDuration(String.valueOf(flyDataMsgBody.getFlightDuration()));
        if (ObjectUtil.isNotNull(flyDataMsgBody.getFinishDistance())) {
            BigDecimal flyDistance = new BigDecimal(String.valueOf(flyDataMsgBody.getFinishDistance()));
            realTimeUavFlightDO.setFlightMiles(flyDistance.divide(new BigDecimal("1000"), 1, RoundingMode.DOWN).toString());
        }
        realTimeUavFlightDO.setVoltage(ObjectUtil.isNull(flyDataMsgBody.getVoltage()) ? null : String.valueOf(flyDataMsgBody.getVoltage() / 10));
        realTimeUavFlightDO.setSoc(flyDataMsgBody.getSoc());
        realTimeUavFlightDO.setReportTime(LocalDateTime.ofInstant(
                Instant.ofEpochMilli(flyDataMsgBody.getTimestamp()),
                ZoneId.of("Asia/Shanghai")
        ));
        realTimeUavFlightDO.setGmtCreated(LocalDateTime.now());
        realTimeUavFlightDO.setGmtModified(LocalDateTime.now());
        realTimeUavFlightDO.setTenantId(tenantConfig.getTenantId());
        return realTimeUavFlightDO;
    }

    public static String buildUavPosition(Long lon, Long lat) {
        // 经度, wgs84经度 X 1E7
        double x = lon / 1E7;
        double y = lat / 1E7;
        var coordinate = CoordinateUtil.wgs84ToGcj02(String.valueOf(x), String.valueOf(y));
        return coordinate.getLongitude() + "," + coordinate.getLatitude();
    }


    @Override
    public String getTopicPrefix() {
        return YuanFeiMqttTopicEnum.REAL_TIME_FLY_DATA.getTopic();
    }

    /**
     * 通用保存方法，捕获 DuplicateKeyException 和其它异常。
     *
     * @param operation       执行保存操作的 lambda 表达式
     * @param data            保存的数据对象（用于日志输出）
     * @param duplicateLogMsg 重复数据的日志模板，例如 "消息中包含的实时飞行数据已存在, data: {}"
     * @param errorLogMsg     失败的日志模板，例如 "消息入库失败, data: {}"
     * @param errorCode       出现异常时抛出的业务错误码
     */
    private void safeSave(Runnable operation, Object data, String duplicateLogMsg, String errorLogMsg, BizErrorCode errorCode) {
        try {
            operation.run();
        } catch (DuplicateKeyException ex) {
            LogUtil.warn(duplicateLogMsg, JSONObject.toJSONString(data));
        } catch (Exception e) {
            LogUtil.warn(errorLogMsg, JSONObject.toJSONString(data), e);
            throw new BizException(errorCode.getCode(), errorCode.getDesc());
        }
    }

}
