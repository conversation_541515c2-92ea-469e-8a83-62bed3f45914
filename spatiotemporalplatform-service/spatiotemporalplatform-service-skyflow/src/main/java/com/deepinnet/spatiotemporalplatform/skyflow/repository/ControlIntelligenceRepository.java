package com.deepinnet.spatiotemporalplatform.skyflow.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 管控情报Repository接口
 *
 * <AUTHOR>
 */
public interface ControlIntelligenceRepository {

    /**
     * 保存管控情报信息
     *
     * @param controlIntelligenceDO 管控情报信息
     * @return 是否保存成功
     */
    boolean save(ControlIntelligenceDO controlIntelligenceDO);

    /**
     * 批量保存管控情报信息
     *
     * @param controlIntelligenceDOList 管控情报信息列表
     * @return 是否保存成功
     */
    boolean saveBatch(List<ControlIntelligenceDO> controlIntelligenceDOList);

    /**
     * 根据ID查询管控情报信息
     *
     * @param id 管控情报ID
     * @return 管控情报信息
     */
    ControlIntelligenceDO getById(Long id);

    /**
     * 根据条件查询管控情报数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 管控情报数据列表
     */
    List<ControlIntelligenceDO> listByCondition(LocalDateTime startTime, LocalDateTime endTime);


    List<ControlIntelligenceDO> list();

    /**
     * 更新管控情报信息
     *
     * @param controlIntelligenceDO 管控情报信息
     * @return 是否更新成功
     */
    boolean updateById(ControlIntelligenceDO controlIntelligenceDO);

    boolean updateBatchById(List<ControlIntelligenceDO> controlIntelligenceDOs);

    /**
     * 根据ID删除管控情报信息
     *
     * @param id 管控情报ID
     * @return 是否删除成功
     */
    boolean removeById(Long id);

    /**
     * 批量删除管控情报信息
     *
     * @param ids ID列表
     * @return 是否删除成功
     */
    boolean removeByIds(List<Long> ids);
}