package com.deepinnet.spatiotemporalplatform.skyflow.condition;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2025/3/4 15:57
 * @Description
 */
@Data
public class RiskWarningQueryCondition {

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private Boolean queryHistory = false;

    private List<String> planIds;

    private List<String> needWarningTypeList;

    private Integer pageNum;

    private Integer pageSize;
}
