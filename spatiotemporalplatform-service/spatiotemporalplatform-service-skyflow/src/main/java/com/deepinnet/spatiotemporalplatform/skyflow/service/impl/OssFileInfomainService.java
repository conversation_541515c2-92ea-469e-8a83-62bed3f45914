package com.deepinnet.spatiotemporalplatform.skyflow.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.OssFileInfoService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.OssFileInfoDO;
import com.deepinnet.spatiotemporalplatform.model.skyflow.OssFileInfo;
import com.deepinnet.spatiotemporalplatform.skyflow.convert.OssFileInfoConvert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class OssFileInfomainService {
    @Resource
    private OssFileInfoConvert ossFileInfoConvert;

    @Autowired
    private OssFileInfoService ossFileInfoService;

    public OssFileInfo getLatestByType(String fileBusinessType) {
        List<OssFileInfoDO> ossFileInfoDOS = ossFileInfoService.list(Wrappers.lambdaQuery(OssFileInfoDO.class).eq(OssFileInfoDO::getFileBusinessType, fileBusinessType).orderByDesc(OssFileInfoDO::getFileVersion).last("limit 1"));
        if (CollectionUtil.isEmpty(ossFileInfoDOS)) {
            return null;
        }
        return ossFileInfoConvert.toDomain(ossFileInfoDOS.get(0));
    }

}