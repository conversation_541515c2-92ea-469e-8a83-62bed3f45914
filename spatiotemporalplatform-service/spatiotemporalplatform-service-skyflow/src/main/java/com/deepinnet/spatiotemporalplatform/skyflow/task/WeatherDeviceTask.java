package com.deepinnet.spatiotemporalplatform.skyflow.task;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.localdata.integration.WeatherRadarClient;
import com.deepinnet.localdata.integration.model.output.*;
import com.deepinnet.spatiotemporalplatform.base.config.TenantConfig;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.WeatherDeviceDO;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.WeatherDeviceRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 天气设备数据同步任务
 * 每5分钟从鸢飞系统拉取天气设备数据并保存到本地数据库
 *
 * <AUTHOR> wong
 * @create 2025/01/14
 */
@Component
@RequiredArgsConstructor
@Profile({"local", "local-inner", "test", "demo", "alicloud"})
public class WeatherDeviceTask {

    private final WeatherDeviceRepository weatherDeviceRepository;

    private final WeatherRadarClient weatherRadarClient;

    private final TenantConfig tenantConfig;

    /**
     * 每5分钟执行一次
     */
    @Scheduled(cron = "0 0/5 * * * *")
    public void syncWeatherDevices() {
        LogUtil.info("[天气设备数据同步] 任务开始执行");

        try {
            // 调用鸢飞接口获取天气设备数据
            WeatherRadarResponseDTO weatherDeviceResponse = weatherRadarClient.getAllWeatherRadarData();

            if (weatherDeviceResponse == null) {
                LogUtil.error("鸢飞返回气象设备响应为空");
                return;
            }

            if (weatherDeviceResponse.getStatus() != 200) {
                LogUtil.error("调用鸢飞气象设备接口失败，status={}", weatherDeviceResponse.getStatus());
                return;
            }

            List<WeatherRadaDataDTO> weatherDeviceList = weatherDeviceResponse.getData();
            if (CollUtil.isEmpty(weatherDeviceList)) {
                LogUtil.info("[天气设备数据同步] 没有天气设备数据需要同步");
                return;
            }

            // 由于主键改为自增，使用序列号作为业务唯一标识
            List<String> serialNos = weatherDeviceList.stream()
                    .map(WeatherRadaDataDTO::getSerialNo)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 查询本地数据库已存在的设备记录（通过序列号）
            List<WeatherDeviceDO> existingDevices = weatherDeviceRepository.list(
                    Wrappers.lambdaQuery(WeatherDeviceDO.class)
                            .in(WeatherDeviceDO::getSerialNo, serialNos)
            );

            // 将已存在的设备按序列号分组
            Map<String, WeatherDeviceDO> existingMap = existingDevices.stream()
                    .collect(Collectors.toMap(
                            WeatherDeviceDO::getSerialNo,
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));

            // 分别存储需要新增和更新的记录
            List<WeatherDeviceDO> toInsertList = new ArrayList<>();
            List<WeatherDeviceDO> toUpdateList = new ArrayList<>();

            // 处理每条设备数据
            for (WeatherRadaDataDTO weatherRadaDataDTO : weatherDeviceList) {
                try {
                    WeatherDeviceDO deviceDO = convertToWeatherDeviceDO(weatherRadaDataDTO);
                    if (deviceDO == null || deviceDO.getSerialNo() == null) {
                        LogUtil.warn("[天气设备数据同步] 设备数据转换失败或序列号为空，跳过处理");
                        continue;
                    }

                    if (existingMap.containsKey(deviceDO.getSerialNo())) {
                        // 更新现有记录
                        WeatherDeviceDO existingDevice = existingMap.get(deviceDO.getSerialNo());
                        updateExistingDevice(existingDevice, deviceDO);
                        toUpdateList.add(existingDevice);
                    } else {
                        // 新增记录，ID会自动生成
                        deviceDO.setId(null); // 确保ID为null，让数据库自增
                        Date now = new Date();
                        deviceDO.setGmtCreated(now);
                        deviceDO.setGmtModified(now);
                        toInsertList.add(deviceDO);
                    }
                } catch (Exception e) {
                    LogUtil.error("同步天气设备异常，e：{}", e);
                    throw e;
                }
            }

            // 批量保存数据
            int insertCount = 0;
            int updateCount = 0;

            if (!toInsertList.isEmpty()) {
                toInsertList.forEach(weatherDeviceDO -> weatherDeviceDO.setTenantId(tenantConfig.getTenantId()));
                boolean insertResult = weatherDeviceRepository.saveBatch(toInsertList);
                if (insertResult) {
                    insertCount = toInsertList.size();
                    LogUtil.info("[天气设备数据同步] 成功新增 {} 条设备记录", insertCount);
                }
            }

            if (!toUpdateList.isEmpty()) {
                toUpdateList.forEach(weatherDeviceDO -> weatherDeviceDO.setTenantId(tenantConfig.getTenantId()));
                boolean updateResult = weatherDeviceRepository.updateBatchById(toUpdateList);
                if (updateResult) {
                    updateCount = toUpdateList.size();
                    LogUtil.info("[天气设备数据同步] 成功更新 {} 条设备记录", updateCount);
                }
            }

            LogUtil.info("[天气设备数据同步] 任务执行完成，新增: {} 条，更新: {} 条", insertCount, updateCount);

        } catch (Exception e) {
            LogUtil.error("[天气设备数据同步] 同步天气设备数据失败", e);
            throw e;
        }
    }

    /**
     * 将鸢飞响应对象转换为WeatherDeviceDO
     *
     * @param deviceResponse 鸢飞接口响应的设备对象
     * @return WeatherDeviceDO对象
     */
    private WeatherDeviceDO convertToWeatherDeviceDO(WeatherRadaDataDTO deviceResponse) {
        if (deviceResponse == null) {
            return null;
        }

        WeatherDeviceDO deviceDO = new WeatherDeviceDO();
        BeanUtils.copyProperties(deviceResponse, deviceDO);
        return deviceDO;
    }


    /**
     * 更新现有设备记录
     *
     * @param existingDevice 现有设备记录
     * @param newDevice      新设备数据
     */
    private void updateExistingDevice(WeatherDeviceDO existingDevice, WeatherDeviceDO newDevice) {
        // 只更新指定的字段，保持ID不变
        existingDevice.setManufacturer(newDevice.getManufacturer());
        existingDevice.setDeviceName(newDevice.getDeviceName());
        existingDevice.setBrandModel(newDevice.getBrandModel());
        existingDevice.setSerialNo(newDevice.getSerialNo());
        existingDevice.setTechParams(newDevice.getTechParams());
        existingDevice.setDataStandard(newDevice.getDataStandard());
        existingDevice.setEquipUsage(newDevice.getEquipUsage());
        existingDevice.setInstallEnvir(newDevice.getInstallEnvir());
        existingDevice.setInstallAdress(newDevice.getInstallAdress());
        existingDevice.setInstallDate(newDevice.getInstallDate());
        existingDevice.setInstallCount(newDevice.getInstallCount());
        existingDevice.setInstallSituation(newDevice.getInstallSituation());
        existingDevice.setPropCompany(newDevice.getPropCompany());
        existingDevice.setPropLinkUser(newDevice.getPropLinkUser());
        existingDevice.setDeviceManager(newDevice.getDeviceManager());
        existingDevice.setLon(newDevice.getLon());
        existingDevice.setLat(newDevice.getLat());
        existingDevice.setLinkPhone(newDevice.getLinkPhone());
        existingDevice.setDevicePhone(newDevice.getDevicePhone());
        existingDevice.setGmtModified(new Date());

    }
}