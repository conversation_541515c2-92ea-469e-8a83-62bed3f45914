package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.GdPoiDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.GdPoiMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.GdPoiRepository;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Service
public class GdPoiRepositoryImpl extends ServiceImpl<GdPoiMapper, GdPoiDO> implements GdPoiRepository {

    @Override
    public List<GdPoiDO> getPoiByRegionAndType(String region, List<String> types) {
        Assert.hasText(region, "region can not be null");
        return baseMapper.getPoiByRegionAndType(region, types);
    }
}
