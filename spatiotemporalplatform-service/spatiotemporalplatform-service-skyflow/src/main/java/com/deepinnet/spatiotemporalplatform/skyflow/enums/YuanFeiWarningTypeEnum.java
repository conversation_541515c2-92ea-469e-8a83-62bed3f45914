package com.deepinnet.spatiotemporalplatform.skyflow.enums;

/**
 * 预警/告警类型枚举
 */
public enum YuanFeiWarningTypeEnum {

    WARNING("1", "预警"),
    ALERT("3", "告警");

    /**
     * 类型编码
     */
    private String code;

    /**
     * 类型描述
     */
    private String description;

    /**
     * 构造方法
     *
     * @param code        类型编码
     * @param description 类型描述
     */
    YuanFeiWarningTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据编码获取枚举
     *
     * @param code 类型编码
     * @return 对应的枚举值，如果不存在返回null
     */
    public static YuanFeiWarningTypeEnum getByCode(String code) {
        for (YuanFeiWarningTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 获取类型编码
     *
     * @return 类型编码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取类型描述
     *
     * @return 类型描述
     */
    public String getDescription() {
        return description;
    }
}