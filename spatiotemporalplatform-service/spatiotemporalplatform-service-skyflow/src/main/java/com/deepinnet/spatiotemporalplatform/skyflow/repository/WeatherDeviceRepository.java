package com.deepinnet.spatiotemporalplatform.skyflow.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.WeatherDeviceDO;

/**
 * 天气设备Repository接口
 *
 * <AUTHOR> wong
 * @create 2025/01/14
 */
public interface WeatherDeviceRepository extends IService<WeatherDeviceDO> {
    
    /**
     * 根据租户ID删除天气设备数据
     *
     * @param tenantId 租户ID
     */
    void deleteByTenantId(String tenantId);
} 