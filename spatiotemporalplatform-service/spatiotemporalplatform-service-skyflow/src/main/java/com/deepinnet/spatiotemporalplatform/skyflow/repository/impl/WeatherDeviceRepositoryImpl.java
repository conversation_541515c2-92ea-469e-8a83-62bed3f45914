package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.WeatherDeviceDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.WeatherDeviceMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.WeatherDeviceRepository;
import org.springframework.stereotype.Repository;

/**
 * 天气设备Repository实现类
 *
 * <AUTHOR> wong
 * @create 2025/01/14
 */
@Repository
public class WeatherDeviceRepositoryImpl extends ServiceImpl<WeatherDeviceMapper, WeatherDeviceDO> 
        implements WeatherDeviceRepository {
    
    @Override
    public void deleteByTenantId(String tenantId) {
        super.remove(Wrappers.lambdaQuery(WeatherDeviceDO.class)
                .eq(WeatherDeviceDO::getTenantId, tenantId));
    }
}