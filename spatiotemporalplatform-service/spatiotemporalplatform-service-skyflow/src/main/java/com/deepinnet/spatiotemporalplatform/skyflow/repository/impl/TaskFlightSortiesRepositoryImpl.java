package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.TaskFlightSortiesDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.TaskFlightSortiesMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.TaskFlightSortiesRepository;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Service
public class TaskFlightSortiesRepositoryImpl extends ServiceImpl<TaskFlightSortiesMapper, TaskFlightSortiesDO> implements TaskFlightSortiesRepository {

}
