package com.deepinnet.spatiotemporalplatform.skyflow.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.RealTimeUavFlightDO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 实时无人机飞行数据表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
public interface RealTimeUavFlightRepository extends IService<RealTimeUavFlightDO> {

    /**
     * 统计当天的去重飞行数据
     * @param planIds 计划ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    int countDistinctFlightsFast(List<String> planIds, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询当天每个计划最新飞行数据
     * @param planIds 计划id
     * @param uavIds 无人机id
     * @param warningPlanIds 预警计划ID
     * @return List<RealTimeUavFlightDO>
     */
    List<RealTimeUavFlightDO> queryLatestFlights(List<String> planIds, List<String> uavIds, List<String> warningPlanIds);


    /**
     * 查询计划下的所有飞行数据
     * @param planIds
     * @return
     */
    List<RealTimeUavFlightDO> queryPlanFlights(List<String> planIds, Integer pageNo, Integer pageSize);

    /**
     * 根据上报时间查询当天的飞行数据
     * @param reportTime 上报时间
     * @param planId 计划id
     * @return RealTimeUavFlightDO
     */
    RealTimeUavFlightDO queryLatestFlightsByReportTime(LocalDateTime reportTime, String planId);

    /**
     * 查询计划下最近一次的飞行架次编号
     * @param planIds 计划编号
     * @return List<RealTimeUavFlightDO>
     */
    List<RealTimeUavFlightDO> queryFlightPlanIdLastFlightId(List<String> planIds);

    /**
     * 查询计划下最新的一条飞行数据
     * @param planIds 计划ID
     * @return 飞行数据
     */
    List<RealTimeUavFlightDO> queryFlightPlanLastStatus(List<String> planIds);
}
