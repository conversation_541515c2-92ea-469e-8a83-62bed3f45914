package com.deepinnet.spatiotemporalplatform.skyflow.service;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.dto.AirspacePageQueryDTO;
import com.deepinnet.spatiotemporalplatform.model.skyflow.Airspace;

/**
 * Description: 空域服务
 * Date: 2025/3/13
 * Author: claude-ai
 */
public interface AirspaceService {
    
    /**
     * 创建空域
     * @param airspace 空域信息
     * @return 创建的空域代码
     */
    String createAirspace(Airspace airspace);

    /**
     * 分页查询空域列表
     * @param queryDTO 查询参数
     * @return 空域列表
     */
    CommonPage<Airspace> pageQueryAirspace(AirspacePageQueryDTO queryDTO);

    /**
     * 根据代码查询空域详情
     * @param code 空域代码
     * @return 空域详情
     */
    Airspace getAirspaceByCode(String code);


    /**
     * 根据airspaceId查询空域详情
     * @param airspaceId 空域id
     * @return 空域详情
     */
    Airspace getAirspaceByAirspaceId(String airspaceId);

} 