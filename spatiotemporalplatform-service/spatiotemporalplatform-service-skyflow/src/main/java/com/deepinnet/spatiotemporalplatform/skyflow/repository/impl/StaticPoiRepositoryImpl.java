package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.StaticPoiDaoService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.StaticPoiDO;
import com.deepinnet.spatiotemporalplatform.model.staticpoi.StaticPoi;
import com.deepinnet.spatiotemporalplatform.model.staticpoi.StaticPoiQueryDTO;
import com.deepinnet.spatiotemporalplatform.skyflow.convert.StaticPoiConvert;
import com.deepinnet.spatiotemporalplatform.skyflow.error.BizErrorCode;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.StaticPoiRepository;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 静态核心POI 仓储实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-10
 */
@Service
public class StaticPoiRepositoryImpl implements StaticPoiRepository {

    @Resource
    private StaticPoiDaoService staticPoiDaoService;

    @Override
    public CommonPage<StaticPoi> pageStaticPoi(StaticPoiQueryDTO queryDTO) {
        if (queryDTO == null || queryDTO.getPageNum() == null || queryDTO.getPageSize() == null) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        LambdaQueryWrapper<StaticPoiDO> wrapper = Wrappers.lambdaQuery(StaticPoiDO.class)
                .eq(StringUtils.isNotBlank(queryDTO.getPoiId()), StaticPoiDO::getPoiId, queryDTO.getPoiId())
                .like(StringUtils.isNotBlank(queryDTO.getPoiName()), StaticPoiDO::getPoiName, queryDTO.getPoiName())
                .orderByDesc(StaticPoiDO::getGmtCreated);

        List<StaticPoiDO> staticPoiDOList = staticPoiDaoService.list(wrapper);
        if (CollectionUtils.isEmpty(staticPoiDOList)) {
            return CommonPage.buildEmptyPage();
        }

        List<StaticPoi> staticPoiList = StaticPoiConvert.toModelList(staticPoiDOList);
        PageInfo<StaticPoi> pageInfo = PageInfo.of(staticPoiList);
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPages(page.getPages());

        // 按创建时间倒序排序
        List<StaticPoi> sortedModels = staticPoiList.stream()
                .sorted(Comparator.comparing(StaticPoi::getGmtCreated).reversed())
                .collect(Collectors.toList());

        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), pageInfo.getPages(), pageInfo.getTotal(), sortedModels);
    }

    @Override
    public StaticPoi getByPoiId(String poiId) {
        StaticPoiDO staticPoiDO = staticPoiDaoService.getByPoiId(poiId);
        return StaticPoiConvert.toModel(staticPoiDO);
    }

} 