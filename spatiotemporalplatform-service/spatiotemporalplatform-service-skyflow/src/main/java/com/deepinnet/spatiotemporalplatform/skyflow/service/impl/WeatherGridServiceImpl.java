package com.deepinnet.spatiotemporalplatform.skyflow.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.*;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.WeatherGridDO;
import com.deepinnet.spatiotemporalplatform.enums.WeatherGridFactor;
import com.deepinnet.spatiotemporalplatform.model.skyflow.*;
import com.deepinnet.spatiotemporalplatform.skyflow.condition.WeatherGridSpecifications;
import com.deepinnet.spatiotemporalplatform.skyflow.convert.WeatherGridConvert;
import com.deepinnet.spatiotemporalplatform.skyflow.flush.GridCenterCoordinate;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.WeatherGridRepository;
import com.deepinnet.spatiotemporalplatform.skyflow.service.WeatherGridService;
import com.deepinnet.spatiotemporalplatform.skyflow.util.WktGridUtil;
import com.deepinnet.spatiotemporalplatform.skyflow.util.weather.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * Creator zengjuerui
 * Date 2025-04-19
 **/

@Slf4j
@Service
@Profile("!qz-gov-inner-network")
public class WeatherGridServiceImpl implements WeatherGridService {

    @Resource
    private WeatherGridRepository weatherGridRepository;
    @Resource
    private WeatherGridConvert weatherGridConvert;
    @Resource
    private CommonDataService commonDataService;

    @Value("${weather.node.url}")
    private String weatherNodeUrl;

    @Value("${file.attachment.dir}")
    private String weatherDataFilePath;

    @Value("${file.attachment.url}")
    private String urlPrefix;

    /**
     * 注入通用任务线程池，但限制并发数为5
     */
    @Resource(name = "commonTaskPool")
    private ExecutorService commonTaskPool;

    @Override
    public List<LocationWeatherData> queryWeatherGrid(WeatherGridPostBody weatherGridPostBody) {

        List<WeatherGridDO> weatherGridDOS = weatherGridRepository
                .listByCondition(weatherGridConvert.toWeatherGridQueryCondition(weatherGridPostBody));

        List<LocationWeatherData> data = weatherGridConvert.toLocationWeatherDataList(weatherGridDOS);

        return data.parallelStream()
                .map(WeatherGridSpecifications.createSimplifier(weatherGridPostBody))
                .collect(Collectors.toList());
    }

    @Override
    public List<String> processWeatherGridDataAndGenerateFiles(WeatherGridPostBody weatherGridPostBody) {
        // 1. 根据传入的bounds切割wkt
        String bounds = weatherGridPostBody.getBounds();
        if (StringUtils.isBlank(bounds)) {
            LogUtil.warn("传入的bounds为空");
            return new ArrayList<>();
        }

        List<String> gridWktList = WktGridUtil.splitIntoGrids(bounds, 1.0);
        LogUtil.info("WKT被拆分为{}个网格", gridWktList.size());

        if (CollectionUtils.isEmpty(gridWktList)) {
            LogUtil.warn("拆分网格为空");
            return new ArrayList<>();
        }

        // 生成时间戳和日期
        long timestamp = System.currentTimeMillis();
        String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        int totalGrids = gridWktList.size();

        // 使用信号量控制并发数为5
        Semaphore semaphore = new Semaphore(5);
        List<Future<List<String>>> futures = new ArrayList<>();

        Long startTime = System.currentTimeMillis();

        // 2. 循环遍历切割后的wkt，将每个网格的处理任务提交给线程池
        for (int i = 0; i < gridWktList.size(); i++) {
            final int gridIndex = i;
            final String gridWkt = gridWktList.get(i);

            Future<List<String>> future = commonTaskPool.submit(() -> {
                try {
                    // 获取信号量许可，控制并发数
                    semaphore.acquire();
                    try {
                        return processGridWeatherData(weatherGridPostBody, gridWkt, gridIndex,
                                totalGrids, timestamp, dateStr);
                    } finally {
                        // 释放信号量许可
                        semaphore.release();
                    }
                } catch (InterruptedException e) {
                    LogUtil.error("网格{}处理被中断", gridIndex, e);
                    Thread.currentThread().interrupt();
                    return new ArrayList<String>();
                }
            });

            futures.add(future);
        }

        // 3. 收集所有任务的结果
        List<String> allFileUrls = new ArrayList<>();
        for (int i = 0; i < futures.size(); i++) {
            try {
                List<String> gridFileUrls = futures.get(i).get(60, TimeUnit.SECONDS); // 设置超时时间
                if (CollectionUtils.isNotEmpty(gridFileUrls)) {
                    allFileUrls.addAll(gridFileUrls);
                    LogUtil.info("网格{}处理完成，生成{}个文件", i, gridFileUrls.size());
                }
            } catch (ExecutionException e) {
                LogUtil.error("处理网格{}任务执行异常", i, e.getCause());
                // 继续处理其他网格，不中断整个流程
            } catch (InterruptedException e) {
                LogUtil.error("处理网格{}任务被中断", i, e);
                Thread.currentThread().interrupt();
                break;
            } catch (TimeoutException e) {
                LogUtil.error("处理网格{}任务超时", i, e);
                // 继续处理其他网格
            }
        }

        Long endTime = System.currentTimeMillis();
        LogUtil.info("=============多线程处理完成，总耗时：{}ms", endTime - startTime);

        // 4. 返回所有文件的URL
        LogUtil.info("总共生成{}个气象数据文件", allFileUrls.size());
        return allFileUrls;
    }

    /**
     * 处理单个网格的气象数据
     *
     * @param weatherGridPostBody 请求参数
     * @param gridWkt 网格WKT
     * @param gridIndex 网格索引
     * @param totalGrids 总网格数
     * @param timestamp 时间戳
     * @param dateStr 日期字符串
     * @return 生成的文件URL列表
     */
    private List<String> processGridWeatherData(WeatherGridPostBody weatherGridPostBody, String gridWkt,
                                               int gridIndex, int totalGrids, long timestamp, String dateStr) {
        try {
            // 调用高德获取气象数据
            String weatherDataResponse = invokeRemoteGetWeatherData(gridWkt);
            if (StringUtils.isBlank(weatherDataResponse)) {
                LogUtil.info("高德返回气象数据响应为空，跳过网格{}", gridIndex);
                return new ArrayList<>();
            }

            // 下载并解析气象数据
            JSONObject jsonObject = JSONUtil.parseObj(weatherDataResponse);
            String dataUrl = (String) jsonObject.get("url");

            Map<String, LocationWeatherData> weatherDataMap = WeatherDataProcessUtil.downloadAndDeserializeWeatherData(dataUrl);
            if (MapUtil.isEmpty(weatherDataMap)) {
                LogUtil.warn("网格{}气象数据为空", gridIndex);
                return new ArrayList<>();
            }

            // 根据factor生成相应类型的文件
            List<String> gridFileUrls = generateWeatherFiles(weatherGridPostBody, weatherDataMap,
                    totalGrids, gridIndex, timestamp, dateStr);

            LogUtil.info("成功处理网格{}，生成{}个文件", gridIndex, gridFileUrls.size());
            return gridFileUrls;

        } catch (Exception e) {
            LogUtil.error("处理网格{}异常", gridIndex, e);
            return new ArrayList<>(); // 返回空列表而不是抛出异常，避免影响其他网格处理
        }
    }

    /**
     * 调用高德接口获取气象数据
     */
    private String invokeRemoteGetWeatherData(String wkt) {
        WeatherGridPostBody queryPostBody = new WeatherGridPostBody();
        queryPostBody.setBounds(wkt);
        queryPostBody.setLevel(7);

        WeatherGridRequest request = new WeatherGridRequest();
        request.setPostBody(queryPostBody);
        WeatherGridUrlParams weatherGridUrlParams = new WeatherGridUrlParams();
        request.setUrlParams(weatherGridUrlParams);

        WeatherGridQueryResponse response = (WeatherGridQueryResponse) commonDataService.fetchData(request);
        if (response == null || StringUtils.isBlank(response.getData())) {
            return null;
        }

        return response.getData();
    }

    /**
     * 根据factor生成相应的气象文件
     *
     * @param weatherGridPostBody 请求体，包含factor和windForceAltitude信息
     * @param weatherDataMap      气象数据映射
     * @param totalGrids          总网格数
     * @param gridIndex           当前网格索引
     * @param timestamp           时间戳
     * @param dateStr             日期字符串
     * @return 生成的文件URL列表
     */
    private List<String> generateWeatherFiles(WeatherGridPostBody weatherGridPostBody,
                                              Map<String, LocationWeatherData> weatherDataMap,
                                              int totalGrids, int gridIndex, long timestamp, String dateStr) {
        try {
            // 获取网格中心点坐标
            Set<String> gridCodes = weatherDataMap.keySet();
            Map<String, GridCenterCoordinate> gridCenterMap = WeatherDataProcessUtil.getGridCenterFromNode(weatherNodeUrl, gridCodes);

            // 生成前缀（用于标识这是API调用生成的文件）
            String filePrefix = "api_weather";

            WeatherGridFactor factor = weatherGridPostBody.getFactor();
            Integer windForceAltitude = weatherGridPostBody.getWindForceAltitude();

            List<String> fileUrls = new ArrayList<>();

            if (factor == null) {
                // 如果没有指定factor，生成所有类型的文件（保持向后兼容性）
                return WeatherDataProcessUtil.generateStandardWeatherFiles(
                        filePrefix, weatherDataFilePath, urlPrefix,
                        weatherDataMap, gridCenterMap,
                        totalGrids, gridIndex, timestamp, dateStr,
                        null
                );
            }

            // 根据factor生成相应的文件
            switch (factor) {
                case TEMPERATURE:
                    String tempFileName = WeatherDataProcessUtil.generateFileName(filePrefix, "温度", dateStr, totalGrids, gridIndex, timestamp);
                    WeatherDataProcessUtil.generateTemperatureFile(tempFileName, weatherDataFilePath, weatherDataMap, gridCenterMap);
                    fileUrls.add(urlPrefix + tempFileName);
                    LogUtil.info("生成温度文件: {}", tempFileName);
                    break;

                case PRECIPITATION:
                    String precipFileName = WeatherDataProcessUtil.generateFileName(filePrefix, "降雨", dateStr, totalGrids, gridIndex, timestamp);
                    WeatherDataProcessUtil.generatePrecipitationFile(precipFileName, weatherDataFilePath, weatherDataMap, gridCenterMap);
                    fileUrls.add(urlPrefix + precipFileName);
                    LogUtil.info("生成降雨文件: {}", precipFileName);
                    break;

                case WIND_FORCE:
                    String windFileName = WeatherDataProcessUtil.generateFileName(filePrefix, "风力", dateStr, totalGrids, gridIndex, timestamp);

                    // 如果指定了windForceAltitude，需要过滤数据只保留指定高度的风速数据
                    Map<String, LocationWeatherData> filteredWeatherDataMap = weatherDataMap;
                    if (windForceAltitude != null) {
                        filteredWeatherDataMap = filterWeatherDataByAltitude(weatherDataMap, windForceAltitude);
                        LogUtil.info("根据指定高度{}米过滤风力数据", windForceAltitude);
                    }

                    WeatherDataProcessUtil.generateWindFile(windFileName, weatherDataFilePath,
                            filteredWeatherDataMap, gridCenterMap, windForceAltitude);
                    fileUrls.add(urlPrefix + windFileName);
                    LogUtil.info("生成风力文件: {}，高度: {}米", windFileName, windForceAltitude);
                    break;

                default:
                    LogUtil.warn("未知的WeatherGridFactor: {}", factor);
                    break;
            }

            return fileUrls;

        } catch (Exception e) {
            LogUtil.error("生成网格{}气象文件失败", gridIndex, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据指定高度过滤气象数据，只保留指定高度的风力数据
     *
     * @param originalWeatherDataMap 原始气象数据
     * @param targetAltitude         目标高度
     * @return 过滤后的气象数据
     */
    private Map<String, LocationWeatherData> filterWeatherDataByAltitude(Map<String, LocationWeatherData> originalWeatherDataMap,
                                                                         int targetAltitude) {
        Map<String, LocationWeatherData> filteredMap = new HashMap<>();

        for (Map.Entry<String, LocationWeatherData> entry : originalWeatherDataMap.entrySet()) {
            String locationCode = entry.getKey();
            LocationWeatherData originalData = entry.getValue();

            if (originalData == null) {
                continue;
            }

            // 创建新的LocationWeatherData对象
            LocationWeatherData filteredData = new LocationWeatherData();
            filteredData.setLocationCode(originalData.getLocationCode());
            filteredData.setCenter(originalData.getCenter());
            filteredData.setCenterPoint(originalData.getCenterPoint());

            // 保留基础信息（可能在某些场景下需要）
            filteredData.setBaseInfo(originalData.getBaseInfo());

            // 只保留指定高度的气象层数据
            if (CollectionUtils.isNotEmpty(originalData.getWeatherLayers())) {
                List<WeatherLayer> filteredLayers = originalData.getWeatherLayers().stream()
                        .filter(layer -> layer.getAltitude() == targetAltitude)
                        .collect(Collectors.toList());
                filteredData.setWeatherLayers(filteredLayers);

                LogUtil.info("位置{}：原始层数{}，过滤后层数{}", locationCode,
                        originalData.getWeatherLayers().size(), filteredLayers.size());
            }

            filteredMap.put(locationCode, filteredData);
        }

        LogUtil.info("高度过滤完成，原始位置数: {}，过滤后位置数: {}",
                originalWeatherDataMap.size(), filteredMap.size());

        return filteredMap;
    }

}
