package com.deepinnet.spatiotemporalplatform.skyflow.task;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.util.*;
import com.deepinnet.localdata.integration.AirspaceClient;
import com.deepinnet.localdata.integration.model.input.*;
import com.deepinnet.localdata.integration.model.output.*;
import com.deepinnet.spatiotemporalplatform.base.config.TenantConfig;
import com.deepinnet.spatiotemporalplatform.common.enums.CoordinateReferenceSystemNameEnum;
import com.deepinnet.spatiotemporalplatform.common.enums.YuanFeiAirSpaceRangeTypeEnum;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.*;
import com.deepinnet.spatiotemporalplatform.model.util.WktUtil;
import com.deepinnet.spatiotemporalplatform.skyflow.convert.*;
import com.deepinnet.spatiotemporalplatform.skyflow.enums.ShapeTypeEnum;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.*;
import lombok.RequiredArgsConstructor;
import org.locationtech.jts.geom.*;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 空域数据同步任务
 * 每2分钟从外部系统拉取空域数据并保存到本地数据库
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@Component
@RequiredArgsConstructor
@Profile({"local", "local-inner", "test", "alicloud"})
public class AirspaceTask {

    private final AirspaceRepository airspaceRepository;
    private final AirspaceConvert airspaceConvert;

    private final FenceSpaceRepository fenceSpaceRepository;
    private final FenceSpaceConvert fenceSpaceConvert;
    private final AirspaceClient airspaceClient;
    @Resource
    private TenantConfig tenantConfig;

    /**
     * 每2分钟执行一次
     */
    @Scheduled(cron = "0 0/2 * * * *")
    public void syncAirspaces() {
        LogUtil.info("[空域数据同步] 任务开始执行");

        try {
            // 使用AirspaceClient获取空域数据
            AirspacePageQueryDTO airspacePageQueryDTO = new AirspacePageQueryDTO();
            airspacePageQueryDTO.setPageNo(1);
            airspacePageQueryDTO.setPageSize(1000);
            List<AirspaceResponseDTO> airspaceResponseList;
            while (true) {
                airspaceResponseList = airspaceClient.getAllEnabledAirSpaces(airspacePageQueryDTO);
                if (CollectionUtil.isEmpty(airspaceResponseList)) {
                    break;
                }
                saveOrUpdateAriSpace(airspaceResponseList);
                airspacePageQueryDTO.setPageNo(airspacePageQueryDTO.getPageNo() + 1);
            }

        } catch (Exception e) {
            LogUtil.error("[空域数据同步] 任务执行异常", e);
        }
    }

    private void saveOrUpdateAriSpace(List<AirspaceResponseDTO> airspaceResponseList) {
        LogUtil.info("[空域数据同步] 获取到空域数据 {} 条", airspaceResponseList.size());

        // 获取所有空域编码，用于后续批量查询
        List<String> codes = airspaceResponseList.stream()
                .map(AirspaceResponseDTO::getCode)
                .collect(Collectors.toList());

        // 查询本地数据库已存在的空域记录
        List<AirspaceDO> existingAirspaces = airspaceRepository.listByCodes(codes);

        // 将已存在的空域按编码分组
        Map<String, AirspaceDO> existingAirSpaceMap = existingAirspaces.stream()
                .collect(Collectors.toMap(
                        AirspaceDO::getCode,
                        airspace -> airspace,
                        (existing, replacement) -> existing
                ));


        // 分别存储需要新增和更新的空域
        List<AirspaceDO> toInsertAirSpaceList = new ArrayList<>();
        List<AirspaceDO> toUpdateAirSpaceList = new ArrayList<>();

        // 处理每条空域数据
        for (AirspaceResponseDTO airspaceResponse : airspaceResponseList) {
            // 处理空域数据
            buildAirSpace(airspaceResponse, existingAirSpaceMap, toUpdateAirSpaceList, toInsertAirSpaceList);

        }

        // 批量保存新增空域记录
        if (CollectionUtil.isNotEmpty(toInsertAirSpaceList)) {
            toInsertAirSpaceList = new ArrayList<>(toInsertAirSpaceList.stream().collect(Collectors.toMap(AirspaceDO::getCode, p -> p, (a, b) -> a)).values());
            boolean saveResult = airspaceRepository.saveBatch(toInsertAirSpaceList);
            LogUtil.info("[空域数据同步] 新增空域数据 {} 条, 结果: {}", toInsertAirSpaceList.size(), saveResult ? "成功" : "失败");
        }


        // 批量更新已有空域
        if (CollectionUtil.isNotEmpty(toUpdateAirSpaceList)) {
            boolean updateResult = airspaceRepository.updateBatchById(toUpdateAirSpaceList);
            LogUtil.info("[空域数据同步] 更新空域数据 {} 条, 结果: {}", toUpdateAirSpaceList.size(), updateResult ? "成功" : "失败");
        }


        LogUtil.info("[空域数据同步] 任务执行完成");
    }

    private void buildAirSpace(AirspaceResponseDTO airspaceResponse, Map<String, AirspaceDO> existingAirSpaceMap, List<AirspaceDO> toUpdateAirSpaceList, List<AirspaceDO> toInsertAirSpaceList) {
        String code = airspaceResponse.getCode();
        if (StringUtils.isEmpty(code)) {
            return;
        }

        if (Objects.equals(airspaceResponse.getRangeType(), YuanFeiAirSpaceRangeTypeEnum.POLYGON.getCode()) || Objects.equals(airspaceResponse.getRangeType(), YuanFeiAirSpaceRangeTypeEnum.LINE.getCode())) {
            // 转换空域对象到空域DO
            AirspaceDO airspaceDO = convertToAirspaceDO(airspaceResponse);

            if (existingAirSpaceMap.containsKey(code)) {
                // 已存在相同编码的记录，需要更新
                AirspaceDO existingAirspaceDO = existingAirSpaceMap.get(code);
                // 保留原始ID
                airspaceDO.setId(existingAirspaceDO.getId());
                airspaceDO.setGmtModified(LocalDateTime.now());
                airspaceDO.setTenantId(tenantConfig.getTenantId());
                toUpdateAirSpaceList.add(airspaceDO);

            } else {
                // 不存在相同编码的空域，需要新增
                airspaceDO.setGmtCreated(LocalDateTime.now());
                airspaceDO.setGmtModified(LocalDateTime.now());
                airspaceDO.setTenantId(tenantConfig.getTenantId());
                toInsertAirSpaceList.add(airspaceDO);
            }
        }
    }

    /**
     * 转换AirspaceResponseDTO到AirspaceDO
     */
    private AirspaceDO convertToAirspaceDO(AirspaceResponseDTO airspaceResponse) {
        AirspaceDO airspaceDO = new AirspaceDO();
        airspaceDO.setRangeType(airspaceResponse.getRangeType());
        // 单位千米转为米
        airspaceDO.setWidth(airspaceResponse.getWidth() == null ? null : airspaceResponse.getWidth() * 1000);
        // 单位千米转为米
        airspaceDO.setRadius(airspaceResponse.getRadius() == null ? null: airspaceResponse.getRadius() * 1000);
        LineString originalPoints = builtWkt2Line(airspaceResponse.getDegreePoints());
        airspaceDO.setOriginWkt(airspaceResponse.getDegreePoints());
        if (Objects.equals(airspaceResponse.getRangeType(), YuanFeiAirSpaceRangeTypeEnum.LINE.getCode())) {
            airspaceDO.setWkt1(originalPoints);
            airspaceDO.setWkt2(bufferLineString2Polygon(originalPoints, airspaceResponse.getWidth()));
        } else if (Objects.equals(airspaceResponse.getRangeType(), YuanFeiAirSpaceRangeTypeEnum.POLYGON.getCode())) {
            airspaceDO.setWkt2(builtWkt2Polygon(airspaceResponse.getDegreePoints(), airspaceResponse.getRangeType()));
        }
        airspaceDO.setAirspaceId(airspaceResponse.getId());
        airspaceDO.setMinHeight(airspaceResponse.getMinHeight());
        airspaceDO.setMaxHeight(airspaceResponse.getMaxHeight());
        airspaceDO.setShapeType(ShapeTypeEnum.CYLINDER.getCode());
        airspaceDO.setPrivateType(airspaceResponse.getType());
        airspaceDO.setGmtCreated(LocalDateTime.now());
        airspaceDO.setGmtModified(LocalDateTime.now());
        airspaceDO.setCode(airspaceResponse.getCode());
        airspaceDO.setBusinessCode(airspaceResponse.getCode());
        airspaceDO.setName(airspaceResponse.getName());
        airspaceDO.setStatus(airspaceResponse.getStatus());
        airspaceDO.setIsDeleted(false);
        return airspaceDO;
    }

    private Polygon bufferLineString2Polygon(LineString originalPoints, Integer width) {
        // 判断宽度是否为空，为空则设置默认宽度10米
        if (width == null || width == 0) {
            width = 10;
        } else {
            // 单位转换，千米转为米，再除以二。原因是宽度扩充分为两边各扩一半
            width = width * 500;
        }

        // 先转成3857进行buffer计算
        Geometry lineGeometry3857 = WktUtil.transformGeometry(originalPoints, CoordinateReferenceSystemNameEnum.WGS_84, CoordinateReferenceSystemNameEnum.WEB_MERCATOR);
        // buffer计算后再转成4326
        Geometry bufferedGeometry4326 = WktUtil.transformGeometry(lineGeometry3857.buffer(width), CoordinateReferenceSystemNameEnum.WEB_MERCATOR, CoordinateReferenceSystemNameEnum.WGS_84);

        return (Polygon) bufferedGeometry4326;
    }

    private LineString builtWkt2Line(String degreePoints) {
        if (StringUtils.isBlank(degreePoints)) {
            return null;
        }
        // 先将degreePoints转换为List
        List<String> degreePointList = Arrays.asList(degreePoints.split("\\|"));
        // 将List转换为LineString
        List<Point> points = degreePointList.stream().map(degreePoint -> {
            String[] parts = degreePoint.split(",");
            double lat = Double.parseDouble(parts[1]);
            double lon = Double.parseDouble(parts[0]);
            return WktUtil.toPoint(lon, lat);
        }).collect(Collectors.toList());
        return WktUtil.pointListToLineString(points);
    }


    /**
     * 构建空域 wkt
     * todo 未处理 rangeType 为圆形和扇形的空域数据
     *
     * @param degreePoints
     * @param rangeType
     * @return
     */
    private Polygon builtWkt2Polygon(String degreePoints, String rangeType) {
        if (StringUtils.isBlank(degreePoints)) {
            return null;
        }

        if (Objects.equals(rangeType, YuanFeiAirSpaceRangeTypeEnum.POLYGON.getCode())) {
            List<Point> points = getPoints(degreePoints);
            // 连接第一个点，形成多边形闭环
            points.add(points.get(0));
            return WktUtil.pointListToPolygon(points);
        }

        return null;
    }

    private static List<Point> getPoints(String degreePoints) {
        // 先将degreePoints转换为List
        List<String> degreePointList = Arrays.asList(degreePoints.split("\\|"));
        // 将List转换为LineString
        List<Point> points = degreePointList.stream().map(degreePoint -> {
            String[] parts = degreePoint.split(",");
            double lat = Double.parseDouble(parts[1]);
            double lon = Double.parseDouble(parts[0]);
            PointCoordinate pointCoordinate = CoordinateTransform.transformWGS84ToGCJ02(lon, lat);
            return WktUtil.toPoint(pointCoordinate.getLongitude(), pointCoordinate.getLatitude());
        }).collect(Collectors.toList());
        return points;
    }

    /**
     * 每2分钟执行一次，同步禁飞区数据
     */
    @Scheduled(cron = "0 0/2 * * * *")
    public void syncFenceSpaces() {
        LogUtil.info("[禁飞区数据同步] 任务开始执行");

        try {
            // 使用FenceSpaceClient获取禁飞区数据
            NoFlyZoneQueryDTO queryDTO = new NoFlyZoneQueryDTO();
            // 禁飞区固定值type = 1
            queryDTO.setType(1);
            List<NoFlyZoneResponseDTO> fenceSpaceResponseList = airspaceClient.getNoFlyZones(queryDTO);

            if (CollectionUtil.isEmpty(fenceSpaceResponseList)) {
                LogUtil.info("[禁飞区数据同步] 没有禁飞区数据需要同步");
                return;
            }

            LogUtil.info("[禁飞区数据同步] 获取到禁飞区数据 {} 条", fenceSpaceResponseList.size());

            // 获取所有禁飞区编码，用于后续批量查询
            List<String> fenceNums = fenceSpaceResponseList.stream()
                    .filter(fenceSpace -> StringUtils.isNotBlank(fenceSpace.getFenceNum())).map(NoFlyZoneResponseDTO::getFenceNum)
                    .collect(Collectors.toList());

            // 查询本地数据库已存在的禁飞区记录
            List<FenceSpaceDO> existingFenceSpaces = fenceSpaceRepository.lambdaQuery()
                    .in(FenceSpaceDO::getFenceNum, fenceNums)
                    .list();

            // 将已存在的禁飞区按编码分组
            Map<String, FenceSpaceDO> existingMap = existingFenceSpaces.stream()
                    .collect(Collectors.toMap(
                            FenceSpaceDO::getFenceNum,
                            fenceSpace -> fenceSpace,
                            (existing, replacement) -> existing
                    ));

            // 分别存储需要新增和更新的记录
            List<FenceSpaceDO> toInsertList = new ArrayList<>();
            List<FenceSpaceDO> toUpdateList = new ArrayList<>();

            // 处理每条禁飞区数据
            for (NoFlyZoneResponseDTO fenceSpaceResponse : fenceSpaceResponseList) {
                String fenceNum = fenceSpaceResponse.getFenceNum();

                // 转换对象到DO
                FenceSpaceDO fenceSpaceDO = convertToFenceSpaceDO(fenceSpaceResponse);

                if (existingMap.containsKey(fenceNum)) {
                    // 已存在相同编码的记录，需要更新
                    FenceSpaceDO existingDO = existingMap.get(fenceNum);
                    // 保留原始ID
                    fenceSpaceDO.setId(existingDO.getId());
                    fenceSpaceDO.setGmtModified(LocalDateTime.now());
                    toUpdateList.add(fenceSpaceDO);
                } else {
                    // 不存在相同编码的记录，需要新增
                    fenceSpaceDO.setGmtCreated(LocalDateTime.now());
                    fenceSpaceDO.setGmtModified(LocalDateTime.now());
                    toInsertList.add(fenceSpaceDO);
                }
            }

            // 批量保存新增记录
            if (CollectionUtil.isNotEmpty(toInsertList)) {
                boolean saveResult = fenceSpaceRepository.saveBatch(toInsertList);
                LogUtil.info("[禁飞区数据同步] 新增禁飞区数据 {} 条, 结果: {}", toInsertList.size(), saveResult ? "成功" : "失败");
            }

            // 批量更新已有记录
            if (CollectionUtil.isNotEmpty(toUpdateList)) {
                boolean updateResult = fenceSpaceRepository.updateBatchById(toUpdateList);
                LogUtil.info("[禁飞区数据同步] 更新禁飞区数据 {} 条, 结果: {}", toUpdateList.size(), updateResult ? "成功" : "失败");
            }

            LogUtil.info("[禁飞区数据同步] 任务执行完成，新增: {}条, 更新: {}条", toInsertList.size(), toUpdateList.size());
        } catch (Exception e) {
            LogUtil.error("[禁飞区数据同步] 任务执行异常", e);
        }
    }

    /**
     * 转换NoFlyZoneResponseDTO到FenceSpaceDO
     */
    private FenceSpaceDO convertToFenceSpaceDO(NoFlyZoneResponseDTO fenceSpaceResponse) {
        FenceSpaceDO fenceSpaceDO = new FenceSpaceDO();
        fenceSpaceDO.setHeight(String.valueOf(fenceSpaceResponse.getHeight()));
        fenceSpaceDO.setAlt(String.valueOf(fenceSpaceResponse.getAlt()));
        fenceSpaceDO.setBeginTime(fenceSpaceResponse.getBeginTime());
        fenceSpaceDO.setEndTime(fenceSpaceResponse.getEndTime());
        fenceSpaceDO.setAddress(fenceSpaceResponse.getAddress());
        fenceSpaceDO.setFreeUavType(fenceSpaceResponse.getFreeUavType());
        fenceSpaceDO.setZoneType(fenceSpaceResponse.getZoneType());
        fenceSpaceDO.setLimitMode(fenceSpaceResponse.getLimitMode());
        fenceSpaceDO.setStatus(fenceSpaceResponse.getStatus());
        fenceSpaceDO.setGmtCreated(LocalDateTime.now());
        fenceSpaceDO.setGmtModified(LocalDateTime.now());
        fenceSpaceDO.setAddressCode(fenceSpaceResponse.getAddressCode());
        fenceSpaceDO.setName(fenceSpaceResponse.getName());
        fenceSpaceDO.setFenceNum(fenceSpaceResponse.getFenceNum());
        fenceSpaceDO.setIsDeleted(false);
        fenceSpaceDO.setFenceType(fenceSpaceResponse.getFenceType());
        fenceSpaceDO.setBufferWidth(String.valueOf(fenceSpaceResponse.getBufferWidth()));
        fenceSpaceDO.setAddress(fenceSpaceResponse.getAddress());
        fenceSpaceDO.setTenantId(tenantConfig.getTenantId());
        String spatialDetailPoints = fenceSpaceResponse.getSpatialDetailPoints();
        Polygon fencePolygon = builtWkt2Polygon(spatialDetailPoints, YuanFeiAirSpaceRangeTypeEnum.POLYGON.getCode());

        fenceSpaceDO.setSpatialDetailPoints(WktUtil.toWkt(fencePolygon));
        return fenceSpaceDO;
    }
} 