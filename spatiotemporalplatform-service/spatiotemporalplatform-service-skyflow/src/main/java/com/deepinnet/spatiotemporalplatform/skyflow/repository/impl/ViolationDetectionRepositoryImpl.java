package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.skyflow.condition.ViolationDetectionQueryCondition;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.ViolationDetectionDO;
import com.deepinnet.spatiotemporalplatform.dto.ViolationGroupStatisticsDTO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.ViolationDetectionMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.ViolationDetectionRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 违规检测Repository实现类
 *
 * <AUTHOR>
 */
@Repository
public class ViolationDetectionRepositoryImpl extends ServiceImpl<ViolationDetectionMapper, ViolationDetectionDO>
        implements ViolationDetectionRepository {

    @Override
    public boolean save(ViolationDetectionDO violationDetectionDO) {
        return super.save(violationDetectionDO);
    }

    @Override
    public boolean saveBatch(List<ViolationDetectionDO> violationDetectionDOList) {
        return super.saveBatch(violationDetectionDOList);
    }

    @Override
    public ViolationDetectionDO getById(Long id) {
        return super.getById(id);
    }

    @Override
    public List<ViolationDetectionDO> list(LambdaQueryWrapper<ViolationDetectionDO> queryWrapper) {
        return super.list(queryWrapper);
    }

    @Override
    public List<ViolationDetectionDO> listByTypeAndTimeRange(String violationType, LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<ViolationDetectionDO> wrapper = new LambdaQueryWrapper<ViolationDetectionDO>()
                .eq(StringUtils.isNotBlank(violationType), ViolationDetectionDO::getViolationType, violationType)
                .ge(startTime != null, ViolationDetectionDO::getViolationTime, startTime)
                .le(endTime != null, ViolationDetectionDO::getViolationTime, endTime)
                .orderByDesc(ViolationDetectionDO::getGmtCreated);

        return super.list(wrapper);
    }

    @Override
    public List<ViolationDetectionDO> listByOperatingEntity(String operatingEntity) {
        LambdaQueryWrapper<ViolationDetectionDO> wrapper = new LambdaQueryWrapper<ViolationDetectionDO>()
                .eq(StringUtils.isNotBlank(operatingEntity), ViolationDetectionDO::getOperatingEntity, operatingEntity)
                .orderByDesc(ViolationDetectionDO::getGmtCreated);

        return super.list(wrapper);
    }

    @Override
    public List<ViolationDetectionDO> listByAircraftSn(String aircraftSn) {
        LambdaQueryWrapper<ViolationDetectionDO> wrapper = new LambdaQueryWrapper<ViolationDetectionDO>()
                .eq(StringUtils.isNotBlank(aircraftSn), ViolationDetectionDO::getAircraftSn, aircraftSn)
                .orderByDesc(ViolationDetectionDO::getGmtCreated);

        return super.list(wrapper);
    }

    @Override
    public boolean updateById(ViolationDetectionDO violationDetectionDO) {
        return super.updateById(violationDetectionDO);
    }

    @Override
    public boolean removeById(Long id) {
        return super.removeById(id);
    }

    @Override
    public boolean removeByIds(List<Long> ids) {
        return super.removeByIds(ids);
    }

    @Override
    public List<ViolationDetectionDO> listByCondition(ViolationDetectionQueryCondition queryCondition) {
        LambdaQueryWrapper<ViolationDetectionDO> wrapper = Wrappers.lambdaQuery(ViolationDetectionDO.class)
                .eq(StringUtils.isNotBlank(queryCondition.getOperatingEntity()), ViolationDetectionDO::getOperatingEntity, queryCondition.getOperatingEntity())
                .ge(queryCondition.getStartTime() != null, ViolationDetectionDO::getViolationTime, queryCondition.getStartTime())
                .lt(queryCondition.getEndTime() != null, ViolationDetectionDO::getViolationTime, queryCondition.getEndTime())
                .orderByDesc(ViolationDetectionDO::getGmtCreated);
        return super.list(wrapper);
    }

    @Override
    public List<ViolationGroupStatisticsDTO> countGroupByOperatingEntityAndType(ViolationDetectionQueryCondition queryCondition) {
        List<ViolationDetectionDO> violationDetectionDOs = listByCondition(queryCondition);

        // 使用Java流进行分组统计
        Map<String, Map<String, Long>> groupedStats = violationDetectionDOs.stream()
                .collect(Collectors.groupingBy(
                        ViolationDetectionDO::getOperatingEntity,
                        Collectors.groupingBy(
                                ViolationDetectionDO::getViolationType,
                                Collectors.counting()
                        )
                ));

        // 将分组结果转换为DTO列表
        List<ViolationGroupStatisticsDTO> result = new ArrayList<>();
        groupedStats.forEach((operatingEntity, typeMap) -> {
            typeMap.forEach((violationType, count) -> {
                result.add(ViolationGroupStatisticsDTO.builder()
                        .operatingEntity(operatingEntity)
                        .violationType(violationType)
                        .count(count)
                        .build());
            });
        });

        return result;
    }
} 