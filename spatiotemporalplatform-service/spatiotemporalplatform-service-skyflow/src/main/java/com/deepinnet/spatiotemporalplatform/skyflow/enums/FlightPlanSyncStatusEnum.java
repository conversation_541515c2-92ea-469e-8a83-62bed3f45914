package com.deepinnet.spatiotemporalplatform.skyflow.enums;

import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * Description: 飞行计划成果同步枚举
 */
@AllArgsConstructor
@Getter
public enum FlightPlanSyncStatusEnum {

    /**
     * 初始化
     */
    INIT("INIT", "初始化"),

    /**
     * 未同步
     */
    UN_SYNC("UN_SYNC", "未同步"),

    /**
     * 已同步
     */
    SYNCED("SYNCED", "已同步"),

    /**
     * 已终止
     */
    TERMINATED("TERMINATED", "完成状态"),

    ;

    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态名称
     */
    private final String name;
    
    /**
     * 根据code获取枚举
     *
     * @param code 状态码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static FlightPlanSyncStatusEnum getByCode(String code) {
        for (FlightPlanSyncStatusEnum status : FlightPlanSyncStatusEnum.values()) {
            if (ObjectUtil.equals(code, status.getCode())) {
                return status;
            }
        }
        return null;
    }
} 