package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightProcessRecordDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.FlightProcessRecordMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.FlightProcessRecordRepository;
import org.springframework.stereotype.Service;

/**
 * 无人机数据上报表 Repository 实现类
 */
@Service
public class FlightProcessRecordRepositoryImpl extends ServiceImpl<FlightProcessRecordMapper, FlightProcessRecordDO> implements FlightProcessRecordRepository {

}