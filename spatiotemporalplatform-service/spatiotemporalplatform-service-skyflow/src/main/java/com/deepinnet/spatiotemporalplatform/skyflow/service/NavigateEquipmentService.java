package com.deepinnet.spatiotemporalplatform.skyflow.service;

import com.deepinnet.spatiotemporalplatform.dto.NavigateEquipmentQueryDTO;
import com.deepinnet.spatiotemporalplatform.vo.NavigateEquipmentVO;

import java.util.List;

/**
 * 导航设备Service接口
 *
 * <AUTHOR>
 */
public interface NavigateEquipmentService {

    /**
     * 分页查询导航设备
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    List<NavigateEquipmentVO> pageQueryNavigateEquipment(NavigateEquipmentQueryDTO queryDTO);

    /**
     * 根据ID查询导航设备详情
     *
     * @param id 主键ID
     * @return 导航设备详情
     */
    NavigateEquipmentVO getNavigateEquipmentById(Long id);

    /**
     * 根据外部ID查询导航设备详情
     *
     * @param externalId 外部ID
     * @return 导航设备详情
     */
    NavigateEquipmentVO getNavigateEquipmentByExternalId(String externalId);

    /**
     * 查询所有导航设备数据
     *
     * @return 导航设备数据列表
     */
    List<NavigateEquipmentVO> listAllNavigateEquipments();

    /**
     * 统计导航设备总数
     *
     * @return 导航设备总数
     */
    int countNavigateEquipments();
} 