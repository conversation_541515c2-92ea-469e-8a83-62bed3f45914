package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.DetectedDeviceDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.DetectedDeviceMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.DetectedDeviceRepository;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

/**
 * 侦测定位设备数据Repository实现类
 *
 * <AUTHOR>
 */
@Repository
public class DetectedDeviceRepositoryImpl extends ServiceImpl<DetectedDeviceMapper, DetectedDeviceDO> 
        implements DetectedDeviceRepository {

    @Override
    public DetectedDeviceDO getByExternalId(String externalId) {
        if (!StringUtils.hasText(externalId)) {
            return null;
        }
        
        LambdaQueryWrapper<DetectedDeviceDO> wrapper = Wrappers.lambdaQuery(DetectedDeviceDO.class)
                .eq(DetectedDeviceDO::getExternalId, externalId);
        return super.getOne(wrapper);
    }
} 