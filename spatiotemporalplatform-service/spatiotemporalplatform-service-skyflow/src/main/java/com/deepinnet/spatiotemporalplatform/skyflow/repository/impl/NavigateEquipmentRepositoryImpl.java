package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.NavigateEquipmentDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.NavigateEquipmentMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.NavigateEquipmentRepository;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 导航设备数据Repository实现
 *
 * <AUTHOR>
 */
@Repository
public class NavigateEquipmentRepositoryImpl extends ServiceImpl<NavigateEquipmentMapper, NavigateEquipmentDO>
        implements NavigateEquipmentRepository {

    @Override
    public NavigateEquipmentDO getByExternalId(String externalId) {
        if (!StringUtils.hasText(externalId)) {
            return null;
        }
        return baseMapper.selectByExternalId(externalId);
    }

    @Override
    public void deleteByTenantId(String tenantId) {
        super.remove(Wrappers.lambdaQuery(NavigateEquipmentDO.class)
                .eq(NavigateEquipmentDO::getTenantId, tenantId));
    }

    @Override
    public List<NavigateEquipmentDO> listByCondition(String manufacturer, String deviceName, String brandModel,
                                                   String serialNo, String stationType, String equipUsage,
                                                   String installAdress, String tenantId, String departId) {
        return super.list(Wrappers.lambdaQuery(NavigateEquipmentDO.class)
                .like(StringUtils.hasText(manufacturer), NavigateEquipmentDO::getManufacturer, manufacturer)
                .like(StringUtils.hasText(deviceName), NavigateEquipmentDO::getDeviceName, deviceName)
                .like(StringUtils.hasText(brandModel), NavigateEquipmentDO::getBrandModel, brandModel)
                .like(StringUtils.hasText(serialNo), NavigateEquipmentDO::getSerialNo, serialNo)
                .like(StringUtils.hasText(stationType), NavigateEquipmentDO::getStationType, stationType)
                .like(StringUtils.hasText(equipUsage), NavigateEquipmentDO::getEquipUsage, equipUsage)
                .like(StringUtils.hasText(installAdress), NavigateEquipmentDO::getInstallAdress, installAdress)
                .eq(StringUtils.hasText(tenantId), NavigateEquipmentDO::getTenantId, tenantId)
                .eq(StringUtils.hasText(departId), NavigateEquipmentDO::getDepartId, departId)
                .orderByDesc(NavigateEquipmentDO::getGmtCreated));
    }
}