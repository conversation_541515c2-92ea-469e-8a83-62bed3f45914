package com.deepinnet.spatiotemporalplatform.skyflow.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.json.JSONUtil;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.localdata.integration.TempFenceClient;
import com.deepinnet.localdata.integration.model.output.TempFenceResponseDTO;
import com.deepinnet.spatiotemporalplatform.base.config.TenantConfig;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.ControlIntelligenceDO;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.ControlIntelligenceRepository;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wong
 * @create 2025/3/12 15:17
 * @Description
 */
@Component
@RequiredArgsConstructor
@Profile({"local", "local-inner", "test", "demo", "alicloud", "pc"})
public class FenceTask {

    private final ControlIntelligenceRepository controlIntelligenceRepository;

    private final TempFenceClient tempFenceClient;

    private final TenantConfig tenantConfig;

    // @Scheduled(cron = "*/10 * * * * *")
    @Scheduled(cron = "0 0/2 * * * *")
    public void process() {
        List<TempFenceResponseDTO> tempFenceList = tempFenceClient.getTempFenceList();
        if (CollectionUtil.isEmpty(tempFenceList)) {
            return;
        }

        // 获取数据库中已存在的管控情报列表
        List<ControlIntelligenceDO> controllerIntelligenceList = controlIntelligenceRepository.list();

        // 如果数据库中没有数据，则直接批量插入所有临时围栏数据
        if (CollectionUtil.isEmpty(controllerIntelligenceList)) {
            controlIntelligenceRepository.saveBatch(convertToControlIntelligenceDOs(tempFenceList));
            return;
        }

        // 将已存在的管控情报按ID分组，便于快速查找
        Map<String, ControlIntelligenceDO> existingMap = controllerIntelligenceList.stream()
                .collect(Collectors.toMap(
                        ControlIntelligenceDO::getControlIntelligenceNo,
                        item -> item,
                        (existing, replacement) -> existing
                ));

        // 分别存储需要新增和更新的记录
        List<ControlIntelligenceDO> toInsertList = new java.util.ArrayList<>();
        List<ControlIntelligenceDO> toUpdateList = new java.util.ArrayList<>();

        // 遍历临时围栏列表，确定新增或更新操作
        for (TempFenceResponseDTO tempFence : tempFenceList) {
            String fenceId = tempFence.getId();
            if (existingMap.containsKey(fenceId)) {
                // 已存在相同ID的记录，需要更新
                ControlIntelligenceDO existingDO = existingMap.get(fenceId);
                updateControlIntelligenceDO(existingDO, tempFence);
                toUpdateList.add(existingDO);
            } else {
                // 不存在相同ID的记录，需要新增
                ControlIntelligenceDO controlIntelligenceDO = convertToControlIntelligenceDO(tempFence);
                if (controlIntelligenceDO != null) {
                    toInsertList.add(controlIntelligenceDO);
                }
            }
        }

        // 批量保存新增记录
        if (CollectionUtil.isNotEmpty(toInsertList)) {
            controlIntelligenceRepository.saveBatch(toInsertList);
            LogUtil.info("【管控情报】新增数据成功，保存的数据为：{}", JSONUtil.toJsonStr(toInsertList));
        }

        // 批量更新已有记录
        if (CollectionUtil.isNotEmpty(toUpdateList)) {
            LogUtil.info("【管控情报】更新数据成功，为：{}", JSONUtil.toJsonStr(toUpdateList));
            controlIntelligenceRepository.updateBatchById(toUpdateList);
        }
    }

    private void updateControlIntelligenceDO(ControlIntelligenceDO existingDO, TempFenceResponseDTO tempFence) {
        existingDO.setPublishTime(LocalDateTimeUtil.of(tempFence.getBeginTime()));
        existingDO.setStatus(tempFence.getStatus());
        existingDO.setContent(tempFence.getName());
    }

    private List<ControlIntelligenceDO> convertToControlIntelligenceDOs(List<TempFenceResponseDTO> tempFenceList) {
        List<ControlIntelligenceDO> controlIntelligenceDOList = Lists.newArrayList();
        tempFenceList.forEach(fence -> {
            if (fence.getBeginTime() == null) {
                LogUtil.warn("当前管控情报开始时间为空，已过滤，数据为：{}", JSONUtil.toJsonStr(fence));
                return;
            }

            ControlIntelligenceDO controlIntelligenceDO = convertToControlIntelligenceDO(fence);
            if (controlIntelligenceDO != null) {
                controlIntelligenceDOList.add(controlIntelligenceDO);
            }
        });

        return controlIntelligenceDOList;
    }

    private ControlIntelligenceDO convertToControlIntelligenceDO(TempFenceResponseDTO fence) {
        if (fence.getBeginTime() == null) {
            LogUtil.warn("当前管控情报开始时间为空，已过滤，数据为：{}", JSONUtil.toJsonStr(fence));
            return null;
        }

        ControlIntelligenceDO controlIntelligenceDO = new ControlIntelligenceDO();
        controlIntelligenceDO.setControlIntelligenceNo(fence.getId());
        controlIntelligenceDO.setSource(null);
        controlIntelligenceDO.setStatus(fence.getStatus());
        controlIntelligenceDO.setPublishTime(LocalDateTimeUtil.of(fence.getBeginTime()));
        controlIntelligenceDO.setEndTime(LocalDateTimeUtil.of(fence.getEndTime()));
        controlIntelligenceDO.setContent(fence.getName());
        controlIntelligenceDO.setGmtCreated(LocalDateTime.now());
        controlIntelligenceDO.setGmtModified(LocalDateTime.now());
        controlIntelligenceDO.setTenantId(tenantConfig.getTenantId());
        return controlIntelligenceDO;
    }

}
