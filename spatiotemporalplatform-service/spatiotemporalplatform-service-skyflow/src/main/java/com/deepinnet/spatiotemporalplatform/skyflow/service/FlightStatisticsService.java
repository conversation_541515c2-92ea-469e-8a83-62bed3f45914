package com.deepinnet.spatiotemporalplatform.skyflow.service;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.localdata.integration.model.input.FlightTaskNatureStatisticsQueryDTO;
import com.deepinnet.localdata.integration.model.output.FlightTaskNatureStatisticsResponseDTO;
import com.deepinnet.spatiotemporalplatform.dto.FlightServiceCompanyStatisticsQueryDTO;
import com.deepinnet.spatiotemporalplatform.vo.FlightServiceCompanyRankingVO;
import com.deepinnet.spatiotemporalplatform.vo.FlightTaskNatureStatisticsDataVO;

import java.util.List;

/**
 * 飞行服务公司统计Service接口
 *
 * <AUTHOR>
 * @create 2025/6/30
 */
public interface FlightStatisticsService {

    /**
     * 获取飞行服务公司统计数据（排行榜格式）
     *
     * @param queryDTO 查询条件
     * @return 飞行服务公司排行榜数据
     */
    List<FlightServiceCompanyRankingVO> getFlightServiceCompanyStatistics(FlightServiceCompanyStatisticsQueryDTO queryDTO);

    /**
     * 获取飞行任务性质统计信息
     * @return
     */
    List<FlightTaskNatureStatisticsDataVO> getFlightTaskNatureStatistics();
} 