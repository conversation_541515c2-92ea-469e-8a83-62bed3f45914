package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.GdGroundFeatureDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.GdGroundFeatureMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.GdGroundFeatureRepository;
import org.locationtech.jts.geom.Point;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2025-01-14 16:02:48
 */
@Service
public class GdGroundFeatureRepositoryImpl extends ServiceImpl<GdGroundFeatureMapper, GdGroundFeatureDO>
        implements GdGroundFeatureRepository {

    @Resource
    private GdGroundFeatureMapper gdGroundFeatureMapper;

    @Override
    public List<GdGroundFeatureDO> getGroundFeatureList(List<Point> pointList, String region) {
        return gdGroundFeatureMapper.getGroundFeatureList(pointList, region);
    }
}




