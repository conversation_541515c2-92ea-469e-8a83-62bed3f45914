package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.skyflow.condition.WeatherIntelligenceQueryCondition;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.WeatherIntelligenceDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.WeatherIntelligenceMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.WeatherIntelligenceRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 气象情报Repository实现类
 *
 * <AUTHOR>
 */
@Repository
@Slf4j
public class WeatherIntelligenceRepositoryImpl extends ServiceImpl<WeatherIntelligenceMapper, WeatherIntelligenceDO>
        implements WeatherIntelligenceRepository {

    @Override
    public boolean save(WeatherIntelligenceDO weatherIntelligenceDO) {
        return super.save(weatherIntelligenceDO);
    }

    @Override
    public boolean saveBatch(List<WeatherIntelligenceDO> weatherIntelligenceDOList) {
        return super.saveBatch(weatherIntelligenceDOList);
    }

    @Override
    public WeatherIntelligenceDO getById(Long id) {
        return super.getById(id);
    }

    @Override
    public List<WeatherIntelligenceDO> list(LambdaQueryWrapper<WeatherIntelligenceDO> queryWrapper) {
        return super.list(queryWrapper);
    }

    @Override
    public List<WeatherIntelligenceDO> listByCondition(WeatherIntelligenceQueryCondition queryCondition) {
        LocalDateTime startTime = queryCondition.getStartTime();
        LocalDateTime endTime = queryCondition.getEndTime();
        LambdaQueryWrapper<WeatherIntelligenceDO> wrapper = new LambdaQueryWrapper<WeatherIntelligenceDO>()
                .eq(WeatherIntelligenceDO::getGridCode, queryCondition.getGridCode())
                .ge(startTime != null, null, startTime)
                .le(endTime != null, null, endTime)
                .orderByDesc(WeatherIntelligenceDO::getGmtCreated);
        return super.list(wrapper);
    }

    @Override
    public boolean updateById(WeatherIntelligenceDO weatherIntelligenceDO) {
        return super.updateById(weatherIntelligenceDO);
    }

    @Override
    public boolean removeById(Long id) {
        return super.removeById(id);
    }

    @Override
    public boolean removeByIds(List<Long> ids) {
        return super.removeByIds(ids);
    }
} 