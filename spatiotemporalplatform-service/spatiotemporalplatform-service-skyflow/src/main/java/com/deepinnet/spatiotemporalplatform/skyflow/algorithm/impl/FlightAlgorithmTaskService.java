package com.deepinnet.spatiotemporalplatform.skyflow.algorithm.impl;

import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.localdata.integration.DetectedDeviceClient;
import com.deepinnet.localdata.integration.FlightThirdPartyAlgorithmClient;
import com.deepinnet.localdata.integration.error.RepeatExecException;
import com.deepinnet.localdata.integration.model.output.DeviceIdInfoDTO;
import com.deepinnet.localdata.integration.model.outsidebean.AlgoTaskDimensionDTO;
import com.deepinnet.localdata.integration.model.outsidebean.FlightAlgorithmTaskCreateDTO;
import com.deepinnet.localdata.integration.model.outsidebean.FlightAlgorithmTaskResponse;
import com.deepinnet.spatiotemporalplatform.enums.FlightEventTypeEnum;
import com.deepinnet.spatiotemporalplatform.skyflow.error.BizErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * Description:
 * Date: 2025/8/23
 * Author: lijunheng
 */
@Slf4j
@Component
public class FlightAlgorithmTaskService {

    public static final String key = "flight:plan:batch:end";

    /**
     * hash结构：key:算法code、计划id、架次id value:任务id
     */
    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private ScheduledThreadPoolExecutor scheduleTaskExecutor;

    @Resource
    private FlightThirdPartyAlgorithmClient flightThirdPartyAlgorithmClient;

    @Resource
    private DetectedDeviceClient detectedDeviceClient;

    public FlightAlgorithmTaskResponse startForestFireAlertTask(FlightAlgorithmTaskCreateDTO createDTO, String sn) {
        AlgoTaskDimensionDTO algoTaskDimensionDTO = new AlgoTaskDimensionDTO();
        algoTaskDimensionDTO.setTaskFlightTaskId(createDTO.getFlight_task_id());
        algoTaskDimensionDTO.setTaskFlightSortieId(createDTO.getFlight_sortie_id());
        algoTaskDimensionDTO.setTaskAlgoCode(createDTO.getAlgo_code());
        boolean hasTask = hasTask(algoTaskDimensionDTO);
        AlgoTaskDimensionDTO latestSortTask = getLatestSortTask(algoTaskDimensionDTO);

        //不存在任务或者架次不是最新的
        //如果是最新的，先结束上一个任务
        if (!hasTask) {
            if (latestSortTask != null) {
                endTask(latestSortTask);
            }
            //启动新任务
            try {
                DeviceIdInfoDTO deviceIdInfoDTO = detectedDeviceClient.getCameraId(sn);
                if (deviceIdInfoDTO == null) {
                    return null;
                }
                createDTO.setGb_cam_id(deviceIdInfoDTO.getGbAddress());
                createDTO.setLhs_cam_id(deviceIdInfoDTO.getCameraId());
                FlightAlgorithmTaskResponse result = flightThirdPartyAlgorithmClient.createThirdPartyFlightMonitorTask(createDTO);
                putTask(algoTaskDimensionDTO);
                //启动任务成功时开启续期
                expireTask(algoTaskDimensionDTO);
                return result;
            } catch (RepeatExecException e) {
                log.info("任务已存在，算法：{}，计划：{}，架次：{}", FlightEventTypeEnum.valueOf(algoTaskDimensionDTO.getTaskAlgoCode()).getName(), algoTaskDimensionDTO.getTaskFlightTaskId(), algoTaskDimensionDTO.getTaskFlightSortieId());
            } catch (Exception e) {
                //其他异常不能加入任务池，因为可能算法没有启动，需要后续重试
                log.error("添加任务异常", e);
            }
        } else {
            //当前任务已经存在，并且是最新的时候，需要续期
            if (latestSortTask != null && Objects.equals(latestSortTask.getTaskFlightSortieId(), algoTaskDimensionDTO.getTaskFlightSortieId())) {
                expireTask(algoTaskDimensionDTO);
                throw new BizException(BizErrorCode.IDEMPOTENT_REQUEST_ERROR.getCode(), "任务已存在");
            }
        }
        return null;
    }

    public void endTask(AlgoTaskDimensionDTO algoTaskDimensionDTO) {
        try {
            // 删除
            Long remove = redisTemplate.opsForZSet().remove(key, getKey(algoTaskDimensionDTO));
            if (remove > 0) {
                flightThirdPartyAlgorithmClient.stopTask(algoTaskDimensionDTO);
            }

        } catch (RepeatExecException e) {
            log.info("结束任务重复", e);
        } catch (Exception e) {
            log.error("结束任务异常", e);
        }
    }

    private boolean hasTask(AlgoTaskDimensionDTO algoTaskDimensionDTO) {
        String taskCacheKey = getCacheTaskKey(algoTaskDimensionDTO);
        Double score = redisTemplate.opsForZSet().score(taskCacheKey, algoTaskDimensionDTO.getTaskFlightSortieId());
        return score != null;
    }

    private void putTask(AlgoTaskDimensionDTO algoTaskDimensionDTO) {
        String taskCacheKey = getCacheTaskKey(algoTaskDimensionDTO);
        // 只新增，不覆盖
        redisTemplate.opsForZSet().addIfAbsent(taskCacheKey, algoTaskDimensionDTO.getTaskFlightSortieId(), System.currentTimeMillis());
    }

    /**
     * 获取上一个架次的任务，如果不存在返回null
     *
     * @return
     */
    private AlgoTaskDimensionDTO getLatestSortTask(AlgoTaskDimensionDTO algoTaskDimensionDTO) {
        String taskCacheKey = getCacheTaskKey(algoTaskDimensionDTO);
        //算法id、计划id
        Set<String> prev = redisTemplate.opsForZSet().reverseRange(taskCacheKey, 0, 0);
        if (CollectionUtils.isNotEmpty(prev)) {
            String prevSortie = prev.iterator().next();
            AlgoTaskDimensionDTO lastTask = new AlgoTaskDimensionDTO();
            lastTask.setTaskAlgoCode(algoTaskDimensionDTO.getTaskAlgoCode());
            lastTask.setTaskFlightTaskId(algoTaskDimensionDTO.getTaskFlightTaskId());
            lastTask.setTaskFlightSortieId(prevSortie);
            return lastTask;
        }

        return null;
    }

    /**
     * 给任务续期，
     * 如果返回true，则说明任务续期成功；
     * 如果返回false，则说明任务已经过期，不能续期了
     *
     * @param algoTaskDimensionDTO
     * @return
     */
    private boolean expireTask(AlgoTaskDimensionDTO algoTaskDimensionDTO) {
        String member = getKey(algoTaskDimensionDTO);

        // 当前 score
        Double oldScore = redisTemplate.opsForZSet().score(key, member);

        long now = System.currentTimeMillis();
        long expireAt = now + 5 * 60 * 1000;

        if (oldScore != null && oldScore < now) {
            // 已经过期，不再续期。这里不处理结束逻辑，统一都在定时任务中处理
            log.info("架次已过期{} ", member);
            return false;
        }
        // 还没过期或者为空，更新新的过期时间。
        // 这条数据可能是MQ中隔了很久的数据，这时候可能会重新插入一条延期任务。这个数据需要在定时任务中过滤掉
        redisTemplate.opsForZSet().add(key, member, expireAt);
        return true;
    }

    @Scheduled(fixedDelay = 1000 * 60 * 5)
    public void handleExpireTask() {
        long futureTime = System.currentTimeMillis() + 1000 * 60 * 5;
        // 定时任务扫描
        Set<ZSetOperations.TypedTuple<String>> expired =
                redisTemplate.opsForZSet().rangeByScoreWithScores(key, 0, futureTime);

        if (CollectionUtils.isNotEmpty(expired)) {
            for (ZSetOperations.TypedTuple<String> tuple : expired) {
                Double score = tuple.getScore();
                String member = tuple.getValue();
                long delay = score.longValue() - System.currentTimeMillis();
                AlgoTaskDimensionDTO algoTaskDimensionDTO = convert(member);
                scheduleTaskExecutor.schedule(() -> {
                    endTask(algoTaskDimensionDTO);
                }, delay, TimeUnit.MILLISECONDS);
            }
        }
    }

    private String getKey(AlgoTaskDimensionDTO algoTaskDimensionDTO) {
        return algoTaskDimensionDTO.getTaskAlgoCode() + ":" + algoTaskDimensionDTO.getTaskFlightTaskId() + ":" + algoTaskDimensionDTO.getTaskFlightSortieId();
    }

    private AlgoTaskDimensionDTO convert(String key) {
        String[] parts = key.split(":");
        String algoCode = parts[0];
        String plan = parts[1];
        String batchId = parts[2];
        AlgoTaskDimensionDTO algoTaskDimensionDTO = new AlgoTaskDimensionDTO();
        algoTaskDimensionDTO.setTaskAlgoCode(algoCode);
        algoTaskDimensionDTO.setTaskFlightTaskId(plan);
        algoTaskDimensionDTO.setTaskFlightSortieId(batchId);
        return algoTaskDimensionDTO;
    }

    private String getCacheTaskKey(AlgoTaskDimensionDTO algoTaskDimensionDTO) {
        return algoTaskDimensionDTO.getTaskAlgoCode() + ":" + algoTaskDimensionDTO.getTaskFlightTaskId();
    }
}
