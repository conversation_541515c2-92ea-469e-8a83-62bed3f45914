package com.deepinnet.spatiotemporalplatform.skyflow.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deepinnet.spatiotemporalplatform.skyflow.condition.WeatherIntelligenceQueryCondition;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.WeatherIntelligenceDO;

import java.util.List;

/**
 * 气象情报Repository接口
 *
 * <AUTHOR>
 */
public interface WeatherIntelligenceRepository {

    /**
     * 保存气象情报信息
     *
     * @param weatherIntelligenceDO 气象情报信息
     * @return 是否保存成功
     */
    boolean save(WeatherIntelligenceDO weatherIntelligenceDO);

    /**
     * 批量保存气象情报信息
     *
     * @param weatherIntelligenceDOList 气象情报信息列表
     * @return 是否保存成功
     */
    boolean saveBatch(List<WeatherIntelligenceDO> weatherIntelligenceDOList);

    /**
     * 根据ID查询气象情报信息
     *
     * @param id 气象情报ID
     * @return 气象情报信息
     */
    WeatherIntelligenceDO getById(Long id);

    /**
     * 根据条件查询气象情报列表
     *
     * @param queryWrapper 查询条件
     * @return 气象情报列表
     */
    List<WeatherIntelligenceDO> list(LambdaQueryWrapper<WeatherIntelligenceDO> queryWrapper);

    /**
     * 根据时间范围查询气象情报数据
     *
     * @return 气象情报数据列表
     */
    List<WeatherIntelligenceDO> listByCondition(WeatherIntelligenceQueryCondition queryCondition);

    /**
     * 更新气象情报信息
     *
     * @param weatherIntelligenceDO 气象情报信息
     * @return 是否更新成功
     */
    boolean updateById(WeatherIntelligenceDO weatherIntelligenceDO);

    /**
     * 根据ID删除气象情报信息
     *
     * @param id 气象情报ID
     * @return 是否删除成功
     */
    boolean removeById(Long id);

    /**
     * 批量删除气象情报信息
     *
     * @param ids ID列表
     * @return 是否删除成功
     */
    boolean removeByIds(List<Long> ids);
} 