package com.deepinnet.spatiotemporalplatform.skyflow.convert;

import com.alibaba.fastjson2.JSONObject;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.*;


import com.deepinnet.spatiotemporalplatform.dal.model.FlightDemandPlanStatDO;
import com.deepinnet.spatiotemporalplatform.dto.FlightDemandPlanStatDTO;
import com.deepinnet.spatiotemporalplatform.dto.FlightQueryDTO;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightQuery;
import com.deepinnet.spatiotemporalplatform.enums.FlightAchievementTypeEnum;
import com.deepinnet.spatiotemporalplatform.skyflow.enums.FlightPlanStatusEnum;
import com.deepinnet.spatiotemporalplatform.skyflow.enums.FlightStatusEnum;
import com.deepinnet.spatiotemporalplatform.skyflow.mqtt.body.AirspaceStatusUpdateMsgBody;
import com.deepinnet.spatiotemporalplatform.skyflow.mqtt.body.RealTimeFlyDataMsgBody;
import com.deepinnet.spatiotemporalplatform.skyflow.util.beidou.WktUtil;
import com.deepinnet.spatiotemporalplatform.vo.*;
import org.mapstruct.*;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * <p>
 * 飞行数据转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, imports = {WktUtil.class})
public interface FlightConverter {

    List<FlightAchievementVO> toFlightAchievementVOList(List<FlightPlanAchievementDO> flightPlanAchievementDOList);

    @Mapping(target = "planId", ignore = true)
    @Mapping(source = "achievementLocationStr", target = "achievementLocation")
    @Mapping(source = "achievementTime", target = "achievementTime", qualifiedByName = "localDateTimeToLong")
    @Mapping(source = "achievementType", target = "achievementType", qualifiedByName = "achievementTypeEnum")
    FlightAchievementVO toFlightAchievementVO(FlightPlanAchievementDO flightPlanAchievementDO);

    FlightRecordVO toFlightRecordVO(FlightRecordDO flightRecordDO);
    
    /**
     * 将 FlightMissionDO 转换为 FlightMissionVO
     *
     * @param missionDO 飞行任务DO
     * @return FlightMissionVO 飞行任务VO
     */
    @Mapping(source = "missionId", target = "missionId")
    @Mapping(source = "status", target = "status")
    @Mapping(source = "planedTakeoffTime", target = "planedTakeoffTime")
    @Mapping(source = "planedLandingTime", target = "planedLandingTime")
    @Mapping(source = "flightUnit", target = "flightUnit")
    @Mapping(source = "flightUnitId", target = "flightUnitId")
    @Mapping(target = "flightPlanList", ignore = true)
    FlightMissionVO toFlightMissionVO(FlightMissionDO missionDO);
    
    /**
     * 将 FlightMissionDO 列表转换为 FlightMissionVO 列表
     *
     * @param missionDOList 飞行任务DO列表
     * @return List<FlightMissionVO> 飞行任务VO列表
     */
    List<FlightMissionVO> toFlightMissionVOList(List<FlightMissionDO> missionDOList);
    
    /**
     * 将 FlightPlanDO 转换为 FlightPlanVO
     *
     * @param planDO 飞行计划DO
     * @return FlightPlanVO 飞行计划VO
     */
    @Mapping(source = "missionId", target = "missionId")
    @Mapping(source = "planId", target = "planId")
    @Mapping(source = "status", target = "status")
    @Mapping(source = "planedTakeoffTime", target = "planedTakeoffTime")
    @Mapping(source = "planedLandingTime", target = "planedLandingTime")
    @Mapping(source = "operatorUser", target = "operatorUser")
    @Mapping(source = "operatorPhone", target = "operatorPhone")
    @Mapping(source = "planedAltitude", target = "planedAltitude")
    @Mapping(target = "realtimeUavInfoList", ignore = true)
    @Mapping(target = "uavInfo", ignore = true)
    @Mapping(target = "landingAerodrome", ignore = true)
    @Mapping(source = "planCreateTime", target = "planCreateTime", qualifiedByName = "localDateTimeToLong")
    FlightPlanVO toFlightPlanVO(FlightPlanDO planDO);
    
    /**
     * 将 FlightPlanDO 列表转换为 FlightPlanVO 列表
     *
     * @param planDOList 飞行计划DO列表
     * @return List<FlightPlanVO> 飞行计划VO列表
     */
    List<FlightPlanVO> toFlightPlanVOList(List<FlightPlanDO> planDOList);
    
    /**
     * 将 RealTimeUavFlightDO 转换为 RealTimeUavFlightVO
     *
     * @param flightDO 实时飞行数据DO
     * @return RealTimeUavFlightVO 实时飞行数据VO
     */
    @Mapping(target = "flightUnit", ignore = true)
    @Mapping(source = "reportTime", target = "reportTime", qualifiedByName = "localDateTimeToLong")
    RealTimeUavFlightVO toRealTimeUavFlightVO(RealTimeUavFlightDO flightDO);

    List<RealTimeUavFlightVO> toRealTimeUavFlightVOList(List<RealTimeUavFlightDO> flightDOList);
    
    /**
     * 将 UavDO 转换为 UavVO
     *
     * @param uavDO 无人机DO
     * @return UavVO 无人机VO
     */
    UavVO toUavVO(UavDO uavDO);
    
    /**
     * 将 AerodromeDO 转换为 AerodromeVO
     *
     * @param aerodromeDO 机场DO
     * @return AerodromeVO 机场VO
     */
    AerodromeVO toAerodromeVO(AerodromeDO aerodromeDO);

    List<AerodromeVO> toListAerodromeVO(List<AerodromeDO> aerodromeDO);

    /**
     * 将queryDTO转为queryModel
     * @param queryDTO 查询参数
     * @return queryModel
     */
    FlightQuery toFlightQuery(FlightQueryDTO queryDTO);

    @Mapping(source = "load", target = "load", qualifiedByName = "objectToJson")
    @Mapping(source = "waring", target = "warning", qualifiedByName = "objectToJson")
    @Mapping(source = "error", target = "error", qualifiedByName = "objectToJson")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "gmtCreated", ignore = true)
    @Mapping(target = "gmtModified", ignore = true)
    UavRealtimeMqRecordDO toUavRealtimeMqRecordDO(RealTimeFlyDataMsgBody flyDataMsgBody);

    List<FlightDailyStatisticsVO> toFlightDailyStatisticsVOList(List<FlightDailyStatisticsDO> doList);

    FlightDailyStatisticsVO toFlightDailyStatisticsVO(FlightDailyStatisticsDO flightDailyStatisticsDO);

    @Named("doStatusToVoStatus")
    default String doStatusToVoStatus(String doStatus, LocalDateTime reportTime) {
        FlightStatusEnum voStatus = FlightStatusEnum.getVOStatus(doStatus, reportTime);
        return voStatus == null ? null : voStatus.getCode();
    }
    @Named("localDateTimeToLong")
    default Long localDateTimeToLong(LocalDateTime time) {
        return time == null ? null : time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    @Named("objectToJson")
    default String objectToJson(Object obj) {
        return obj == null ? null : JSONObject.toJSONString(obj);
    }

    List<FlightDemandPlanStatDTO> toFlightDemandPlanStatDTOList(List<FlightDemandPlanStatDO> flightDemandPlanStatDOList);

    FlightDemandPlanStatDTO toFlightDemandPlanStatDTO(FlightDemandPlanStatDO flightDemandPlanStatDO);

    @Mappings({
            @Mapping(target = "airspaceId", source = "asid"),
            @Mapping(target = "code", source = "asid"),
            @Mapping(target = "name", source = "asid"),
            @Mapping(target = "wkt2", expression = "java(WktUtil.toPolygon(msgBody.getBoundary()))"),
            @Mapping(target = "maxHeight", source = "top"),
            @Mapping(target = "minHeight", source = "bot"),
            @Mapping(target = "status", source = "status"),

    })
    AirspaceDO fromAirspaceStatusUpdateMsgBody(AirspaceStatusUpdateMsgBody msgBody);

    List<AirspaceDO> fromAirspaceStatusUpdateMsgBodyList(List<AirspaceStatusUpdateMsgBody> msgBodyList);

    @Named("achievementTypeEnum")
    default FlightAchievementTypeEnum achievementTypeEnum(String typeEnum) {
        return FlightAchievementTypeEnum.getEnumByDeepCode(typeEnum);
    }
}