package com.deepinnet.spatiotemporalplatform.skyflow.service.impl;

import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.model.staticpoi.StaticPoi;
import com.deepinnet.spatiotemporalplatform.model.staticpoi.StaticPoiQueryDTO;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.StaticPoiRepository;
import com.deepinnet.spatiotemporalplatform.skyflow.service.StaticPoiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 静态核心POI 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-10
 */
@Service
public class StaticPoiServiceImpl implements StaticPoiService {

    @Resource
    private StaticPoiRepository staticPoiRepository;

    @Override
    public CommonPage<StaticPoi> pageStaticPoi(StaticPoiQueryDTO queryDTO) {
        LogUtil.info("分页查询静态POI入参：[{}]", queryDTO);
        CommonPage<StaticPoi> result = staticPoiRepository.pageStaticPoi(queryDTO);
        LogUtil.info("分页查询静态POI结果：总条数[{}]", result.getTotal());
        return result;
    }

    @Override
    public StaticPoi getByPoiId(String poiId) {
        LogUtil.info("根据POI ID查询静态POI：[{}]", poiId);
        return staticPoiRepository.getByPoiId(poiId);
    }

} 