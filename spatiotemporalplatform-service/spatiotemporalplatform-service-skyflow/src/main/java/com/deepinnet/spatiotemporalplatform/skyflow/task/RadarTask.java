package com.deepinnet.spatiotemporalplatform.skyflow.task;

import cn.hutool.core.collection.CollectionUtil;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.util.PointCoordinate;
import com.deepinnet.localdata.integration.RadarClient;
import com.deepinnet.localdata.integration.model.input.RadarQueryDTO;
import com.deepinnet.localdata.integration.model.output.RadarResponseDTO;
import com.deepinnet.spatiotemporalplatform.base.config.TenantConfig;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.RadarDO;
import com.deepinnet.spatiotemporalplatform.model.util.*;
import com.deepinnet.spatiotemporalplatform.skyflow.convert.RadarConvert;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.RadarRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 雷达数据同步任务
 * 每2分钟从外部系统拉取雷达数据并保存到本地数据库
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@Component
@RequiredArgsConstructor
@Profile({"local", "local-inner", "test", "alicloud"})
public class RadarTask {

    private final RadarRepository radarRepository;
    private final RadarConvert radarConvert;
    private final RadarClient radarClient;
    @Resource
    private TenantConfig tenantConfig;
    /**
     * 每2分钟执行一次
     */
    @Scheduled(cron = "0 0/2 * * * *")
    public void syncRadars() {
        LogUtil.info("[雷达数据同步] 任务开始执行");
        
        try {
            // 使用RadarClient获取雷达数据
            List<RadarResponseDTO> radarResponseList = radarClient.getAllRadars(RadarQueryDTO.builder().build());

            if (CollectionUtil.isEmpty(radarResponseList)) {
                LogUtil.info("[雷达数据同步] 没有雷达数据需要同步");
                return;
            }
            
            LogUtil.info("[雷达数据同步] 获取到雷达数据 {} 条", radarResponseList.size());
            
            // 获取所有雷达编码，用于后续批量查询
            List<String> codes = radarResponseList.stream()
                    .map(RadarResponseDTO::getCode)
                    .collect(Collectors.toList());
            
            // 查询本地数据库已存在的雷达记录
            List<RadarDO> existingRadars = radarRepository.lambdaQuery()
                    .in(RadarDO::getCode, codes)
                    .list();
            
            // 将已存在的雷达按编码分组
            Map<String, RadarDO> existingMap = existingRadars.stream()
                    .collect(Collectors.toMap(
                            RadarDO::getCode,
                            radar -> radar,
                            (existing, replacement) -> existing
                    ));
            
            // 分别存储需要新增和更新的记录
            List<RadarDO> toInsertList = new ArrayList<>();
            List<RadarDO> toUpdateList = new ArrayList<>();
            
            // 处理每条雷达数据
            for (RadarResponseDTO radarResponse : radarResponseList) {
                String code = radarResponse.getCode();
                
                // 转换对象到DO
                RadarDO radarDO = convertToRadarDO(radarResponse);
                
                if (existingMap.containsKey(code)) {
                    // 已存在相同编码的记录，需要更新
                    RadarDO existingDO = existingMap.get(code);
                    // 保留原始ID
                    radarDO.setId(existingDO.getId());
                    radarDO.setGmtCreated(existingDO.getGmtCreated());
                    radarDO.setGmtModified(LocalDateTime.now());
                    toUpdateList.add(radarDO);
                } else {
                    // 不存在相同编码的记录，需要新增
                    radarDO.setGmtCreated(LocalDateTime.now());
                    radarDO.setGmtModified(LocalDateTime.now());
                    toInsertList.add(radarDO);
                }
            }
            
            // 批量保存新增记录
            if (CollectionUtil.isNotEmpty(toInsertList)) {
                boolean saveResult = radarRepository.saveBatch(toInsertList);
                LogUtil.info("[雷达数据同步] 新增雷达数据 {} 条, 结果: {}", toInsertList.size(), saveResult ? "成功" : "失败");
            }
            
            // 批量更新已有记录
            if (CollectionUtil.isNotEmpty(toUpdateList)) {
                boolean updateResult = radarRepository.updateBatchById(toUpdateList);
                LogUtil.info("[雷达数据同步] 更新雷达数据 {} 条, 结果: {}", toUpdateList.size(), updateResult ? "成功" : "失败");
            }
            
            LogUtil.info("[雷达数据同步] 任务执行完成，新增: {}条, 更新: {}条", toInsertList.size(), toUpdateList.size());
        } catch (Exception e) {
            LogUtil.error("[雷达数据同步] 任务执行异常", e);
        }
    }
    
    /**
     * 转换RadarResponseDTO到RadarDO
     */
    private RadarDO convertToRadarDO(RadarResponseDTO radarResponse) {
        RadarDO radarDO = new RadarDO();
        radarDO.setAlt(radarResponse.getAlt());
        radarDO.setHeight(radarResponse.getHeight());
        radarDO.setRadius(radarResponse.getRadius());

        String spatialDetailPoints = radarResponse.getPoint();
        PointCoordinate pointCoordinate = CoordinateUtil.wgs84ToGcj02(spatialDetailPoints.split(",")[0], spatialDetailPoints.split(",")[1]);
        radarDO.setPoint(WktUtil.toWkt(pointCoordinate));

        radarDO.setAddress(radarResponse.getAddress());
        radarDO.setRemark(radarResponse.getRemark());
        radarDO.setDepartIds(radarResponse.getDepartIds());
        radarDO.setCode(radarResponse.getCode());
        radarDO.setName(radarResponse.getName());
        radarDO.setIsDeleted(false);
        radarDO.setIsDisabled(radarResponse.getIsDisabled());
        radarDO.setTenantId(tenantConfig.getTenantId());
        return radarDO;
    }
} 