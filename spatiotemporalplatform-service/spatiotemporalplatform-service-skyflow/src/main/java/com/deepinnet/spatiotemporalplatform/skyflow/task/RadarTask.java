package com.deepinnet.spatiotemporalplatform.skyflow.task;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.util.PointCoordinate;
import com.deepinnet.localdata.integration.RadarClient;
import com.deepinnet.localdata.integration.model.input.RadarQueryDTO;
import com.deepinnet.localdata.integration.model.output.RadarResponseDTO;
import com.deepinnet.spatiotemporalplatform.base.config.TenantConfig;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.RadarDO;
import com.deepinnet.spatiotemporalplatform.model.util.CoordinateUtil;
import com.deepinnet.spatiotemporalplatform.model.util.WktUtil;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.RadarRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 雷达数据同步任务
 * 每2分钟从外部系统拉取雷达数据并保存到本地数据库
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@Component
@RequiredArgsConstructor
@Profile({"local", "test", "alicloud", "sz-lg-gov"})
public class RadarTask {

    @Resource
    private RadarRepository radarRepository;

    @Resource
    private RadarClient radarClient;

    @Resource
    private TenantConfig tenantConfig;

    /**
     * 每2分钟执行一次
     */
    @Scheduled(cron = "0 0/2 * * * *")
    public void syncRadars() {
        LogUtil.info("[雷达数据同步] 任务开始执行");

        try {
            // 使用RadarClient获取雷达数据
            List<RadarResponseDTO> radarResponseList = radarClient.getAllRadars(RadarQueryDTO.builder().build());

            if (CollectionUtil.isEmpty(radarResponseList)) {
                LogUtil.info("[雷达数据同步] 没有雷达数据需要同步");
                return;
            }

            LogUtil.info("[雷达数据同步] 获取到雷达数据 {} 条", radarResponseList.size());

            // 删除老数据
            LambdaQueryWrapper<RadarDO> queryWrapper = Wrappers.lambdaQuery(RadarDO.class);
            queryWrapper.eq(RadarDO::getTenantId, tenantConfig.getTenantId());
            radarRepository.remove(queryWrapper);

            // 插入新数据
            List<RadarDO> toInsertList = radarResponseList.stream().map(this::convertToRadarDO).collect(Collectors.toList());
            boolean saveResult = radarRepository.saveBatch(toInsertList);
            LogUtil.info("[雷达数据同步] 新增雷达数据 {} 条, 结果: {}", toInsertList.size(), saveResult ? "成功" : "失败");

        } catch (Exception e) {
            LogUtil.error("[雷达数据同步] 任务执行异常", e);
        }
    }

    /**
     * 转换RadarResponseDTO到RadarDO
     */
    private RadarDO convertToRadarDO(RadarResponseDTO radarResponse) {
        RadarDO radarDO = new RadarDO();
        radarDO.setAlt(radarResponse.getAlt());
        radarDO.setHeight(radarResponse.getHeight());
        radarDO.setRadius(radarResponse.getRadius());

        String spatialDetailPoints = radarResponse.getPoint();
        PointCoordinate pointCoordinate = CoordinateUtil.wgs84ToGcj02(spatialDetailPoints.split(",")[0], spatialDetailPoints.split(",")[1]);
        radarDO.setPoint(WktUtil.toWkt(pointCoordinate));

        radarDO.setAddress(radarResponse.getAddress());
        radarDO.setRemark(radarResponse.getRemark());
        radarDO.setDepartIds(radarResponse.getDepartIds());
        radarDO.setCode(radarResponse.getCode());
        radarDO.setName(radarResponse.getName());
        radarDO.setIsDeleted(false);
        radarDO.setIsDisabled(radarResponse.getIsDisabled());
        radarDO.setTenantId(tenantConfig.getTenantId());
        return radarDO;
    }
} 