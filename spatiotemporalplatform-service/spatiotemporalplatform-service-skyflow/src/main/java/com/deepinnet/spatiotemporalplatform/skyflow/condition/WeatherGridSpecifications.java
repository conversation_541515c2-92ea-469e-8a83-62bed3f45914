package com.deepinnet.spatiotemporalplatform.skyflow.condition;

import com.deepinnet.spatiotemporalplatform.model.skyflow.WeatherGridPostBody;
import com.deepinnet.spatiotemporalplatform.skyflow.util.beidou.WktUtil;
import com.deepinnet.spatiotemporalplatform.skyflow.util.weather.BaseGeographicInfo;
import com.deepinnet.spatiotemporalplatform.skyflow.util.weather.LocationWeatherData;
import org.apache.commons.collections4.CollectionUtils;
import org.locationtech.jts.geom.Polygon;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * C<PERSON> zengjuerui
 * Date 2025-04-19
 **/
public class WeatherGridSpecifications {

    public static Function<LocationWeatherData, LocationWeatherData> createSimplifier(WeatherGridPostBody postBody) {

        List<Function<LocationWeatherData, LocationWeatherData>> specs = new ArrayList<>();

        if(postBody.getFactor()  == null) {
            return Function.identity();
        }

        switch (postBody.getFactor()) {

//            case TEMPERATURE:
//                specs.add(grid -> {
//                    if(grid == null) return null;
//
//                    LocationWeatherData g = new LocationWeatherData();
//                    g.setCenter(grid.getCenter());
//                    g.setLocationCode(grid.getLocationCode());
//                    g.setCenterPoint(grid.getCenterPoint());
//
//                    BaseGeographicInfo baseInfo = grid.getBaseInfo();
//                    if(baseInfo != null) {
//                        BaseGeographicInfo b = new BaseGeographicInfo();
//                        b.setTemperature(baseInfo.getTemperature());
//                        g.setBaseInfo(b);
//                    }
//                    return g;
//                });
//                break;
//
//            case PRECIPITATION:
//                specs.add(grid -> {
//                    if(grid == null) return null;
//
//                    LocationWeatherData g = new LocationWeatherData();
//                    g.setCenter(grid.getCenter());
//                    g.setLocationCode(grid.getLocationCode());
//                    g.setCenterPoint(grid.getCenterPoint());
//
//                    BaseGeographicInfo baseInfo = grid.getBaseInfo();
//                    if(baseInfo != null) {
//                        BaseGeographicInfo b = new BaseGeographicInfo();
//                        b.setTemperature(baseInfo.getPrecipitationIntensity());
//                        g.setBaseInfo(b);
//                    }
//                    return g;
//                });
//                break;

            case WIND_FORCE:
                specs.add(grid -> {
                    if(grid == null) return null;

                    LocationWeatherData g = new LocationWeatherData();
                    g.setCenter(grid.getCenter());
                    g.setLocationCode(grid.getLocationCode());
                    g.setCenterPoint(grid.getCenterPoint());

                    g.setWeatherLayers( CollectionUtils.emptyIfNull(grid.getWeatherLayers()).stream()
                            .filter(weatherLayer -> Optional.ofNullable(postBody.getWindForceAltitude()).orElse(0) == (int) weatherLayer.getAltitude())
                            .collect(Collectors.toList()));
                    return g;
                });
                break;
        }

        return specs.stream()
                .reduce(Function.identity(), Function::andThen);
    }
}
