package com.deepinnet.spatiotemporalplatform.skyflow.condition;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 违规检测查询条件
 *
 * <AUTHOR> wong
 * @create 2025/3/4 15:57
 * @Description
 */
@Data
public class ViolationDetectionQueryCondition {

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 运营主体
     */
    private String operatingEntity;

    private Integer pageNum;

    private Integer pageSize;
}
