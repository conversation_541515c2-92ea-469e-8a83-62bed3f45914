package com.deepinnet.spatiotemporalplatform.skyflow.task;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.WeatherGridDO;
import com.deepinnet.spatiotemporalplatform.model.skyflow.WeatherGridPostBody;
import com.deepinnet.spatiotemporalplatform.model.skyflow.WeatherGridQueryResponse;
import com.deepinnet.spatiotemporalplatform.model.skyflow.WeatherGridRequest;
import com.deepinnet.spatiotemporalplatform.model.skyflow.WeatherGridUrlParams;
import com.deepinnet.spatiotemporalplatform.skyflow.error.BizErrorCode;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.WeatherGridRepository;
import com.deepinnet.spatiotemporalplatform.skyflow.util.weather.*;
import com.deepinnet.spatiotemporalplatform.skyflow.util.weather.WeatherDataProcessUtil;
import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.StreamSupport;
import java.util.zip.GZIPInputStream;

/**
 * Creator zengjuerui
 * Date 2025-04-21
 **/

@Slf4j
@Profile({"local", "test", "alicloud", "demo"})
@Component
public class WeatherGridPersistenceTask {

    private static final String WKT = "POLYGON ((114.210943 22.697135,114.215265 22.70108,114.219311 22.699596,114.224138 22.695184,114.227678 22.694718,114.231495 22.693148,114.228598 22.688949,114.227633 22.685852,114.227495 22.682883,114.222299 22.683519,114.210943 22.697135))";

    @Resource
    private CommonDataService commonDataService;
    @Resource
    private WeatherGridRepository weatherGridRepository;

    @Value("${tmp.file.path:/tmp/}")
    private String tempFilePath;

    private static final int BATCH_SIZE = 1000;
    private static final int BUFFER_SIZE = 8 * 1024 * 1024;

    private static final JsonFactory jsonFactory = new JsonFactory();

    private final static List<WeatherGridDO> BATCH_BUFFER = new ArrayList<>(BATCH_SIZE);

    @Scheduled(fixedRate = 10, timeUnit = TimeUnit.MINUTES, initialDelay = 1)
    public void persistence() {
        String weatherFileUrl = getWeatherFileUrl();
        if(StringUtils.isBlank(weatherFileUrl)) {
            return;
        }

        String filename = tempFilePath + UUID.randomUUID().toString().concat(".gz");
        MultiThreadFileDownloader.downloadFile(weatherFileUrl, filename);

        try(GZIPInputStream gzipInputStream = new GZIPInputStream(new FileInputStream(filename), BUFFER_SIZE);
            BufferedReader reader = new BufferedReader(new InputStreamReader(gzipInputStream), BUFFER_SIZE);
            JsonParser parser = jsonFactory.createParser(reader)) {

            Spliterator<LocationWeatherData> spliterator = new WeatherGridSpliterator(parser);

            StreamSupport.stream(spliterator, false) // 关闭并行流
                    .forEach(this::processDataItem);

            // 处理最后一批数据
            if (!BATCH_BUFFER.isEmpty()) {
                flushBatch();
            }

        } catch (Exception e) {
            LogUtil.error("读取文件失败" + filename, e);
        } finally {
            FileUtil.del(filename);
        }
    }

    private String getWeatherFileUrl() {
        WeatherGridPostBody weatherGridPostBody = new WeatherGridPostBody();
        // todo 目前高德对接口参数并未处理返回的数据都一样，就先用一个龙岗的临时数据
        // todo 之后需要根据业务城市的空域数据去处理
        weatherGridPostBody.setBounds(WKT);
        weatherGridPostBody.setLevel(7);

        WeatherGridRequest request = new WeatherGridRequest();
        request.setPostBody(weatherGridPostBody);
        WeatherGridUrlParams weatherGridUrlParams = new WeatherGridUrlParams();
        request.setUrlParams(weatherGridUrlParams);
        WeatherGridQueryResponse response = (WeatherGridQueryResponse) commonDataService.fetchData(request);
        if (response == null || StringUtils.isBlank(response.getData())) {
            LogUtil.warn("高德接口返回气象数据为空，入参为：{}", JSONUtil.toJsonStr(response));
            return null;
        }

        String data = response.getData();
        if (StringUtils.isBlank(data)) {
            LogUtil.warn("高德接口返回气象数据为空，入参为：{}", JSONUtil.toJsonStr(response));
            return null;
        }

        JSONObject jsonObject = JSONUtil.parseObj(data);
        return (String) jsonObject.get("url");
    }

    private void processDataItem(LocationWeatherData data) {
        try {
            WeatherGridDO weatherGridDO = new WeatherGridDO();
            weatherGridDO.setGridCode(data.getLocationCode());
            weatherGridDO.setCenterPoint(data.getCenterPoint());
            if(data.getBaseInfo() != null) {
                BeanUtils.copyProperties(data.getBaseInfo(), weatherGridDO);
            }
            if(CollectionUtils.isNotEmpty(data.getWeatherLayers())) {
                weatherGridDO.setWeatherLayers(JSONUtil.toJsonStr(data.getWeatherLayers()));
            }

            weatherGridDO.computeMd5();

            BATCH_BUFFER.add(weatherGridDO);

            if (BATCH_BUFFER.size() >= BATCH_SIZE) {
                flushBatch();
            }
        } catch (Exception e) {
            LogUtil.error("单条数据处理失败，locationCode: " + data.getLocationCode(), e);
            // 记录错误但继续处理后续数据
        }
    }

    private void flushBatch() {
        try {
            weatherGridRepository.upsertBatch(List.copyOf(BATCH_BUFFER));
        } catch (Exception e) {
            LogUtil.error("批次数据写入失败，数量：" + BATCH_BUFFER.size(), e);
        } finally {
            BATCH_BUFFER.clear();
        }
    }

    private static class WeatherGridSpliterator implements Spliterator<LocationWeatherData> {
        private final JsonParser jsonParser;
        private boolean firstAdvance = true;

        public WeatherGridSpliterator(JsonParser jsonParser) {
            this.jsonParser = jsonParser;
        }

        @Override
        public boolean tryAdvance(Consumer<? super LocationWeatherData> action) {
            try {

                if (firstAdvance) {
                    JsonToken token = jsonParser.nextToken();
                    if (token != JsonToken.START_OBJECT) {
                        LogUtil.error("反序列化数据失败");
                        throw new BizException(BizErrorCode.DESIALIZATION_FAILED.getCode(), BizErrorCode.DESIALIZATION_FAILED.getDesc());
                    }
                    firstAdvance = false;
                }

                JsonToken token = jsonParser.nextToken();
                if (token == JsonToken.END_OBJECT) {
                    return false; // 对象结束
                }

                if (token != JsonToken.FIELD_NAME) {
                    throw new BizException(BizErrorCode.DESIALIZATION_FAILED.getCode(), BizErrorCode.DESIALIZATION_FAILED.getDesc());
                }

                LocationWeatherData data = WeatherDataProcessUtil.parseLocationData(jsonParser);
                action.accept(data);
                return true;

            } catch (IOException e) {
                LogUtil.error("流式解析失败", e);
                throw new BizException(BizErrorCode.DESIALIZATION_FAILED.getCode(), BizErrorCode.DESIALIZATION_FAILED.getDesc());
            }
        }

        @Override
        public Spliterator<LocationWeatherData> trySplit() {
            // 保守策略：不支持分割（确保超大 JSON 行安全）
            // 注：若数据为独立记录数组，可在此实现逻辑分割
            return null;
        }

        @Override
        public long estimateSize() {
            return Long.MAX_VALUE; // 未知大小
        }

        @Override
        public int characteristics() {
            return Spliterator.ORDERED | Spliterator.NONNULL;
        }
    }
}
