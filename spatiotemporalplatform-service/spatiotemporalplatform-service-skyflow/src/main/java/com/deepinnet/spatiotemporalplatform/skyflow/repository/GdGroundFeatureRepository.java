package com.deepinnet.spatiotemporalplatform.skyflow.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.GdGroundFeatureDO;
import org.locationtech.jts.geom.Point;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2025-01-14 16:02:48
 */
public interface GdGroundFeatureRepository extends IService<GdGroundFeatureDO> {

    List<GdGroundFeatureDO> getGroundFeatureList(List<Point> pointList, String region);
}
