package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.UavRealtimeMqRecordDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.UavRealtimeMqRecordMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.UavRealtimeMqRecordRepository;
import org.apache.cxf.common.util.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 从消息队列接收的无人机实时数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Service
public class UavRealtimeMqRecordRepositoryImpl extends ServiceImpl<UavRealtimeMqRecordMapper, UavRealtimeMqRecordDO> implements UavRealtimeMqRecordRepository {

    @Override
    public Map<String, UavRealtimeMqRecordDO> getLatestFlightRecords(List<String> uavIds, List<String> planIds) {
        if (CollectionUtils.isEmpty(uavIds)) {
            return new HashMap<>(0);
        }
        
        // 一次查询太多可能会导致性能问题，这里分批查询
        int batchSize = 50;
        Map<String, UavRealtimeMqRecordDO> resultMap = new HashMap<>(uavIds.size());
        
        // 分批处理uavIds
        for (int i = 0; i < uavIds.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, uavIds.size());
            List<String> batchUavIds = uavIds.subList(i, endIndex);
            
            // 使用Mapper的方法查询每个无人机的最新记录
            List<UavRealtimeMqRecordDO> batchResults = this.baseMapper.selectLatestByUavIds(batchUavIds, planIds);
            
            // 将结果添加到Map中
            if (!CollectionUtils.isEmpty(batchResults)) {
                Map<String, UavRealtimeMqRecordDO> batchMap = batchResults.stream()
                        .collect(Collectors.toMap(
                                UavRealtimeMqRecordDO::getUavId,
                                record -> record,
                                (existing, replacement) -> existing // 如果有重复的uavId，保留第一个
                        ));
                resultMap.putAll(batchMap);
            }
        }
        
        LogUtil.info("[查询最新无人机记录] 获取到的最新记录数量：{}", resultMap.size());
        return resultMap;
    }
}
