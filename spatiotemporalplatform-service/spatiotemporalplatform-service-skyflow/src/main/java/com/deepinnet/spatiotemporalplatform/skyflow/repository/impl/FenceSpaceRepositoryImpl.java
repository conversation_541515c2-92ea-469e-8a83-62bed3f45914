package com.deepinnet.spatiotemporalplatform.skyflow.repository.impl;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FenceSpaceDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.FenceSpaceMapper;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.FenceSpaceRepository;

/**
 * <p>
 * 禁飞区/管制区 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@Service
public class FenceSpaceRepositoryImpl extends ServiceImpl<FenceSpaceMapper, FenceSpaceDO> implements FenceSpaceRepository {

} 