package com.deepinnet.spatiotemporalplatform.skyflow.algorithm.impl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.util.PointCoordinate;
import com.deepinnet.localdata.integration.GaoDeEnterpriseMapClient;
import com.deepinnet.localdata.integration.model.outsidebean.LocationNameResp;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.FlightEventsRepository;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.FlightEventDemandRelationRepository;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.DemandArea;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightEventsDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightEventDemandRelationDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.PlanDemandDO;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightEventsQuery;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightEventsStatDO;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightEventsStatQuery;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightPlanExistEventQueryDO;
import com.deepinnet.spatiotemporalplatform.dto.*;
import com.deepinnet.spatiotemporalplatform.enums.FlightEventTypeEnum;
import com.deepinnet.spatiotemporalplatform.enums.FlightEventsStatStartTimeQueryType;
import com.deepinnet.spatiotemporalplatform.skyflow.algorithm.EventMonitorService;
import com.deepinnet.spatiotemporalplatform.skyflow.convert.FlightEventsConvert;
import com.deepinnet.spatiotemporalplatform.skyflow.error.BizErrorCode;
import com.deepinnet.spatiotemporalplatform.skyflow.service.FlightService;
import com.deepinnet.spatiotemporalplatform.skyflow.util.beidou.WktUtil;
import com.deepinnet.spatiotemporalplatform.vo.RealTimeUavFlightVO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.geom.Point;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 算法检测事件服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class EventMonitorServiceImpl implements EventMonitorService {

    @Resource
    private FlightEventsRepository flightEventsRepository;

    @Resource
    private FlightEventsConvert flightEventsConvert;

    @Resource
    private GaoDeEnterpriseMapClient gaoDeEnterpriseMapClient;

    @Resource
    private FlightEventDemandRelationRepository flightEventDemandRelationRepository;

    @Resource
    private FlightService flightService;

    @Override
    public String saveEvent(FlightEventsDTO flightEventsDTO) {
        // 转换为DO并保存
        FlightEventsDO flightEventsDO = flightEventsConvert.convertToDO(flightEventsDTO);
        PointCoordinate eventPoint = flightEventsDTO.getEventPoint();
        flightEventsDO.setEventLocation(getAddressName(String.valueOf(eventPoint.getLongitude()), String.valueOf(eventPoint.getLatitude())));
        boolean success = flightEventsRepository.save(flightEventsDO);
        if (!success) {
            LogUtil.error("保存飞行事件失败: {}", flightEventsDTO.getEventName());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        LogUtil.info("保存飞行事件成功: {}", flightEventsDO.getId());
        return String.valueOf(flightEventsDO.getId());
    }

    @Override
    public void saveEventBatch(List<FlightEventsDTO> flightEventsDTOList) {
        if (CollectionUtil.isNotEmpty(flightEventsDTOList)) {
            // 转换为DO列表
            List<FlightEventsDO> flightEventsDOList = flightEventsDTOList.stream()
                    .map(dto -> {
                        FlightEventsDO flightEventsDO = flightEventsConvert.convertToDO(dto);
                        PointCoordinate eventPoint = dto.getEventPoint();
                        flightEventsDO.setEventLocation(getAddressName(String.valueOf(eventPoint.getLongitude()), String.valueOf(eventPoint.getLatitude())));
                        return flightEventsDO;
                    })
                    .collect(Collectors.toList());

            boolean success = flightEventsRepository.saveBatch(flightEventsDOList);
            if (!success) {
                LogUtil.error("批量保存飞行事件失败");
                throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
            }

            LogUtil.info("批量保存飞行事件成功, 数量: {}", flightEventsDOList.size());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateEventBatch(List<FlightEventsDTO> convertedEvents) {
        if (CollectionUtil.isEmpty(convertedEvents)) {
            LogUtil.info("批量保存或更新飞行事件列表为空，跳过处理");
            return;
        }

        LogUtil.info("开始批量保存或更新飞行事件，数量: {}", convertedEvents.size());

        // 收集所有algorithmEventId
        List<String> algorithmEventIds = convertedEvents.stream()
                .map(FlightEventsDTO::getAlgorithmEventId)
                .collect(Collectors.toList());

        // 批量查询已存在的记录
        LambdaQueryWrapper<FlightEventsDO> wrapper = Wrappers.lambdaQuery(FlightEventsDO.class)
                .in(FlightEventsDO::getAlgorithmEventId, algorithmEventIds);
        List<FlightEventsDO> existingEvents = flightEventsRepository.list(wrapper);

        // 构建已存在记录的映射，key为algorithmEventId，value为完整的DO对象
        Map<String, FlightEventsDO> existingEventMap = existingEvents.stream()
                .collect(Collectors.toMap(FlightEventsDO::getAlgorithmEventId, event -> event));

        LogUtil.info("批量查询已存在事件，查询条件数量: {}，已存在数量: {}", algorithmEventIds.size(), existingEvents.size());

        // 分别收集需要新增和更新的事件
        List<FlightEventsDO> toInsertList = new ArrayList<>();
        List<FlightEventsDO> toUpdateList = new ArrayList<>();

        for (FlightEventsDTO dto : convertedEvents) {
            FlightEventsDO flightEventsDO = flightEventsConvert.convertToDO(dto);
            FlightEventsDO existingEvent = existingEventMap.get(dto.getAlgorithmEventId());

            if (existingEvent != null) {
                // 如果存在，设置ID并添加到更新列表
                flightEventsDO.setId(existingEvent.getId());
                toUpdateList.add(flightEventsDO);
            } else {
                // 如果不存在，添加到新增列表
                toInsertList.add(flightEventsDO);
            }
        }

        LogUtil.info("批量保存或更新分析完成，新增: {}，更新: {}", toInsertList.size(), toUpdateList.size());

        // 批量新增
        if (CollectionUtil.isNotEmpty(toInsertList)) {
            //调用高德的逆地理编码查询地点名称，考虑内网不能访问高德接口，使用的企业地图的接口
            List<FlightEventsDO> filterFlightEventDOList = toInsertList.stream()
                    .map(flightEventsDO -> {
                        //违章建筑算法不需要走轨迹分析查询
                        if (Objects.equals(flightEventsDO.getEventType(), FlightEventTypeEnum.ILLEGAL_CONSTRUCTION_ANALYSIS.name())) {
                            return flightEventsDO;
                        }

                        // 根据算法event表的event_time值查询飞行器轨迹数据
                        RealTimeUavFlightVO nearbyFlight = flightService.queryLatestFlightsByReportTime(flightEventsDO.getEventStartTime(), flightEventsDO.getFlightTaskCode());
                        if (nearbyFlight == null) {
                            log.warn("无法找到匹配的飞行器轨迹数据: algorithmEventId={}, flightTaskId={}", flightEventsDO.getAlgorithmEventId(), flightEventsDO.getFlightTaskCode());
                            return null;
                        }

                        String uavPosition = nearbyFlight.getUavPosition();
                        String[] pointStr = uavPosition.split(",");
                        Point eventPoint = WktUtil.toPoint(Double.parseDouble(pointStr[0]), Double.parseDouble(pointStr[1]));
                        flightEventsDO.setEventPoint(eventPoint);

                        flightEventsDO.setEventLocation(getAddressName(String.valueOf(eventPoint.getX()), String.valueOf(eventPoint.getY())));
                        return flightEventsDO;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(filterFlightEventDOList)) {
                boolean insertSuccess = flightEventsRepository.saveBatch(filterFlightEventDOList);
                if (!insertSuccess) {
                    LogUtil.error("批量新增飞行事件失败");
                    throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "批量新增飞行事件失败");
                }
                LogUtil.info("批量新增飞行事件成功，数量: {}", filterFlightEventDOList.size());

                // 计算事件所属的需求、区域并保存关联关系
//                saveEventDemandRelations(filterFlightEventDOList);
            } else {
                LogUtil.error("批量新增飞行事件失败，没有从实时飞行数据表找到相关数据");
            }
        }

        // 批量更新
        if (CollectionUtil.isNotEmpty(toUpdateList)) {
            toUpdateList.forEach(flightEventsDTO -> flightEventsDTO.setGmtModified(LocalDateTime.now()));
            boolean updateSuccess = flightEventsRepository.updateBatchById(toUpdateList);
            if (!updateSuccess) {
                LogUtil.error("批量更新飞行事件失败");
                throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "批量更新飞行事件失败");
            }
            LogUtil.info("批量更新飞行事件成功，数量: {}", toUpdateList.size());
        }

        LogUtil.info("批量保存或更新飞行事件完成，新增: {}，更新: {}", toInsertList.size(), toUpdateList.size());
    }

    @Override
    public FlightEventsDTO getEventById(String id) {
        if (!StringUtils.isNumeric(id)) {
            LogUtil.error("获取飞行事件失败，ID为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        FlightEventsDO flightEventsDO = flightEventsRepository.getById(Long.valueOf(id));
        if (flightEventsDO == null) {
            LogUtil.error("获取飞行事件失败，事件不存在: {}", id);
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        return flightEventsConvert.convert(flightEventsDO);
    }

    @Override
    public CommonPage<FlightEventsDTO> pageQuery(FlightEventsQueryDTO queryDTO) {
        // 创建分页对象
        Page<FlightEventsDO> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        FlightEventsQuery query = flightEventsConvert.convertFlightEventsQuery(queryDTO);
        List<FlightEventsDO> resultPage = flightEventsRepository.queryUserDemandEvents(query);

        if (CollectionUtils.isEmpty(resultPage)) {
            return CommonPage.buildEmptyPage();
        }

        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), page.getPages(), page.getTotal(), flightEventsConvert.convertList(resultPage));
    }

    @Override
    public List<FlightEventsDTO> queryListByFlightTaskCode(String flightTaskCode, List<String> flightDemandNoList) {
        List<FlightEventsDTO> flightEventsDTOList = this.queryListByFlightTaskCode(flightTaskCode);

        if(CollectionUtil.isNotEmpty(flightDemandNoList)) {
            LambdaQueryWrapper<FlightEventDemandRelationDO> relationDOLambdaQueryWrapper = Wrappers.lambdaQuery(FlightEventDemandRelationDO.class)
                    .in(FlightEventDemandRelationDO::getFlightOriginDemandCode, flightDemandNoList);
            List<FlightEventDemandRelationDO> relationDOS = flightEventDemandRelationRepository.list(relationDOLambdaQueryWrapper);
            List<String> eventIds = CollStreamUtil.toList(relationDOS, FlightEventDemandRelationDO::getAlgorithmEventId);

            return flightEventsDTOList.stream()
                    .filter(f -> eventIds.contains(f.getAlgorithmEventId()))
                    .collect(Collectors.toList());
        }
        return flightEventsDTOList;
    }

    @Override
    public List<FlightEventsDTO> queryListByFlightTaskCode(String flightTaskCode) {
        if (StrUtil.isBlank(flightTaskCode)) {
            LogUtil.error("查询飞行事件列表失败，飞行任务ID为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 构建查询条件
        LambdaQueryWrapper<FlightEventsDO> wrapper = Wrappers.lambdaQuery(FlightEventsDO.class)
                .eq(FlightEventsDO::getFlightTaskCode, flightTaskCode)
                .orderByDesc(FlightEventsDO::getEventStartTime)
                .orderByDesc(FlightEventsDO::getId);

        // 执行查询
        List<FlightEventsDO> flightEventsDOList = flightEventsRepository.list(wrapper);
        return flightEventsConvert.convertList(flightEventsDOList);
    }

    @Override
    public List<FlightEventsStatDTO> queryFlightEventsStat(FlightEventsStatQueryDTO queryDTO) {
        if (queryDTO == null || queryDTO.getStartTime() == null) {
            LogUtil.error("查询飞行事件统计失败，开始时间为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        FlightEventsStatQuery query = new FlightEventsStatQuery();
        query.setStartTime(queryDTO.getStartTime());
        query.setUserNo(queryDTO.getUserNo());
        query.setDemandType(queryDTO.getDemandType());
        query.setTenantId(queryDTO.getTenantId());
        query.setAccount(queryDTO.getAccount());
        query.setUserNoList(queryDTO.getUserNoList());
        query.setStartTimeType(Optional.ofNullable(queryDTO.getStartTimeType()).map(Enum::name)
                .orElse(FlightEventsStatStartTimeQueryType.DEMAND_PUBLISH_TIME.name()));
        query.setDemandScene(queryDTO.getDemandScene());
        // 执行查询获取所有事件
        List<FlightEventsStatDO> statList = flightEventsRepository.queryFlightEventsStat(query);
        return statList.stream().map(statObj -> {
            FlightEventsStatDTO statDTO = new FlightEventsStatDTO();
            statDTO.setEventType(statObj.getEventType());
            statDTO.setEventNum(statObj.getEventNum());
            return statDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<FlightPlanExistEventDTO> queryPlanExistEvent(FlightPlanExistEventQueryDTO queryDTO) {
        if (CollectionUtil.isEmpty(queryDTO.getPlanIdList())) {
            return new ArrayList<>();
        }
        FlightPlanExistEventQueryDO planExistEventQueryDO = flightEventsConvert.convert(queryDTO);
        List<String> planIdList = flightEventsRepository.queryPlanExistEvent(planExistEventQueryDO);
        Set<String> planIdSet = new HashSet<>(planIdList);
        return queryDTO.getPlanIdList().stream().map(planId -> {
            FlightPlanExistEventDTO flightPlanExistEventDTO = new FlightPlanExistEventDTO();
            flightPlanExistEventDTO.setPlanId(planId);
            flightPlanExistEventDTO.setExistEvent(planIdSet.contains(planId));
            return flightPlanExistEventDTO;
        }).collect(Collectors.toList());
    }

    private String getAddressName(String lng, String lat) {
        try {
            List<LocationNameResp> locationNameByPoint = gaoDeEnterpriseMapClient.getLocationNameByPoint(lng, lat);
            if (CollectionUtil.isNotEmpty(locationNameByPoint)) {
                return locationNameByPoint.get(0).getFormatted_address();
            }
        } catch (Exception e) {
            log.error("获取企业地图经纬度地址失败, lng:{}, lat:{}", lng, lat, e);
        }
        return null;
    }

    /**
     * 保存事件与原始需求的关联关系
     * 如果事件关联的计划的需求是应急需求则全部保存，如果不是则判断如果区域是面则计算包含关系，如果是点则拓展50米成面再计算包含关系
     *
     * @param flightEventsDOList 新增的飞行事件列表
     */
    private void saveEventDemandRelations(List<FlightEventsDO> flightEventsDOList) {
        if (CollectionUtil.isEmpty(flightEventsDOList)) {
            return;
        }

        //如果事件关联的计划的需求是应急需求则全部保存
        //计划 -> 事件列表
        Map<String, List<FlightEventsDO>> planEventListMap = flightEventsDOList.stream().collect(Collectors.groupingBy(FlightEventsDO::getFlightTaskCode));

        List<String> planIdList = ListUtil.toList(planEventListMap.keySet());

        List<PlanDemandDO> emergencyDemandPlanList = flightEventDemandRelationRepository.queryPlanListOfEmergencyDemand(planIdList);

        //计划 -> 需求
        Map<String, String> emergencyDemandPlanMap = emergencyDemandPlanList.stream().collect(Collectors.toMap(PlanDemandDO::getPlanId, PlanDemandDO::getDemandNo));

        List<FlightEventDemandRelationDO> emergencyDemandEventRelationList = emergencyDemandPlanMap.entrySet().stream().map(entry -> {
            List<FlightEventsDO> emergencyFlightEventsDOList = planEventListMap.get(entry.getKey());
            return emergencyFlightEventsDOList.stream().map(flightEventsDO -> {
                FlightEventDemandRelationDO relation = new FlightEventDemandRelationDO();
                relation.setAlgorithmEventId(flightEventsDO.getAlgorithmEventId());
                relation.setTenantId(flightEventsDO.getTenantId());
                relation.setFlightOriginDemandCode(entry.getValue());
                relation.setAreaSequence(1);
                return relation;
            }).collect(Collectors.toList());
        }).flatMap(List::stream).collect(Collectors.toList());

        // 批量保存应急需求的事件关联关系
        if (CollectionUtil.isNotEmpty(emergencyDemandEventRelationList)) {
            flightEventDemandRelationRepository.saveBatch(emergencyDemandEventRelationList);
        }


        //如果不是则判断如果区域是面则计算包含关系，如果是点则拓展50米成面再计算包含关系
        //除应急需求的其他需求需要走区域范围过滤
        List<FlightEventsDO> remainingFlightEventList = flightEventsDOList.stream()
                .filter(event -> !emergencyDemandPlanMap.containsKey(event.getFlightTaskCode()))
                .collect(Collectors.toList());

        List<FlightEventDemandRelationDO> notEmergencyEventRelationList = new ArrayList<>();
        for (FlightEventsDO eventDO : remainingFlightEventList) {
            // 根据飞行任务编号和事件坐标查询原始需求和区域序号
            Point eventPoint = eventDO.getEventPoint();
            if (eventPoint != null && StrUtil.isNotBlank(eventDO.getFlightTaskCode())) {
                List<DemandArea> demandAreaList = flightEventDemandRelationRepository.queryDemandAreaByTaskCodeAndLocation(
                        eventDO.getFlightTaskCode(),
                        eventPoint.getX(),
                        eventPoint.getY(),
                        50
                );

                for (DemandArea demandArea : demandAreaList) {
                    // 设置事件ID和其他必要字段
                    FlightEventDemandRelationDO relation = new FlightEventDemandRelationDO();
                    relation.setAlgorithmEventId(eventDO.getAlgorithmEventId());
                    relation.setTenantId(eventDO.getTenantId());
                    relation.setFlightOriginDemandCode(demandArea.getDemandCode());
                    relation.setAreaSequence(demandArea.getSequence());
                    notEmergencyEventRelationList.add(relation);
                }
            }
        }

        // 批量保存关联关系
        if (CollectionUtil.isNotEmpty(notEmergencyEventRelationList)) {
            flightEventDemandRelationRepository.saveBatch(notEmergencyEventRelationList);
        }
    }
}
