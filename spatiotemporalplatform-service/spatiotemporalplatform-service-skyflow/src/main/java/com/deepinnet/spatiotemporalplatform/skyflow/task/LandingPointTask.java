package com.deepinnet.spatiotemporalplatform.skyflow.task;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.localdata.integration.LandingPointClient;
import com.deepinnet.localdata.integration.model.input.LandingPointPageQueryDTO;
import com.deepinnet.localdata.integration.model.output.LandingPointDataDTO;
import com.deepinnet.localdata.integration.model.output.LandingPointPageResponseDTO;
import com.deepinnet.spatiotemporalplatform.base.config.TenantConfig;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.AerodromeDO;
import com.deepinnet.spatiotemporalplatform.model.util.CoordinateUtil;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.AerodromeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 起降点信息同步任务（从第三方合作伙伴拉取数据存库）
 *
 * <AUTHOR>
 * @version 2025-03-15
 */
@Component
@RequiredArgsConstructor
@Profile({"local", "local-inner", "test", "alicloud"})
public class LandingPointTask {

    private final AerodromeRepository aerodromeRepository;
    private final LandingPointClient landingPointClient;
    private final TenantConfig tenantConfig;

    @Scheduled(cron = "0 0/2 * * * *")
    public void syncLandingPoints() {
        try {
            long start = System.currentTimeMillis();
            // 获取第三方合作伙伴的起降点数据
            LandingPointPageQueryDTO queryDTO = new LandingPointPageQueryDTO();
            queryDTO.setPage(1);
            queryDTO.setLimit(1000);
            queryDTO.setCrtType("0");
            LandingPointPageResponseDTO landingPoints;
            while (true) {
                landingPoints = landingPointClient.getPageLandingPoints(queryDTO);
                if (CollectionUtil.isEmpty(landingPoints.getData().getRows())) {
                    break;
                }
                insertLandingPoints(landingPoints.getData().getRows(), start);
                queryDTO.setPage(queryDTO.getPage() + 1);
            }
        } catch (Exception e) {
            LogUtil.error("[起降点数据同步] 同步起降点数据失败", e);
        }
    }

    private void insertLandingPoints(List<LandingPointDataDTO> landingPoints, long start) {
        if (landingPoints == null || landingPoints.isEmpty()) {
            LogUtil.info("[起降点数据同步] 没有起降点数据需要同步");
        }
        LogUtil.info("[起降点数据同步] 获取到第三方合作伙伴的起降点数据 {} 条", landingPoints.size());

        // 转换为DO对象
        var aerodromeList = landingPoints.stream().map(lp -> toAerodromeDO(lp)).collect(Collectors.toList());

        long dbStart = System.currentTimeMillis();

        // 删除已存在的起降点数据
        LambdaQueryWrapper<AerodromeDO> queryWrapper = Wrappers.lambdaQuery(AerodromeDO.class);
        queryWrapper.eq(AerodromeDO::getTenantId, tenantConfig.getTenantId());
        aerodromeRepository.remove(queryWrapper);

        // 保存到数据库
        aerodromeRepository.saveOrUpdateBatch(aerodromeList);

        LogUtil.info("[起降点数据同步] 成功同步 {} 条起降点数据,数据库耗时:{}ms,同步任务耗时:{}ms", landingPoints.size(), System.currentTimeMillis() - dbStart, System.currentTimeMillis() - start);
    }

    private AerodromeDO toAerodromeDO(LandingPointDataDTO landingPoint) {
        AerodromeDO aerodromeDO = new AerodromeDO();
        aerodromeDO.setUavPortId(landingPoint.getId());
        var coordinate = CoordinateUtil.wgs84ToGcj02(landingPoint.getPoint().split(",")[0], landingPoint.getPoint().split(",")[1]);
        aerodromeDO.setName(landingPoint.getName());
        aerodromeDO.setPosition(coordinate.getLongitude() + "," + coordinate.getLatitude());
        aerodromeDO.setStatus(landingPoint.getIsDisabled());
        aerodromeDO.setGmtCreated(LocalDateTime.now());
        aerodromeDO.setGmtModified(LocalDateTime.now());
        aerodromeDO.setCode(landingPoint.getCode());
        aerodromeDO.setAlt(String.valueOf(landingPoint.getAlt()));
        aerodromeDO.setHeight(String.valueOf(landingPoint.getHeight()));
        aerodromeDO.setAddress(landingPoint.getAddress());
        aerodromeDO.setRadius(String.valueOf(landingPoint.getRadius()));
        aerodromeDO.setTenantId(tenantConfig.getTenantId());
        return aerodromeDO;
    }


}
