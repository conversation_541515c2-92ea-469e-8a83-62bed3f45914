package com.deepinnet.spatiotemporalplatform.smartcity.service.repository;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.deepinnet.spatiotemporalplatform.base.repository.AbstractRepository;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.OnRoadBaseChannelDetailDaoService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.OnRoadBaseChannelDetailDO;
import com.deepinnet.spatiotemporalplatform.smartcity.service.dto.OnRoadBaseChannelDetail;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.deepinnet.spatiotemporalplatform.dal.constant.DataSourceConstants.CAR_PASS_STATICS;

/**
 * Description:
 * Date: 2024/8/5
 * Author: lijunheng
 */
@Service
@DS(CAR_PASS_STATICS)
public class OnRoadBaseChannelDetailRepositorySlave extends AbstractRepository<OnRoadBaseChannelDetailDO, OnRoadBaseChannelDetail> {
    @Resource
    private OnRoadBaseChannelDetailDaoService onRoadBaseChannelDetailDaoService;

}
