package com.deepinnet.spatiotemporalplatform.smartcity.service.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.spatiotemporalplatform.base.repository.AbstractRepository;
import com.deepinnet.spatiotemporalplatform.smartcity.service.convert.IntercomPointRecordConvert;
import com.deepinnet.spatiotemporalplatform.model.intercom.IntercomPointRecord;
import com.deepinnet.spatiotemporalplatform.dal.mapper.IntercomPointRecordMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.IntercomPointRecordDO;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * Description:
 * Date: 2024/9/27
 * Author: lijunheng
 */
@Repository
public class IntercomPointRecordRepository extends AbstractRepository<IntercomPointRecordDO, IntercomPointRecord> {
    @Resource
    private IntercomPointRecordMapper intercomPointRecordMapper;
    @Resource
    private IntercomPointRecordConvert intercomPointRecordConvert;

    public List<IntercomPointRecord> findByAreaCode(String areaCode) {
        return list(Wrappers.lambdaQuery(IntercomPointRecordDO.class).eq(IntercomPointRecordDO::getAreaCode, areaCode));
    }

    public void removeByAreaCode(String areaCode) {
        remove(Wrappers.lambdaQuery(IntercomPointRecordDO.class).eq(IntercomPointRecordDO::getAreaCode, areaCode));
    }

    public List<IntercomPointRecord> findByAreaCodeAndTime(String areaCode, DateTime time) {
        return list(Wrappers.lambdaQuery(IntercomPointRecordDO.class)
                .eq(IntercomPointRecordDO::getAreaCode, areaCode)
                .ge(IntercomPointRecordDO::getGpsArriveTime, time)
        );
    }

    public void saveOrUpdateBatchCustom(List<IntercomPointRecord> intercomPointList) {
        if (CollectionUtil.isNotEmpty(intercomPointList)) {
            intercomPointRecordMapper.saveOrUpdateBatchCustom(intercomPointRecordConvert.toDOList(intercomPointList));
        }
    }


    public List<IntercomPointRecord> queryNearIntercomPoint(Point point, Double distance) {
        return intercomPointRecordConvert.toModelList(intercomPointRecordMapper.queryNearIntercomPoint(point, distance));
    }

    public List<IntercomPointRecord> queryPolygonContainsAndBeforeTime(String areaWkt, Date date) {
        return intercomPointRecordConvert.toModelList(intercomPointRecordMapper.queryPolygonContainsAndBeforeTime(areaWkt, date));
    }

    public List<IntercomPointRecord> queryLatestListByDeviceId(List<String> codeList) {
        if (CollectionUtil.isEmpty(codeList)) {
            return CollUtil.empty(List.class);
        }
        return intercomPointRecordConvert.toModelList(intercomPointRecordMapper.queryLatestRecordByCode(codeList));
    }
}
