package com.deepinnet.spatiotemporalplatform.smartcity.service.service;


import com.deepinnet.spatiotemporalplatform.model.area.CircleLayerBatchDTO;
import com.deepinnet.spatiotemporalplatform.model.area.CircleLayerDTO;

import java.util.List;

public interface CircleLayerDomainService {

    CircleLayerDTO createCircleLayer(CircleLayerDTO circleLayerDTO);


    boolean deleteCircleLayer(String layerId);

    CircleLayerDTO getCircleLayerByLayerId(String layerId);

    List<CircleLayerDTO> getAllCircleLayers(int page, int size);

    List<CircleLayerDTO> listByAreaeCodeList(List<String> areaCodeList);

    List<CircleLayerDTO> batchCreateOrUpdateCircleLayer(CircleLayerBatchDTO circleLayerDTOS);

    CircleLayerDTO updateCircleLayer(CircleLayerDTO circleLayerDTO);
}
