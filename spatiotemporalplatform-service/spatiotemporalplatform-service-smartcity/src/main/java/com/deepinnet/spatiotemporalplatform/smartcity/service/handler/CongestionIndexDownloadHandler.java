package com.deepinnet.spatiotemporalplatform.smartcity.service.handler;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis.RealTimeDistrictIndexRanking;
import com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis.RealTimeDistrictIndexRankingRequest;
import com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis.RealTimeDistrictIndexRankingUrlParams;
import com.deepinnet.spatiotemporalplatform.base.enums.BizEnum;
import com.deepinnet.spatiotemporalplatform.base.ftp.FtpUtil;
import com.deepinnet.spatiotemporalplatform.base.ftp.AbstractFtpDownloadHandler;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *    拥堵指数 下载处理器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/14
 */

@Component
public class CongestionIndexDownloadHandler extends AbstractFtpDownloadHandler<RealTimeDistrictIndexRankingRequest> {

    @Override
    public BizEnum getBizEnum() {
        return BizEnum.REGION_CONGESTION_INDEX;
    }

    @Override
    protected String buildFilePath(RealTimeDistrictIndexRankingRequest request) {

        RealTimeDistrictIndexRankingUrlParams params = request.getUrlParams();

        return getBizEnum().getCode() + "_" + params.getAdcode() + "_" + LocalDateTime.now().toLocalDate().toString();
    }

    @Override
    protected String resolveFileName(String filePath, RealTimeDistrictIndexRankingRequest request) {

        RealTimeDistrictIndexRankingUrlParams params = request.getUrlParams();

        String lastFile = FtpUtil.getLastFileByPath(filePath);

        if (StrUtil.isBlank(lastFile)) {
            LogUtil.warn("[拥堵指数]-当前文件夹:{}, 不存在", filePath);
            return null;
        }

        String[] parts = lastFile.split("_");
        String secondLast = parts[parts.length - 2];

        String fileName = getBizEnum().getCode() + "_"
                + params.getAdcode() + "_"
                + secondLast + "_"
                + params.getTypes() + lastFile.replaceAll(".*(\\.[^.]+)$", "$1");

        // 判断文件是否存在
        boolean exist = FtpUtil.fileExist(filePath, fileName);
        if (!exist) {
            return null;
        }

        return fileName;
    }

    @Override
    protected Type getResponseType() {
        return new TypeReference<List<RealTimeDistrictIndexRanking>>() {}.getType();
    }
}
