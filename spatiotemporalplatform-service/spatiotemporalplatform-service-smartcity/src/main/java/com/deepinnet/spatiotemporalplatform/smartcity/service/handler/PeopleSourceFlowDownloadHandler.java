package com.deepinnet.spatiotemporalplatform.smartcity.service.handler;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.base.enums.BizEnum;
import com.deepinnet.spatiotemporalplatform.base.ftp.AbstractFtpDownloadHandler;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.RegionPeopleSourceFlowData;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.RegionPeopleSourceFlowRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;

/**
 * <p>
 * 实时人流来源地 下载处理器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/14
 */
@Slf4j
@Component
public class PeopleSourceFlowDownloadHandler extends AbstractFtpDownloadHandler<RegionPeopleSourceFlowRequest> {
    public static final String PEOPLE_SOURCE_FLOW_PATH = "people_source_flow";
    @Override
    public String buildFilePath(RegionPeopleSourceFlowRequest request) {
        return PEOPLE_SOURCE_FLOW_PATH;
    }

    @Override
    public BizEnum getBizEnum() {
        return BizEnum.REAL_TIME_PEOPLE_SOURCE_FLOW;
    }


    @Override
    protected Type getResponseType() {
        return new TypeReference<RegionPeopleSourceFlowData>() {
        }.getType();
    }
}
