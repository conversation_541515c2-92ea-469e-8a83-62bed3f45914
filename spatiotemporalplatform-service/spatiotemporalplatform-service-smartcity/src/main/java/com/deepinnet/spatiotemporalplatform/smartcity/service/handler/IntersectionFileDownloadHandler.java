package com.deepinnet.spatiotemporalplatform.smartcity.service.handler;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.base.enums.BizEnum;
import com.deepinnet.spatiotemporalplatform.base.ftp.AbstractFileDownloadHandler;
import com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis.RealTimeIntersectionEvaluationData;
import com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis.RealTimeIntersectionEvaluationRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;

/**
 * <p>
 * 实时路口评价
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/14
 */
@Slf4j
@Component
public class IntersectionFileDownloadHandler extends AbstractFileDownloadHandler<RealTimeIntersectionEvaluationRequest> {
    public static final String INTERSECTION_PATH = "intersection";

    @Override
    public String buildFilePath(RealTimeIntersectionEvaluationRequest request) {
        return INTERSECTION_PATH;
    }

    @Override
    public BizEnum getBizEnum() {
        return BizEnum.REAL_TIME_INTERSECTION_EVALUATION;
    }

    @Override
    protected Type getResponseType() {
        return new TypeReference<RealTimeIntersectionEvaluationData>() {
        }.getType();
    }
}
