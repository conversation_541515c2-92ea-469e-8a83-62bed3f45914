package com.deepinnet.spatiotemporalplatform.smartcity.service.convert;

import com.deepinnet.spatiotemporalplatform.dal.dataobject.RestrictedAreaVehicleAccessRecordDO;
import com.deepinnet.spatiotemporalplatform.dto.RestrictedAreaVehicleAccessRecordDTO;
import com.deepinnet.spatiotemporalplatform.model.area.AreaDTO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.AreaDO;
import com.deepinnet.spatiotemporalplatform.smartcity.service.enums.PlateRecordFiledStatus;
import org.mapstruct.*;

import java.util.List;
import java.util.Optional;

/**
 * AreaConvert
 * Author: chenkaiyang
 * Date: 2024/7/31
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface AreaConvert {
    @Mappings({
            @Mapping(target = "geo", expression = "java(com.deepinnet.spatiotemporalplatform.model.util.WktUtil.toPolygon(areaDTO.getGeo()))"),
            @Mapping(target = "geoBig", expression = "java(com.deepinnet.spatiotemporalplatform.model.util.WktUtil.toPolygon(areaDTO.getGeoBig()))")

    })
    AreaDO toDO(AreaDTO areaDTO);
    @Mappings({
            @Mapping(target = "geo", expression = "java(com.deepinnet.spatiotemporalplatform.model.util.WktUtil.toWkt(areaDO.getGeo()))"),
            @Mapping(target = "geoBig", expression = "java(com.deepinnet.spatiotemporalplatform.model.util.WktUtil.toWkt(areaDO.getGeoBig()))")

    })
    AreaDTO toDTO(AreaDO areaDO);

    List<AreaDTO> toDTOList(List<AreaDO> areaDOList);

    RestrictedAreaVehicleAccessRecordDTO toRestrictedAreaVehicleAccessRecordDTO(RestrictedAreaVehicleAccessRecordDO recordDO);

    List<RestrictedAreaVehicleAccessRecordDTO> toRestrictedAreaVehicleAccessRecordDTOList(List<RestrictedAreaVehicleAccessRecordDO> recordDOS);


}
