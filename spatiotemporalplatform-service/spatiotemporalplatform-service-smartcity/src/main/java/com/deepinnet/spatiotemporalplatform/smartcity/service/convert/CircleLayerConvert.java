package com.deepinnet.spatiotemporalplatform.smartcity.service.convert;

import com.deepinnet.spatiotemporalplatform.model.area.CircleLayerDTO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.CircleLayerDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * CircleLayerConvert
 * Author: chenkaiyang
 * Date: 2024/7/31
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface CircleLayerConvert {
    @Mappings({
            @Mapping(target = "boundaryCoordinates", expression = "java(com.deepinnet.spatiotemporalplatform.model.util.WktUtil.toPolygon(circleLayerDTO.getBoundaryCoordinates()))")

    })
    CircleLayerDO toDO(CircleLayerDTO circleLayerDTO);
    @Mappings({
            @Mapping(target = "boundaryCoordinates", expression = "java(com.deepinnet.spatiotemporalplatform.model.util.WktUtil.toWkt(circleLayerDO.getBoundaryCoordinates()))")

    })
    CircleLayerDTO toDTO(CircleLayerDO circleLayerDO);

    List<CircleLayerDTO> toDTOList(List<CircleLayerDO> circleLayerDOList);

    List<CircleLayerDO> toDOList(List<CircleLayerDTO> circleLayerDTOS);
}
