package com.deepinnet.spatiotemporalplatform.smartcity.service.service;

import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.spatiotemporalplatform.smartcity.service.convert.CommonBaseServiceConvert;
import com.deepinnet.spatiotemporalplatform.model.police.PoliceDeploymentDTO;
import com.deepinnet.spatiotemporalplatform.common.enums.ObjectType;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.PoliceDeploymentDO;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.PoliceDeploymentIService;
import com.deepinnet.spatiotemporalplatform.model.police.PoliceDeployment;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * PoliceDeploymentRepository
 * Author: chenkaiyang
 * Date: 2024/8/5
 */
@Service
public class PoliceDeploymentRepository {
    @Resource
    private PoliceDeploymentIService policeDeploymentIService;
    @Resource
    private CommonBaseServiceConvert commonBaseServiceConvert;

    public List<PoliceDeployment> listByObjCode(String objCode) {
        List<PoliceDeploymentDO> list = policeDeploymentIService.list(Wrappers.lambdaQuery(PoliceDeploymentDO.class)
                .eq(PoliceDeploymentDO::getObjectCode, objCode)
                .orderByDesc(PoliceDeploymentDO::getGmtCreated));
        return commonBaseServiceConvert.toPoliceDeploymentList(list);
    }

    public void deleteByObjCode(String objCode) {
        policeDeploymentIService.remove(Wrappers.<PoliceDeploymentDO>lambdaQuery().eq(PoliceDeploymentDO::getObjectCode, objCode));
    }

    public void batchSave(List<PoliceDeployment> policeDeployments) {
        policeDeploymentIService.saveBatch(commonBaseServiceConvert.toPoliceDeploymentDOList(policeDeployments));
    }

    public List<PoliceDeployment> listByObjCodeList(List<String> objCodeList) {
        List<PoliceDeploymentDO> list = policeDeploymentIService.list(Wrappers.lambdaQuery(PoliceDeploymentDO.class)
                .in(CollectionUtils.isNotEmpty(objCodeList), PoliceDeploymentDO::getObjectCode, objCodeList)
                .orderByDesc(PoliceDeploymentDO::getGmtCreated));
        return commonBaseServiceConvert.toPoliceDeploymentList(list);
    }

    public PoliceDeploymentDTO save(PoliceDeploymentDTO policeDeployment) {
        PoliceDeploymentDO policeDeploymentDO = commonBaseServiceConvert.toPoliceDeploymentDO(policeDeployment);
        policeDeploymentIService.save(policeDeploymentDO);
        return commonBaseServiceConvert.toDTO(policeDeploymentIService.getById(policeDeploymentDO.getId()));
    }

    public List<PoliceDeployment> list(String areaCode, String objectType, String objectCode) {
        return commonBaseServiceConvert.toPoliceDeploymentList(policeDeploymentIService.list(Wrappers.lambdaQuery(PoliceDeploymentDO.class)
                .eq(PoliceDeploymentDO::getAreaCode, areaCode)
                .eq(Objects.nonNull(objectType), PoliceDeploymentDO::getObjectType, objectType)
                .eq(Objects.nonNull(objectCode), PoliceDeploymentDO::getObjectCode, objectCode)
                .orderByDesc(PoliceDeploymentDO::getGmtCreated)));

    }

    public List<PoliceDeployment> listByAreaCodeAndPlanCodeList(String areaCode, List<String> contingencyPlanCodeList) {
        Assert.notEmpty(areaCode, "区域code不可为空");
        Assert.notEmpty(contingencyPlanCodeList, "预案code列表不可为空");
        return commonBaseServiceConvert.toPoliceDeploymentList(policeDeploymentIService.list(Wrappers.lambdaQuery(PoliceDeploymentDO.class)
                .eq(PoliceDeploymentDO::getAreaCode, areaCode)
                .eq(PoliceDeploymentDO::getObjectType, ObjectType.CONTINGENCY_PLAN.getCode())
                .in(CollectionUtils.isNotEmpty(contingencyPlanCodeList), PoliceDeploymentDO::getObjectCode, contingencyPlanCodeList)
                .orderByDesc(PoliceDeploymentDO::getGmtCreated)));
    }

    public Boolean delete(Integer id) {
        return policeDeploymentIService.removeById(id);
    }

    public void deleteByIds(List<Integer> batchDeleteList) {
        policeDeploymentIService.removeByIds(batchDeleteList);
    }

    public void deleteByObjCodeAndType(String planCode, String type) {
        policeDeploymentIService.remove(Wrappers.lambdaQuery(PoliceDeploymentDO.class)
                .eq(PoliceDeploymentDO::getObjectCode, planCode)
                .eq(PoliceDeploymentDO::getObjectType, type));
    }
}
