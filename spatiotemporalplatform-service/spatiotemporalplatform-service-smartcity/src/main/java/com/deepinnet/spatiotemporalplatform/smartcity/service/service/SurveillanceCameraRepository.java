package com.deepinnet.spatiotemporalplatform.smartcity.service.service;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.smartcity.service.convert.CommonBaseServiceConvert;
import com.deepinnet.spatiotemporalplatform.smartcity.service.error.BizErrorCode;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.SurveillanceCameraDO;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.SurveillanceCameraIService;
import com.deepinnet.spatiotemporalplatform.model.camera.SurveillanceCamera;
import com.deepinnet.spatiotemporalplatform.model.camera.SurveillanceCameraCondition;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * Creator zengjuerui
 * Date 2024-07-30
 **/

@Slf4j
@Component
public class SurveillanceCameraRepository {

    private static final int BATCH_SIZE = 1000;

    @Resource
    private SurveillanceCameraIService cameraIService;
    @Resource
    private CommonBaseServiceConvert serviceConvert;

    public List<SurveillanceCamera> listByCodesAndType(List<String> codes, String type) {

        List<SurveillanceCameraDO> cameraDOS = cameraIService.list(Wrappers.<SurveillanceCameraDO>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(codes), SurveillanceCameraDO::getCameraCode, codes)
                .eq(StringUtils.isNotBlank(type), SurveillanceCameraDO::getType, type));

        return serviceConvert.toSurveillanceCameraList(cameraDOS);
    }

    public CommonPage<SurveillanceCamera> listPage(SurveillanceCameraCondition condition) {

        try(Page<SurveillanceCameraDO> iPage = PageHelper.startPage(condition.getPageNum(), condition.getPageSize())) {
            List<SurveillanceCameraDO> cameraDOS;
            if(StringUtils.isNotBlank(condition.getWkt())) {
                cameraDOS = cameraIService.queryLineNearCamera(serviceConvert.condition2DO(condition));
            } else {
                cameraDOS = cameraIService.queryWithGis(serviceConvert.condition2DO(condition));
            }

            if(iPage.getTotal() == 0) {
                return CommonPage.buildPage(condition.getPageNum(), condition.getPageSize(), 0, 0L, Collections.emptyList());
            }

            List<SurveillanceCamera> routeList = serviceConvert.toSurveillanceCameraList(cameraDOS);

            return CommonPage.buildPage(condition.getPageNum(),
                    condition.getPageSize(),
                    iPage.getPages(),
                    iPage.getTotal(),
                    routeList);
        } catch (Exception e) {
            log.error("查询摄像头列表错误", e);
            throw new BizException(BizErrorCode.UNKNOWN_EXCEPTION.getCode(), "查询摄像头异常");
        }
    }

    public List<SurveillanceCamera> listAreaNearCamera(SurveillanceCameraCondition condition) {
        List<SurveillanceCameraDO> nearCameraList = cameraIService.queryAreaNearCamera(serviceConvert.condition2DO(condition));
        return serviceConvert.toSurveillanceCameraList(nearCameraList);
    }

    public SurveillanceCamera getByCodeAndType(String code, String type) {
        return serviceConvert.toSurveillanceCamera(cameraIService.getOne(Wrappers.<SurveillanceCameraDO>lambdaQuery()
                .eq(SurveillanceCameraDO::getType, type)
                .eq(SurveillanceCameraDO::getCameraCode, code)));
    }

    public List<SurveillanceCamera> listByAreaCodeAndType(String areaCode, String type) {
        List<SurveillanceCameraDO> list = cameraIService.queryCameraWithInArea(areaCode, type);
        return serviceConvert.toSurveillanceCameraList(list);
    }

    public void batchInsert(List<SurveillanceCamera> cameraList) {
        int size = cameraList.size();
        int index = 0;
        while (index < size) {
            cameraIService.saveBatch(serviceConvert.toSurveillanceCameraDOList(cameraList.subList(index, Math.min(size, index += BATCH_SIZE))));
        }
    }

    public List<SurveillanceCamera> queryCameraListByIndex(Date afterCreateDataTime, int startIndex, int size) {
        return serviceConvert.toSurveillanceCameraList(cameraIService.queryCameraListByIndex(afterCreateDataTime, startIndex, size));
    }

    public void updateStatusByCodeList(List<String> cameraCodeList, String status) {
        if (CollectionUtils.isNotEmpty(cameraCodeList)) {
            LambdaUpdateWrapper<SurveillanceCameraDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(SurveillanceCameraDO::getStatus, status)
                    .in(SurveillanceCameraDO::getCameraCode, cameraCodeList);
            cameraIService.update(updateWrapper);
        }
    }

    public Long countAllData() {
        return cameraIService.count();
    }

    public void updateHasPassCarDataByCodeList(List<String> cameraCodeList, Boolean hasPassCarData) {
        if (CollectionUtils.isNotEmpty(cameraCodeList)) {
            LambdaUpdateWrapper<SurveillanceCameraDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(SurveillanceCameraDO::getHasPassCarData, hasPassCarData)
                    .in(SurveillanceCameraDO::getCameraCode, cameraCodeList);
            cameraIService.update(updateWrapper);
        }
    }

    public List<SurveillanceCamera> queryCameraTypeAndCodeList(String type, List<String> cameraCodeList) {
        if (CollectionUtils.isNotEmpty(cameraCodeList)) {
            LambdaQueryWrapper<SurveillanceCameraDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SurveillanceCameraDO::getType, type)
                    .in(SurveillanceCameraDO::getCameraCode, cameraCodeList);
            return serviceConvert.toSurveillanceCameraList(cameraIService.list(queryWrapper));
        }
        return new ArrayList<>();
    }

    /**
     * 查询区域外展距离内的卡口摄像头
     * @param areaCode
     * @param extendDistance 外展距离
     * @return
     */
    public List<SurveillanceCamera> findAreaCarCamerasWithExtends(String areaCode, int extendDistance) {
        return serviceConvert.toSurveillanceCameraList(cameraIService.findAreaCarCamerasWithExtends(areaCode, extendDistance));
    }

    public boolean isCameraInArea(double lng, double lat, String areaCode) {
        return cameraIService.isCameraInArea(lng, lat, areaCode);
    }

    public void upsertBatch(List<SurveillanceCamera> cameraList) {
        if(CollectionUtils.isEmpty(cameraList)) {
            return;
        }

        cameraIService.upsertBatch(serviceConvert.toSurveillanceCameraDOList(cameraList));
    }
}