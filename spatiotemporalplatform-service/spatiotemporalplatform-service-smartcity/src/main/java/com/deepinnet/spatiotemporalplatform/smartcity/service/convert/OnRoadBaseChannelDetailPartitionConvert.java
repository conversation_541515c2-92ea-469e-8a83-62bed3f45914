package com.deepinnet.spatiotemporalplatform.smartcity.service.convert;

import com.deepinnet.spatiotemporalplatform.smartcity.service.dto.OnRoadBaseChannelDetailPartition;
import com.deepinnet.spatiotemporalplatform.base.convert.Convert;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.OnRoadBaseChannelDetailPartitionDO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * AreaConvert
 * Author: chenkaiyang
 * Date: 2024/7/31
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface OnRoadBaseChannelDetailPartitionConvert extends Convert<OnRoadBaseChannelDetailPartitionDO, OnRoadBaseChannelDetailPartition> {

}
