package com.deepinnet.spatiotemporalplatform.smartcity.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.AreaDomainService;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.SurveillanceCameraRepository;
import com.deepinnet.spatiotemporalplatform.model.area.AreaCondition;
import com.deepinnet.spatiotemporalplatform.model.area.AreaDTO;
import com.deepinnet.spatiotemporalplatform.model.camera.SurveillanceCamera;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 卡口过车明细统计任务
 *
 * <AUTHOR>
 * @version 2024-09-28
 */
@Component
@Slf4j
@Profile({"qz-gov-inner-network"})
public class CameraPassCarDetailStatisticsTask extends AbstractCarPassStatisticsTask {

    private static final Integer FIRST_PAGE_NO = 1;
    private static final Integer PAGE_SIZE = 1000;
    @Resource
    private SurveillanceCameraRepository surveillanceCameraRepository;

    @Resource
    private AreaDomainService areaDomainService;

    /**
     * 定时统计每个卡口过车明细
     */
    @Scheduled(fixedRate = 5, timeUnit = TimeUnit.MINUTES)
    public void statistics() {
        AreaCondition areaCondition = new AreaCondition();
        areaCondition.setPageNum(FIRST_PAGE_NO);
        areaCondition.setPageSize(PAGE_SIZE);

        CommonPage<AreaDTO> areaDTOCommonPage = areaDomainService.pageArea(areaCondition);

        if (ObjectUtil.isNull(areaDTOCommonPage) || CollUtil.isEmpty(areaDTOCommonPage.getList())) {
            LogUtil.warn("未查询到区域信息, 查询参数:{}", JSONUtil.toJsonStr(areaDTOCommonPage));
            return;
        }

        areaDTOCommonPage.getList().forEach(area -> {
            List<SurveillanceCamera> cameraListAll = surveillanceCameraRepository.findAreaCarCamerasWithExtends(area.getAreaCode(), AREA_EXTENDS_DISTANCE);

            // 摄像头太多，拆分成多任务
            List<List<SurveillanceCamera>> cameraListList = Lists.partition(cameraListAll, PARTITION_SIZE);

            cameraListList.forEach(cameraList -> {
                long startTime = System.currentTimeMillis();
                List<String> cameraCodeList = doStatistics(area.getAreaCode(), cameraList);
                long endTime = System.currentTimeMillis();
                log.info("car pass detail query finish,areaCode:{}, channelCodes size:{}, costTime:{}ms", area.getAreaCode(), cameraCodeList.size(), endTime - startTime);
            });

        });
    }
}
