package com.deepinnet.spatiotemporalplatform.smartcity.service.enums;

import cn.hutool.core.util.EnumUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <PERSON><PERSON> z<PERSON>
 * Date 2025-08-22
 * 车牌备案状态
 **/

@Getter
@AllArgsConstructor
public enum PlateRecordFiledStatus {

    RECORD_FILED("RECORD_FILED", "已备案"),
    RECORD_FILED_QUERY_FAILED("RECORD_FILED_QUERY_FAILED", "备案查询失败"),
    RECORD_FILED_QUERY_FAILED_OVER_LIMIT("RECORD_FILED_QUERY_FAILED_OVER_LIMIT", "备案查询失败超过限制"),
    NOT_RECORD_FILED("NOT_RECORD_FILED", "未备案")

    ;
    private final String code;

    private final String desc;


    public static PlateRecordFiledStatus fromCode(String code) {
        return EnumUtil.getBy(PlateRecordFiledStatus.class, c -> StringUtils.equals(c.getCode(), code));
    }
}
