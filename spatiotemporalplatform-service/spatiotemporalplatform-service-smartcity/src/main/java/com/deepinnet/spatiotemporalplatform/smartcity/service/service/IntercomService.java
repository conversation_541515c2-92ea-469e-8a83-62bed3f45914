package com.deepinnet.spatiotemporalplatform.smartcity.service.service;

import com.deepinnet.spatiotemporalplatform.model.intercom.IntercomPointRecord;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;

import java.util.List;

/**
 * Description: 对讲机服务
 * Date: 2024/11/7
 * Author: lijunheng
 */
public interface IntercomService {

    /**
     * 获取区域内最近规定时间内在线的对讲机，单位分钟
     * @param areaCode
     * @return
     */
    List<IntercomPointRecord> getIntercomPointList(String areaCode, Integer interval);


    List<IntercomPointRecord> queryNearIntercomPoint(Point point, double distance);


    List<IntercomPointRecord> queryPolygonContainsWithIn5Min(String areaWkt);
}
