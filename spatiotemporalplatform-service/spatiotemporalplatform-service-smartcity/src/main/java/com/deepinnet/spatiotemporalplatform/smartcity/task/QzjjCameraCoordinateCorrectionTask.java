package com.deepinnet.spatiotemporalplatform.smartcity.task;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepinnet.localdata.integration.model.outsidebean.CameraTypeEnum;
import com.deepinnet.localdata.integration.model.outsidebean.DeviceStatusEnum;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.impl.DhDevViewIServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.DhDevViewDO;
import com.deepinnet.spatiotemporalplatform.model.camera.SurveillanceCamera;
import com.deepinnet.spatiotemporalplatform.model.common.GeoPoint;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.SurveillanceCameraRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Creator zengjuerui
 * Date 2025-04-01
 **/

@Component
@Slf4j
@Profile({"qz-gov-inner-network", "local", "local-inner"})
public class QzjjCameraCoordinateCorrectionTask {

    @Resource
    private DhDevViewIServiceImpl dhDevViewIService;
    @Resource
    private SurveillanceCameraRepository surveillanceCameraRepository;

    /**
     * 每天一点
     * */
    @Scheduled(cron = "0 0 1 * * ? ")
    public void correct() {

        log.info("QzjjCameraCoordinateCorrectionTask staring ................................");

        StopWatch totalStopWatch = new StopWatch("衢州交警摄像头坐标校正执行记录");
        totalStopWatch.start();

        int totalProcessed = 0;
        int totalSkipped = 0;

        IPage<DhDevViewDO> page = new Page<>(0, 1_000L);

        while (CollectionUtils.isNotEmpty((page = dhDevViewIService.page(page)).getRecords())) {

            StopWatch loopStopWatch = new StopWatch("执行循环");
            loopStopWatch.start("处理批次");

            List<DhDevViewDO> list = page.getRecords();
            int batchProcessed = 0;
            int batchSkipped = 0;

            List<SurveillanceCamera> surveillanceCameras = new ArrayList<>();
            for (DhDevViewDO dhDevViewDO : list) {
                if(StringUtils.isAnyBlank(dhDevViewDO.getGpsX(), dhDevViewDO.getGpsY(), dhDevViewDO.getDevSn())) {
                    log.info("DhDevViewDO has no gps info or sn {}", dhDevViewDO);
                    batchSkipped++;
                    continue;
                }

                SurveillanceCamera camera = new SurveillanceCamera();
                camera.setCameraCode(dhDevViewDO.getDevSn());
                camera.setLng(dhDevViewDO.getGpsX());
                camera.setLat(dhDevViewDO.getGpsY());
                camera.setCoordinates(new GeoPoint(dhDevViewDO.getGpsX(), dhDevViewDO.getGpsY()).toWkt());
                camera.setType(CameraTypeEnum.VIDEO.getCode());
                camera.setName(dhDevViewDO.getDeviceName());
                camera.setStatus(DeviceStatusEnum.ONLINE.getStatus());
                surveillanceCameras.add(camera);
                batchProcessed++;
            }

            surveillanceCameraRepository.upsertBatch(surveillanceCameras);
            loopStopWatch.stop();

            totalProcessed += batchProcessed;
            totalSkipped += batchSkipped;

            page = new Page<>(page.getCurrent() + 1, page.getSize());

            log.info("衢州交警摄像头坐标校正批次处理明细:\n{}", loopStopWatch.prettyPrint());
        }

        totalStopWatch.stop();

        log.info("衢州交警摄像头坐标校正总计处理数量: {}, 总计跳过数: {}, 总耗时: {}ms",
                totalProcessed, totalSkipped, totalStopWatch.getTotalTimeMillis());
        log.info("衢州交警摄像头坐标校正总计处理明细:\n{}", totalStopWatch.prettyPrint());
    }
}
