package com.deepinnet.spatiotemporalplatform.smartcity.listener;

import com.deepinnet.spatiotemporalplatform.smartcity.service.dto.OnRoadBaseChannelDetail;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * C<PERSON> zeng<PERSON>
 * Date 2025-08-15
 **/

@Getter
public class OnRoadBaseChannelDetailEvent extends ApplicationEvent {

    private final List<OnRoadBaseChannelDetail> detailDOList;

    public OnRoadBaseChannelDetailEvent(List<OnRoadBaseChannelDetail> detailDOList) {
        super(detailDOList);
        this.detailDOList = detailDOList;
    }
}
