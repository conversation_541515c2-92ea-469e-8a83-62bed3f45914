package com.deepinnet.spatiotemporalplatform.smartcity.service.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.spatiotemporalplatform.smartcity.service.convert.ParkingLotConvert;
import com.deepinnet.spatiotemporalplatform.model.parkinglot.AreaParkingLotSummaryDTO;
import com.deepinnet.spatiotemporalplatform.model.parkinglot.ParkingLotDTO;
import com.deepinnet.spatiotemporalplatform.smartcity.service.error.BizErrorCode;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.ParkingLotDO;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.ParkingLotService;
import com.deepinnet.spatiotemporalplatform.model.area.AreaDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ParkingLotDomainService {

    @Resource
    private ParkingLotService parkingLotService;
    @Resource
    private ParkingLotConvert parkingLotConvert;
    @Resource
    private AreaDomainService areaDomainService;

    public AreaParkingLotSummaryDTO getParkingLotsByType(@NotNull String areaCode, @NotNull String type) {
        // 通过区域code查询区域车流区域位置信息
        AreaDTO area = areaDomainService.getAreaByCode(areaCode);
        if (area == null) {
            throw new BizException(BizErrorCode.AREA_NOT_FOUND.getCode(), BizErrorCode.AREA_NOT_FOUND.getDesc());
        }

        // 车流区域wkt
        String geoBig = area.getGeoBig();
        Assert.notEmpty(geoBig, "车流区域wkt不可为空");

        // 根据车流区域wkt和类型查询车流区域下的停车场
        List<ParkingLotDO> parkingLotDOS = parkingLotService.listByGeoAndType(geoBig, type);
        List<ParkingLotDTO> parkingLotDTOS = parkingLotConvert.toDTOS(parkingLotDOS);
        if (CollectionUtil.isEmpty(parkingLotDTOS)) {
            return null;
        }
        //排序=饱和度倒序
        parkingLotDTOS.stream().forEach(parkingLotDTO -> {
            parkingLotDTO.setSaturation(parkingLotDTO.calculateSaturation());
        });
        parkingLotDTOS = parkingLotDTOS.stream().sorted(Comparator.comparingDouble(ParkingLotDTO::getSaturation).reversed()).collect(Collectors.toList());
        AreaParkingLotSummaryDTO summaryDTO = AreaParkingLotSummaryDTO.builder().parkingLotList(parkingLotDTOS).build();
        summaryDTO.buildSaturation();
        summaryDTO.setSize(parkingLotDTOS.size());
        summaryDTO.setAreaCode(areaCode);
        return summaryDTO;
    }

    public ParkingLotDTO create(ParkingLotDTO parkingLotDTO) {
        parkingLotDTO.setId(null);
        // 保存
        parkingLotService.save(parkingLotConvert.toDO(parkingLotDTO));

        // 查询
        return parkingLotConvert.toDTO(parkingLotService.getById(parkingLotDTO.getId()));
    }
}