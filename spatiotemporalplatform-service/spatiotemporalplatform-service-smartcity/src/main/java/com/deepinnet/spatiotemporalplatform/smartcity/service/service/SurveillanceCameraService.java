package com.deepinnet.spatiotemporalplatform.smartcity.service.service;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.localdata.integration.model.input.MonitorPlaybackQueryDTO;
import com.deepinnet.localdata.integration.model.input.MonitorRecordQueryDTO;
import com.deepinnet.localdata.integration.model.input.RealMonitorQueryDTO;
import com.deepinnet.localdata.integration.model.output.MonitorRecordResponseDTO;
import com.deepinnet.spatiotemporalplatform.model.camera.KeyMonitorQueryDTO;
import com.deepinnet.spatiotemporalplatform.model.camera.MarkKeyMonitorDTO;
import com.deepinnet.spatiotemporalplatform.model.camera.SurveillanceCamera;
import com.deepinnet.spatiotemporalplatform.model.camera.SurveillanceCameraCondition;

import java.io.InputStream;
import java.util.List;

/**
 * Creator zengjuerui
 * Date 2024-08-03
 **/
public interface SurveillanceCameraService {

    CommonPage<SurveillanceCamera> listPage(SurveillanceCameraCondition condition);

    SurveillanceCamera getByCodeAndType(String code, String type);

    List<SurveillanceCamera> listByAreaCodeAndType(String areaCode, String type);

    List<SurveillanceCamera> queryAreaNearCamera(SurveillanceCameraCondition condition);

    /**
     * 标记重点摄像头
     * @param markKeyMonitorDTO
     */
    void markKeyMonitor(MarkKeyMonitorDTO markKeyMonitorDTO);

    /**
     * 获取区域内的重点摄像头原数据信息
     *
     * @param keyMonitorQueryDTO
     * @return
     */
    List<SurveillanceCamera> getKeyMonitorInfo(KeyMonitorQueryDTO keyMonitorQueryDTO);

    void updateKeyMonitor(MarkKeyMonitorDTO markKeyMonitorDTO);

    void deleteKeyMonitor(MarkKeyMonitorDTO markKeyMonitorDTO);

    String getRealMonitorUrlByCode(RealMonitorQueryDTO queryDTO);

    /**
     * 获取回放视频地址
     *
     * @return
     */
    String getPlaybackUrl(MonitorPlaybackQueryDTO queryDTO);

    MonitorRecordResponseDTO getRecord(MonitorRecordQueryDTO queryDTO);

    void importCamera(InputStream file);

    void saveBatch(List<SurveillanceCamera> cameraList);
}
