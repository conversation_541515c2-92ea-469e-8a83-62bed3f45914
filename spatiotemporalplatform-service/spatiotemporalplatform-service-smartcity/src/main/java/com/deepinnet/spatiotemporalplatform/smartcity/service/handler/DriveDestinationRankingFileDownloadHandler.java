package com.deepinnet.spatiotemporalplatform.smartcity.service.handler;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.model.hotod.DriverDestinationRanking;
import com.deepinnet.spatiotemporalplatform.model.hotod.DriverDestinationRankingRequest;
import com.deepinnet.spatiotemporalplatform.model.hotod.DriverDestinationRankingUrlParams;
import com.deepinnet.spatiotemporalplatform.base.enums.BizEnum;
import com.deepinnet.spatiotemporalplatform.base.ftp.FtpUtil;
import com.deepinnet.spatiotemporalplatform.base.ftp.AbstractFtpDownloadHandler;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.time.LocalDateTime;

/**
 * <p>
 *    目的地排行榜
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/14
 */

@Component
public class DriveDestinationRankingFileDownloadHandler extends AbstractFtpDownloadHandler<DriverDestinationRankingRequest> {

    @Override
    public BizEnum getBizEnum() {
        return BizEnum.DRIVE_DESTINATION_RANKING;
    }

    @Override
    protected String buildFilePath(DriverDestinationRankingRequest request) {

        DriverDestinationRankingUrlParams params = request.getUrlParams();

        return getBizEnum().getCode() + "_" + params.getAdcode() + "_" + LocalDateTime.now().toLocalDate().toString();
    }

    @Override
    protected String resolveFileName(String filePath, DriverDestinationRankingRequest request) {
        return FtpUtil.getLastFileByPath(filePath);
    }

    @Override
    protected Type getResponseType() {
        return new TypeReference<DriverDestinationRanking>() {}.getType();
    }
}
