package com.deepinnet.spatiotemporalplatform.smartcity.service.handler;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.base.enums.BizEnum;
import com.deepinnet.spatiotemporalplatform.base.ftp.AbstractFileDownloadHandler;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.GridRealTimePeopleFlowData;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.GridRealTimePeopleFlowRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;

/**
 * <p>
 * 实时网格人流 下载处理器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/14
 */
@Slf4j
@Component
public class RealTimeGridPeopleFlowDownloadHandler extends AbstractFileDownloadHandler<GridRealTimePeopleFlowRequest> {
    public static final String GRID_PEOPLE_FLOW_PATH = "grid_people_flow";
    @Override
    public BizEnum getBizEnum() {
        return BizEnum.REAL_TIME_GRID_PEOPLE_FLOW;
    }

    @Override
    public String buildFilePath(GridRealTimePeopleFlowRequest request) {
        String areaCode = request.getUrlParams().getCustomAreaId();
        return GRID_PEOPLE_FLOW_PATH + "_" + areaCode;
    }

    @Override
    protected Type getResponseType() {
        return new TypeReference<GridRealTimePeopleFlowData>() {
        }.getType();
    }
}
