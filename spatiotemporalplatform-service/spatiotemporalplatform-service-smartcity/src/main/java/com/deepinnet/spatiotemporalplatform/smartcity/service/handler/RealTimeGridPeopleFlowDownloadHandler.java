package com.deepinnet.spatiotemporalplatform.smartcity.service.handler;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.GridRealTimePeopleFlowData;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.GridRealTimePeopleFlowParams;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.GridRealTimePeopleFlowRequest;
import com.deepinnet.spatiotemporalplatform.base.enums.BizEnum;
import com.deepinnet.spatiotemporalplatform.base.ftp.FtpUtil;
import com.deepinnet.spatiotemporalplatform.base.ftp.AbstractFtpDownloadHandler;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.time.LocalDateTime;

/**
 * <p>
 *    实时网格人流 下载处理器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/14
 */

@Component
public class RealTimeGridPeopleFlowDownloadHandler extends AbstractFtpDownloadHandler<GridRealTimePeopleFlowRequest> {

    @Override
    public BizEnum getBizEnum() {
        return BizEnum.REAL_TIME_GRID_PEOPLE_FLOW;
    }

    @Override
    protected String buildFilePath(GridRealTimePeopleFlowRequest request) {
        GridRealTimePeopleFlowParams params = request.getUrlParams();
        return getBizEnum().getCode() + "_" + params.getCustomAreaId() + "_" + LocalDateTime.now().toLocalDate().toString();
    }

    @Override
    protected String resolveFileName(String filePath, GridRealTimePeopleFlowRequest request) {
        return FtpUtil.getLastFileByPath(filePath);
    }

    @Override
    protected Type getResponseType() {
        return new TypeReference<GridRealTimePeopleFlowData>() {}.getType();
    }
}
