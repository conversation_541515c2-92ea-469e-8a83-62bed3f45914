package com.deepinnet.spatiotemporalplatform.smartcity.service.service.impl;

import com.deepinnet.spatiotemporalplatform.smartcity.service.convert.CommonBaseServiceConvert;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.PoliceDeploymentRepository;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.PoliceDeploymentService;
import com.deepinnet.spatiotemporalplatform.model.police.PoliceDeployment;
import com.deepinnet.spatiotemporalplatform.model.police.PoliceDeploymentDTO;
import org.apache.cxf.common.util.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * PoliceDeploymentServiceImpl
 * Author: chenkaiyang
 * Date: 2024/8/5
 */
@Service
public class PoliceDeploymentServiceImpl implements PoliceDeploymentService {
    @Resource
    private PoliceDeploymentRepository policeDeploymentRepository;
    @Resource
    private CommonBaseServiceConvert commonBaseServiceConvert;

    @Override
    public List<PoliceDeployment> listByObjCode(String objCode) {
        return policeDeploymentRepository.listByObjCode(objCode);
    }

    @Override
    public List<PoliceDeployment> listByObjCodeList(List<String> objCodeList) {
        return policeDeploymentRepository.listByObjCodeList(objCodeList);
    }

    @Override
    public void deleteByObjCode(String objCode) {
        policeDeploymentRepository.deleteByObjCode(objCode);
    }

    @Override
    public void batchSave(List<PoliceDeploymentDTO> policeDeploymentList) {
        policeDeploymentRepository.batchSave(commonBaseServiceConvert.fromDTOtoPoliceDeploymentList(policeDeploymentList));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void assignToObject(List<PoliceDeployment> policeDeploymentList, String objCode) {
        policeDeploymentRepository.deleteByObjCode(objCode);

        if (CollectionUtils.isEmpty(policeDeploymentList)) {
            return;
        }

        policeDeploymentRepository.batchSave(policeDeploymentList);
    }

    @Override
    public PoliceDeploymentDTO create(PoliceDeploymentDTO policeDeployment) {
        return policeDeploymentRepository.save(policeDeployment);
    }

    @Override
    public List<PoliceDeployment> list(String areaCode, String objectType, String objectCode) {
        return policeDeploymentRepository.list(areaCode, objectType, objectCode);
    }

    @Override
    public List<PoliceDeployment> listByAreaCodeAndPlanCodeList(String areaCode, List<String> contingencyPlanCodeList) {
        return policeDeploymentRepository.listByAreaCodeAndPlanCodeList(areaCode, contingencyPlanCodeList);
    }

    @Override
    public Boolean delete(Integer id) {
        return policeDeploymentRepository.delete(id);

    }

    @Override
    public void deleteByIds(List<Integer> batchDeleteList) {
        policeDeploymentRepository.deleteByIds(batchDeleteList);
    }

    @Override
    public void deleteByObjCodeAndType(String planCode, String type) {
        policeDeploymentRepository.deleteByObjCodeAndType(planCode, type);
    }

    @Override
    public List<PoliceDeployment> listRealTimeByAreaCodeAndObjectCode(String areaCode, String objectType, String objectCode) {
        // 获取当前区域下的警力部署
        List<PoliceDeployment> policeDeploymentList = policeDeploymentRepository.list(areaCode, objectType, objectCode);
        if (CollectionUtils.isEmpty(policeDeploymentList)) {
            return Collections.emptyList();
        }

        // 计算警力部署点位范围
        return policeDeploymentList;
    }

}
