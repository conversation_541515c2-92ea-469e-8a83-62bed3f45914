package com.deepinnet.spatiotemporalplatform.smartcity.service.convert;

import com.deepinnet.spatiotemporalplatform.dal.dataobject.SurveillanceCameraConditionDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.SurveillanceCameraDO;
import com.deepinnet.spatiotemporalplatform.model.camera.SurveillanceCamera;
import com.deepinnet.spatiotemporalplatform.model.camera.SurveillanceCameraCondition;
import com.deepinnet.spatiotemporalplatform.model.police.PoliceDeploymentDTO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.DeviceGpsModelDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.PoliceDeploymentDO;
import com.deepinnet.spatiotemporalplatform.model.gps.DeviceGpsModel;
import com.deepinnet.spatiotemporalplatform.model.police.PoliceDeployment;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * Creator zengjuerui
 * Date 2024-08-03
 **/


@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface CommonBaseServiceConvert {

    List<PoliceDeployment> toPoliceDeploymentList(List<PoliceDeploymentDO> list);

    @Mappings({
            @Mapping(target = "coordinates", expression = "java(com.deepinnet.spatiotemporalplatform.model.util.WktUtil.toWkt(policeDeploymentDO.getCoordinates()))"),
            @Mapping(target = "objectType", expression = "java(com.deepinnet.spatiotemporalplatform.common.enums.ObjectType.getByCode(policeDeploymentDO.getObjectType()))")
    })
    PoliceDeployment toPoliceDeployment(PoliceDeploymentDO policeDeploymentDO);


    @Mappings({
            @Mapping(target = "objectType", source = "objectType.code"),
            @Mapping(target = "coordinates", expression = "java(com.deepinnet.spatiotemporalplatform.model.util.WktUtil.toPoint(deployment.getCoordinates()))")
    })
    PoliceDeploymentDO toPoliceDeploymentDO(PoliceDeployment deployment);

    List<PoliceDeploymentDO> toPoliceDeploymentDOList(List<PoliceDeployment> list);

    @Mappings({
            @Mapping(target = "coordinates", expression = "java(com.deepinnet.spatiotemporalplatform.model.util.WktUtil.toWkt(policeDeploymentDO.getCoordinates()))")
    })
    PoliceDeploymentDTO toDTO(PoliceDeploymentDO policeDeploymentDO);

    @Mappings({
            @Mapping(target = "coordinates", expression = "java(com.deepinnet.spatiotemporalplatform.model.util.WktUtil.toPoint(policeDeployment.getCoordinates()))")
    })
    PoliceDeploymentDO toPoliceDeploymentDO(PoliceDeploymentDTO policeDeployment);

    DeviceGpsModelDO toDeviceGpsModelDO(DeviceGpsModel deviceGpsModel);

    DeviceGpsModel toDeviceGpsModel(DeviceGpsModelDO deviceGpsModel);


    @Mappings({
            @Mapping(target = "objectType", expression = "java(com.deepinnet.spatiotemporalplatform.common.enums.ObjectType.getByCode(policeDeploymentDTO.getObjectType()))")
    })
    PoliceDeployment toPoliceDeployment(PoliceDeploymentDTO policeDeploymentDTO);

    List<PoliceDeployment> fromDTOtoPoliceDeploymentList(List<PoliceDeploymentDTO> policeDeploymentList);


    @Mappings({
            @Mapping(target = "coordinates", expression = "java(com.deepinnet.spatiotemporalplatform.model.util.WktUtil.toWkt(surveillanceCameraDO.getCoordinates()))")
    })
    SurveillanceCamera toSurveillanceCamera(SurveillanceCameraDO surveillanceCameraDO);

    List<SurveillanceCamera> toSurveillanceCameraList(List<SurveillanceCameraDO> surveillanceCameraDOS);

    @Mappings({
            @Mapping(target = "coordinates", expression = "java(com.deepinnet.spatiotemporalplatform.model.util.WktUtil.toPoint(surveillanceCamera.getLng(),surveillanceCamera.getLat()))")
    })
    SurveillanceCameraDO toSurveillanceCameraDO(SurveillanceCamera surveillanceCamera);

    List<SurveillanceCameraDO> toSurveillanceCameraDOList(List<SurveillanceCamera> surveillanceCameras);


    SurveillanceCameraConditionDO condition2DO(SurveillanceCameraCondition condition);
  

    //DeviceVehicleVideoRelation toDeviceVehicleVideoRelation(DeviceVehicleVideoRelationDO deviceVehicleVideoRelationDO);

   // List<DeviceVehicleVideoRelation> toDeviceVehicleVideoRelationList(List<DeviceVehicleVideoRelationDO> deviceVehicleVideoRelationDOS);

}
