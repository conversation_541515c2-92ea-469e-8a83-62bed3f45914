package com.deepinnet.spatiotemporalplatform.smartcity.service.repository;

import com.deepinnet.spatiotemporalplatform.smartcity.service.dto.CameraCarPassDetail;
import com.deepinnet.spatiotemporalplatform.base.repository.AbstractRepository;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.CameraCarPassDetailDO;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.CameraCarPassDetailDaoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:
 * Date: 2024/8/5
 * Author: lijunheng
 */
@Service
public class CameraCarPassDetailRepository extends AbstractRepository<CameraCarPassDetailDO, CameraCarPassDetail> {
    @Resource
    private CameraCarPassDetailDaoService cameraCarPassDetailDaoService;

    public List<CameraCarPassDetail> queryLatestCarPassDetail(String areaCode, String currentHour) {
        List<CameraCarPassDetailDO> cameraCarPassDetails = cameraCarPassDetailDaoService.queryLatestCarPassDetail(areaCode, currentHour);
        return cameraCarPassDetails.stream().map(cameraCarPassDetailDO -> {
            CameraCarPassDetail cameraCarPassDetail = new CameraCarPassDetail();
            cameraCarPassDetail.setGmtCreated(cameraCarPassDetailDO.getGmtCreated());
            cameraCarPassDetail.setGmtModified(cameraCarPassDetailDO.getGmtModified());
            cameraCarPassDetail.setCameraCode(cameraCarPassDetailDO.getCameraCode());
            cameraCarPassDetail.setCurrentHour(cameraCarPassDetailDO.getCurrentHour());
            cameraCarPassDetail.setStartMinute(cameraCarPassDetailDO.getStartMinute());
            cameraCarPassDetail.setEndMinute(cameraCarPassDetailDO.getEndMinute());
            cameraCarPassDetail.setPlateNum(cameraCarPassDetailDO.getPlateNum());
            cameraCarPassDetail.setPassCarTime(cameraCarPassDetailDO.getPassCarTime());
            cameraCarPassDetail.setAreaCode(cameraCarPassDetailDO.getAreaCode());
            return cameraCarPassDetail;
        }).collect(Collectors.toList());
    }

}
