package com.deepinnet.spatiotemporalplatform.smartcity.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.deepinnet.spatiotemporalplatform.base.repository.AbstractRepository;
import com.deepinnet.spatiotemporalplatform.smartcity.service.enums.MonitorCameraType;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.SurveillanceCamerasMarkDO;
import com.deepinnet.spatiotemporalplatform.model.camera.KeyMonitorQueryDTO;
import com.deepinnet.spatiotemporalplatform.model.camera.MarkKeyMonitorDTO;
import com.deepinnet.spatiotemporalplatform.model.camera.SurveillanceCamerasMark;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Description:
 * Date: 2024/8/5
 * Author: lijunheng
 */
@Service
public class KeyMonitorRepository extends AbstractRepository<SurveillanceCamerasMarkDO, SurveillanceCamerasMark> {
    public List<SurveillanceCamerasMark> queryKeyMonitorList(KeyMonitorQueryDTO keyMonitorQueryDTO) {
        LambdaQueryWrapper<SurveillanceCamerasMarkDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SurveillanceCamerasMarkDO::getCameraType, MonitorCameraType.KEY.name())
                .eq(SurveillanceCamerasMarkDO::getAreaCode, keyMonitorQueryDTO.getAreaCode())
                .eq(StringUtils.isNotBlank(keyMonitorQueryDTO.getObjectCode()), SurveillanceCamerasMarkDO::getObjectCode, keyMonitorQueryDTO.getObjectCode());
        return list(queryWrapper);
    }

    public void deleteKeyMonitor(MarkKeyMonitorDTO markKeyMonitorDTO) {
        LambdaQueryWrapper<SurveillanceCamerasMarkDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SurveillanceCamerasMarkDO::getAreaCode, markKeyMonitorDTO.getAreaCode())
                .eq(StringUtils.isNotBlank(markKeyMonitorDTO.getObjectCode()), SurveillanceCamerasMarkDO::getObjectCode, markKeyMonitorDTO.getObjectCode())
                .in(CollectionUtils.isNotEmpty(markKeyMonitorDTO.getMonitorCodeList()), SurveillanceCamerasMarkDO::getCameraCode, markKeyMonitorDTO.getMonitorCodeList());
        remove(queryWrapper);
    }

    public void deleteByAreaCode(String areaCode, String objectCode) {
        LambdaQueryWrapper<SurveillanceCamerasMarkDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SurveillanceCamerasMarkDO::getAreaCode, areaCode)
                .eq(StringUtils.isNotBlank(objectCode), SurveillanceCamerasMarkDO::getObjectCode, objectCode);
        remove(queryWrapper);
    }
}
