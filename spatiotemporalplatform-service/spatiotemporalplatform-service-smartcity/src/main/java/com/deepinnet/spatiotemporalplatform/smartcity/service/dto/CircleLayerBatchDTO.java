package com.deepinnet.spatiotemporalplatform.smartcity.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * CircleLayerBatchDTO
 * Author: chenkaiyang
 * Date: 2024/9/11
 */
@Data
public class CircleLayerBatchDTO {
    @ApiModelProperty(value = "区域code")
    private String areaCode;
    @ApiModelProperty(value = "圈层列表")
    private List<CircleLayerDTO> circleLayers;
}
