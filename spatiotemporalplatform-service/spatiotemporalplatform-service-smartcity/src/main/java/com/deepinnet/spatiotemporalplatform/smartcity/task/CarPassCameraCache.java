package com.deepinnet.spatiotemporalplatform.smartcity.task;

import cn.hutool.core.map.MapUtil;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;

/**
 * CarPassCameraCache
 * Author: chenkaiyang
 * Date: 2025/3/04
 */
@Component
@Data
public class CarPassCameraCache {
    /**
     * key: areaCode
     * value: cameraCode -> inArea
     */
    private ConcurrentHashMap<String, ConcurrentHashMap<String, Integer>> areaCode2CameraCode2InAreaMap= MapUtil.newConcurrentHashMap();

    /**
     * 根据区域code和摄像头code获取是否在区域内
     * @param areaCode
     * @param cameraCode
     * @return
     */
    public Integer getByAreaCodeAndCameraCode(String areaCode, String cameraCode) {
        ConcurrentHashMap<String, Integer> cameraCode2InAreaMap = areaCode2CameraCode2InAreaMap.get(areaCode);
        if (cameraCode2InAreaMap == null) {
            return null;
        }
        return cameraCode2InAreaMap.get(cameraCode);
    }


    /**
     * 缓存区域code和摄像头code是否在区域内
     * @param areaCode
     * @param channelCode
     * @param i
     */
    public void putAreaCode2CameraCodeCache(String areaCode, String channelCode, Integer i) {
        ConcurrentHashMap<String, Integer> cameraCode2InAreaMap = areaCode2CameraCode2InAreaMap.computeIfAbsent(areaCode, k -> MapUtil.newConcurrentHashMap());
        cameraCode2InAreaMap.put(channelCode, i);
    }
}
