package com.deepinnet.spatiotemporalplatform.smartcity.service.handler;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.base.enums.BizEnum;
import com.deepinnet.spatiotemporalplatform.base.ftp.AbstractFileDownloadHandler;
import com.deepinnet.spatiotemporalplatform.model.traffic.event.TrafficEvent;
import com.deepinnet.spatiotemporalplatform.model.traffic.event.TrafficEventRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.util.List;

/**
 * <p>
 * 交通事件 下载处理器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/14
 */
@Slf4j
@Component
public class TrafficEventDownloadHandler extends AbstractFileDownloadHandler<TrafficEventRequest> {
    public static final String TRAFFIC_EVENT_PATH = "traffic_event";
    @Override
    public String buildFilePath(TrafficEventRequest request) {
        return TRAFFIC_EVENT_PATH;
    }

    @Override
    public BizEnum getBizEnum() {
        return BizEnum.TRAFFIC_EVENT;
    }

    @Override
    protected Type getResponseType() {
        return new TypeReference<List<TrafficEvent>>() {
        }.getType();
    }
}
