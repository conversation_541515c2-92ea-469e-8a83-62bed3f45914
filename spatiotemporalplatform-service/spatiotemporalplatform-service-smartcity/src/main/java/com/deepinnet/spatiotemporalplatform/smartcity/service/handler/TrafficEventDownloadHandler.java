package com.deepinnet.spatiotemporalplatform.smartcity.service.handler;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.model.traffic.event.TrafficEvent;
import com.deepinnet.spatiotemporalplatform.model.traffic.event.TrafficEventRequest;
import com.deepinnet.spatiotemporalplatform.model.traffic.event.TrafficEventUrlParams;
import com.deepinnet.spatiotemporalplatform.base.enums.BizEnum;
import com.deepinnet.spatiotemporalplatform.base.ftp.FtpUtil;
import com.deepinnet.spatiotemporalplatform.base.ftp.AbstractFtpDownloadHandler;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *    交通事件 下载处理器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/14
 */

@Component
public class TrafficEventDownloadHandler extends AbstractFtpDownloadHandler<TrafficEventRequest> {

    @Override
    public BizEnum getBizEnum() {
        return BizEnum.TRAFFIC_EVENT;
    }

    @Override
    protected String buildFilePath(TrafficEventRequest request) {

        TrafficEventUrlParams params = request.getUrlParams();

        return getBizEnum().getCode() + "_" + params.getAdcode() + "_" + LocalDateTime.now().toLocalDate().toString();
    }

    @Override
    protected String resolveFileName(String filePath, TrafficEventRequest request) {
        return FtpUtil.getLastFileByPath(filePath);
    }

    @Override
    protected Type getResponseType() {
        return new TypeReference<List<TrafficEvent>>() {}.getType();
    }
}
