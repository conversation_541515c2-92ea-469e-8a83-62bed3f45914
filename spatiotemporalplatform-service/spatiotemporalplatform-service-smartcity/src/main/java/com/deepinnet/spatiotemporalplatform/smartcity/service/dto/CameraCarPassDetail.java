package com.deepinnet.spatiotemporalplatform.smartcity.service.dto;

import lombok.Data;

import java.util.Date;

/**
 * Description:
 * Date: 2024/9/28
 * Author: lijunheng
 */
@Data
public class CameraCarPassDetail {
    /**
     * id
     */
    private Long id;

    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 摄像头编号
     */
    private String cameraCode;

    /**
     * 当前小时（yyyyMMddHH）
     */
    private String currentHour;

    /**
     * 开始分钟
     */
    private String startMinute;

    /**
     * 结束分钟
     */
    private String endMinute;

    /**
     * 车牌号
     */
    private String plateNum;

    /**
     * 过车时间
     */
    private Date passCarTime;
}