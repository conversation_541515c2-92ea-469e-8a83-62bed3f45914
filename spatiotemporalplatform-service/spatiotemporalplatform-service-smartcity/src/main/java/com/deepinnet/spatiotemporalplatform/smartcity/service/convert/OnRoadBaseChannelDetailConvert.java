package com.deepinnet.spatiotemporalplatform.smartcity.service.convert;

import com.deepinnet.spatiotemporalplatform.smartcity.service.dto.OnRoadBaseChannelDetail;
import com.deepinnet.spatiotemporalplatform.base.convert.Convert;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.OnRoadBaseChannelDetailDO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * AreaConvert
 * Author: chenkaiyang
 * Date: 2024/7/31
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface OnRoadBaseChannelDetailConvert extends Convert<OnRoadBaseChannelDetailDO, OnRoadBaseChannelDetail> {

}
