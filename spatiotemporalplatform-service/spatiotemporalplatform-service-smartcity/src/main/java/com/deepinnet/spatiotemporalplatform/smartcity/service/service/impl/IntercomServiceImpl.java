package com.deepinnet.spatiotemporalplatform.smartcity.service.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.deepinnet.spatiotemporalplatform.model.intercom.IntercomPointRecord;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.IntercomPointRecordRepository;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.IntercomService;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * Description:
 * Date: 2024/11/7
 * Author: lijunheng
 */
@Service
public class IntercomServiceImpl implements IntercomService {

    @Resource
    private IntercomPointRecordRepository intercomPointRecordRepository;

    @Override
    public List<IntercomPointRecord> getIntercomPointList(String areaCode, Integer interval) {
        DateTime lastMinTime = DateUtil.offsetMinute(new Date(), -interval);
        return intercomPointRecordRepository.findByAreaCodeAndTime(areaCode, lastMinTime);
    }


    @Override
    public List<IntercomPointRecord> queryNearIntercomPoint(Point point, double distance) {
        return intercomPointRecordRepository.queryNearIntercomPoint(point, distance);
    }

    @Override
    public List<IntercomPointRecord> queryPolygonContainsWithIn5Min(String areaWkt) {
        DateTime last5MinTime = DateUtil.offsetMinute(new Date(), -5);
        return intercomPointRecordRepository.queryPolygonContainsAndBeforeTime(areaWkt, last5MinTime);
    }
}
