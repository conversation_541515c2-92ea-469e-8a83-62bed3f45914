package com.deepinnet.spatiotemporalplatform.smartcity.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.spatiotemporalplatform.model.hotod.AreaArriveCarPredictRequest;
import com.deepinnet.spatiotemporalplatform.model.hotod.AreaArriveCarPredictUrlParams;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**

 *
 * <AUTHOR>
 * @since 2024-11-25 星期一
 **/
@Component
@Profile({"local", "test"})
public class AreaArriveCarPredictTask {

    @Resource
    private CommonDataService commonDataService;

    @Resource
    private AreaProperties areaProperties;

    @Scheduled(cron = "0 */10 * * * *")
    public void areaArriveCarPredict() {
        Map<String, String> map = areaProperties.getMap();
        if (MapUtil.isEmpty(map)) {
            LogUtil.warn("未配置区域编码，跳过任务");
        }
        map.forEach((adcode, areaIds) -> {
            List<String> areaIdList = StrUtil.split(areaIds, ",");
            if (CollectionUtil.isEmpty(areaIdList)) {
                LogUtil.warn("未配置区域ID，跳过任务");
            }
            AreaArriveCarPredictRequest areaArriveCarPredictRequest = new AreaArriveCarPredictRequest();
            AreaArriveCarPredictUrlParams areaArriveCarPredictUrlParams = new AreaArriveCarPredictUrlParams();
            areaArriveCarPredictUrlParams.setAdcode(adcode);
            areaArriveCarPredictUrlParams.setType("0");
            areaArriveCarPredictUrlParams.setIds(areaIds);
            areaArriveCarPredictRequest.setUrlParams(areaArriveCarPredictUrlParams);
            commonDataService.fetchData(areaArriveCarPredictRequest);
        });
    }

}
