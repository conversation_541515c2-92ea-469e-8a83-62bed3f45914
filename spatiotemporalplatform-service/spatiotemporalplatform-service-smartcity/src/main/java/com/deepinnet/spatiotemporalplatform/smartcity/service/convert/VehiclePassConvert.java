package com.deepinnet.spatiotemporalplatform.smartcity.service.convert;

import com.deepinnet.localdata.integration.model.outsidebean.VehiclePassResponse;
import com.deepinnet.spatiotemporalplatform.base.convert.Convert;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.VehiclePassRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * VehiclePassConvert
 * Author: chenkaiyang
 * Date: 2025/1/23
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface VehiclePassConvert extends Convert<VehiclePassRecordDO, VehiclePassResponse> {

}
