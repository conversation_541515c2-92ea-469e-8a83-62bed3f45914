package com.deepinnet.spatiotemporalplatform.smartcity.service.client;

/**
 * CustomAreaClient
 * Author: chenkaiyang
 * Date: 2024/9/5
 */

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.area.AreaDTO;
import com.deepinnet.spatiotemporalplatform.model.reggov.create.CustomAreaCreatePostBody;
import com.deepinnet.spatiotemporalplatform.model.reggov.create.CustomAreaCreateRequest;
import com.deepinnet.spatiotemporalplatform.model.reggov.create.CustomAreaCreateUrlParams;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

@Component
public class CustomAreaClient {


    /**
     * 动态计算指标类型，多个进⾏组合加和
     * 1.实时⼈流 2.实时⻋流流⼊通道 8.历史⼈流 16.⼈流流⼊
     * 通道
     * 例如同⼀区域只创建实时⼈流，传参为1；
     * 同⼀区域同时创建实时⼈流+历史⼈流，传参为9
     * （1+8）；
     */
    @Value("${gaode.area.dynamicCalType:105}")
    private Integer dynamicCalType;
    @Resource
    private CommonDataService commonDataService;

    @Value("${city.adcode}")
    private String AD_CODE;

    @Value("${digest.sign.api.key-temp}")
    private String digestSignApikeyTemp;
    public String createCustomArea(AreaDTO areaDTO) {
        String areaName = areaDTO.getAreaName();
        String geo = areaDTO.getGeo();
        String geoBig = areaDTO.getGeoBig();

        CustomAreaCreatePostBody customAreaCreatePostBody = new CustomAreaCreatePostBody();
        customAreaCreatePostBody.setName(areaName);
        customAreaCreatePostBody.setGeo(geo);
        customAreaCreatePostBody.setGeoBig(geoBig);
        customAreaCreatePostBody.setDynamicCalType(dynamicCalType);
        customAreaCreatePostBody.setAdcode(AD_CODE);

        String lastYearDay = DateUtil.format(DateUtils.addYears(new Date(), -1), "yyyy-MM-dd");
        String yesterDay = DateUtil.format(DateUtils.addDays(new Date(), -1), "yyyy-MM-dd");

        customAreaCreatePostBody.setPeopleFlowHistoryStartDate(lastYearDay);
        customAreaCreatePostBody.setPeopleFlowHistoryEndDate(yesterDay);

        CustomAreaCreateRequest request = new CustomAreaCreateRequest();
        customAreaCreatePostBody.setUserKey(digestSignApikeyTemp);
        request.setPostBody(customAreaCreatePostBody);
        CustomAreaCreateUrlParams customAreaCreateUrlParams = new CustomAreaCreateUrlParams();
        request.setUrlParams(customAreaCreateUrlParams);
        Result<String> trafficEventsResult = Result.success((String) commonDataService.fetchData(request));

        LogUtil.info("feign客户端调用结果:" + JSONUtil.toJsonPrettyStr(trafficEventsResult));
        LogUtil.info(trafficEventsResult.getData());

        return trafficEventsResult.getData();
    }

}
