package com.deepinnet.spatiotemporalplatform.smartcity.service.convert;

import com.deepinnet.spatiotemporalplatform.base.lock.LockObject;
import com.deepinnet.spatiotemporalplatform.base.convert.Convert;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.LockObjectDO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * AreaConvert
 * Author: chenkaiyang
 * Date: 2024/7/31
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface LockObjectConvert extends Convert<LockObjectDO, LockObject> {

}
