package com.deepinnet.spatiotemporalplatform.smartcity.service.service;


import com.deepinnet.spatiotemporalplatform.model.police.PoliceDeploymentDTO;
import com.deepinnet.spatiotemporalplatform.model.police.PoliceDeployment;

import java.util.List;

/**
 * PoliceDeploymentService
 * Author: chenkaiyang
 * Date: 2024/8/5
 */
public interface PoliceDeploymentService {
    List<PoliceDeployment> listByObjCode(String objCode);

    List<PoliceDeployment> listByObjCodeList(List<String> objCodeList);

    void deleteByObjCode(String objCode);

    void batchSave(List<PoliceDeploymentDTO> policeDeploymentList);

    void assignToObject(List<PoliceDeployment> policeDeploymentList, String objCode);

    PoliceDeploymentDTO create(PoliceDeploymentDTO policeDeployment);

    List<PoliceDeployment> list(String areaCode, String objectType, String objectCode);

    List<PoliceDeployment> listByAreaCodeAndPlanCodeList(String areaCode, List<String> contingencyPlanCodeList);

    Boolean delete(Integer id);

    void deleteByIds(List<Integer> batchDeleteList);

    void deleteByObjCodeAndType(String planCode, String code);

    List<PoliceDeployment> listRealTimeByAreaCodeAndObjectCode(String areaCode, String objectType, String objectCode);
}
