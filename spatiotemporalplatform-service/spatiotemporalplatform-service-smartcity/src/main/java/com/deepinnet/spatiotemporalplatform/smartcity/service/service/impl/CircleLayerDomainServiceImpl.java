package com.deepinnet.spatiotemporalplatform.smartcity.service.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.spatiotemporalplatform.smartcity.service.convert.CircleLayerConvert;
import com.deepinnet.spatiotemporalplatform.model.area.CircleLayerBatchDTO;
import com.deepinnet.spatiotemporalplatform.model.area.CircleLayerDTO;
import com.deepinnet.spatiotemporalplatform.base.sequence.SequenceTypeEnum;
import com.deepinnet.spatiotemporalplatform.smartcity.service.error.BizErrorCode;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.CircleLayerDomainService;
import com.deepinnet.spatiotemporalplatform.base.sequence.SequenceUtil;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.CircleLayerDO;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.CircleLayerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class CircleLayerDomainServiceImpl implements CircleLayerDomainService {

    private final CircleLayerService circleLayerService;
    @Resource
    private CircleLayerConvert CircleLayerConverter;

    @Autowired
    public CircleLayerDomainServiceImpl(CircleLayerService circleLayerService) {
        this.circleLayerService = circleLayerService;
    }

    @Override
    public CircleLayerDTO createCircleLayer(CircleLayerDTO circleLayerDTO) {
        CircleLayerDO circleLayerDO = CircleLayerConverter.toDO(circleLayerDTO);
        circleLayerDO.setId(null);
        if (circleLayerService.save(circleLayerDO)) {
            return CircleLayerConverter.toDTO(circleLayerDO);
        }
        return null;
    }

    @Override
    public boolean deleteCircleLayer(String layerId) {
        QueryWrapper<CircleLayerDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("layer_id", layerId);
        return circleLayerService.remove(queryWrapper);
    }

    @Override
    public CircleLayerDTO getCircleLayerByLayerId(String layerId) {
        QueryWrapper<CircleLayerDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("layer_id", layerId);
        CircleLayerDO circleLayerDO = circleLayerService.getOne(queryWrapper);
        return CircleLayerConverter.toDTO(circleLayerDO);
    }

    @Override
    public List<CircleLayerDTO> getAllCircleLayers(int page, int size) {
        List<CircleLayerDO> circleLayerDOList = circleLayerService.page(new Page<>(page, size), Wrappers.lambdaQuery(CircleLayerDO.class).orderByDesc(CircleLayerDO::getGmtCreated)).getRecords();
        return CircleLayerConverter.toDTOList(circleLayerDOList);
    }

    @Override
    public List<CircleLayerDTO> listByAreaeCodeList(List<String> areaCodeList) {
        List<CircleLayerDO> circleLayerDOList = circleLayerService.listByAreaeCodeList(areaCodeList);
        return CircleLayerConverter.toDTOList(circleLayerDOList);
    }

    @Override
    @Transactional
    public List<CircleLayerDTO> batchCreateOrUpdateCircleLayer(CircleLayerBatchDTO circleLayerBatchDTO) {
        String areaCode = circleLayerBatchDTO.getAreaCode();
        Assert.notEmpty(circleLayerBatchDTO.getAreaCode(), "区域code不可为空");

        List<CircleLayerDTO> circleLayerDTOS = circleLayerBatchDTO.getCircleLayers();

        // 删除所有圈层
        circleLayerService.remove(new LambdaQueryWrapper<CircleLayerDO>().eq(CircleLayerDO::getAreaCode, areaCode));

        if (circleLayerDTOS == null || circleLayerDTOS.isEmpty()) {
            return Collections.emptyList();
        }

        // 新增圈层
        circleLayerDTOS.forEach(c -> c.setLayerId(SequenceUtil.getSequence(SequenceTypeEnum.CIRCLE_LAYER)));
        boolean batchCreateCircleLayer = circleLayerService.saveBatch(CircleLayerConverter.toDOList(circleLayerDTOS));
        if (!batchCreateCircleLayer) {
            throw new BizException(BizErrorCode.DATA_INVALID.getCode(), "批量创建圈层失败");
        }


        return CircleLayerConverter.toDTOList(circleLayerService.listByAreaeCodeList(circleLayerDTOS.stream().map(CircleLayerDTO::getAreaCode).collect(Collectors.toList())));
    }

    @Override
    public CircleLayerDTO updateCircleLayer(CircleLayerDTO circleLayerDTO) {
        Assert.notEmpty(circleLayerDTO.getLayerId(), "圈层id不可为空");

        QueryWrapper<CircleLayerDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("layer_id", circleLayerDTO.getLayerId());
        CircleLayerDO circleLayerDO = circleLayerService.getOne(queryWrapper);
        if (circleLayerDO == null) {
            throw new BizException(BizErrorCode.DATA_INVALID.getCode(), "圈层不存在");
        }
        // 更新圈层
        CircleLayerDO updateDO = CircleLayerConverter.toDO(circleLayerDTO);
        circleLayerService.update(updateDO, queryWrapper);

        // 查询圈层
        return CircleLayerConverter.toDTO(circleLayerService.getOne(queryWrapper));

    }
}
