package com.deepinnet.spatiotemporalplatform.smartcity.service.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.deepinnet.spatiotemporalplatform.smartcity.service.convert.DateToTimestampSerializer;
import com.deepinnet.spatiotemporalplatform.smartcity.service.convert.TimestampToDateDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 区域
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AreaDTO implements Serializable {
    /**
     * 自增ID
     */
    private Integer id;

    /**
     * 城市code
     */
    private String adCode;

    /**
     * 区域编码
     */
    @ApiModelProperty("区域编码")
    private String areaCode;

    /**
     * 人流区域code
     */
    @ApiModelProperty("人流区域code")
    private String geoAreaCode;

    /**
     * 车流区域code
     */
    @ApiModelProperty("车流区域code")
    private String geoBigAreaCode;

    /**
     * 区域名称
     */
    @ApiModelProperty("区域名称")
    private String areaName;

    /**
     * 区域类型
     */
    @ApiModelProperty("区域类型")
    private String areaType;

    /**
     * 人流区域
     */
    @ApiModelProperty("人流区域-wkt")
    private String geo;


    /**
     * 车流区域
     */
    @ApiModelProperty("车流区域-wkt")
    private String geoBig;


    /**
     * 描述
     */
    private String description;

    /**
     * 面积（平方公里）
     */
    private BigDecimal areaSize;

    /**
     * 负责人
     */
    private String responsiblePerson;

    /**
     * 上级区域ID
     */
    private Integer superiorAreaId;

    /**
     * 是否重点监测区域
     */
    private Boolean isKeyMonitorArea;

    /**
     * 负责人电话
     */
    private String responsiblePersonPhone;

    /**
     * 安保开始时间
     */
    @JsonSerialize(using = DateToTimestampSerializer.class)
    @JsonDeserialize(using = TimestampToDateDeserializer.class)
    private Date securityPeriodStart;

    /**
     * 安保结束时间
     */
    @JsonSerialize(using = DateToTimestampSerializer.class)
    @JsonDeserialize(using = TimestampToDateDeserializer.class)
    private Date securityPeriodEnd;

    /**
     * 圈层列表
     */
    @ApiModelProperty("圈层列表,如核心区、疏导区")
    private List<CircleLayerDTO> circleLayerList;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}