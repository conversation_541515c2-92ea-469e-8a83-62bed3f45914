package com.deepinnet.spatiotemporalplatform.smartcity.service.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.deepinnet.spatiotemporalplatform.model.camera.SurveillanceCamera;
import com.deepinnet.spatiotemporalplatform.model.util.WktUtil;
import lombok.Data;

/**
 * C<PERSON>
 * Date 2024-09-12
 **/

@Data
public class ImportCamera {

    @ExcelProperty("CHANNEL_CODE")
    private String code;
    @ExcelProperty("CHANNEL_NAME")
    private String name;
    @ExcelProperty("安装地址")
    private String location;
    @ExcelProperty("经度")
    private String lng;
    @ExcelProperty("纬度")
    private String lat;

    public SurveillanceCamera toSurveillanceCamera() {
        SurveillanceCamera camera = new SurveillanceCamera();
        camera.setCameraCode(code);
        camera.setName(name);
        camera.setInstallationLocation(location);
        camera.setCoordinates(WktUtil.toPoint(lng, lat).toString());
        return camera;
    }
}
