package com.deepinnet.spatiotemporalplatform.smartcity.service.service.impl;

import com.alibaba.excel.EasyExcel;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.smartcity.service.helper.CameraReadListener;
import com.deepinnet.localdata.integration.CameraClient;
import com.deepinnet.localdata.integration.model.input.MonitorPlaybackQueryDTO;
import com.deepinnet.localdata.integration.model.input.MonitorRecordQueryDTO;
import com.deepinnet.localdata.integration.model.input.RealMonitorQueryDTO;
import com.deepinnet.localdata.integration.model.output.MonitorRecordResponseDTO;
import com.deepinnet.localdata.integration.model.outsidebean.CameraTypeEnum;
import com.deepinnet.spatiotemporalplatform.smartcity.service.dto.ImportCamera;
import com.deepinnet.spatiotemporalplatform.smartcity.service.enums.MonitorCameraType;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.SurveillanceCameraService;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.KeyMonitorRepository;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.SurveillanceCameraRepository;
import com.deepinnet.spatiotemporalplatform.model.camera.KeyMonitorQueryDTO;
import com.deepinnet.spatiotemporalplatform.model.camera.MarkKeyMonitorDTO;
import com.deepinnet.spatiotemporalplatform.model.camera.SurveillanceCamera;
import com.deepinnet.spatiotemporalplatform.model.camera.SurveillanceCameraCondition;
import com.deepinnet.spatiotemporalplatform.model.camera.SurveillanceCamerasMark;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Creator zengjuerui
 * Date 2024-07-31
 **/

@Service
public class SurveillanceCameraServiceImpl implements SurveillanceCameraService {

   @Resource
   private SurveillanceCameraRepository cameraRepository;

    @Resource
    private KeyMonitorRepository keyMonitorRepository;

    @Resource
    private CameraClient cameraClient;

    @Override
    public CommonPage<SurveillanceCamera> listPage(SurveillanceCameraCondition condition) {
        return cameraRepository.listPage(condition);
    }

    public SurveillanceCamera getByCodeAndType(String code, String type) {
        return cameraRepository.getByCodeAndType(code, type);
    }

    @Override
    public List<SurveillanceCamera> listByAreaCodeAndType(String areaCode, String type) {

        return cameraRepository.listByAreaCodeAndType(areaCode, type);
    }

    @Override
    public List<SurveillanceCamera> queryAreaNearCamera(SurveillanceCameraCondition condition) {
        return cameraRepository.listAreaNearCamera(condition);
    }

    @Override
    public void markKeyMonitor(MarkKeyMonitorDTO markKeyMonitorDTO) {
        List<SurveillanceCamerasMark> modelList = markKeyMonitorDTO.getMonitorCodeList().stream().map(v -> {
            SurveillanceCamerasMark model = new SurveillanceCamerasMark();
            model.setCameraCode(v);
            model.setAreaCode(markKeyMonitorDTO.getAreaCode());
            model.setObjectCode(markKeyMonitorDTO.getObjectCode());
            model.setCameraType(MonitorCameraType.KEY.name());
            return model;
        }).collect(Collectors.toList());
        //批量保存重点摄像头
        keyMonitorRepository.saveBatch(modelList);
    }

    @Override
    public List<SurveillanceCamera> getKeyMonitorInfo(KeyMonitorQueryDTO keyMonitorQueryDTO) {
        List<String> cameraCodeList = keyMonitorRepository.queryKeyMonitorList(keyMonitorQueryDTO)
                .stream().map(SurveillanceCamerasMark::getCameraCode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cameraCodeList)) {
            return new ArrayList<>(0);
        }
        return cameraRepository.listByCodesAndType(cameraCodeList, CameraTypeEnum.VIDEO.getCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateKeyMonitor(MarkKeyMonitorDTO markKeyMonitorDTO) {
        keyMonitorRepository.deleteByAreaCode(markKeyMonitorDTO.getAreaCode(), markKeyMonitorDTO.getObjectCode());
        this.markKeyMonitor(markKeyMonitorDTO);
    }

    @Override
    public void deleteKeyMonitor(MarkKeyMonitorDTO markKeyMonitorDTO) {
        keyMonitorRepository.deleteKeyMonitor(markKeyMonitorDTO);
    }

    @Override
    public String getRealMonitorUrlByCode(RealMonitorQueryDTO queryDTO) {
        return cameraClient.getRealtimeCameraVideoStreamUrl(queryDTO);
    }

    @Override
    public String getPlaybackUrl(MonitorPlaybackQueryDTO queryDTO) {
        return cameraClient.getPlaybackUrl(queryDTO);
    }

    @Override
    public MonitorRecordResponseDTO getRecord(MonitorRecordQueryDTO queryDTO) {
        return cameraClient.getRecord(queryDTO);
    }

    @Override
    public void importCamera(InputStream file) {
        EasyExcel.read(file, ImportCamera.class, new CameraReadListener(cameraRepository))
                .sheet()
                .doRead();
    }

    @Override
    public void saveBatch(List<SurveillanceCamera> cameraList) {
        cameraRepository.batchInsert(cameraList);
    }
}
