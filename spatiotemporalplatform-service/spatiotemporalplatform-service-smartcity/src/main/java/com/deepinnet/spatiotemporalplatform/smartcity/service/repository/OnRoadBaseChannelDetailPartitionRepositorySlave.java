package com.deepinnet.spatiotemporalplatform.smartcity.service.repository;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.deepinnet.spatiotemporalplatform.base.repository.AbstractRepository;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.OnRoadBaseChannelDetailPartitionDaoService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.OnRoadBaseChannelDetailPartitionDO;
import com.deepinnet.spatiotemporalplatform.smartcity.service.dto.OnRoadBaseChannelDetailPartition;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.deepinnet.spatiotemporalplatform.dal.constant.DataSourceConstants.CAR_PASS_STATICS;

@Service
@DS(CAR_PASS_STATICS)
public class OnRoadBaseChannelDetailPartitionRepositorySlave extends AbstractRepository<OnRoadBaseChannelDetailPartitionDO, OnRoadBaseChannelDetailPartition> {
    
    @Resource
    private OnRoadBaseChannelDetailPartitionDaoService onRoadBaseChannelDetailPartitionDaoService;
} 