package com.deepinnet.spatiotemporalplatform.smartcity.service.handler;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.HistoryPeopleFlowData;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.HistoryPeopleFlowParams;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.HistoryPeopleFlowRequest;
import com.deepinnet.spatiotemporalplatform.base.enums.BizEnum;
import com.deepinnet.spatiotemporalplatform.base.ftp.FtpUtil;
import com.deepinnet.spatiotemporalplatform.base.ftp.AbstractFtpDownloadHandler;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *    历史人流量 下载处理器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/14
 */

@Component
public class HistoryPeopleFlowDownloadHandler extends AbstractFtpDownloadHandler<HistoryPeopleFlowRequest> {

    @Override
    public BizEnum getBizEnum() {
        return BizEnum.HISTORY_PEOPLE_FLOW;
    }

    @Override
    protected String buildFilePath(HistoryPeopleFlowRequest request) {

        HistoryPeopleFlowParams params = request.getUrlParams();

        return getBizEnum().getCode() + "_" + params.getCustomAreaId() + "_" + LocalDateTime.now().toLocalDate().toString();
    }

    @Override
    protected String resolveFileName(String filePath, HistoryPeopleFlowRequest request) {
        return FtpUtil.getLastFileByPath(filePath);
    }

    @Override
    protected Type getResponseType() {
        return new TypeReference<List<HistoryPeopleFlowData>>() {}.getType();
    }
}
