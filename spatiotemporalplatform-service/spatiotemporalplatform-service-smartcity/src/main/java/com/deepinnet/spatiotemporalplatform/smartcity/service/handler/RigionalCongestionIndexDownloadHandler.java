package com.deepinnet.spatiotemporalplatform.smartcity.service.handler;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.base.enums.BizEnum;
import com.deepinnet.spatiotemporalplatform.base.ftp.AbstractFtpDownloadHandler;
import com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis.RealTimeDistrictIndexRanking;
import com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis.RealTimeDistrictIndexRankingRequest;
import com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis.RealTimeDistrictIndexRankingUrlParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.util.List;

/**
 * <p>
 * 区域拥堵指数 下载处理器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/14
 */
@Slf4j
@Component
public class RigionalCongestionIndexDownloadHandler extends AbstractFtpDownloadHandler<RealTimeDistrictIndexRankingRequest> {

    public static final String REGION_CONGESTION_INDEX_PATH = "district_ranking_index";

    @Override
    public BizEnum getBizEnum() {
        return BizEnum.REGION_CONGESTION_INDEX;
    }

    @Override
    public String buildFilePath(RealTimeDistrictIndexRankingRequest request) {
        RealTimeDistrictIndexRankingUrlParams params = request.getUrlParams();
        String ids = params.getIds();
        String type = params.getTypes();
        String postFix = StrUtil.isBlank(ids) ? "_" + type : "_" + ids + "_" + type;

        return REGION_CONGESTION_INDEX_PATH + postFix;
    }

    @Override
    protected Type getResponseType() {
        return new TypeReference<List<RealTimeDistrictIndexRanking>>() {
        }.getType();
    }
}
