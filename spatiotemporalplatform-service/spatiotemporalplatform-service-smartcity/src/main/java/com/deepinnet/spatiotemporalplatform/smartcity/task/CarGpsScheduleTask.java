package com.deepinnet.spatiotemporalplatform.smartcity.task;

import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.util.CoordinateTransform;
import com.deepinnet.digitaltwin.common.util.PointCoordinate;
import com.deepinnet.spatiotemporalplatform.base.lock.DatabaseDistributedLock;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.IntercomPointRecordRepository;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.DeviceGpsModelDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.GpsDeviceDO;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.DeviceGpsModelService;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.GpsDeviceRepository;
import com.deepinnet.spatiotemporalplatform.model.intercom.IntercomPointRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 对讲机用于车辆定位定位记录任务
 *
 * <AUTHOR>
 * @version 2024-11-22
 */
@Component
@Slf4j
@Profile({"qz-gov-inner-network", "test"})
public class CarGpsScheduleTask {

    private static String lockName = "car:gps";

    @Resource
    private DatabaseDistributedLock lock;

    @Resource
    private GpsDeviceRepository deviceRepository;

    @Resource
    private DeviceGpsModelService deviceGpsModelService;

    @Resource
    private IntercomPointRecordRepository intercomPointRecordRepository;

    /**
     * 记录车辆gps
     */
    @Scheduled(fixedRate = 5, timeUnit = TimeUnit.SECONDS)
    public void record() {
        if (lock.tryLock(lockName)) {
            try {
                List<GpsDeviceDO> deviceDOList = deviceRepository.list();
                if (CollectionUtils.isEmpty(deviceDOList)) {
                    LogUtil.info("没有需要查询的设备");
                    return;
                }

                List<String> deviceIds = deviceDOList.stream().map(GpsDeviceDO::getDeviceId).collect(Collectors.toList());
                List<IntercomPointRecord> intercomPointRecordList = intercomPointRecordRepository.queryLatestListByDeviceId(deviceIds);
                saveDeviceGpsModel(intercomPointRecordList);
            } catch (Exception e) {
                log.error("写入gps失败", e);
            } finally {
                lock.unlock(lockName);
            }
        }
    }


    private void saveDeviceGpsModel(List<IntercomPointRecord> itemList) {
        List<DeviceGpsModelDO> deviceGpsModelDOList = itemList.stream().map(item -> {
            try {
                DeviceGpsModelDO deviceGpsModelDO = new DeviceGpsModelDO();
                deviceGpsModelDO.setOrientationStatus(null);
                deviceGpsModelDO.setDeviceCode(item.getUserAccount());
                deviceGpsModelDO.setDeviceId(item.getUserAccount());
                deviceGpsModelDO.setSpeed(null);
                deviceGpsModelDO.setUserDeviceCode(item.getUserAccount());
                deviceGpsModelDO.setUid(item.getUserAccount());
                deviceGpsModelDO.setStarCount(null);
                deviceGpsModelDO.setAntennaStatus(null);
                deviceGpsModelDO.setAngle(null);
                deviceGpsModelDO.setEvent(null);
                deviceGpsModelDO.setCapTime(String.valueOf(item.getGpsArriveTime().getTime()));

                if (!StringUtils.isAnyBlank(item.getLng(), item.getLat())) {
                    PointCoordinate coordinate = CoordinateTransform.transformGCJ02ToWGS84(Double.parseDouble(item.getLng()),
                            Double.parseDouble(item.getLat()));
                    deviceGpsModelDO.setGpsX(String.valueOf(coordinate.getLongitude()));
                    deviceGpsModelDO.setGpsY(String.valueOf(coordinate.getLatitude()));
                }

                deviceGpsModelDO.setHeight(null);
                return deviceGpsModelDO;
            } catch (Exception e) {
                log.error("转换gps失败:" + item.toString(), e);
                return null;
            }
        }).filter(item -> item != null).collect(Collectors.toList());
        deviceGpsModelService.saveBatch(deviceGpsModelDOList);
    }

}
