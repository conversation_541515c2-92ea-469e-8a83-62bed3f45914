package com.deepinnet.spatiotemporalplatform.smartcity.service.convert;

import com.deepinnet.spatiotemporalplatform.model.intercom.IntercomPointRecord;
import com.deepinnet.spatiotemporalplatform.base.convert.Convert;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.IntercomPointRecordDO;
import com.deepinnet.spatiotemporalplatform.model.gps.RemoteGps;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Mappings;
import org.mapstruct.Named;

import java.util.List;

/**
 * AreaConvert
 * Author: chenkaiyang
 * Date: 2024/7/31
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface IntercomPointRecordConvert extends Convert<IntercomPointRecordDO, IntercomPointRecord> {

    List<IntercomPointRecord> out2ModelList(List<RemoteGps> intercomPointList);

    @Mappings({
            @Mapping(target = "code", source = "userAccount"),
            @Mapping(target = "online", source = "online", qualifiedByName = "integerToBoolean")
    })
    IntercomPointRecord out2ModelList(RemoteGps intercomPoint);

    @Mapping(target = "coordinate", expression = "java(com.deepinnet.spatiotemporalplatform.model.util.WktUtil.toPoint(model.getLng(), model.getLat()))")
    IntercomPointRecordDO toDO(IntercomPointRecord model);

    @Mapping(target = "coordinate", expression = "java(com.deepinnet.spatiotemporalplatform.model.util.WktUtil.toWkt(doEntity.getCoordinate()))")
    IntercomPointRecord toModel(IntercomPointRecordDO doEntity);


    @Named("integerToBoolean")
    static Boolean integerToBoolean(Integer status) {
        return status != null && status != 0;
    }
}
