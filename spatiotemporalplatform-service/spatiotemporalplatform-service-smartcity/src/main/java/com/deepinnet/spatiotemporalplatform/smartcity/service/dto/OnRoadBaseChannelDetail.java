package com.deepinnet.spatiotemporalplatform.smartcity.service.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 2024-10-12
 */
@Data
public class OnRoadBaseChannelDetail {
    /**
     * recordId
     */
    private String recordId;

    /**
     * 城市Code
     */
    private String tenantId;

    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 摄像头编号
     */
    private String channelId;

    /**
     * 车牌号
     */
    private String plateNum;

    /**
     * 过车时间
     */
    private Date capTime;

    /**
     * 是否在区域内
     */
    private Integer insideArea;

    /**
     * 车牌类型
     * */
    private String carNumType;
}
