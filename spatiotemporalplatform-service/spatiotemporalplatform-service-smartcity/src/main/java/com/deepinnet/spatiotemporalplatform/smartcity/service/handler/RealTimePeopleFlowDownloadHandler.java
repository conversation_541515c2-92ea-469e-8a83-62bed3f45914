package com.deepinnet.spatiotemporalplatform.smartcity.service.handler;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.base.enums.BizEnum;
import com.deepinnet.spatiotemporalplatform.base.ftp.AbstractFileDownloadHandler;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.RealTimePeopleFlowData;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.RealTimePeopleFlowRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;

/**
 * <p>
 * 实时人流量 下载处理器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/14
 */
@Slf4j
@Component
public class RealTimePeopleFlowDownloadHandler extends AbstractFileDownloadHandler<RealTimePeopleFlowRequest> {
    public static final String PEOPLE_FLOW_PATH = "people_flow";

    @Override
    public String buildFilePath(RealTimePeopleFlowRequest request) {
        String areaCode = request.getUrlParams().getCustomAreaId();
        return PEOPLE_FLOW_PATH + "_" + areaCode;
    }

    @Override
    public BizEnum getBizEnum() {
        return BizEnum.REAL_TIME_PEOPLE_FLOW;
    }

    @Override
    protected Type getResponseType() {
        return new TypeReference<RealTimePeopleFlowData>() {
        }.getType();
    }
}
