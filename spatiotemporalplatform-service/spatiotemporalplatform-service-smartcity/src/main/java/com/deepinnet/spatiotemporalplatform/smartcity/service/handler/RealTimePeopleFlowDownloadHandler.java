package com.deepinnet.spatiotemporalplatform.smartcity.service.handler;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.RealTimePeopleFlowData;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.RealTimePeopleFlowParams;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.RealTimePeopleFlowRequest;
import com.deepinnet.spatiotemporalplatform.base.enums.BizEnum;
import com.deepinnet.spatiotemporalplatform.base.ftp.FtpUtil;
import com.deepinnet.spatiotemporalplatform.base.ftp.AbstractFtpDownloadHandler;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.time.LocalDateTime;

/**
 * <p>
 *    实时人流量 下载处理器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/14
 */

@Component
public class RealTimePeopleFlowDownloadHandler extends AbstractFtpDownloadHandler<RealTimePeopleFlowRequest> {

    @Override
    public BizEnum getBizEnum() {
        return BizEnum.REAL_TIME_PEOPLE_FLOW;
    }

    @Override
    protected String buildFilePath(RealTimePeopleFlowRequest request) {

        RealTimePeopleFlowParams params = request.getUrlParams();

        return getBizEnum().getCode() + "_" + params.getCustomAreaId() + "_" + LocalDateTime.now().toLocalDate().toString();
    }

    @Override
    protected String resolveFileName(String filePath, RealTimePeopleFlowRequest request) {
        return FtpUtil.getLastFileByPath(filePath);
    }

    @Override
    protected Type getResponseType() {
        return new TypeReference<RealTimePeopleFlowData>() {}.getType();
    }
}
