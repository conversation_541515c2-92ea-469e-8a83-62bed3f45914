package com.deepinnet.spatiotemporalplatform.smartcity.service.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.spatiotemporalplatform.model.congestion.RealTimeCongestionPostBody;
import com.deepinnet.spatiotemporalplatform.model.congestion.RealTimeCongestionRequest;
import com.deepinnet.spatiotemporalplatform.model.hotod.SourceDestinationDetail;
import com.deepinnet.spatiotemporalplatform.base.enums.BizEnum;
import com.deepinnet.spatiotemporalplatform.base.ftp.FtpUtil;
import com.deepinnet.spatiotemporalplatform.base.ftp.AbstractFtpDownloadHandler;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.time.LocalDateTime;

/**
 * <p>
 *    交通拥堵事件 下载处理器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/24
 */

@Component
public class TrafficCongestionEventDownloadHandler extends AbstractFtpDownloadHandler<RealTimeCongestionRequest> {

    @Override
    public BizEnum getBizEnum() {
        return BizEnum.TRAFFIC_CONGESTION_EVENT;
    }

    @Override
    protected String buildFilePath(RealTimeCongestionRequest request) {

        RealTimeCongestionPostBody paramsBody = request.getPostBody();

        if (CollUtil.isEmpty(paramsBody.getQueryList())) {
            LogUtil.warn("[交通拥堵事件]-查询参数异常, 查询参数:{}", JSONObject.toJSONString(paramsBody));
            return getBizEnum().getCode();
        }

        return getBizEnum().getCode() + "_" + paramsBody.getAdCodes().get(0) + "_" + paramsBody.getAreas().get(0) + "_" + LocalDateTime.now().toLocalDate().toString();
    }

    @Override
    protected String resolveFileName(String filePath, RealTimeCongestionRequest request) {
        RealTimeCongestionPostBody paramsBody = request.getPostBody();

        String lastFile = FtpUtil.getLastFileByPath(filePath);

        if (StrUtil.isBlank(lastFile)) {
            LogUtil.warn("[交通拥堵事件]-当前文件夹:{}, 不存在", filePath);
            return null;
        }

        String[] parts = lastFile.split("_");
        String last = parts[parts.length - 1];

        String fileName = getBizEnum().getCode() + "_"
                + paramsBody.getAdCodes().get(0) + "_"
                + paramsBody.getAreas().get(0) + "_"
                + paramsBody.getQueryList().get(0).getCongestType() + "_"
                + last;

        // 判断文件是否存在
        boolean exist = FtpUtil.fileExist(filePath, fileName);
        if (!exist) {
            return null;
        }

        return fileName;
    }

    @Override
    protected Type getResponseType() {
        return new TypeReference<SourceDestinationDetail>() {}.getType();
    }
}
