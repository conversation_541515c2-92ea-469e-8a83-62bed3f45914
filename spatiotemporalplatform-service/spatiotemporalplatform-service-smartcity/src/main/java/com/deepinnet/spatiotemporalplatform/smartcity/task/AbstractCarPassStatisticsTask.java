package com.deepinnet.spatiotemporalplatform.smartcity.task;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.localdata.integration.RegionTrafficFlowClient;
import com.deepinnet.localdata.integration.model.outsidebean.CameraSourceEnum;
import com.deepinnet.localdata.integration.model.outsidebean.VehiclePassQuery;
import com.deepinnet.localdata.integration.model.outsidebean.VehiclePassResponse;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.VehiclePassRecordDO;
import com.deepinnet.spatiotemporalplatform.model.camera.SurveillanceCamera;
import com.deepinnet.spatiotemporalplatform.smartcity.service.convert.VehiclePassConvert;
import com.deepinnet.spatiotemporalplatform.smartcity.service.dto.OnRoadBaseChannelDetail;
import com.deepinnet.spatiotemporalplatform.smartcity.service.dto.OnRoadBaseChannelDetailPartition;
import com.deepinnet.spatiotemporalplatform.smartcity.service.repository.OnRoadBaseChannelDetailPartitionRepository;
import com.deepinnet.spatiotemporalplatform.smartcity.service.repository.OnRoadBaseChannelDetailPartitionRepositorySlave;
import com.deepinnet.spatiotemporalplatform.smartcity.service.repository.OnRoadBaseChannelDetailRepository;
import com.deepinnet.spatiotemporalplatform.smartcity.service.repository.OnRoadBaseChannelDetailRepositorySlave;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.SurveillanceCameraRepository;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.VehiclePassRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

/**
 * <AUTHOR>
 * @version 2024-11-26
 */
@Slf4j
public abstract class AbstractCarPassStatisticsTask {

    private static final int PAGE_SIZE = 1000;

    protected static final int PARTITION_SIZE = 300;

    protected static final String STRING_SPLIT = "_";

    /**
     * 区域外扩展距离
     */
    protected static int AREA_EXTENDS_DISTANCE = 500;
    @Resource
    private VehiclePassConvert vehiclePassConvert;
    @Resource
    protected SurveillanceCameraRepository surveillanceCameraRepository;

    @Resource
    protected OnRoadBaseChannelDetailRepository onRoadBaseChannelDetailRepository;

    @Resource
    protected OnRoadBaseChannelDetailPartitionRepository onRoadBaseChannelDetailPartitionRepository;

    @Resource
    protected OnRoadBaseChannelDetailRepositorySlave onRoadBaseChannelDetailRepositorySlave;

    @Resource
    protected OnRoadBaseChannelDetailPartitionRepositorySlave onRoadBaseChannelDetailPartitionRepositorySlave;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    protected RegionTrafficFlowClient regionTrafficFlowClient;

    @Value("${city.adcode}")
    private String cityCode;

    @Resource
    private VehiclePassRecordService vehiclePassRecordService;
    @Resource
    private VehiclePassDataSourceToggle featureToggle;
    @Resource
    private CarPassCameraCache carPassCameraCache;


    protected List<String> doStatistics(String areaCode, List<SurveillanceCamera> cameraList) {
        // 摄像头 code 与 SurveillanceCamera 映射
        Map<String, SurveillanceCamera> surveillanceCameraMap = cameraList.stream().collect(Collectors.toMap(camera -> {
            if (CameraSourceEnum.getByCode(camera.getSource()) == CameraSourceEnum.QZ_CD) {
                // 诚道摄像头取过车记录需要在摄像头编号最后加上21
                return camera.getCameraCode() + "21";
            }
            return camera.getCameraCode();
        }, Function.identity(), (oldValue, newValue) -> oldValue));

        // 摄像头 code 列表
        List<String> cameraCodeList = new ArrayList<>(surveillanceCameraMap.keySet());

        // 统计过去5分钟的过车明细
        Date endTime = DateUtils.addMinutes(new Date(), -1);
        Date startTime = DateUtils.addMinutes(new Date(), -6);

        if (featureToggle.isFromMQ()) {
            int pageNum = 1;
            while (true) {
                // 从数据库查询过车记录明细
                IPage<VehiclePassRecordDO> vehiclePassRecordDOIPage = getVehiclePassRecordDOIPage(pageNum, startTime, endTime, cameraCodeList);
                if (vehiclePassRecordDOIPage == null || CollectionUtil.isEmpty(vehiclePassRecordDOIPage.getRecords())) {
                    break;
                }
                List<VehiclePassResponse> vehiclePassResponses = vehiclePassConvert.toModelList(vehiclePassRecordDOIPage.getRecords());
                // 设置经纬度
                setLngLat(vehiclePassResponses, surveillanceCameraMap);
                // 筛选掉没有经纬度的记录
                List<VehiclePassResponse> hasLngLatVehiclePassList = vehiclePassResponses.stream().filter(vehiclePassResponse -> vehiclePassResponse.getLng() != null && vehiclePassResponse.getLat() != null).collect(Collectors.toList());
                // 保存到数据库
                persistToDB(hasLngLatVehiclePassList, areaCode);
                // 继续下一页
                pageNum++;
            }
        } else {
            // 大华接口查询查询并保存过车记录明细
            VehiclePassQuery query = new VehiclePassQuery();
            query.setEndTimeStr(DateUtil.format(endTime, NORM_DATETIME_PATTERN));
            query.setStartTimeStr(DateUtil.format(startTime, NORM_DATETIME_PATTERN));
            query.setChannelCodes(cameraCodeList);
            queryAndSaveResult(1, query, areaCode);

        }
        return cameraCodeList;
    }

    /**
     * 设置经纬度
     * @param vehiclePassResponses
     * @param surveillanceCameraMap
     */
    private static void setLngLat(List<VehiclePassResponse> vehiclePassResponses, Map<String, SurveillanceCamera> surveillanceCameraMap) {
        vehiclePassResponses.forEach(vehiclePassResponse -> {
            SurveillanceCamera surveillanceCamera = surveillanceCameraMap.get(vehiclePassResponse.getChannelId());
            if (surveillanceCamera != null) {
                vehiclePassResponse.setLng(surveillanceCamera.getLng());
                vehiclePassResponse.setLat(surveillanceCamera.getLat());
            }
        });
    }

    private IPage<VehiclePassRecordDO> getVehiclePassRecordDOIPage(int pageNum, Date startTime, Date endTime, List<String> cameraCodeList) {
        IPage<VehiclePassRecordDO> iPage = new Page<>(pageNum, PAGE_SIZE);
        LambdaQueryWrapper<VehiclePassRecordDO> pageQuery = Wrappers.lambdaQuery(VehiclePassRecordDO.class).gt(VehiclePassRecordDO::getCapTime, startTime.getTime()).le(VehiclePassRecordDO::getCapTime, endTime.getTime())
                .in(VehiclePassRecordDO::getChannelId, cameraCodeList);

        IPage<VehiclePassRecordDO> vehiclePassRecordDOIPage = vehiclePassRecordService.page(iPage, pageQuery);

        return vehiclePassRecordDOIPage;
    }

    public void queryAndSaveResult(int pageNum, VehiclePassQuery query, String areaCode) {
        log.info("start query vehicle pass records,areaCode:{}, pageNum:{}", areaCode, pageNum);
        CommonPage<VehiclePassResponse> vehiclePassPage = regionTrafficFlowClient.getVehiclePass(query, pageNum, PAGE_SIZE);

        while (vehiclePassPage != null && CollectionUtil.isNotEmpty(vehiclePassPage.getList())) {
            log.info("areaCode:{},getVehiclePass pageNum:{},total:{},start to query nextPage", areaCode, pageNum, vehiclePassPage.getTotal());
            persistToDB(vehiclePassPage.getList(), areaCode);
            // 增加页码，继续查询
            pageNum++;
            vehiclePassPage = regionTrafficFlowClient.getVehiclePass(query, pageNum, PAGE_SIZE);
        }
    }

    public void persistToDB(List<VehiclePassResponse> list, String areaCode) {
        List<OnRoadBaseChannelDetail> onRoadBaseChannelDetails = list.parallelStream().filter(vehiclePassResponse -> {
            String plateNum = vehiclePassResponse.getPlateNum();
            // 过滤无效车牌
            return StringUtils.isNotBlank(plateNum) && !StringUtils.equalsIgnoreCase(plateNum, "unknown") && !StringUtils.equalsIgnoreCase(plateNum, "无牌车");
        }).map(vehiclePassResponse -> {
            Date now = new Date();
            OnRoadBaseChannelDetail onRoadBaseChannelDetail = buildOnRoadBaseChannelDetail(areaCode, vehiclePassResponse, now);
            return onRoadBaseChannelDetail;
        }).collect(Collectors.toList());

        // 分区数据
        List<OnRoadBaseChannelDetailPartition> onRoadBaseChannelPartitionDetails = onRoadBaseChannelDetails.stream().map(
                onRoadBaseChannelDetail -> {
                    OnRoadBaseChannelDetailPartition onRoadBaseChannelDetailPartition = BeanUtil.copyProperties(onRoadBaseChannelDetail, OnRoadBaseChannelDetailPartition.class);
                    String key = cityCode + STRING_SPLIT + onRoadBaseChannelDetailPartition.getAreaCode() + STRING_SPLIT + onRoadBaseChannelDetailPartition.getPlateNum();
                    onRoadBaseChannelDetailPartition.setHashKey(Math.abs(key.hashCode() % 10));
                    return onRoadBaseChannelDetailPartition;
                }
        ).collect(Collectors.toList());

        try {
            // 写入标准在途量计算表
            long startTime = System.currentTimeMillis();
            onRoadBaseChannelDetailRepository.saveBatch(onRoadBaseChannelDetails);
            onRoadBaseChannelDetailPartitionRepository.saveBatch(onRoadBaseChannelPartitionDetails);
            onRoadBaseChannelDetailRepositorySlave.saveBatch(onRoadBaseChannelDetails);
            onRoadBaseChannelDetailPartitionRepositorySlave.saveBatch(onRoadBaseChannelPartitionDetails);
            long endTime = System.currentTimeMillis();
            log.info("区域:{}过车明细入库成功,数量:{},耗时:{}m", areaCode, onRoadBaseChannelDetails.size(), endTime - startTime);
        } catch (Exception e) {
            log.error("过车明细入库失败,areaCode:" + areaCode, e);
        }


    }

    private OnRoadBaseChannelDetail buildOnRoadBaseChannelDetail(String areaCode, VehiclePassResponse vehiclePassResponse, Date now) {
        OnRoadBaseChannelDetail onRoadBaseChannelDetail = new OnRoadBaseChannelDetail();
        onRoadBaseChannelDetail.setRecordId(vehiclePassResponse.getRecordId());
        onRoadBaseChannelDetail.setAreaCode(areaCode);
        onRoadBaseChannelDetail.setGmtCreated(now);
        onRoadBaseChannelDetail.setGmtModified(now);
        onRoadBaseChannelDetail.setChannelId(vehiclePassResponse.getChannelId());
        onRoadBaseChannelDetail.setPlateNum(vehiclePassResponse.getPlateNum());
        onRoadBaseChannelDetail.setCapTime(new Date(vehiclePassResponse.getCapTime()));
        onRoadBaseChannelDetail.setInsideArea(inArea(vehiclePassResponse, areaCode));
        return onRoadBaseChannelDetail;
    }

    protected Integer inArea(VehiclePassResponse vehiclePassResponse, String areaCode) {
        // 获取缓存
        Integer inAreaCache = carPassCameraCache.getByAreaCodeAndCameraCode(areaCode, vehiclePassResponse.getChannelCode());
        if (inAreaCache != null) {
            return inAreaCache;
        }
        boolean inArea = surveillanceCameraRepository.isCameraInArea(Double.parseDouble(vehiclePassResponse.getLng()), Double.parseDouble(vehiclePassResponse.getLat()), areaCode);
        // 添加缓存
        carPassCameraCache.putAreaCode2CameraCodeCache(areaCode, vehiclePassResponse.getChannelCode(), inArea ? 1 : 0);
        return inArea ? 1 : 0;
    }
}
