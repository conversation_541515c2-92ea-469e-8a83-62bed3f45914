package com.deepinnet.spatiotemporalplatform.smartcity.service.service;



import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.model.area.AreaDTO;
import com.deepinnet.spatiotemporalplatform.model.area.AreaCondition;

import java.util.List;

/**
 * Domain Service interface for managing Area entities with business logic.
 */
public interface AreaDomainService {

    AreaDTO createArea(AreaDTO areaDTO);


    boolean deleteArea(String areaCode);

    AreaDTO getAreaByCode(String areaCode);

    CommonPage<AreaDTO> pageArea(AreaCondition condition);

    List<AreaDTO> searchAreasByName(String name);

    AreaDTO getAreaByName(String name);

    List<AreaDTO> getAreaByNameList(List<String> nameList);

    AreaDTO updateArea(AreaDTO area);


}
