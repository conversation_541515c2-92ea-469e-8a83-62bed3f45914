package com.deepinnet.spatiotemporalplatform.smartcity.service.service;



import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.dto.RestrictedAreaVehicleAccessRecordDTO;
import com.deepinnet.spatiotemporalplatform.dto.RestrictedAreaVehiclePassageRecordStatDTO;
import com.deepinnet.spatiotemporalplatform.model.area.AreaDTO;
import com.deepinnet.spatiotemporalplatform.model.area.AreaCondition;
import com.deepinnet.spatiotemporalplatform.model.area.RestrictedAreaVehiclePassageRecordCondition;

import java.util.List;

/**
 * Domain Service interface for managing Area entities with business logic.
 */
public interface AreaDomainService {

    AreaDTO createArea(AreaDTO areaDTO);


    boolean deleteArea(String areaCode);

    AreaDTO getAreaByCode(String areaCode);

    CommonPage<AreaDTO> pageArea(AreaCondition condition);

    List<AreaDTO> searchAreasByName(String name);

    AreaDTO getAreaByName(String name);

    List<AreaDTO> getAreaByNameList(List<String> nameList);

    AreaDTO updateArea(AreaDTO area);

    RestrictedAreaVehiclePassageRecordStatDTO getRestrictedAreaVehiclePassageRecordStat(String areaCode);

    CommonPage<RestrictedAreaVehicleAccessRecordDTO> listRestrictedAreaVehicleAccessRecord(RestrictedAreaVehiclePassageRecordCondition condition);
}
