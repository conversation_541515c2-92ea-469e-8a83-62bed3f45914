package com.deepinnet.spatiotemporalplatform.smartcity.service.helper;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.deepinnet.spatiotemporalplatform.smartcity.service.dto.ImportCamera;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.SurveillanceCameraRepository;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Creator zengjuerui
 * Date 2024-09-12
 **/

public class CameraReadListener implements ReadListener<ImportCamera> {

    private final List<ImportCamera> cameraList;
    private final SurveillanceCameraRepository cameraRepository;

    public CameraReadListener(SurveillanceCameraRepository cameraRepository) {
        this.cameraRepository = cameraRepository;
        this.cameraList = new ArrayList<>();
    }

    @Override
    public void invoke(ImportCamera data, AnalysisContext context) {
        cameraList.add(data);

        if(cameraList.size() >= 100) {
            cameraRepository.batchInsert(cameraList.stream().map(ImportCamera::toSurveillanceCamera).collect(Collectors.toList()));
            cameraList.clear();
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if(cameraList.isEmpty()) {
            return;
        }

        cameraRepository.batchInsert(cameraList.stream().map(ImportCamera::toSurveillanceCamera).collect(Collectors.toList()));
    }
}
