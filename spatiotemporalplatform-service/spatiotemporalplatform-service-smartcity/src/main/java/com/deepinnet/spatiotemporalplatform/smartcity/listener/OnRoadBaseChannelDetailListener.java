package com.deepinnet.spatiotemporalplatform.smartcity.listener;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.impl.RestrictedAreaVehicleAccessRecordRepository;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.RestrictedAreaVehicleAccessRecordDO;
import com.deepinnet.spatiotemporalplatform.model.area.AreaCondition;
import com.deepinnet.spatiotemporalplatform.model.area.AreaDTO;
import com.deepinnet.spatiotemporalplatform.model.camera.SurveillanceCamera;
import com.deepinnet.spatiotemporalplatform.model.tour.openapi.VehicleAreaPassReservationPostBody;
import com.deepinnet.spatiotemporalplatform.model.tour.openapi.VehicleAreaPassReservationRecord;
import com.deepinnet.spatiotemporalplatform.model.tour.openapi.VehicleAreaPassReservationRequest;
import com.deepinnet.spatiotemporalplatform.model.tour.openapi.VehicleAreaPassReservationResponse;
import com.deepinnet.spatiotemporalplatform.smartcity.service.dto.OnRoadBaseChannelDetail;
import com.deepinnet.spatiotemporalplatform.smartcity.service.enums.PlateRecordFiledStatus;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.AreaDomainService;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.SurveillanceCameraRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.context.ApplicationListener;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
/**
 * Creator zengjuerui
 * Date 2025-08-14
 **/

@Slf4j
@Component
public class OnRoadBaseChannelDetailListener implements ApplicationListener<OnRoadBaseChannelDetailEvent> {

    @Resource
    private AreaDomainService areaDomainService;
    @Resource
    private RestrictedAreaVehicleAccessRecordRepository restrictedAreaVehicleAccessRecordRepository;
    @Resource
    private SurveillanceCameraRepository surveillanceCameraRepository;
    @Resource
    private CommonDataService commonDataService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private static final int AREA_EXTENDS_DISTANCE = 2_000;

    private static final String VEHICLE_ACCESS_BOOKING_QUERY_CACHE_KEY = "TRAFFIC_POLICE:VEHICLE:ACCESS:BOOKING:QUERY:";

    private static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    private static final List<Integer> VEHICLE_AREA_PASS_RESERVATIONLIST = List.of(1, 2, 3);

    @Async(value = "asyncEventListenerExecutor")
    @Override
    public void onApplicationEvent(OnRoadBaseChannelDetailEvent event) {
        List<OnRoadBaseChannelDetail> detailDOList = event.getDetailDOList();
        if(CollectionUtils.isEmpty(detailDOList)) return;

        log.info("OnRoadBaseChannelDetailListener received event: {}", detailDOList);

        AreaCondition areaCondition = new AreaCondition();
        areaCondition.setPageNum(1);
        areaCondition.setPageSize(10);
        areaCondition.setInVehicleRestrictedArea(true);
        CommonPage<AreaDTO> areaDTOCommonPage = areaDomainService.pageArea(areaCondition);

        areaDTOCommonPage.getList().forEach(a -> processRestrictedAreaVehicleAccessRecord(a, detailDOList));
    }

    private void processRestrictedAreaVehicleAccessRecord(AreaDTO areaDO, final List<OnRoadBaseChannelDetail> onRoadBaseChannelDetailDOS) {
        if(CollectionUtils.isEmpty(onRoadBaseChannelDetailDOS)) return;

        //List<SurveillanceCamera> cameraListAll = surveillanceCameraRepository.findAreaCarCamerasWithExtends(areaDO.getAreaCode(), AREA_EXTENDS_DISTANCE);

        List<OnRoadBaseChannelDetail> funcDetailList = onRoadBaseChannelDetailDOS.stream()
                .filter(d -> areaDO.getAreaCode().equals(d.getAreaCode()))
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(funcDetailList)) return;

        Map<String, List<OnRoadBaseChannelDetail>> plateMap = onRoadBaseChannelDetailDOS.stream()
                .collect(Collectors.groupingBy(OnRoadBaseChannelDetail::getPlateNum));

        List<RestrictedAreaVehicleAccessRecordDO> oldList = restrictedAreaVehicleAccessRecordRepository
                .listLastOneByPlateNumList(areaDO.getAreaCode(), new ArrayList<>(plateMap.keySet()));

        Map<String, RestrictedAreaVehicleAccessRecordDO> oldMap = oldList.stream()
                .collect(Collectors.toMap(RestrictedAreaVehicleAccessRecordDO::getPlateNum, Function.identity()));

        List<RestrictedAreaVehicleAccessRecordDO> recordDOS = new ArrayList<>();
        for (Map.Entry<String, List<OnRoadBaseChannelDetail>> stringListEntry : plateMap.entrySet()) {
            VehiclePassRecordFlow recordFlow = new VehiclePassRecordFlow(stringListEntry.getKey(), areaDO,
                    oldMap.get(stringListEntry.getKey()), stringListEntry.getValue());
            recordDOS.addAll(recordFlow.calculateRecords());
        }

        restrictedAreaVehicleAccessRecordRepository.saveOrUpdateBatch(recordDOS);
    }

    private boolean isInArea(List<SurveillanceCamera> cameraListAll, OnRoadBaseChannelDetail onRoadBaseChannelDetailDO) {
        return cameraListAll.stream().anyMatch(s -> s.getCameraCode().equals(onRoadBaseChannelDetailDO.getChannelId()));
    }

    private void queryPlateRecordFiled(AreaDTO areaDO, RestrictedAreaVehicleAccessRecordDO recordDO) {

        if(PlateRecordFiledStatus.RECORD_FILED_QUERY_FAILED.getCode().equals(recordDO.getPlateRecordFiledStatus())
            && recordDO.getPlateRecordFiledQueryTimes() >= 3) {
            recordDO.setPlateRecordFiledStatus(PlateRecordFiledStatus.RECORD_FILED_QUERY_FAILED_OVER_LIMIT.getCode());
            return;
        }

        String key = VEHICLE_ACCESS_BOOKING_QUERY_CACHE_KEY + recordDO.getPlateNum();

        String value = stringRedisTemplate.opsForValue().get(key);
        if(value != null) {
            recordDO.setPlateRecordFiledStatus(value);
        }

        VehicleAreaPassReservationPostBody body = new VehicleAreaPassReservationPostBody();
        body.setPlateNo(recordDO.getPlateNum());
        body.setPassTime(LocalDateTimeUtil.format(recordDO.getInflowTime(), DEFAULT_DATE_FORMAT));
        body.setArea(areaDO.getAreaName());
        body.setCarType(recordDO.getPlateNumType());

        VehicleAreaPassReservationRequest request = new VehicleAreaPassReservationRequest();
        request.setPassReservationPostBody(body);

        try {
            VehicleAreaPassReservationResponse response = (VehicleAreaPassReservationResponse) commonDataService.fetchData(request);
            VehicleAreaPassReservationRecord reservationRecord = response.getData();
            if(reservationRecord == null) {
                log.warn("特定区域通行查询没有返回，" + recordDO.getPlateNum());
                recordDO.setPlateRecordFiledStatus(PlateRecordFiledStatus.RECORD_FILED_QUERY_FAILED.getCode());
                recordDO.setPlateRecordFiledQueryTimes(recordDO.getPlateRecordFiledQueryTimes() + 1);
                return;
            }

            PlateRecordFiledStatus status;
            if(VEHICLE_AREA_PASS_RESERVATIONLIST.contains(reservationRecord.getBookType())) {
                status = PlateRecordFiledStatus.RECORD_FILED;
            } else {
                status = PlateRecordFiledStatus.NOT_RECORD_FILED;
            }

            recordDO.setPlateRecordFiledStatus(status.getCode());

            // 缓存保存每天的
            stringRedisTemplate.opsForValue().set(key, status.getCode(),
                    Duration.between(LocalDateTime.now(), LocalDateTime.of(LocalDate.now(), LocalTime.MAX)));

        } catch (Exception e) {
            log.error("特定区域通行查询失败，" + recordDO.getPlateNum(), e);
            recordDO.setPlateRecordFiledStatus(PlateRecordFiledStatus.RECORD_FILED_QUERY_FAILED.getCode());
            recordDO.setPlateRecordFiledQueryTimes(recordDO.getPlateRecordFiledQueryTimes() + 1);
        }
    }


    class VehiclePassRecordFlow {

        final String plateNum;

        final AreaDTO areaDO;

        final RestrictedAreaVehicleAccessRecordDO oldRecord;

        final List<OnRoadBaseChannelDetail> currentDetailList;

        public VehiclePassRecordFlow(String plateNum, AreaDTO areaDO, RestrictedAreaVehicleAccessRecordDO oldRecord,
                                     List<OnRoadBaseChannelDetail> currentDetailList) {
            this.plateNum = plateNum;
            this.areaDO = areaDO;
            this.oldRecord = oldRecord;
            this.currentDetailList = currentDetailList;
        }

        List<RestrictedAreaVehicleAccessRecordDO> calculateRecords() {

            List<RestrictedAreaVehicleAccessRecordDO> res = new ArrayList<>();


            List<OnRoadBaseChannelDetail> sortedDetailList = currentDetailList.stream()
                    .sorted(Comparator.comparing(OnRoadBaseChannelDetail::getCapTime))
                    .collect(Collectors.toList());


            RestrictedAreaVehicleAccessRecordDO currentRecord = null;
            if(oldRecord != null) {
                currentRecord = oldRecord;
            }

            for (OnRoadBaseChannelDetail detailDO : sortedDetailList) {

                if(detailDO.getInsideArea() == 1) {
                    // 流入，但是没有记录，添加一条
                    // 当前的记录已经是驶离了，那再加一条新的
                    if(currentRecord == null || BooleanUtils.isTrue(currentRecord.getIsDeparted())) {
                        currentRecord = fromDetailDO(areaDO, detailDO);
                    }

                    // 更新最早的过车记录时间
                    if(currentRecord.getInflowTime().isAfter(LocalDateTimeUtil.of(detailDO.getCapTime()))) {
                        currentRecord.setInflowTime(LocalDateTimeUtil.of(detailDO.getCapTime()));
                        currentRecord.setGmtModified(LocalDateTime.now());
                    }

                    queryPlateRecordFiled(areaDO, currentRecord);
                    res.add(currentRecord);

                } else {

                    // 流出，但是记录未驶离，更新驻留时长并标记已驶离
                    if(currentRecord != null && BooleanUtils.isFalse(currentRecord.getIsDeparted())) {
                        currentRecord.setDuration(Duration.between(currentRecord.getInflowTime(), LocalDateTimeUtil.of(detailDO.getCapTime())).getSeconds());

                        // duration 小于零表示过车记录是延迟来的，暂时先跳过不处理
                        if(currentRecord.getDuration() < 0) {
                            continue;
                        }

                        currentRecord.setIsDeparted(true);
                        currentRecord.setOutflowTime(LocalDateTimeUtil.of(detailDO.getCapTime()));
                        currentRecord.setGmtModified(LocalDateTime.now());
                        res.add(currentRecord);
                    }
                }
            }

            return res;
        }

        private RestrictedAreaVehicleAccessRecordDO fromDetailDO(AreaDTO areaDO, OnRoadBaseChannelDetail detailDO) {
            LocalDateTime now = LocalDateTime.now();

            RestrictedAreaVehicleAccessRecordDO res = new RestrictedAreaVehicleAccessRecordDO();
            res.setAreaCode(areaDO.getAreaCode());
            res.setInflowTime(LocalDateTimeUtil.of(detailDO.getCapTime().toInstant(), TimeZone.getDefault()));
            res.setPlateNum(detailDO.getPlateNum());
            res.setGmtCreated(now);
            res.setGmtModified(now);
            res.setIsDeparted(false);
            res.setIsPlateRecordFiled(false);
            res.setPlateNumType(detailDO.getCarNumType());

            return res;
        }
    }
}
