package com.deepinnet.spatiotemporalplatform.smartcity.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.VehiclePassRecordService;
import com.deepinnet.spatiotemporalplatform.dal.mapper.VehiclePassRecordMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.VehiclePassRecordDO;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【vehicle_pass_record】的数据库操作Service实现
* @createDate 2025-01-23 14:54:09
*/
@Service
public class VehiclePassRecordServiceImpl extends ServiceImpl<VehiclePassRecordMapper, VehiclePassRecordDO>
    implements VehiclePassRecordService {

}




