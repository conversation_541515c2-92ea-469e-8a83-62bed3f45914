package com.deepinnet.spatiotemporalplatform.smartcity.service.handler;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.base.enums.BizEnum;
import com.deepinnet.spatiotemporalplatform.base.ftp.AbstractFileDownloadHandler;
import com.deepinnet.spatiotemporalplatform.model.road.RealRoadIndex;
import com.deepinnet.spatiotemporalplatform.model.road.RealRoadIndexRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.util.List;

/**
 * <p>
 * 拥堵里程 下载处理器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/14
 */
@Slf4j
@Component
public class RoadTrafficMilesDownloadHandler extends AbstractFileDownloadHandler<RealRoadIndexRequest> {
    public static final String ROAD_TRAFFIC_PATH = "road_traffic";
    @Override
    public String buildFilePath(RealRoadIndexRequest request) {
        return ROAD_TRAFFIC_PATH;
    }

    @Override
    public BizEnum getBizEnum() {
        return BizEnum.CONGESTION_MILES;
    }

    @Override
    protected Type getResponseType() {
        return new TypeReference<List<RealRoadIndex>>() {
        }.getType();
    }
}
