package com.deepinnet.spatiotemporalplatform.smartcity.service.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.impl.RestrictedAreaVehicleAccessRecordRepository;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.RestrictedAreaVehicleAccessRecordDO;
import com.deepinnet.spatiotemporalplatform.dal.model.RestrictedAreaVehiclePassageRecordQuery;
import com.deepinnet.spatiotemporalplatform.dal.model.RestrictedAreaVehiclePassageRecordStat;
import com.deepinnet.spatiotemporalplatform.dto.RestrictedAreaVehicleAccessRecordDTO;
import com.deepinnet.spatiotemporalplatform.dto.RestrictedAreaVehiclePassageRecordStatDTO;
import com.deepinnet.spatiotemporalplatform.model.area.RestrictedAreaVehiclePassageRecordCondition;
import com.deepinnet.spatiotemporalplatform.smartcity.service.client.CustomAreaClient;
import com.deepinnet.spatiotemporalplatform.smartcity.service.convert.AreaConvert;
import com.deepinnet.spatiotemporalplatform.model.area.AreaDTO;
import com.deepinnet.spatiotemporalplatform.model.area.CircleLayerDTO;
import com.deepinnet.spatiotemporalplatform.model.area.AreaCondition;
import com.deepinnet.spatiotemporalplatform.smartcity.service.enums.PlateRecordFiledStatus;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.AreaDomainService;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.CircleLayerDomainService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.AreaDO;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.AreaService;
import com.deepinnet.spatiotemporalplatform.model.util.WktUtil;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Domain Service implementation for managing Area entities.
 */
@Service
public class AreaDomainServiceImpl implements AreaDomainService {

    private static final Logger log = LoggerFactory.getLogger(AreaDomainServiceImpl.class);
    private final AreaService areaService;
    @Resource
    private AreaConvert AreaConverter;
    @Resource
    private CircleLayerDomainService circleLayerDomainService;
    @Resource
    private CustomAreaClient customAreaClient;
    @Resource
    private RestrictedAreaVehicleAccessRecordRepository restrictedAreaVehicleAccessRecordRepository;

    @Autowired
    public AreaDomainServiceImpl(AreaService areaService) {
        this.areaService = areaService;
    }

    @Value("${smartcity.area_vehicle_illegal_stay_time_seconds_threshold:1200}")
    private int areaVehicleIllegalStayTimeThreshold;

    @Override
    public AreaDTO createArea(AreaDTO areaDTO) {
        // todo 创建区域api
        String areaCode = customAreaClient.createCustomArea(areaDTO);
        //String areaCode = RandomUtil.randomNumbers(6);
        Assert.notBlank(areaCode, "创建区域失败");
        areaDTO.setAreaCode(areaCode);

        AreaDO areaDO = AreaConverter.toDO(areaDTO);
        areaDO.setId(null);
        if (areaService.save(areaDO)) {
            return AreaConverter.toDTO(areaDO);
        }
        return null;
    }

    @Override
    public boolean deleteArea(String areaCode) {
        QueryWrapper<AreaDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("area_code", areaCode);
        return areaService.remove(queryWrapper);
    }

    @Override
    public AreaDTO getAreaByCode(String areaCode) {
        QueryWrapper<AreaDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("area_code", areaCode);
        AreaDO areaDO = areaService.getOne(queryWrapper);
        AreaDTO areaDTO = AreaConverter.toDTO(areaDO);
        if (areaDTO == null) {
            return null;
        }

        List<CircleLayerDTO> circleLayerDTOS = circleLayerDomainService.listByAreaeCodeList(Arrays.asList(areaDTO.getAreaCode()));
        Map<String, List<CircleLayerDTO>> areaCode2CircleLayerMap =
                Optional.ofNullable(circleLayerDTOS).orElse(new ArrayList<>()).stream().collect(Collectors.groupingBy(CircleLayerDTO::getAreaCode));

        List<CircleLayerDTO> circleLayerDTOList = areaCode2CircleLayerMap.get(areaDTO.getAreaCode());
        areaDTO.setCircleLayerList(circleLayerDTOList);
        return areaDTO;
    }

    @Override
    public CommonPage<AreaDTO> pageArea(AreaCondition condition) {
        IPage<AreaDO> iPage = new Page<>(condition.getPageNum(), condition.getPageSize());
        LambdaQueryWrapper<AreaDO> pageQuery = Wrappers.lambdaQuery(AreaDO.class)
                .like(StringUtils.isNotEmpty(condition.getAreaName()), AreaDO::getAreaName, condition.getAreaName())
                .eq(Objects.nonNull(condition.getInVehicleRestrictedArea()), AreaDO::getInVehicleRestrictedArea, condition.getInVehicleRestrictedArea())
                .orderByAsc(AreaDO::getGmtCreated);

        if (StringUtils.isNotBlank(condition.getAdCode())) {
            pageQuery.eq(AreaDO::getAdCode, condition.getAdCode());
        }

        iPage = areaService.page(iPage, pageQuery);

        if(iPage.getTotal() == 0) {
            return CommonPage.buildPage(condition.getPageNum(), condition.getPageSize(), 0, 0L, new ArrayList<>());
        }
        List<AreaDO> areaDOList = iPage.getRecords();

        List<String> areaCodeList = areaDOList.stream().map(AreaDO::getAreaCode).collect(Collectors.toList());
        List<CircleLayerDTO> circleLayerDTOS = circleLayerDomainService.listByAreaeCodeList(areaCodeList);
        Map<String, List<CircleLayerDTO>> areaCode2CircleLayerMap =
                Optional.ofNullable(circleLayerDTOS).orElse(new ArrayList<>()).stream().collect(Collectors.groupingBy(CircleLayerDTO::getAreaCode));

        List<AreaDTO> areaDTOList = AreaConverter.toDTOList(areaDOList);
        areaDTOList.forEach(areaDTO -> {
            List<CircleLayerDTO> circleLayerDTOList = areaCode2CircleLayerMap.get(areaDTO.getAreaCode());
            areaDTO.setCircleLayerList(circleLayerDTOList);
        });
        return CommonPage.buildPage(condition.getPageNum(),
                condition.getPageSize(),
                (int) iPage.getPages(),
                iPage.getTotal(),
                areaDTOList);
    }



    @Override
    public List<AreaDTO> searchAreasByName(String name) {
        QueryWrapper<AreaDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("area_name", name);
        List<AreaDO> areaDOList = areaService.list(queryWrapper);
        return AreaConverter.toDTOList(areaDOList);
    }


    @Override
    public AreaDTO getAreaByName(String name) {
        QueryWrapper<AreaDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("area_name", name);
        List<AreaDO> areaDOList = areaService.list(queryWrapper);
        if (areaDOList.isEmpty()) {
            return null;
        }
        return AreaConverter.toDTOList(areaDOList).get(0);
    }

    @Override
    public List<AreaDTO> getAreaByNameList(List<String> nameList) {
        QueryWrapper<AreaDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("area_name", nameList);
        List<AreaDO> areaDOList = areaService.list(queryWrapper);
        if (areaDOList.isEmpty()) {
            return null;
        }
        return AreaConverter.toDTOList(areaDOList);
    }

    @Override
    public AreaDTO updateArea(AreaDTO area) {
        // 用Assert校验area的合法性
        Assert.notNull(area);
        Assert.notNull(area.getAreaCode());
        // 校验坐标数据的合法性
        if (area.getGeo() != null) {
            Assert.isTrue(WktUtil.isPolygon(area.getGeo()), "geo数据不合法");
        }
        if (area.getGeoBig() != null) {
            Assert.isTrue(WktUtil.isPolygon(area.getGeoBig()), "geoBig数据不合法");
        }

        // 更新
        areaService.updatebByAreaCode(AreaConverter.toDO(area));

        // 查询
        return getAreaByCode(area.getAreaCode());
    }

    @Override
    public RestrictedAreaVehiclePassageRecordStatDTO getRestrictedAreaVehiclePassageRecordStat(String areaCode) {
        RestrictedAreaVehiclePassageRecordStat stat = restrictedAreaVehicleAccessRecordRepository
                .listRestrictedAreaVehiclePassageRecordStat(areaCode, areaVehicleIllegalStayTimeThreshold);

        RestrictedAreaVehiclePassageRecordStatDTO res =  new RestrictedAreaVehiclePassageRecordStatDTO();
        BeanUtils.copyProperties(stat, res);

        return res;
    }

    @Override
    public CommonPage<RestrictedAreaVehicleAccessRecordDTO> listRestrictedAreaVehicleAccessRecord(RestrictedAreaVehiclePassageRecordCondition condition) {

        try(com.github.pagehelper.Page<RestrictedAreaVehicleAccessRecordDO> page = PageHelper.startPage(condition.getPageNum(), condition.getPageSize())){
            List<RestrictedAreaVehicleAccessRecordDO> recordDOS = restrictedAreaVehicleAccessRecordRepository.getBaseMapper()
                    .listByAreaCode(condition.getAreaCode(), condition.getIsIllegal(), areaVehicleIllegalStayTimeThreshold);

            List<RestrictedAreaVehicleAccessRecordDTO> recordDTOS = AreaConverter.toRestrictedAreaVehicleAccessRecordDTOList(recordDOS);

            for (RestrictedAreaVehicleAccessRecordDTO recordDTO : recordDTOS) {
                if(recordDTO.getDuration() == null) {
                    recordDTO.setDuration(Duration.between(recordDTO.getInflowTime(), LocalDateTime.now()).getSeconds());
                }

                recordDTO.setIsIllegal(recordDTO.getDuration() >= areaVehicleIllegalStayTimeThreshold && !recordDTO.getIsPlateRecordFiled());
            }

            return CommonPage.buildPage(condition.getPageNum(), condition.getPageSize(), page.getPages(), page.getTotal(), recordDTOS);
        } catch (Exception e) {
            log.error("查询特定区域车辆记录错误，" + e.getMessage(), e);
            return CommonPage.buildPage(condition.getPageNum(), condition.getPageSize(), 0, 0L, Collections.emptyList());
        } finally {
            PageHelper.clearPage();
        }
    }
}
