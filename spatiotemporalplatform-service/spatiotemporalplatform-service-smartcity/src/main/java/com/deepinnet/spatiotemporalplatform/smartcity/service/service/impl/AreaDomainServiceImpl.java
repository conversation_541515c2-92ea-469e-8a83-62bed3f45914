package com.deepinnet.spatiotemporalplatform.smartcity.service.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.smartcity.service.client.CustomAreaClient;
import com.deepinnet.spatiotemporalplatform.smartcity.service.convert.AreaConvert;
import com.deepinnet.spatiotemporalplatform.model.area.AreaDTO;
import com.deepinnet.spatiotemporalplatform.model.area.CircleLayerDTO;
import com.deepinnet.spatiotemporalplatform.model.area.AreaCondition;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.AreaDomainService;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.CircleLayerDomainService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.AreaDO;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.AreaService;
import com.deepinnet.spatiotemporalplatform.model.util.WktUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Domain Service implementation for managing Area entities.
 */
@Service
public class AreaDomainServiceImpl implements AreaDomainService {

    private final AreaService areaService;
    @Resource
    private AreaConvert AreaConverter;
    @Resource
    private CircleLayerDomainService circleLayerDomainService;
    @Resource
    private CustomAreaClient customAreaClient;

    @Autowired
    public AreaDomainServiceImpl(AreaService areaService) {
        this.areaService = areaService;
    }

    @Override
    public AreaDTO createArea(AreaDTO areaDTO) {
        // todo 创建区域api
        String areaCode = customAreaClient.createCustomArea(areaDTO);
        //String areaCode = RandomUtil.randomNumbers(6);
        Assert.notBlank(areaCode, "创建区域失败");
        areaDTO.setAreaCode(areaCode);

        AreaDO areaDO = AreaConverter.toDO(areaDTO);
        areaDO.setId(null);
        if (areaService.save(areaDO)) {
            return AreaConverter.toDTO(areaDO);
        }
        return null;
    }

    @Override
    public boolean deleteArea(String areaCode) {
        QueryWrapper<AreaDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("area_code", areaCode);
        return areaService.remove(queryWrapper);
    }

    @Override
    public AreaDTO getAreaByCode(String areaCode) {
        QueryWrapper<AreaDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("area_code", areaCode);
        AreaDO areaDO = areaService.getOne(queryWrapper);
        AreaDTO areaDTO = AreaConverter.toDTO(areaDO);
        if (areaDTO == null) {
            return null;
        }

        List<CircleLayerDTO> circleLayerDTOS = circleLayerDomainService.listByAreaeCodeList(Arrays.asList(areaDTO.getAreaCode()));
        Map<String, List<CircleLayerDTO>> areaCode2CircleLayerMap =
                Optional.ofNullable(circleLayerDTOS).orElse(new ArrayList<>()).stream().collect(Collectors.groupingBy(CircleLayerDTO::getAreaCode));

        List<CircleLayerDTO> circleLayerDTOList = areaCode2CircleLayerMap.get(areaDTO.getAreaCode());
        areaDTO.setCircleLayerList(circleLayerDTOList);
        return areaDTO;
    }

    @Override
    public CommonPage<AreaDTO> pageArea(AreaCondition condition) {
        IPage<AreaDO> iPage = new Page<>(condition.getPageNum(), condition.getPageSize());
        LambdaQueryWrapper<AreaDO> pageQuery = Wrappers.lambdaQuery(AreaDO.class).like(StringUtils.isNotEmpty(condition.getAreaName()), AreaDO::getAreaName, condition.getAreaName()).orderByAsc(AreaDO::getGmtCreated);

        iPage = areaService.page(iPage, pageQuery);

        if(iPage.getTotal() == 0) {
            return CommonPage.buildPage(condition.getPageNum(), condition.getPageSize(), 0, 0L, new ArrayList<>());
        }
        List<AreaDO> areaDOList = iPage.getRecords();

        List<String> areaCodeList = areaDOList.stream().map(AreaDO::getAreaCode).collect(Collectors.toList());
        List<CircleLayerDTO> circleLayerDTOS = circleLayerDomainService.listByAreaeCodeList(areaCodeList);
        Map<String, List<CircleLayerDTO>> areaCode2CircleLayerMap =
                Optional.ofNullable(circleLayerDTOS).orElse(new ArrayList<>()).stream().collect(Collectors.groupingBy(CircleLayerDTO::getAreaCode));

        List<AreaDTO> areaDTOList = AreaConverter.toDTOList(areaDOList);
        areaDTOList.forEach(areaDTO -> {
            List<CircleLayerDTO> circleLayerDTOList = areaCode2CircleLayerMap.get(areaDTO.getAreaCode());
            areaDTO.setCircleLayerList(circleLayerDTOList);
        });
        return CommonPage.buildPage(condition.getPageNum(),
                condition.getPageSize(),
                (int) iPage.getPages(),
                iPage.getTotal(),
                areaDTOList);
    }



    @Override
    public List<AreaDTO> searchAreasByName(String name) {
        QueryWrapper<AreaDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("area_name", name);
        List<AreaDO> areaDOList = areaService.list(queryWrapper);
        return AreaConverter.toDTOList(areaDOList);
    }


    @Override
    public AreaDTO getAreaByName(String name) {
        QueryWrapper<AreaDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("area_name", name);
        List<AreaDO> areaDOList = areaService.list(queryWrapper);
        if (areaDOList.isEmpty()) {
            return null;
        }
        return AreaConverter.toDTOList(areaDOList).get(0);
    }

    @Override
    public List<AreaDTO> getAreaByNameList(List<String> nameList) {
        QueryWrapper<AreaDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("area_name", nameList);
        List<AreaDO> areaDOList = areaService.list(queryWrapper);
        if (areaDOList.isEmpty()) {
            return null;
        }
        return AreaConverter.toDTOList(areaDOList);
    }

    @Override
    public AreaDTO updateArea(AreaDTO area) {
        // 用Assert校验area的合法性
        Assert.notNull(area);
        Assert.notNull(area.getAreaCode());
        // 校验坐标数据的合法性
        if (area.getGeo() != null) {
            Assert.isTrue(WktUtil.isPolygon(area.getGeo()), "geo数据不合法");
        }
        if (area.getGeoBig() != null) {
            Assert.isTrue(WktUtil.isPolygon(area.getGeoBig()), "geoBig数据不合法");
        }

        // 更新
        areaService.updatebByAreaCode(AreaConverter.toDO(area));

        // 查询
        return getAreaByCode(area.getAreaCode());
    }
}
