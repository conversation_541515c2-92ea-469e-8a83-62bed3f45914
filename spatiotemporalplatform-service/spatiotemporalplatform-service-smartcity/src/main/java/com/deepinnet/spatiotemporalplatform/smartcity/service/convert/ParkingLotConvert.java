package com.deepinnet.spatiotemporalplatform.smartcity.service.convert;

import com.deepinnet.spatiotemporalplatform.model.parkinglot.ParkingLotDTO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.ParkingLotDO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

import java.util.List;

/**
 * ParkingLotConvert
 * Author: chenkaiyang
 * Date: 2024/8/2
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface ParkingLotConvert {



    List<ParkingLotDTO> toDTOS(List<ParkingLotDO> parkingLotDOS);

    ParkingLotDTO toDTO(ParkingLotDO parkingLotDO);

    ParkingLotDO toDO(ParkingLotDTO parkingLotDTO);
}
