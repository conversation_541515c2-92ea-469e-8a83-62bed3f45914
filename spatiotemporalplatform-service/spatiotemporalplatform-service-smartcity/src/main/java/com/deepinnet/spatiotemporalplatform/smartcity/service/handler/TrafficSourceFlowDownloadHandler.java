package com.deepinnet.spatiotemporalplatform.smartcity.service.handler;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.spatiotemporalplatform.model.hotod.SourceDestinationDetail;
import com.deepinnet.spatiotemporalplatform.model.hotod.SourceDestinationDetailRequest;
import com.deepinnet.spatiotemporalplatform.model.hotod.SourceDestinationDetailUrlParams;
import com.deepinnet.spatiotemporalplatform.base.enums.BizEnum;
import com.deepinnet.spatiotemporalplatform.base.ftp.FtpUtil;
import com.deepinnet.spatiotemporalplatform.base.ftp.AbstractFtpDownloadHandler;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.time.LocalDateTime;

/**
 * <p>
 *    车流来源地 下载处理器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/14
 */

@Component
public class TrafficSourceFlowDownloadHandler extends AbstractFtpDownloadHandler<SourceDestinationDetailRequest> {

    @Override
    public BizEnum getBizEnum() {
        return BizEnum.REAL_TIME_TRAFFIC_SOURCE_FLOW;
    }

    @Override
    protected String buildFilePath(SourceDestinationDetailRequest request) {

        SourceDestinationDetailUrlParams params = request.getUrlParams();

        return getBizEnum().getCode() + "_" + params.getId() + "_" + LocalDateTime.now().toLocalDate().toString();
    }

    @Override
    protected String resolveFileName(String filePath, SourceDestinationDetailRequest request) {

        SourceDestinationDetailUrlParams params = request.getUrlParams();

        String lastFile = FtpUtil.getLastFileByPath(filePath);

        if (StrUtil.isBlank(lastFile)) {
            LogUtil.warn("[车流来源地]-当前文件夹:{}, 不存在", filePath);
            return null;
        }

        String[] parts = lastFile.split("_");
        String secondLast = parts[parts.length - 1];

        String fileName = getBizEnum().getCode() + "_"
                + params.getId() + "_"
                + params.getSourceType() + "_"
                + secondLast;

        // 判断文件是否存在
        boolean exist = FtpUtil.fileExist(filePath, fileName);
        if (!exist) {
            return null;
        }

        return fileName;
    }

    @Override
    protected Type getResponseType() {
        return new TypeReference<SourceDestinationDetail>() {}.getType();
    }
}
