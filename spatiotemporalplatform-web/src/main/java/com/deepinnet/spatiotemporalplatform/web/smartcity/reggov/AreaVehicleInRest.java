package com.deepinnet.spatiotemporalplatform.web.smartcity.reggov;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.vehicle.AreaVehicleInLink;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.vehicle.AreaVehicleInRequest;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.vehicle.AreaVehicleInUrlParams;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**

 *
 * <AUTHOR>
 * @since 2024-08-03 星期六
 **/
@RestController
@RequestMapping("/stpf/area/channel")
@CrossOrigin
@Api(tags = "区域车辆")
@SuppressWarnings("unchecked")
public class AreaVehicleInRest {

    @Resource
    private CommonDataService commonDataService;

    @ApiOperation("区域车辆流入通道热度")
    @PostMapping("/inflow/heat")
    public Result<List<AreaVehicleInLink>> inflowHeat(@RequestBody AreaVehicleInUrlParams areaVehicleInUrlParams) {
        AreaVehicleInRequest areaVehicleInRequest = new AreaVehicleInRequest();
        areaVehicleInRequest.setUrlParams(areaVehicleInUrlParams);
        return Result.success((List<AreaVehicleInLink>) commonDataService.fetchData(areaVehicleInRequest));
    }

}
