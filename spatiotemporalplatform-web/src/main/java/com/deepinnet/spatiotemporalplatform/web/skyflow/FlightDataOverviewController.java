package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.skyflow.service.FlightDataOverviewService;
import com.deepinnet.spatiotemporalplatform.vo.FlightDataOverviewVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 飞行数据概览REST接口
 *
 * <AUTHOR> wong
 * @create 2025/01/14
 */
@RestController
@RequestMapping("/stpf/flight/data/overview")
@RequiredArgsConstructor
@Api(tags = "[低空经济] => 飞行数据概览管理")
public class FlightDataOverviewController {

    private final FlightDataOverviewService flightDataOverviewService;

    /**
     * 获取最新的飞行数据概览
     *
     * @return 最新的飞行数据概览
     */
    @GetMapping("/latest")
    @ApiOperation(value = "[飞行数据概览接口] => 获取最新的飞行数据概览")
    public Result<FlightDataOverviewVO> getLatestFlightDataOverview() {
        FlightDataOverviewVO overview = flightDataOverviewService.getLatestFlightDataOverview();
        return Result.success(overview);
    }
} 