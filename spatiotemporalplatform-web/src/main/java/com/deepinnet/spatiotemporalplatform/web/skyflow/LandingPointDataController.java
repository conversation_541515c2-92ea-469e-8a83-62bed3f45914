package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.LandingPointQueryDTO;
import com.deepinnet.spatiotemporalplatform.skyflow.service.LandingPointService;
import com.deepinnet.spatiotemporalplatform.vo.LandingPointVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 起降点数据Controller
 *
 * <AUTHOR>
 */
@Api(tags = "起降点数据管理")
@RestController
@RequestMapping("/stpf/landing-point-data")
public class LandingPointDataController {

    @Resource
    private LandingPointService landingPointService;

    /**
     * 查询起降点列表
     *
     * @param queryDTO 查询条件
     * @return 起降点列表
     */
    @ApiOperation("查询起降点列表")
    @PostMapping("/list")
    public Result<List<LandingPointVO>> listLandingPoint(@RequestBody LandingPointQueryDTO queryDTO) {
        return Result.success(landingPointService.listLandingPoint(queryDTO));
    }

} 