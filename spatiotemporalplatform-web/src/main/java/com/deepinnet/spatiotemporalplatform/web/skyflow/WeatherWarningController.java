package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.*;
import com.deepinnet.spatiotemporalplatform.skyflow.service.WeatherWarningService;
import com.deepinnet.spatiotemporalplatform.skyflow.task.WeatherWarningTask;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR> wong
 * @create 2025/3/12 16:59
 * @Description
 */
@RestController
@RequestMapping("/stpf/weather")
@Api(tags = "气象监测接口")
public class WeatherWarningController {

    @Autowired(required = false)
    private WeatherWarningTask weatherWarningTask;

    @Resource
    private WeatherWarningService weatherWarningService;

    @PostMapping("/intelligence/page")
    public Result<CommonPage<IntelligenceDTO>> pageQueryIntelligenceList(@RequestBody IntelligenceQueryDTO queryDTO) {
        return Result.success(weatherWarningService.pageQueryIntelligenceDTO(queryDTO));
    }
}
