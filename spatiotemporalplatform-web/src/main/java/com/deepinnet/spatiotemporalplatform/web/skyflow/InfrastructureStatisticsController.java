package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.enums.AirType;
import com.deepinnet.spatiotemporalplatform.skyflow.service.BaseStationService;
import com.deepinnet.spatiotemporalplatform.skyflow.service.NavigateEquipmentService;
import com.deepinnet.spatiotemporalplatform.skyflow.service.LandingPointService;
import com.deepinnet.spatiotemporalplatform.skyflow.service.DetectedDeviceService;
import com.deepinnet.spatiotemporalplatform.skyflow.service.WeatherDeviceService;
import com.deepinnet.spatiotemporalplatform.vo.InfrastructureStatisticsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 基础设施建设统计REST接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/stpf/infrastructure")
@RequiredArgsConstructor
@Api(tags = "[基础设施] => 基础设施建设统计")
public class InfrastructureStatisticsController {

    private final BaseStationService baseStationService;
    private final NavigateEquipmentService navigateEquipmentService;
    private final LandingPointService landingPointService;
    private final DetectedDeviceService detectedDeviceService;
    private final WeatherDeviceService weatherDeviceService;

    /**
     * 获取基础设施建设统计数据
     *
     * @return 基础设施建设统计数据
     */
    @PostMapping("/statistics")
    @ApiOperation(value = "[基础设施统计] => 获取基础设施建设统计数据")
    public Result<InfrastructureStatisticsVO> getStatistics() {
        LogUtil.info("[基础设施统计] 开始获取基础设施建设统计数据");
        
        try {
            // 获取通信设备（基站）数量
            int communicationDeviceCount = baseStationService.countBaseStations();
            
            // 获取导航设备数量
            int navigationDeviceCount = navigateEquipmentService.countNavigateEquipments();
            
            // 获取监视设备数量
            int detectedDeviceCount = detectedDeviceService.countDetectedDevices();
            
            // 获取不同类型起降点数量
            int airportCount = landingPointService.countByAirType(AirType.AIRPORT.getCode());
            int fixedLandingPointCount = landingPointService.countByAirType(AirType.FIXED_LANDING_SITE.getCode());
            int temporaryLandingPointCount = landingPointService.countByAirType(AirType.TEMPORARY_LANDING_SITE.getCode());

            // 获取气象设备数量
            Long weatherDevicesCount = weatherDeviceService.countWeatherDevices();

            // 构建返回结果
            InfrastructureStatisticsVO statistics = InfrastructureStatisticsVO.builder()
                    .communicationDeviceCount(communicationDeviceCount)
                    .navigationDeviceCount(navigationDeviceCount)
                    .detectedDeviceCount(detectedDeviceCount)
                    .airportCount(airportCount)
                    .fixedLandingPointCount(fixedLandingPointCount)
                    .temporaryLandingPointCount(temporaryLandingPointCount)
                    .totalLandingPointCount(airportCount + fixedLandingPointCount + temporaryLandingPointCount)
                    .weatherDevicesCount(weatherDevicesCount)
                    .build();
            
            LogUtil.info("[基础设施统计] 统计数据获取成功: {}", statistics);
            return Result.success(statistics);
            
        } catch (Exception e) {
            LogUtil.error("[基础设施统计] 获取统计数据失败", e);
            throw e;
        }
    }
} 