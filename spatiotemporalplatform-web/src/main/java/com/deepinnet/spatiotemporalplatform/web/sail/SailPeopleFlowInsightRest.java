package com.deepinnet.spatiotemporalplatform.web.sail;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import com.deepinnet.spatiotemporalplatform.model.sail.*;
import com.deepinnet.spatiotemporalplatform.model.tour.insight.*;
import io.swagger.annotations.*;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-08-27 星期二
 **/
@RestController
@RequestMapping("/stpf/sail/people")
@CrossOrigin
@Api(tags = "")
public class SailPeopleFlowInsightRest {

    @Resource
    private CommonDataService commonDataService;

    @ApiOperation("人流洞察")
    @PostMapping("/flow/insight/realtime")
    public Result<List<SailPeopleFlowInsightDTO>> getRealTimePeopleFlowInsight(@RequestBody SailPeopleFlowInsightUrlParams peopleFlowInsightUrlParams) {
        SailPeopleFlowInsightRequest peopleFlowInsightRequest = new SailPeopleFlowInsightRequest();
        peopleFlowInsightRequest.setUrlParams(peopleFlowInsightUrlParams);
        peopleFlowInsightUrlParams.setIsPersistence(false);
        return Result.success((List<SailPeopleFlowInsightDTO>) commonDataService.fetchData(peopleFlowInsightRequest));
    }
}
