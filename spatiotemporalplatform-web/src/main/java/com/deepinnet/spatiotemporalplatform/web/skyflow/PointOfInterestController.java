package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.*;
import com.deepinnet.spatiotemporalplatform.skyflow.service.PointOfInterestService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *   点位信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/13
 */

@Api(tags = "poi")
@RestController
@RequestMapping("/poi")
public class PointOfInterestController {

    @Resource
    private PointOfInterestService pointOfInterestService;

    /**
     * 查询点位信息
     * @param dto
     * @return
     */
    @PostMapping("/around")
    @ApiOperation(value = "查询点位信息")
    public Result<List<Poi>> queryPoi(@RequestBody POIQueryDTO dto) {
        return Result.success(pointOfInterestService.getPoi(dto));
    }

    /**
     * 查询多边形点位信息
     * @param dto
     * @return
     */
    @PostMapping("/polygon")
    @ApiOperation(value = "查询多边形点位信息")
    public Result<List<Poi>> queryPolygonPoi(@RequestBody POIPolygonQueryDTO dto) {
        return Result.success(pointOfInterestService.getPolygonPoi(dto));
    }

    /**
     * 查询静态障碍物
     * @param dto
     * @return
     */
    @PostMapping("/static/obstacle/query")
    @ApiOperation(value = "查询静态障碍物")
    public Result<List<StaticSurfaceObstacle>> queryStaticObstacle(@RequestBody StaticSurfaceObstacleQueryDTO dto) {
        return Result.success(pointOfInterestService.getStaticObstacle(dto));
    }

}
