package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.staticpoi.StaticPoi;
import com.deepinnet.spatiotemporalplatform.model.staticpoi.StaticPoiQueryDTO;
import com.deepinnet.spatiotemporalplatform.skyflow.service.StaticPoiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 静态核心POI控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-10
 */
@RestController
@RequestMapping("/stpf/poi/static")
@Api(tags = "静态核心POI管理")
public class StaticPoiController {

    @Resource
    private StaticPoiService staticPoiService;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询静态POI")
    public Result<CommonPage<StaticPoi>> pageStaticPoi(@RequestBody StaticPoiQueryDTO queryDTO) {
        if (queryDTO == null || queryDTO.getPageNum() == null || queryDTO.getPageSize() == null) {
            return Result.fail("ILLEGAL_PARAMS", "分页参数不能为空");
        }
        return Result.success(staticPoiService.pageStaticPoi(queryDTO));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "根据POI ID获取静态POI")
    public Result<StaticPoi> getByPoiId(@RequestParam("poiId") String poiId) {
        if (poiId == null) {
            return Result.fail("ILLEGAL_PARAMS", "POI ID不能为空");
        }
        return Result.success(staticPoiService.getByPoiId(poiId));
    }

} 