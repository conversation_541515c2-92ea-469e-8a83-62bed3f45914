package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.ControlIntelligenceDTO;
import com.deepinnet.spatiotemporalplatform.dto.ControlIntelligenceQueryDTO;
import com.deepinnet.spatiotemporalplatform.skyflow.service.ControlIntelligenceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 管控情报REST接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/control/intelligence")
@RequiredArgsConstructor
@Validated
@Api(tags = "管控情报")
public class ControlIntelligenceController {

    private final ControlIntelligenceService controlIntelligenceService;

    /**
     * 分页查询管控情报
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询管控情报")
    public Result<CommonPage<ControlIntelligenceDTO>> pageQuery(@RequestBody @Valid ControlIntelligenceQueryDTO queryDTO) {
        CommonPage<ControlIntelligenceDTO> pageResult = controlIntelligenceService.pageQuery(queryDTO);
        return Result.success(pageResult);
    }

} 