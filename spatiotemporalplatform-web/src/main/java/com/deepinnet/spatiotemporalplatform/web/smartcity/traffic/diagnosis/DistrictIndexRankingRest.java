package com.deepinnet.spatiotemporalplatform.web.smartcity.traffic.diagnosis;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.common.constants.GlobalConstant;
import com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis.*;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2024-07-31
 */
@RestController
@RequestMapping("/stpf/traffic/diagnosis")
@CrossOrigin
@Api(tags = "区域交通指数API")
public class DistrictIndexRankingRest {
    @Resource
    private CommonDataService commonDataService;

    @ApiOperation("实时区域交通指数查询")
    @PostMapping("/realtime")
    public Result<List<RealTimeDistrictIndexRanking>> getRealTimeDistrictIndexRanking(@RequestBody RealTimeDistrictIndexRankingUrlParams districtIndexRankingUrlParams) {
        // 因为衢州项目因为省钱使用了衢时代的TOP排行，其他城市需要购买的是全量指数排行API
        // 深圳大鹏也用TOP排行
        RealTimeDistrictIndexRankingRequest request = new RealTimeDistrictIndexRankingAllRequest();
        if (GlobalConstant.QZ_AD_CODE.equals(districtIndexRankingUrlParams.getAdcode()) || GlobalConstant.SHENZHEN_DP_AD_CODE.equals(districtIndexRankingUrlParams.getAdcode())) {
            request = new RealTimeDistrictIndexRankingRequest();
        }
        request.setUrlParams(districtIndexRankingUrlParams);
        return Result.success((List<RealTimeDistrictIndexRanking>) commonDataService.fetchData(request));
    }

    @ApiOperation("历史区域交通指数查询")
    @PostMapping("/history")
    public Result<List<HistoryDistrictIndexRanking>> getHistoryDistrictIndexRanking(@RequestBody HistoryDistrictIndexRankingUrlParams districtIndexRankingUrlParams) {
        HistoryDistrictIndexRankingRequest request = new HistoryDistrictIndexRankingRequest();
        request.setUrlParams(districtIndexRankingUrlParams);
        return Result.success((List<HistoryDistrictIndexRanking>) commonDataService.fetchData(request));
    }

}
