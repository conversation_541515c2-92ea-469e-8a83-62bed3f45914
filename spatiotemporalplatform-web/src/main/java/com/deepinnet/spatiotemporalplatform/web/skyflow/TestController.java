package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.FileInfoDTO;
import com.deepinnet.localdata.integration.error.RepeatExecException;
import com.deepinnet.localdata.integration.model.outsidebean.FlightMonitorTaskResp;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightPlanDO;
import com.deepinnet.spatiotemporalplatform.dto.*;
import com.deepinnet.spatiotemporalplatform.enums.FlightEventTypeEnum;
import com.deepinnet.spatiotemporalplatform.skyflow.client.FlightTaskNotifyClient;
import com.deepinnet.spatiotemporalplatform.skyflow.mqtt.processor.WarningMsgProcessor;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.FlightPlanRepository;
import com.deepinnet.spatiotemporalplatform.skyflow.service.FlightReportService;
import com.deepinnet.spatiotemporalplatform.skyflow.task.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.*;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR> wong
 * @create 2025/4/12 11:21
 * @Description
 */
@RestController
@RequestMapping("/stpf/test")
@Profile("!qz-gov-inner-network")
public class TestController {

    @Autowired(required = false)
    private WeatherWarningTask weatherWarningTask;

    @Autowired(required = false)
    private WeatherDeviceTask weatherDeviceTask;

    @Autowired(required = false)
    private FlightBusinessAnalysisTask analysisTask;

    @Autowired(required = false)
    private FlightDataOverviewTask flightDataOverviewTask;

    @Autowired(required = false)
    private UnPlannedFlightDetectionTask unPlannedFlightDetectionTask;

    @Autowired(required = false)
    private RiskWarningTask riskWarningTask;

    @Resource
    private FlightTaskNotifyClient flightTaskNotifyClient;

    @Value("${inner-api.algorithm.monitor-video.url}")
    private String algorithmMonitorVideoUrl;

    @Resource
    private FlightPlanRepository flightPlanRepository;

    @Resource
    private FlightReportService flightReportService;

    @Resource
    private WarningMsgProcessor warningMsgProcessor;

    @PostMapping("/weather/task")
    public Result<Boolean> pageQueryIntelligenceList(String weatherData) {
        weatherWarningTask.process();
        return Result.success(true);
    }


    @PostMapping("/task")
    @ApiOperation(value = "测试定时任务")
    public Result<Boolean> testTask() {
        unPlannedFlightDetectionTask.process();
        return Result.success(true);
    }

    @PostMapping("/risk/task")
    @ApiOperation(value = "测试风险预警定时任务")
    public Result<Boolean> testRiskWarningTask() {
        riskWarningTask.updateRiskWarningStatus();
        return Result.success(true);
    }

    @GetMapping("/live_test/start")
    @ApiOperation(value = "算法直播测试")
    public Result<String> liveTest(@RequestParam(value = "planId") String planId
            , @RequestParam(value = "url") String url, @RequestParam(value = "flightId") String flightId) {
        FlightAlgorithmMonitorCreateDTO flightAlgorithmMonitorCreateDTO = new FlightAlgorithmMonitorCreateDTO();
        flightAlgorithmMonitorCreateDTO.setFlightTaskId(planId);
        flightAlgorithmMonitorCreateDTO.setVideoUrl(url);
        flightAlgorithmMonitorCreateDTO.setFlightId(flightId);
        List<FlightEventTypeEnum> eventTypeList = new ArrayList<>();
        eventTypeList.add(FlightEventTypeEnum.PARKING_VIOLATION);
        flightAlgorithmMonitorCreateDTO.setEventTypeList(eventTypeList);

        try {
            FlightMonitorTaskResp flightMonitorTaskResp = flightTaskNotifyClient.startFlightTask(flightAlgorithmMonitorCreateDTO);
            //算法任务启动时会返回一个检测视频流地址，需要保存到计划表里，因为计划和飞行记录是一一对应的
            String monitorUrl = String.format("%s/live/%s_alg/%s", algorithmMonitorVideoUrl, flightMonitorTaskResp.getId(), "hls.m3u8");
            flightPlanRepository.update(Wrappers.<FlightPlanDO>lambdaUpdate()
                    .eq(FlightPlanDO::getPlanId, planId)
                    .set(FlightPlanDO::getMonitorUrl, monitorUrl)
                    .set(FlightPlanDO::getGmtModified, LocalDateTime.now()));
            return Result.success(monitorUrl);
        } catch (RepeatExecException e) {
            LogUtil.warn("重复了！！！");
            throw new BizException("REPEAT", "重复了！");
        }
    }

    @GetMapping("/live_test/end")
    @ApiOperation(value = "算法直播测试")
    public Result<String> liveTestEnd(@RequestParam(value = "planId") String planId,  @RequestParam(value = "flightId") String flightId) {
        try {
            FlightTaskNotifyDTO flightTaskNotifyDTO = new FlightTaskNotifyDTO();
            flightTaskNotifyDTO.setFlightTaskId(planId);
            flightTaskNotifyDTO.setTenantId("deepinnet");
            flightTaskNotifyDTO.setFlightBatchId(flightId);
            flightTaskNotifyClient.endFlightTask(flightTaskNotifyDTO);
            //通知完成后生成飞行报告
            FlightReportGenerateDTO generateDTO = new FlightReportGenerateDTO();
            generateDTO.setPlanId(planId);
            FileInfoDTO fileInfoDTO = flightReportService.generateFlightReportDocument(generateDTO);
            if (fileInfoDTO == null) {
                return Result.success(null);
            }
            flightPlanRepository.update(Wrappers.<FlightPlanDO>lambdaUpdate()
                    .eq(FlightPlanDO::getPlanId, planId)
                    .set(FlightPlanDO::getFlightReport, fileInfoDTO.getUrl())
                    .set(FlightPlanDO::getGmtModified, LocalDateTime.now()));
            return Result.success(fileInfoDTO.getUrl());
        } catch (RepeatExecException e) {
            LogUtil.warn("重复了！！！");
            throw new BizException("REPEAT", "重复了！");
        }
    }

    @GetMapping("report")
    @ApiOperation(value = "生成飞行报告")
    public Result<String> generateFlightReport(@RequestParam(value = "planId") String planId) {
        //通知完成后生成飞行报告
        FlightReportGenerateDTO generateDTO = new FlightReportGenerateDTO();
        generateDTO.setPlanId(planId);
        FileInfoDTO fileInfoDTO = flightReportService.generateFlightReportDocument(generateDTO);
        if (fileInfoDTO == null) {
            return Result.success(null);
        }
        return Result.success(fileInfoDTO.getUrl());
    }

    @GetMapping("/weather/device")
    @ApiOperation(value = "气象设备")
    public Result<Boolean> testWeatherDevice() {
        weatherDeviceTask.syncWeatherDevices();
        return Result.success(true);
    }

    @GetMapping("/business/analysis")
    @ApiOperation(value = "业务分析")
    public Result<Boolean> getFlightBusinessAnalysis() {
        analysisTask.syncFlightBusinessAnalysisData();
        return Result.success(true);
    }

    @GetMapping("/flight/overview")
    @ApiOperation(value = "业务分析")
    public Result<Boolean> syncFlightDataOverview() {
        flightDataOverviewTask.syncFlightDataOverview();
        return Result.success(true);
    }

    @GetMapping("/risk/warning")
    @ApiOperation(value = "风险预警")
    public Result<Boolean> testRiskWarning(String msg) {
        warningMsgProcessor.handle(null, msg);
        return Result.success(true);
    }
}
