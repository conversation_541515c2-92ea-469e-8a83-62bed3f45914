package com.deepinnet.spatiotemporalplatform.web.smartcity.tour;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.road.RoadLinkFlowRequest;
import com.deepinnet.spatiotemporalplatform.model.road.RoadLinkFlowUrlParams;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**

 *
 * <AUTHOR>
 * @since 2024-08-27 星期二
 **/
@RestController
@RequestMapping("/stpf/tour")
@CrossOrigin
@Api(tags = "")
public class RoadLinkFlowRest {

    @Resource
    private CommonDataService commonDataService;

    @Value("${digest.sign.api.key}")
    private String digestSignApikey;


    @ApiOperation("实时网格客流")
    @PostMapping("/road/link/flow")
    public Result<String> heat(@RequestBody RoadLinkFlowUrlParams roadLinkFlowUrlParams) {
        roadLinkFlowUrlParams.setPubname(digestSignApikey);
        RoadLinkFlowRequest realHeatRequest = new RoadLinkFlowRequest();
        realHeatRequest.setUrlParams(roadLinkFlowUrlParams);
        return Result.success((String) commonDataService.fetchData(realHeatRequest));
    }

}
