package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.spatiotemporalplatform.dto.WeatherGridDTO;
import com.deepinnet.spatiotemporalplatform.model.common.GeoPoint;
import com.deepinnet.spatiotemporalplatform.skyflow.util.weather.LocationWeatherData;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * Creator zengjuerui
 * Date 2025-04-19
 **/


@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, imports = {GeoPoint.class})
public interface SkyflowWebConverter {


    @Mappings({
            @Mapping(target = "centerPoint", expression = "java(new GeoPoint(locationWeatherData.getCenterPoint()))")
    })
    WeatherGridDTO toWeatherGridDTO(LocationWeatherData locationWeatherData);

    List<WeatherGridDTO> toWeatherGridDTOList(List<LocationWeatherData> list);
}
