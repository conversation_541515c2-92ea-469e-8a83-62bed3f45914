package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import com.deepinnet.spatiotemporalplatform.model.skyflow.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR> wong
 * @create 2025/3/11 10:23
 * @Description
 */
@RestController
@RequestMapping("/stpf/gd")
public class GdFlightDetectionRest {

    @Resource
    private CommonDataService commonDataService;

    @ApiOperation("获取高德的飞行监测数据")
    @PostMapping("/flight/detection")
    public Result<GdFlightDetectionResponse> getGdFlightDetection(@RequestBody GdFlightDetectionParam param) {
        GdFlightDetectionRequest request = new GdFlightDetectionRequest();
        request.setUrlParams(param);
        return Result.success((GdFlightDetectionResponse) commonDataService.fetchData(request));
    }
}
