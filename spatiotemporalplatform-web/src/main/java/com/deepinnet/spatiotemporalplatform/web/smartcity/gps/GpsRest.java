package com.deepinnet.spatiotemporalplatform.web.smartcity.gps;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.gps.*;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import io.swagger.annotations.*;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * gps信息
 *
 * <AUTHOR>
 * @version 2024-11-22
 */
@RestController
@RequestMapping("/stpf/gps")
@CrossOrigin
@Api(tags = "gps接收API")
public class GpsRest {

    @Resource
    private CommonDataService commonDataService;

    @ApiOperation("查询全量GPS数据")
    @GetMapping("/queryFullData")
    public Result<List<RemoteGps>> queryFullData() {
        GpsFullQueryDataRequest request = new GpsFullQueryDataRequest();
        return Result.success((List<RemoteGps>) commonDataService.fetchData(request));
    }
}
