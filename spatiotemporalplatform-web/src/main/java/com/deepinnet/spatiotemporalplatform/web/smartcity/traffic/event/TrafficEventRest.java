package com.deepinnet.spatiotemporalplatform.web.smartcity.traffic.event;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.traffic.event.TrafficEvent;
import com.deepinnet.spatiotemporalplatform.model.traffic.event.TrafficEventRequest;
import com.deepinnet.spatiotemporalplatform.model.traffic.event.TrafficEventUrlParams;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2024-07-30
 */
@RestController
@RequestMapping("/stpf/traffic/event")
@CrossOrigin
@Api(tags = "交通事件API")
public class TrafficEventRest {
    @Resource
    private CommonDataService commonDataService;

    @ApiOperation("实时交通事件查询")
    @PostMapping("/realtime")
    public Result<List<TrafficEvent>> getRealTimeCongestion(@RequestBody TrafficEventUrlParams trafficEventUrlParams) {
        TrafficEventRequest request = new TrafficEventRequest();
        request.setUrlParams(trafficEventUrlParams);
        return Result.success((List<TrafficEvent>) commonDataService.fetchData(request));
    }

}
