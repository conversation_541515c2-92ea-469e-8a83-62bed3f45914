package com.deepinnet.spatiotemporalplatform.web.smartcity.hotod;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.hotod.*;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**

 *
 * <AUTHOR>
 * @since 2024-11-22 星期五
 **/
@RestController
@RequestMapping("/stpf/hotod")
@CrossOrigin
@Api(tags = "")
@SuppressWarnings("all")
public class HotOdController {

    @Resource
    private CommonDataService commonDataService;

    @ApiOperation("区域到达车辆数预测")
    @PostMapping("/areaArriveCarPredict")
    public Result<List<AreaArriveCarPredict>> areaArriveCarPredict(@RequestBody AreaArriveCarPredictUrlParams params) {
        AreaArriveCarPredictRequest request = new AreaArriveCarPredictRequest();
        request.setUrlParams(params);
        return Result.success((List<AreaArriveCarPredict>) commonDataService.fetchData(request));
    }

    @ApiOperation("司机目的地排名")
    @PostMapping("/driverDestinationRanking")
    public Result<DriverDestinationRanking> driverDestinationRanking(@RequestBody DriverDestinationRankingUrlParams params) {
        DriverDestinationRankingRequest request = new DriverDestinationRankingRequest();
        request.setUrlParams(params);
        return Result.success((DriverDestinationRanking) commonDataService.fetchData(request));
    }

    @ApiOperation("区域来源地/⽬的地明细")
    @PostMapping("/sourceDestinationDetail")
    public Result<SourceDestinationDetail> sourceDestinationDetail(@RequestBody SourceDestinationDetailUrlParams params) {
        SourceDestinationDetailRequest request = new SourceDestinationDetailRequest();
        request.setUrlParams(params);
        return Result.success((SourceDestinationDetail) commonDataService.fetchData(request));
    }

}
