package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.*;
import com.deepinnet.spatiotemporalplatform.skyflow.service.FlightDetectionService;
import com.deepinnet.spatiotemporalplatform.skyflow.task.*;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2025/3/4 16:36
 * @Description
 */
@RestController
@RequestMapping("/stpf/flight/detection")
@Validated
@Api(tags = "低空经济 => 飞行检测")
public class FlightDetectionController {

    @Resource
    private FlightDetectionService flightDetectionService;


    @PostMapping("/page")
    @ApiOperation(value = "分页查询飞行检测")
    public Result<CommonPage<FlightDetectionDTO>> pageFlightDetection(@Valid @RequestBody FlightDetectionQueryDTO queryDTO) {
        CommonPage<FlightDetectionDTO> commonPage = flightDetectionService.pageFlightDetection(queryDTO);
        return Result.success(commonPage);
    }

    @PostMapping("/path")
    @ApiOperation(value = "获取飞行监测完整路径")
    public Result<List<FlightPathDTO>> getFlightDetectionPath(@RequestBody FlightPathQueryDTO queryDTO) {
        return Result.success(flightDetectionService.getFlightDetectionPath(queryDTO));
    }

    @PostMapping("/position")
    @ApiOperation(value = "获取飞行监测最新位置")
    public Result<List<FlightDetectionLatestPathDTO>> getLatestFlightPosition(@RequestBody FlightDetectionLatestPositionDTO queryDTO) {
        return Result.success(flightDetectionService.getLatestFlightPosition(queryDTO));
    }

}
