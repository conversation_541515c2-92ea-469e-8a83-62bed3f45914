package com.deepinnet.spatiotemporalplatform.web.smartcity.area;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.area.CircleLayerBatchDTO;
import com.deepinnet.spatiotemporalplatform.model.area.CircleLayerDTO;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.CircleLayerDomainService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/domain/circle-layer")
@Api(tags = "后台-圈层")
public class CircleLayerAdminController {

    private final CircleLayerDomainService circleLayerDomainService;

    @Autowired
    public CircleLayerAdminController(CircleLayerDomainService circleLayerDomainService) {
        this.circleLayerDomainService = circleLayerDomainService;
    }

    @PostMapping("/all")
    @ApiOperation(value = "分页获取所有圈层")
    public Result<List<CircleLayerDTO>> getAllCircleLayers(@RequestParam int page, @RequestParam int size) {
        return Result.success(circleLayerDomainService.getAllCircleLayers(page, size));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "获取圈层详情")
    public Result<CircleLayerDTO> getCircleLayerByLayerId(@RequestParam String layerId) {
        return Result.success(circleLayerDomainService.getCircleLayerByLayerId(layerId));
    }

    @PostMapping("/batchCreate")
    @ApiOperation(value = "创建/更新圈层")
    public Result<List<CircleLayerDTO>> batchCreateOrUpdateCircleLayer(@RequestBody CircleLayerBatchDTO circleLayerBatchDTO) {
        return Result.success(circleLayerDomainService.batchCreateOrUpdateCircleLayer(circleLayerBatchDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新圈层")
    public Result<CircleLayerDTO> updateCircleLayer(@RequestBody CircleLayerDTO circleLayerDTO) {
        return Result.success(circleLayerDomainService.updateCircleLayer(circleLayerDTO));
    }

    @DeleteMapping("/delete")
    @ApiOperation(value = "删除圈层")
    public Result<Boolean> deleteCircleLayer(@RequestParam String layerId) {
        return Result.success(circleLayerDomainService.deleteCircleLayer(layerId));
    }
}
