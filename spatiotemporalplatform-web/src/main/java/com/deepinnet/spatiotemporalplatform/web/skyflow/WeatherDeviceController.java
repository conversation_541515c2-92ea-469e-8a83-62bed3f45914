package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.skyflow.service.WeatherDeviceService;
import com.deepinnet.spatiotemporalplatform.vo.WeatherDeviceVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 天气设备REST接口
 *
 * <AUTHOR> wong
 * @create 2025/01/14
 */
@RestController
@RequestMapping("/stpf/weather/device")
@RequiredArgsConstructor
@Api(tags = "[低空经济] => 天气设备管理")
public class WeatherDeviceController {

    private final WeatherDeviceService weatherDeviceService;

    /**
     * 查询所有天气设备
     *
     * @return 天气设备列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "[天气设备接口] => 查询所有天气设备")
    public Result<List<WeatherDeviceVO>> listAllWeatherDevices() {
        List<WeatherDeviceVO> weatherDeviceList = weatherDeviceService.listAllWeatherDevices();
        return Result.success(weatherDeviceList);
    }

    /**
     * 统计当前租户的天气设备总数
     *
     * @return 天气设备总数
     */
    @PostMapping("/count")
    @ApiOperation(value = "[天气设备接口] => 统计当前租户天气设备总数")
    public Result<Long> countWeatherDevices() {
        Long count = weatherDeviceService.countWeatherDevices();
        return Result.success(count);
    }
} 