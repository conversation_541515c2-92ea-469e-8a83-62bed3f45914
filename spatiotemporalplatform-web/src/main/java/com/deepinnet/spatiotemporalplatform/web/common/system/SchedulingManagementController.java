package com.deepinnet.spatiotemporalplatform.web.common.system;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 8/27/25
 */

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.web.common.config.DynamicSchedulingConfig;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 管理接口控制器
 */
@RestController
@RequestMapping("/scheduling")
@ConditionalOnProperty(name = "scheduling.management.enabled", havingValue = "true")
public class SchedulingManagementController {

    @Resource
    private DynamicSchedulingConfig schedulingConfig;

    @GetMapping("/status")
    public Result<Map<String, Object>> getStatus() {
        return Result.success(schedulingConfig.getStatus());
    }

    @PostMapping("/adjust")
    public ResponseEntity<String> manualAdjust(
            @RequestParam int targetSize,
            @RequestParam(defaultValue = "手动调整") String reason) {

        boolean success = schedulingConfig.manualAdjust(targetSize, reason);
        return success ?
                ResponseEntity.ok("调整成功") :
                ResponseEntity.status(500).body("调整失败");
    }
}
