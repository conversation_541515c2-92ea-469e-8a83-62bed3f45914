package com.deepinnet.spatiotemporalplatform.web.smartcity.congestion;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.congestion.*;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2024-07-30
 */
@RestController
@RequestMapping("/stpf/congestion")
@CrossOrigin
@Api(tags = "拥堵预警API")
public class CongestionRest {
    @Resource
    private CommonDataService commonDataService;

    @ApiOperation("实时拥堵信息")
    @PostMapping("/realtime")
    public Result<CongestionData> getRealTimeCongestion(@RequestBody RealTimeCongestionPostBody congestionPostBody) {
        RealTimeCongestionRequest request = new RealTimeCongestionRequest();
        request.setPostBody(congestionPostBody);
        CongestionUrlParams congestionUrlParams = new CongestionUrlParams();
        request.setUrlParams(congestionUrlParams);
        return Result.success((CongestionData) commonDataService.fetchData(request));
    }

    @ApiOperation("历史拥堵信息")
    @PostMapping("/history")
    public Result<CongestionData> getHistoryCongestion(@RequestBody HistoryCongestionPostBody congestionPostBody) {
        HistoryCongestionRequest request = new HistoryCongestionRequest();
        request.setPostBody(congestionPostBody);
        CongestionUrlParams congestionUrlParams = new CongestionUrlParams();
        request.setUrlParams(congestionUrlParams);
        return Result.success((CongestionData) commonDataService.fetchData(request));
    }

}
