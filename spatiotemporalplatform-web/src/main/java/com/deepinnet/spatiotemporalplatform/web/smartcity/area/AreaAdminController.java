package com.deepinnet.spatiotemporalplatform.web.smartcity.area;

import cn.hutool.core.lang.Assert;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.area.AreaDTO;
import com.deepinnet.spatiotemporalplatform.model.area.AreaCondition;
import com.deepinnet.spatiotemporalplatform.model.area.AreaQueryDTO;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.AreaDomainService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Controller for managing Area entities.
 */
@RestController
@RequestMapping("/domain/area")
@Api(tags = "后台-区域")
public class AreaAdminController {

    private final AreaDomainService areaDomainService;

    @Autowired
    public AreaAdminController(AreaDomainService areaDomainService) {
        this.areaDomainService = areaDomainService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "区域列表")
    public Result<CommonPage<AreaDTO>> pageArea(@RequestBody AreaCondition condition) {
        return Result.success(areaDomainService.pageArea(condition));
    }

    @PostMapping("/get")
    @ApiOperation(value = "通过区域编码获取区域信息")
    public Result<AreaDTO> getAreaByCode(@RequestBody AreaQueryDTO queryDTO) {
        return Result.success(areaDomainService.getAreaByCode(queryDTO.getAreaCode()));
    }

    @PostMapping("/create")
    @ApiOperation(value = "创建区域")
    public Result<AreaDTO> createArea(@RequestBody AreaDTO area) {
        return Result.success(areaDomainService.createArea(area));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新区域")
    public Result<AreaDTO> updateArea(@RequestBody AreaDTO area) {
        return Result.success(areaDomainService.updateArea(area));
    }

    @PostMapping("/delete")
    public Result<Boolean> deleteArea(@RequestBody AreaDTO areaDTO) {
        Assert.notEmpty(areaDTO.getAreaCode());
        return Result.success(areaDomainService.deleteArea(areaDTO.getAreaCode()));
    }

    @PostMapping("/getAreaByNameList")
    public Result<List<AreaDTO>> getAreaByNameList(@RequestBody List<String> nameList) {
        return Result.success(areaDomainService.getAreaByNameList(nameList));
    }

}
