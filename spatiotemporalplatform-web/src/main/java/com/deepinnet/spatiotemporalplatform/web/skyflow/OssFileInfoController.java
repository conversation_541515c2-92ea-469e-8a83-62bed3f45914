package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.common.enums.FileBusinessType;
import com.deepinnet.spatiotemporalplatform.model.skyflow.OssFileInfo;
import com.deepinnet.spatiotemporalplatform.skyflow.service.impl.OssFileInfomainService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@RestController
@RequestMapping("/api/oss-file-info")
@Api(tags = "oss文件信息")
public class OssFileInfoController {

    @Resource
    private OssFileInfomainService ossFileInfomainService;

    @GetMapping("/poi")
    @ApiOperation(value = "poi")
    public Result<OssFileInfo> getLatestByType() {
        String code = FileBusinessType.POI.getCode();
        return Result.success(ossFileInfomainService.getLatestByType(code));
    }

    @GetMapping("/obstacle")
    @ApiOperation(value = "障碍物")
    public Result<OssFileInfo> obstacle() {
        String code = FileBusinessType.OBSTACLE.getCode();
        return Result.success(ossFileInfomainService.getLatestByType(code));
    }

    @GetMapping("/traffic-vehicles")
    @ApiOperation(value = "地物车流")
    public Result<OssFileInfo> trafficVehicles() {
        String code = FileBusinessType.TRAFFIC_VEHICLES.getCode();
        return Result.success(ossFileInfomainService.getLatestByType(code));
    }

    @GetMapping("/traffic-people")
    @ApiOperation(value = "地物人流")
    public Result<OssFileInfo> trafficPeople() {
        String code = FileBusinessType.TRAFFIC_PEOPLE.getCode();
        return Result.success(ossFileInfomainService.getLatestByType(code));
    }


    @GetMapping("/wind")
    @ApiOperation(value = "风力")
    public Result<OssFileInfo> wind() {
        String code = FileBusinessType.WIND.getCode();
        return Result.success(ossFileInfomainService.getLatestByType(code));
    }



}