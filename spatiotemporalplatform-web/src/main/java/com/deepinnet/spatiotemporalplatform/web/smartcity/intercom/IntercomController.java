package com.deepinnet.spatiotemporalplatform.web.smartcity.intercom;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.intercom.IntercomGeomWkt;
import com.deepinnet.spatiotemporalplatform.model.intercom.IntercomPointRecord;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.IntercomService;
import com.deepinnet.spatiotemporalplatform.model.util.WktUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 对讲机控制器
 */
@Api(tags = "对讲机服务接口")
@RestController
@RequestMapping("/domain/intercom")
public class IntercomController {

    @Autowired
    private IntercomService intercomService;

    @ApiOperation(value = "获取区域内最近规定时间内在线的对讲机列表", notes = "根据区域编码和时间间隔获取对讲机点记录")
    @PostMapping("/points")
    public Result<List<IntercomPointRecord>> getIntercomPointList(
            @RequestParam String areaCode,
            @RequestParam Integer interval) {
        return Result.success(intercomService.getIntercomPointList(areaCode, interval));
    }

    @ApiOperation(value = "查询指定点周围一定距离内的对讲机点", notes = "根据点坐标和距离查询附近的对讲机点")
    @PostMapping("/points/nearby")
    public Result<List<IntercomPointRecord>> queryNearIntercomPoint(
            @RequestBody IntercomGeomWkt pointWkt,
            @RequestParam double distance) {
        return Result.success(intercomService.queryNearIntercomPoint(WktUtil.toPoint(pointWkt.getWkt()), distance));
    }

    @ApiOperation(value = "查询多边形区域内最近5分钟内的对讲机点", notes = "根据多边形区域查询对讲机点")
    @PostMapping("/points/polygon")
    public Result<List<IntercomPointRecord>> queryPolygonContainsWithIn5Min(
            @RequestBody IntercomGeomWkt polygonWkt) {
        return Result.success(intercomService.queryPolygonContainsWithIn5Min(polygonWkt.getWkt()));
    }
}