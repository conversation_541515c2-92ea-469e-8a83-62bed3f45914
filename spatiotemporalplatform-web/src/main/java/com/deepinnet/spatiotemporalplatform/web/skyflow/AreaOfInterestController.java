package com.deepinnet.spatiotemporalplatform.web.skyflow;

import cn.hutool.core.util.ObjectUtil;
import com.deepinnet.digitaltwin.common.error.ErrorCode;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.FlightRestrictionsAOI;
import com.deepinnet.spatiotemporalplatform.dto.GroundFeatures;
import com.deepinnet.spatiotemporalplatform.dto.GroundFeaturesQueryDTO;
import com.deepinnet.spatiotemporalplatform.skyflow.error.BizErrorCode;
import com.deepinnet.spatiotemporalplatform.skyflow.service.AreaOfInterestService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.UncategorizedSQLException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2025/1/13 09:56
 * @Description
 */
@RestController
@RequestMapping("/aoi")
@Api(tags = "aoi区域信息")
public class AreaOfInterestController {

    @Resource
    private AreaOfInterestService areaOfInterestService;

    /**
     * 获取区域信息
     * @param queryDTO
     * @return
     */
    @PostMapping("/list")
    @ApiOperation(value = "获取aoilist信息")
    public Result<List<FlightRestrictionsAOI>> getAoiList(@RequestBody GroundFeaturesQueryDTO queryDTO) {
        if (queryDTO == null || StringUtils.isBlank(queryDTO.getRegion())) {
            throw new BizException(ErrorCode.ILLEGAL_PARAMS.getCode(), ErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        return Result.success(areaOfInterestService.getAoi(queryDTO));
    }

    /**
     * 获取区域信息
     * @param queryDTO
     * @return
     */
    @PostMapping("/groundFeatures")
    @ApiOperation(value = "获取地物信息")
    public Result<List<GroundFeatures>> getGroundFeatures(@RequestBody GroundFeaturesQueryDTO queryDTO) {
        try {

            if (ObjectUtil.isNull(queryDTO.getCoordinates()) && StringUtils.isBlank(queryDTO.getBd2DGridLocationCode()) && StringUtils.isBlank(queryDTO.getRegion())) {
                throw new BizException(ErrorCode.ILLEGAL_PARAMS.getCode(), "查询条件不能为空");
            }

            return Result.success(areaOfInterestService.queryGroundFeatures(queryDTO));
        } catch (Exception e) {
            if (e instanceof BizException) {
                throw e;
            }

            if (e instanceof UncategorizedSQLException) {
                throw new BizException(BizErrorCode.INVALID_GEO_JSON.getCode(), BizErrorCode.INVALID_GEO_JSON.getDesc());
            }

            throw e;
        }
    }
}
