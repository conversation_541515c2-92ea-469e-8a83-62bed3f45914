package com.deepinnet.spatiotemporalplatform.web.skyflow;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dal.dto.*;
import com.deepinnet.spatiotemporalplatform.dto.*;
import com.deepinnet.spatiotemporalplatform.skyflow.enums.*;
import com.deepinnet.spatiotemporalplatform.skyflow.error.BizErrorCode;
import com.deepinnet.spatiotemporalplatform.skyflow.service.RiskWarningService;
import com.deepinnet.spatiotemporalplatform.skyflow.util.beidou.TimeUtil;
import com.deepinnet.spatiotemporalplatform.vo.RealTimeUavFlightVO;
import io.swagger.annotations.Api;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wong
 * @create 2025/3/4 16:36
 * @Description
 */
@RestController
@RequestMapping("/stpf/warning")
@Validated
@Api(tags = "风险预警接口")
public class RiskWarningController {

    @Resource
    private RiskWarningService riskWarningService;

    @PostMapping("/summary")
    public Result<Map<String, Integer>> getRiskWarningSummary(@Valid @RequestBody RiskWarningQueryDTO queryDTO) {
        return Result.success(getSubTypeCountMap(queryDTO));
    }

    @PostMapping("/page")
    public Result<RiskWarningSummaryDTO> getRiskWaringList(@Valid @RequestBody RiskWarningQueryDTO queryDTO) {
        if (CollUtil.isEmpty(queryDTO.getNeedWarningTypeList())) {
            throw new BizException(BizErrorCode.ILLEGAL_REQUEST.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        boolean isHistorical = queryDTO.getStartTime() != null;
        if (queryDTO.getStartTime() == null) {
            queryDTO.setStartTime(LocalDateTimeUtil.of(TimeUtil.getStartOfDay()));
        }

        if (queryDTO.getEndTime() == null) {
            queryDTO.setEndTime(LocalDateTimeUtil.of(TimeUtil.getEndOfDay()));
        }

        // 查询风险预警列表
        CommonPage<RiskWarningDTO> pageResult = riskWarningService.pageQueryRiskWarning(queryDTO);
        if (pageResult == null || CollectionUtils.isEmpty(pageResult.getList())) {
            Map<String, Integer> resultMap;
            if (isHistorical) {
                resultMap = buildEmptyTypeMap(queryDTO.getNeedWarningTypeList());
            } else {
                resultMap = buildEmptyStatusMap();
            }

            RiskWarningSummaryDTO riskWarningSummaryDTO = new RiskWarningSummaryDTO();
            riskWarningSummaryDTO.setRiskWarningCount(resultMap);
            riskWarningSummaryDTO.setRiskWarningPage(CommonPage.buildEmptyPage());
            return Result.success(riskWarningSummaryDTO);
        }

        Map<String, Integer> resultMap;
        if (isHistorical) {
            // 获取每种风险类型的总数量
            resultMap = getSubTypeCountMap(queryDTO);
        } else {
            resultMap = getStatusCountMap(queryDTO);
        }

        RiskWarningSummaryDTO riskWarningSummaryDTO = new RiskWarningSummaryDTO();
        riskWarningSummaryDTO.setRiskWarningCount(resultMap);
        riskWarningSummaryDTO.setRiskWarningPage(pageResult);
        return Result.success(riskWarningSummaryDTO);
    }

    @PostMapping("/path")
    public Result<List<RiskWarningPathDTO>> getRiskWaringFlightPath(@RequestBody RiskWarningPathQueryDTO queryDTO) {
        return Result.success(riskWarningService.getRiskWarningPath(queryDTO));
    }

    @PostMapping("/history/path")
    public Result<List<RiskWarningPathDTO>> getHistoryRiskWaringFlightPath(@RequestBody RiskWarningPathQueryDTO queryDTO) {
        if (queryDTO == null || CollUtil.isEmpty(queryDTO.getWarningNos())) {
            throw new BizException(BizErrorCode.ILLEGAL_REQUEST.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        return Result.success(riskWarningService.getHistoryRiskWarningPath(queryDTO));
    }

    @PostMapping("/position")
    public Result<List<RealTimeUavFlightVO>> getNewestFlightPosition(@RequestBody RiskWarningPositionQueryDTO queryDTO) {
        return Result.success(riskWarningService.getNewestFlightPosition(queryDTO));
    }

    @GetMapping("/listByPlanId")
    public Result<List<RiskWarningDTO>> getRiskWarningListByPlanId(@RequestParam(value = "planId") String planId) {
        RiskWarningQueryDTO riskWarningQueryDTO = new RiskWarningQueryDTO();
        riskWarningQueryDTO.setPlanId(planId);
        return Result.success(riskWarningService.getRiskWarningList(riskWarningQueryDTO));
    }

    private Map<String, Integer> buildEmptyTypeMap(List<String> needWarningTypeList) {
        Map<String, Integer> resultMap = new HashMap<>();
        for (String type : needWarningTypeList) {
            resultMap.put(type, 0);
        }
        return resultMap;
    }

    private Map<String, Integer> buildEmptyStatusMap() {
        Map<String, Integer> resultMap = new HashMap<>();
        resultMap.put(WarningStatusEnum.ACTIVE.getCode(), 0);
        resultMap.put(WarningStatusEnum.ENDED.getCode(), 0);
        resultMap.put("total", 0);
        return resultMap;
    }

    private Map<String, Integer> getSubTypeCountMap(RiskWarningQueryDTO queryDTO) {
        Map<String, Integer> resultMap = new HashMap<>();
        List<SubTypeCountDTO> subTypeCountList = riskWarningService.getSubTypeCountList(queryDTO);
        subTypeCountList.forEach(subType -> resultMap.put(subType.getSubType(), subType.getCount()));

        List<String> needWarningTypeList = queryDTO.getNeedWarningTypeList();
        for (String type : needWarningTypeList) {
            if (!resultMap.containsKey(type)) {
                resultMap.put(type, 0);
            }
        }
        return resultMap;
    }

    private Map<String, Integer> getStatusCountMap(RiskWarningQueryDTO queryDTO) {
        List<String> statusList = Arrays.stream(WarningStatusEnum.values())
                .map(WarningStatusEnum::getCode)
                .collect(Collectors.toList());

        List<StatusCountDTO> statusCountList = riskWarningService.getStatusCountList(queryDTO);
        Map<String, Integer> resultMap = new HashMap<>();
        AtomicReference<Integer> total = new AtomicReference<>(0);
        statusCountList.forEach(type -> {
            resultMap.put(type.getStatus(), type.getCount());
            total.updateAndGet(v -> v + type.getCount());
        });

        resultMap.put("total", total.get());
        statusList.forEach(e -> {
            if (!resultMap.containsKey(e)) {
                resultMap.put(e, 0);
            }
        });
        return resultMap;
    }
}
