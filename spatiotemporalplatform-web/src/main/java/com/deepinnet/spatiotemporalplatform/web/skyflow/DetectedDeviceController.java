package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.skyflow.service.DetectedDeviceService;
import com.deepinnet.spatiotemporalplatform.vo.DetectedDeviceVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 侦测设备REST接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/stpf/detected/device")
@RequiredArgsConstructor
@Api(tags = "[侦测设备] => 侦测设备管理")
public class DetectedDeviceController {

    private final DetectedDeviceService detectedDeviceService;

    /**
     * 分页查询侦测设备数据
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 分页数据
     */
    @PostMapping("/page")
    @ApiOperation(value = "[侦测设备接口] => 分页查询侦测设备数据")
    public Result<CommonPage<DetectedDeviceVO>> pageQueryDetectedDevices(
            @ApiParam(value = "页码", required = true) @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页大小", required = true) @RequestParam(defaultValue = "10") Integer pageSize) {
        LogUtil.info("[侦测设备查询] 接收到分页查询请求，pageNum: {}, pageSize: {}", pageNum, pageSize);
        CommonPage<DetectedDeviceVO> page = detectedDeviceService.pageQueryDetectedDevices(pageNum, pageSize);
        return Result.success(page);
    }

    /**
     * 查询所有侦测设备数据
     *
     * @return 侦测设备数据列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "[侦测设备接口] => 查询所有侦测设备数据")
    public Result<List<DetectedDeviceVO>> listAllDetectedDevices() {
        LogUtil.info("[侦测设备查询] 接收到查询所有数据请求");
        List<DetectedDeviceVO> list = detectedDeviceService.listAllDetectedDevices();
        return Result.success(list);
    }

    /**
     * 统计侦测设备总数
     *
     * @return 侦测设备总数
     */
    @PostMapping("/count")
    @ApiOperation(value = "[侦测设备接口] => 统计侦测设备总数")
    public Result<Integer> countDetectedDevices() {
        LogUtil.info("[侦测设备统计] 接收到统计请求");
        int count = detectedDeviceService.countDetectedDevices();
        return Result.success(count);
    }
} 