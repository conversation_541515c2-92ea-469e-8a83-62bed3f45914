package com.deepinnet.spatiotemporalplatform.web.smartcity.tour;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.tour.flow.PassengerFlow;
import com.deepinnet.spatiotemporalplatform.model.tour.flow.PassengerFlowRequest;
import com.deepinnet.spatiotemporalplatform.model.tour.flow.PassengerFlowUrlParams;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**

 *
 * <AUTHOR>
 * @since 2024-08-27 星期二
 **/
@RestController
@RequestMapping("/stpf/tour")
@CrossOrigin
@Api(tags = "")
public class PassengerFlowRest {

    @Resource
    private CommonDataService commonDataService;

    @ApiOperation("实时客流")
    @PostMapping("/passenger/flow")
    public Result<PassengerFlow> flow(@RequestBody PassengerFlowUrlParams passengerFlowUrlParams) {
        PassengerFlowRequest passengerFlowRequest = new PassengerFlowRequest();
        passengerFlowRequest.setUrlParams(passengerFlowUrlParams);
        return Result.success((PassengerFlow) commonDataService.fetchData(passengerFlowRequest));
    }

}
