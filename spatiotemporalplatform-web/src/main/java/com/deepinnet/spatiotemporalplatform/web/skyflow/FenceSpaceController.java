package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FenceSpaceDO;
import com.deepinnet.spatiotemporalplatform.dto.FenceSpacePageQueryDTO;
import com.deepinnet.spatiotemporalplatform.model.skyflow.FenceSpace;
import com.deepinnet.spatiotemporalplatform.skyflow.service.FenceSpaceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 禁飞区/管制区管理控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@RestController
@RequestMapping("/stpf/skyflow/fencespace")
@Api(tags = "禁飞区/管制区管理")
public class FenceSpaceController {

    @Resource
    private FenceSpaceService fenceSpaceService;

    @PostMapping("/create")
    @ApiOperation(value = "创建禁飞区/管制区")
    public Result<String> createFenceSpace(@RequestBody FenceSpace fenceSpace) {
        if (fenceSpace == null) {
            return Result.fail("ILLEGAL_PARAMS", "禁飞区/管制区信息不能为空");
        }
        return Result.success(fenceSpaceService.createFenceSpace(fenceSpace));
    }

    @PostMapping("/page")
    @ApiOperation(value = "分页查询禁飞区/管制区")
    public Result<CommonPage<FenceSpace>> pageQueryFenceSpace(@RequestBody FenceSpacePageQueryDTO queryDTO) {
        if (queryDTO == null || queryDTO.getPageNum() == null || queryDTO.getPageSize() == null) {
            return Result.fail("ILLEGAL_PARAMS", "分页参数不能为空");
        }
        return Result.success(fenceSpaceService.pageQueryFenceSpace(queryDTO));
    }

    @GetMapping("/detail")
    @ApiOperation(value = "根据编号查询禁飞区/管制区详情")
    public Result<FenceSpace> getFenceSpaceByFenceNum(@RequestParam("fenceNum") String fenceNum) {
        if (fenceNum == null) {
            return Result.fail("ILLEGAL_PARAMS", "禁飞区/管制区编号不能为空");
        }
        return Result.success(fenceSpaceService.getFenceSpaceByFenceNum(fenceNum));
    }
} 