package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.skyflow.WeatherGridPostBody;
import com.deepinnet.spatiotemporalplatform.skyflow.service.WeatherGridService;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2025/3/11 10:23
 * @Description
 */
@RestController
@RequestMapping("/stpf/weather")
@Profile("!qz-gov-inner-network")
public class WeatherGridRest {

    @Resource
    private WeatherGridService weatherGridService;

    @ApiOperation("获取天气网格数据")
    @PostMapping("/grid")
    public Result<List<String>> queryWeatherGridData(@RequestBody WeatherGridPostBody weatherGridPostBody) {
        List<String> fileUrls = weatherGridService.processWeatherGridDataAndGenerateFiles(weatherGridPostBody);
        return Result.success(fileUrls);
    }
}
