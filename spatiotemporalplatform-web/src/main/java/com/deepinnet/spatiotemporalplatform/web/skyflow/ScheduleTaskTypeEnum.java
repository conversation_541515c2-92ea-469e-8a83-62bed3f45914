package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.spatiotemporalplatform.common.ScheduleTaskExecService;
import com.deepinnet.spatiotemporalplatform.skyflow.algorithm.AlgorithmGenerateReportSchedulerTask;
import com.deepinnet.spatiotemporalplatform.skyflow.algorithm.ThirdPartyEventDataSchedulerTask;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description:
 * Date: 2025/8/26
 * Author: lijunheng
 */
@Getter
@AllArgsConstructor
public enum ScheduleTaskTypeEnum {

    THIRD_PARTY_EVENT_DATA_SCHEDULER("三方算法事件同步任务", ThirdPartyEventDataSchedulerTask.class),
    ALGORITHM_GENERATE_REPORT_SCHEDULER("生成算法事件报告", AlgorithmGenerateReportSchedulerTask.class),
    ;

    private final String name;

    private final Class<? extends ScheduleTaskExecService> clazz;
}
