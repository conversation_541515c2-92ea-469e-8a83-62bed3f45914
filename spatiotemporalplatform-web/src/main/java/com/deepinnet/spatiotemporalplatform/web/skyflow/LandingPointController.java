package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.AerodromeQueryDTO;
import com.deepinnet.spatiotemporalplatform.model.skyflow.LandingPoint;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.AerodromeRepository;
import com.deepinnet.spatiotemporalplatform.skyflow.service.impl.AerodromeService;
import org.springframework.beans.factory.annotation.Autowired;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025-03-20
 */
@Api(tags = "[低空经济] => 起降点")
@RestController
@RequestMapping("/stpf/skyflow/landing")
@Validated
public class LandingPointController {

    @Resource
    private AerodromeRepository aerodromeRepository;
    @Resource
    private AerodromeService aerodromeService;

    @PostMapping("/points/page")
    @ApiOperation(value = "分页查询起降场")
    public Result<CommonPage<LandingPoint>> pageLandingPoint(@RequestBody AerodromeQueryDTO queryDTO) {
        return Result.success(aerodromeService.pageLandingPoint(queryDTO));
    }

    @GetMapping("/points")
    @ApiOperation(value = "根据代码查询空域详情")
    public Result<List<LandingPoint>> queryAllLandingPoint() {
        var list = aerodromeRepository.queryAllLandingPoint();
        return Result.success(list);
    }
}
