package com.deepinnet.spatiotemporalplatform.web.smartcity.parkinglot;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.parkinglot.AreaParkingLotSummaryDTO;
import com.deepinnet.spatiotemporalplatform.model.parkinglot.ParkingLotDTO;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.ParkingLotDomainService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/domain/parking/lots")
@Api(tags = "停车场")
public class ParkingLotController {

    @Resource
    private ParkingLotDomainService parkingLotService;

    @PostMapping("/getParkingLotsByType")
    @ApiOperation(value = "停车场列表")
    public Result<AreaParkingLotSummaryDTO> getParkingLotsByType(@RequestParam String areaCode, @RequestParam(required = false) String type) {
        return Result.success(parkingLotService.getParkingLotsByType(areaCode, type));
    }


    //新增停车场
    @PostMapping("/create")
    @ApiOperation(value = "新增停车场")
    public Result<ParkingLotDTO> addParkingLot(@RequestBody ParkingLotDTO parkingLotDTO) {
        return Result.success(parkingLotService.create(parkingLotDTO));
    }
}