package com.deepinnet.spatiotemporalplatform.web.smartcity.tour;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.tour.group.GroupFlowDetect;
import com.deepinnet.spatiotemporalplatform.model.tour.group.GroupFlowDetectRequest;
import com.deepinnet.spatiotemporalplatform.model.tour.group.GroupFlowDetectUrlParams;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**

 *
 * <AUTHOR>
 * @since 2024-08-27 星期二
 **/
@RestController
@RequestMapping("/stpf/tour")
@CrossOrigin
@Api(tags = "")
public class GroupFlowDetectRest {

    @Resource
    private CommonDataService commonDataService;

    @ApiOperation("客流监测洞察")
    @PostMapping("/group/flow/detect")
    public Result<GroupFlowDetect> detect(@RequestBody GroupFlowDetectUrlParams groupFlowDetectUrlParams) {
        GroupFlowDetectRequest groupFlowDetectRequest = new GroupFlowDetectRequest();
        groupFlowDetectRequest.setUrlParams(groupFlowDetectUrlParams);
        return Result.success((GroupFlowDetect) commonDataService.fetchData(groupFlowDetectRequest));
    }

}
