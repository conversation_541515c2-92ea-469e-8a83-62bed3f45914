package com.deepinnet.spatiotemporalplatform.web.smartcity.tour;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.tour.portrait.RealPortrait;
import com.deepinnet.spatiotemporalplatform.model.tour.portrait.RealPortraitRequest;
import com.deepinnet.spatiotemporalplatform.model.tour.portrait.RealPortraitUrlParams;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**

 *
 * <AUTHOR>
 * @since 2024-08-27 星期二
 **/
@RestController
@RequestMapping("/stpf/tour")
@CrossOrigin
@Api(tags = "")
public class RealPortraitRest {

    @Resource
    private CommonDataService commonDataService;

    @ApiOperation("实时网格客流")
    @PostMapping("/real/portrait")
    public Result<RealPortrait> realPortrait(@RequestBody RealPortraitUrlParams realPortraitUrlParams) {
        RealPortraitRequest realPortraitRequest = new RealPortraitRequest();
        realPortraitRequest.setUrlParams(realPortraitUrlParams);
        return Result.success((RealPortrait) commonDataService.fetchData(realPortraitRequest));
    }

}
