package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.skyflow.service.NavigateEquipmentService;
import com.deepinnet.spatiotemporalplatform.vo.NavigateEquipmentVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 导航设备Controller
 *
 * <AUTHOR>
 */
@Api(tags = "导航设备管理")
@RestController
@RequestMapping("/stpf/navigate/equipment")
public class NavigateEquipmentController {

    @Resource
    private NavigateEquipmentService navigateEquipmentService;

    /**
     * 查询导航设备列表
     *
     * @return 设备列表
     */
    @ApiOperation("查询导航设备列表")
    @PostMapping("/list")
    public Result<List<NavigateEquipmentVO>> listNavigateEquipment() {
        return Result.success(navigateEquipmentService.listAllNavigateEquipments());
    }

} 