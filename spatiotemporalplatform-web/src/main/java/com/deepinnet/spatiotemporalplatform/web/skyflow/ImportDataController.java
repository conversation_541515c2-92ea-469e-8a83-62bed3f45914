package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.skyflow.flush.GenerateGridPolygonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR> wong
 * @create 2025/1/14 16:25
 * @Description
 */
@RestController
@RequestMapping("/import")
@Api(tags = "导入网格数据")
public class ImportDataController {

    @Resource
    private GenerateGridPolygonService generateGridPolygonService;

    @PostMapping("/generate")
    @ApiOperation(value = "生成网格")
    public Result<Boolean> generateGridPolygon(MultipartFile file, String tableName) {
        generateGridPolygonService.generateGridPolygonFromFile(file, tableName);
        return Result.success(true);
    }
}
