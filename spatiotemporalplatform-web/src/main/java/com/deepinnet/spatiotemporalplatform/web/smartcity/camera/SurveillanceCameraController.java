package com.deepinnet.spatiotemporalplatform.web.smartcity.camera;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.localdata.integration.model.input.MonitorPlaybackQueryDTO;
import com.deepinnet.localdata.integration.model.input.MonitorRecordQueryDTO;
import com.deepinnet.localdata.integration.model.input.RealMonitorQueryDTO;
import com.deepinnet.localdata.integration.model.output.MonitorRecordResponseDTO;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.SurveillanceCameraService;
import com.deepinnet.spatiotemporalplatform.model.camera.KeyMonitorQueryDTO;
import com.deepinnet.spatiotemporalplatform.model.camera.MarkKeyMonitorDTO;
import com.deepinnet.spatiotemporalplatform.model.camera.SurveillanceCamera;
import com.deepinnet.spatiotemporalplatform.model.camera.SurveillanceCameraCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.List;

@RestController
@RequestMapping("/domain/camera")
@Api(tags = "摄像头")
public class SurveillanceCameraController {

    @Resource
    private SurveillanceCameraService surveillanceCameraService;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询摄像头")
    public Result<CommonPage<SurveillanceCamera>> listPage(@RequestBody SurveillanceCameraCondition condition) {
        return Result.success(surveillanceCameraService.listPage(condition));
    }

    @PostMapping("/{code}")
    @ApiOperation(value = "根据code查询摄像头")
    public Result<SurveillanceCamera> getByCodeAndType(@PathVariable String code, @RequestParam String type) {
        return Result.success(surveillanceCameraService.getByCodeAndType(code, type));
    }

    @PostMapping("/area")
    @ApiOperation(value = "根据区域code查询摄像头")
    public Result<List<SurveillanceCamera>> listByAreaCodeAndType(@RequestParam String areaCode, @RequestParam String type) {
        return Result.success(surveillanceCameraService.listByAreaCodeAndType(areaCode, type));
    }

    @PostMapping("/area/near")
    @ApiOperation(value = "根据区域code查询附近摄像头")
    public Result<List<SurveillanceCamera>> queryAreaNearCamera(@RequestBody SurveillanceCameraCondition condition) {
        return Result.success(surveillanceCameraService.queryAreaNearCamera(condition));
    }

    @PostMapping("/mark-key-monitor")
    @ApiOperation(value = "标记监控")
    public Result<Boolean> markKeyMonitor(@RequestBody MarkKeyMonitorDTO markKeyMonitorDTO) {
        surveillanceCameraService.markKeyMonitor(markKeyMonitorDTO);
        return Result.success(true);
    }

    @PostMapping("/key-monitor")
    @ApiOperation(value = "获取监控信息")
    public Result<List<SurveillanceCamera>> getKeyMonitorInfo(@RequestBody KeyMonitorQueryDTO keyMonitorQueryDTO) {
        return Result.success(surveillanceCameraService.getKeyMonitorInfo(keyMonitorQueryDTO));
    }

    @PutMapping("/key-monitor")
    @ApiOperation(value = "更新监控信息")
    public Result<Boolean> updateKeyMonitor(@RequestBody MarkKeyMonitorDTO markKeyMonitorDTO) {
        surveillanceCameraService.updateKeyMonitor(markKeyMonitorDTO);
        return Result.success(true);
    }

    @DeleteMapping("/key-monitor")
    @ApiOperation(value = "删除监控信息")
    public Result<Boolean> deleteKeyMonitor(@RequestBody MarkKeyMonitorDTO markKeyMonitorDTO) {
        surveillanceCameraService.deleteKeyMonitor(markKeyMonitorDTO);
        return Result.success(true);
    }

    @PostMapping("/real-monitor-url")
    @ApiOperation(value = "获取实时监控url")
    public Result<String> getRealMonitorUrlByCode(@RequestBody RealMonitorQueryDTO queryDTO) {
        return Result.success(surveillanceCameraService.getRealMonitorUrlByCode(queryDTO));
    }

    @PostMapping("/playback-url")
    @ApiOperation(value = "获取播放url")
    public Result<String> getPlaybackUrl(@RequestBody MonitorPlaybackQueryDTO queryDTO) {
        return Result.success(surveillanceCameraService.getPlaybackUrl(queryDTO));
    }

    @PostMapping("/record")
    @ApiOperation(value = "获取录像")
    public Result<MonitorRecordResponseDTO> getRecord(@RequestBody MonitorRecordQueryDTO queryDTO) {
        return Result.success(surveillanceCameraService.getRecord(queryDTO));
    }

    @PostMapping("/import")
    @ApiOperation(value = "导入摄像头")
    public Result<Boolean> importCamera(@RequestParam("file") MultipartFile file) throws Exception {
        try (InputStream inputStream = file.getInputStream()) {
            surveillanceCameraService.importCamera(inputStream);
        }
        return Result.success(true);
    }

    @PostMapping("/save-batch")
    @ApiOperation(value = "批量保存摄像头")
    public Result<Boolean> saveBatch(@RequestBody List<SurveillanceCamera> cameraList) {
        surveillanceCameraService.saveBatch(cameraList);
        return Result.success(true);
    }
}