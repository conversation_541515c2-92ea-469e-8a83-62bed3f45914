package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.ViolationDetectionDTO;
import com.deepinnet.spatiotemporalplatform.dto.ViolationDetectionQueryDTO;
import com.deepinnet.spatiotemporalplatform.dto.ViolationGroupStatisticsDTO;
import com.deepinnet.spatiotemporalplatform.skyflow.service.ViolationDetectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 违规检测Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/violation")
@Slf4j
@Api(tags ="违规检测")
public class ViolationDetectionController {

    @Resource
    private ViolationDetectionService violationDetectionService;

    /**
     * 分页查询违规统计信息
     *
     * @param queryDTO 查询参数
     * @return 分页结果
     */
    @PostMapping("/summary")
    @ApiOperation(value = "分页查询违规统计信息")
    public Result<List<ViolationGroupStatisticsDTO>> pageQueryStatistics(@RequestBody @Valid ViolationDetectionQueryDTO queryDTO) {
        if (queryDTO.getStartTime() == null) {
            queryDTO.setStartTime(LocalDateTime.of(LocalDate.now(), LocalTime.MIDNIGHT));
        }

        if (queryDTO.getEndTime() == null) {
            queryDTO.setEndTime(LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
        }

        return Result.success(violationDetectionService.groupByOperatingEntityAndType(queryDTO));
    }

    /**
     * 分页查询违规检测详情
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @PostMapping("/detail/page")
    @ApiOperation(value = "分页查询违规检测详情")
    public Result<CommonPage<ViolationDetectionDTO>> pageQueryDetail(@RequestBody @Validated ViolationDetectionQueryDTO queryDTO) {
        CommonPage<ViolationDetectionDTO> result = violationDetectionService.pageQueryDetail(queryDTO);
        return Result.success(result);
    }

} 