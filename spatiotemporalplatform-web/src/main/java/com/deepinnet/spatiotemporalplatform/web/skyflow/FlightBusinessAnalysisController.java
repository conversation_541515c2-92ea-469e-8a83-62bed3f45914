package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.localdata.integration.model.input.FlightBusinessAnalysisQueryDTO;
import com.deepinnet.spatiotemporalplatform.dto.FlightBusinessAnalysisSpaQueryDTO;
import com.deepinnet.spatiotemporalplatform.skyflow.service.FlightBusinessAnalysisService;
import com.deepinnet.spatiotemporalplatform.vo.FlightBusinessAnalysisVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 飞行业务分析REST接口
 *
 * <AUTHOR> wong
 * @create 2025/01/14
 */
@RestController
@RequestMapping("/stpf/flight/business/analysis")
@RequiredArgsConstructor
@Api(tags = "[低空经济] => 飞行业务分析管理")
public class FlightBusinessAnalysisController {

    private final FlightBusinessAnalysisService flightBusinessAnalysisService;

    /**
     * 查询所有飞行业务分析数据
     *
     * @return 飞行业务分析数据列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "[飞行业务分析接口] => 查询所有飞行业务分析数据")
    public Result<List<FlightBusinessAnalysisVO>> listAllFlightBusinessAnalysis() {
        List<FlightBusinessAnalysisVO> analysisList = flightBusinessAnalysisService.listAllFlightBusinessAnalysis();
        return Result.success(analysisList);
    }

    /**
     * 查询指定时间飞行业务分析数据
     *
     * @return 飞行业务分析数据列表
     */
    @PostMapping("/query")
    @ApiOperation(value = "[飞行业务分析接口] => 查询指定时间飞行业务分析数据")
    public Result<List<FlightBusinessAnalysisVO>> listSpecificFlightBusinessAnalysis(@RequestBody FlightBusinessAnalysisSpaQueryDTO queryDTO) {
        List<FlightBusinessAnalysisVO> analysisList = flightBusinessAnalysisService.listSpecificFlightBusinessAnalysis(queryDTO);
        return Result.success(analysisList);
    }
} 