package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.FlightEventsDTO;
import com.deepinnet.spatiotemporalplatform.dto.FlightEventsQueryDTO;
import com.deepinnet.spatiotemporalplatform.dto.FlightEventsStatDTO;
import com.deepinnet.spatiotemporalplatform.dto.FlightEventsStatQueryDTO;
import com.deepinnet.spatiotemporalplatform.skyflow.algorithm.EventMonitorService;
import com.deepinnet.spatiotemporalplatform.skyflow.error.BizErrorCode;
import com.deepinnet.digitaltwin.common.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2024/11/27 11:39
 * @Description
 */
@RestController
@Api(tags = "飞行任务事件管理")
@Validated
@RequestMapping("/stpf/event")
public class FlightEventController {

    @Resource
    private EventMonitorService eventMonitorService;

    /**
     * 保存飞行事件
     *
     * @param flightEventsDTO 飞行事件DTO
     * @return 事件ID
     */
    @ApiOperation(value = "[飞行事件接口] => 保存飞行事件")
    @PostMapping("/save")
    public Result<String> saveEvent(@RequestBody @Valid FlightEventsDTO flightEventsDTO) {
        if (flightEventsDTO == null) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }
        String eventId = eventMonitorService.saveEvent(flightEventsDTO);
        return Result.success(eventId);
    }

    /**
     * 批量保存飞行事件
     *
     * @param flightEventsDTOList 飞行事件DTO列表
     * @return 是否保存成功
     */
    @ApiOperation(value = "[飞行事件接口] => 批量保存飞行事件")
    @PostMapping("/batch/save")
    public Result<Boolean> saveEventBatch(@RequestBody @Valid List<FlightEventsDTO> flightEventsDTOList) {
        if (flightEventsDTOList == null || flightEventsDTOList.isEmpty()) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }
        eventMonitorService.saveEventBatch(flightEventsDTOList);
        return Result.success(true);
    }

    /**
     * 根据ID查询飞行事件
     *
     * @param eventId 事件ID
     * @return 飞行事件DTO
     */
    @ApiOperation(value = "[飞行事件接口] => 根据ID查询飞行事件")
    @PostMapping("/detail")
    public Result<FlightEventsDTO> getEventById(@RequestParam("eventId") String eventId) {
        if (!StringUtils.hasText(eventId)) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }
        FlightEventsDTO flightEventsDTO = eventMonitorService.getEventById(eventId);
        return Result.success(flightEventsDTO);
    }

    /**
     * 分页查询飞行事件
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @ApiOperation(value = "[飞行事件接口] => 分页查询飞行事件")
    @PostMapping("/page")
    public Result<CommonPage<FlightEventsDTO>> pageQuery(@RequestBody @Valid FlightEventsQueryDTO queryDTO) {
        if (queryDTO == null || queryDTO.getPageNum() == null || queryDTO.getPageSize() == null) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }
        CommonPage<FlightEventsDTO> page = eventMonitorService.pageQuery(queryDTO);
        return Result.success(page);
    }

    /**
     * 根据飞行任务ID查询飞行事件列表
     *
     * @param flightTaskCode 飞行任务ID
     * @return 飞行事件DTO列表
     */
    @ApiOperation(value = "[飞行事件接口] => 根据飞行任务ID查询飞行事件列表")
    @PostMapping("/list/by/task")
    public Result<List<FlightEventsDTO>> queryListByFlightTaskCode(@RequestBody FlightEventsQueryDTO queryDTO) {
        if (!StringUtils.hasText(queryDTO.getFlightTaskCode())) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }
        List<FlightEventsDTO> eventsList = eventMonitorService.queryListByFlightTaskCode(queryDTO.getFlightTaskCode(), queryDTO.getDemandNoList());
        return Result.success(eventsList);
    }

    /**
     * 查询飞行事件统计
     *
     * @param queryDTO 查询条件
     * @return 飞行事件统计数据
     */
    @ApiOperation(value = "[飞行事件接口] => 查询飞行事件统计")
    @PostMapping("/stat")
    public Result<List<FlightEventsStatDTO>> queryFlightEventsStat(@RequestBody @Valid FlightEventsStatQueryDTO queryDTO) {
        if (queryDTO == null) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }
        List<FlightEventsStatDTO> statList = eventMonitorService.queryFlightEventsStat(queryDTO);
        return Result.success(statList);
    }
}