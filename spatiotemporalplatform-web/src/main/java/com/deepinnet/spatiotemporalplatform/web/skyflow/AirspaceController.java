package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.AirspaceDO;
import com.deepinnet.spatiotemporalplatform.dto.AirspacePageQueryDTO;
import com.deepinnet.spatiotemporalplatform.model.skyflow.Airspace;
import com.deepinnet.spatiotemporalplatform.skyflow.service.AirspaceService;
import com.deepinnet.spatiotemporalplatform.skyflow.task.AirspaceTask;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 空域管理控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@RestController
@RequestMapping("/stpf/skyflow/airspace")
@Api(tags = "空域管理")
public class AirspaceController {

    @Resource
    private AirspaceService airspaceService;

    @PostMapping("/create")
    @ApiOperation(value = "创建空域")
    public Result<String> createAirspace(@RequestBody Airspace airspace) {
        if (airspace == null) {
            return Result.fail("ILLEGAL_PARAMS", "空域信息不能为空");
        }
        return Result.success(airspaceService.createAirspace(airspace));
    }

    @PostMapping("/page")
    @ApiOperation(value = "分页查询空域")
    public Result<CommonPage<Airspace>> pageQueryAirspace(@RequestBody AirspacePageQueryDTO queryDTO) {
        if (queryDTO == null || queryDTO.getPageNum() == null || queryDTO.getPageSize() == null) {
            return Result.fail("ILLEGAL_PARAMS", "分页参数不能为空");
        }
        return Result.success(airspaceService.pageQueryAirspace(queryDTO));
    }

    @GetMapping("/detail")
    @ApiOperation(value = "根据代码查询空域详情")
    public Result<Airspace> getAirspaceByCode(@RequestParam("code") String code) {
        if (code == null) {
            return Result.fail("ILLEGAL_PARAMS", "空域代码不能为空");
        }
        return Result.success(airspaceService.getAirspaceByCode(code));
    }




} 