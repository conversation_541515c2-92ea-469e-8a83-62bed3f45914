package com.deepinnet.spatiotemporalplatform.web.common.config;

import com.deepinnet.digitaltwin.common.log.LogUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 8/27/25
 */
@Configuration
@EnableScheduling
@ConditionalOnProperty(name = "scheduling.dynamic.enabled", havingValue = "true", matchIfMissing = true)
public class DynamicSchedulingConfig implements SchedulingConfigurer, ApplicationRunner {

    /**
     * 线程池配置参数
     */

    // CPU倍数基准
    @Value("${scheduling.pool.base-multiplier:2}")
    private double baseMultiplier;

    // CPU倍数上限
    @Value("${scheduling.pool.max-multiplier:4}")
    private double maxMultiplier;

    // 最小线程数
    @Value("${scheduling.pool.min-threads:8}")
    private int minThreads;

    // 最大线程数
    @Value("${scheduling.pool.max-threads:200}")
    private int maxThreads;

    // 是否启用自适应
    @Value("${scheduling.adaptation.enabled:true}")
    private boolean adaptationEnabled;

    // 自适应检查间隔 - 缩短间隔以更及时响应负载变化
    @Value("${scheduling.adaptation.interval:180}")
    private long adaptationInterval;

    private final int CPU_CORES = Runtime.getRuntime().availableProcessors();
    private volatile ThreadPoolTaskScheduler scheduler;
    private volatile long lastAdaptTime = 0;

    // 环境相关的配置映射
    private final Map<String, SchedulingProfile> profileConfigs = new HashMap<>();

    @Value("${application.deployment.env:local}")
    private String activeProfile;

    @PostConstruct
    public void initializeProfiles() {
        profileConfigs.put("local", new SchedulingProfile(2.5, 4.0, 24, 96));
        profileConfigs.put("test", new SchedulingProfile(2.0, 2.5, 8, 24));
        profileConfigs.put("demo", new SchedulingProfile(2.0, 2.5, 8, 24));
        profileConfigs.put("alicloud", new SchedulingProfile(1.8, 2.8, 10, 40));
        profileConfigs.put("sz-lg-gov", new SchedulingProfile(2.0, 3.0, 16, 48));
        profileConfigs.put("prod", new SchedulingProfile(2.0, 3.5, 16, 64));
        profileConfigs.put("default", new SchedulingProfile(2.0, 2.5, 8, 32));
    }

    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        scheduler = createOptimalScheduler();
        taskRegistrar.setTaskScheduler(scheduler);
        LogUtil.info("动态定时任务线程池初始化完成 - 环境: {}, CPU核心数: {}, 初始线程数: {}",
                activeProfile, CPU_CORES, scheduler.getPoolSize());
    }

    private ThreadPoolTaskScheduler createOptimalScheduler() {
        SchedulingProfile profile = profileConfigs.getOrDefault(activeProfile, profileConfigs.get("default"));

        // 根据CPU数量和环境计算最佳线程数
        int optimalThreads = calculateOptimalThreads(profile);

        ThreadPoolTaskScheduler taskScheduler = new ThreadPoolTaskScheduler();
        taskScheduler.setPoolSize(optimalThreads);
        taskScheduler.setThreadNamePrefix(String.format("schedule-task-%s-", activeProfile));
        taskScheduler.setWaitForTasksToCompleteOnShutdown(true);
        taskScheduler.setAwaitTerminationSeconds(60);
        taskScheduler.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        taskScheduler.initialize();

        return taskScheduler;
    }

    private int calculateOptimalThreads(SchedulingProfile profile) {
        // 基础计算：CPU核心数 * 倍数
        int baseThreads = (int) (CPU_CORES * profile.baseMultiplier);

        // 应用环境约束
        int threads = Math.max(baseThreads, profile.minThreads);
        threads = Math.min(threads, profile.maxThreads);

        // 全局约束
        threads = Math.max(threads, minThreads);
        threads = Math.min(threads, maxThreads);

        return threads;
    }

    /**
     * 自适应线程池调整任务
     */
    @Scheduled(fixedRate = 60000) // 每分钟检查
    public void adaptiveThreadPoolAdjustment() {
        if (!adaptationEnabled || scheduler == null) {
            return;
        }

        long now = System.currentTimeMillis();
        if (now - lastAdaptTime < adaptationInterval * 1000) {
            return;
        }

        try {
            ThreadPoolExecutor executor = scheduler.getScheduledThreadPoolExecutor();
            ThreadPoolMetrics metrics = collectMetrics(executor);

            int adjustment = calculateAdjustment(metrics);

            if (adjustment != 0) {
                adjustThreadPool(executor, adjustment, metrics);
                lastAdaptTime = now;
            }

            // 定期输出监控信息
            if (now % 300000 < 60000) {
                // 每5分钟输出一次
                logThreadPoolStatus(metrics);
            }

        } catch (Exception e) {
            LogUtil.error("自适应线程池调整异常", e);
        }
    }

    private ThreadPoolMetrics collectMetrics(ThreadPoolExecutor executor) {
        return ThreadPoolMetrics.builder()
                .corePoolSize(executor.getCorePoolSize())
                .activeCount(executor.getActiveCount())
                .queueSize(executor.getQueue().size())
                .completedTaskCount(executor.getCompletedTaskCount())
                .poolSize(executor.getPoolSize())
                .taskCount(executor.getTaskCount())
                .build();
    }

    private int calculateAdjustment(ThreadPoolMetrics metrics) {
        SchedulingProfile profile = profileConfigs.getOrDefault(activeProfile, profileConfigs.get("default"));

        double utilizationRate = (double) metrics.activeCount / metrics.corePoolSize;
        int queuePressure = metrics.queueSize;

        // 扩容条件 - 针对队列积压问题优化
        if ((utilizationRate > 0.6 || queuePressure > 10) &&
                metrics.corePoolSize < calculateOptimalThreads(profile)) {

            // 根据压力程度决定扩容幅度 - 8核CPU环境下更及时的扩容
            if (utilizationRate > 0.80 || queuePressure > 20) {
                // 大幅扩容：最多增加CPU核心数的一半
                return Math.min(CPU_CORES / 2, Math.max(2, (int)(metrics.corePoolSize * 0.3)));
            } else {
                // 适度扩容：最多增加2-4个线程
                return Math.min(4, Math.max(2, (int)(metrics.corePoolSize * 0.2)));
            }
        }

        // 缩容条件 - 更保守的缩容策略
        if (utilizationRate < 0.20 && queuePressure == 0 &&
                metrics.corePoolSize > profile.minThreads) {

            // 适度缩容：最多减少2-3个线程
            return -Math.min(3, Math.max(1, (int)(metrics.corePoolSize * 0.15)));
        }

        // 无需调整
        return 0;
    }

    private void adjustThreadPool(ThreadPoolExecutor executor, int adjustment, ThreadPoolMetrics metrics) {
        int currentSize = metrics.corePoolSize;
        int newSize = currentSize + adjustment;

        SchedulingProfile profile = profileConfigs.getOrDefault(activeProfile, profileConfigs.get("default"));

        // 应用约束
        newSize = Math.max(newSize, profile.minThreads);
        newSize = Math.min(newSize, profile.maxThreads);
        newSize = Math.max(newSize, minThreads);
        newSize = Math.min(newSize, maxThreads);

        if (newSize != currentSize) {
            executor.setCorePoolSize(newSize);
            executor.setMaximumPoolSize(Math.max(newSize, executor.getMaximumPoolSize()));

            LogUtil.info("自适应调整线程池 - 环境: {}, {} -> {}, 利用率: {:.2f}%, 队列积压: {}",
                    activeProfile, currentSize, newSize,
                    (double) metrics.activeCount / currentSize * 100, metrics.queueSize);
        }
    }

    private void logThreadPoolStatus(ThreadPoolMetrics metrics) {
        double utilizationRate = (double) metrics.activeCount / metrics.corePoolSize * 100;

        LogUtil.info("定时任务线程池状态监控 - 环境: {}, CPU核心: {}, 核心线程数: {}, 活跃线程: {}, 利用率: {:.1f}%, 队列积压: {}, 已完成任务: {}",
                activeProfile, CPU_CORES, metrics.corePoolSize, metrics.activeCount,
                utilizationRate, metrics.queueSize, metrics.completedTaskCount);
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 应用启动后，延迟开始自适应调整
        lastAdaptTime = System.currentTimeMillis() + 60000;

        LogUtil.info("定时任务动态线程池管理器启动完成 - " + "环境: {}, CPU核心数: {}, 自适应调整: {}"
                , activeProfile, CPU_CORES, adaptationEnabled ? "启用" : "禁用");
    }

    /**
     * 手动调整接口（用于紧急情况）
     */
    public boolean manualAdjust(int targetSize, String reason) {
        if (scheduler == null) {
            return false;
        }

        try {
            SchedulingProfile profile = profileConfigs.getOrDefault(activeProfile, profileConfigs.get("default"));
            targetSize = Math.max(targetSize, profile.minThreads);
            targetSize = Math.min(targetSize, profile.maxThreads);

            ThreadPoolExecutor executor = scheduler.getScheduledThreadPoolExecutor();
            int currentSize = executor.getCorePoolSize();

            executor.setCorePoolSize(targetSize);
            executor.setMaximumPoolSize(Math.max(targetSize, executor.getMaximumPoolSize()));

            LogUtil.warn("手动调整线程池大小 - 环境: {}, {} -> {}, 原因: {}",
                    activeProfile, currentSize, targetSize, reason);

            return true;
        } catch (Exception e) {
            LogUtil.error("手动调整线程池失败", e);
            return false;
        }
    }

    /**
     * 获取当前线程池状态
     */
    public Map<String, Object> getStatus() {
        if (scheduler == null) {
            return Collections.emptyMap();
        }

        ThreadPoolExecutor executor = scheduler.getScheduledThreadPoolExecutor();
        ThreadPoolMetrics metrics = collectMetrics(executor);

        Map<String, Object> status = new HashMap<>();
        status.put("environment", activeProfile);
        status.put("cpuCores", CPU_CORES);
        status.put("corePoolSize", metrics.corePoolSize);
        status.put("activeCount", metrics.activeCount);
        status.put("poolSize", metrics.poolSize);
        status.put("queueSize", metrics.queueSize);
        status.put("completedTaskCount", metrics.completedTaskCount);
        status.put("utilizationRate", String.format("%.1f%%", (double) metrics.activeCount / metrics.corePoolSize * 100));
        status.put("adaptationEnabled", adaptationEnabled);

        return status;
    }

    // 内部类：环境配置文件
    @Data
    @AllArgsConstructor
    private static class SchedulingProfile {
        // 基础CPU倍数
        private double baseMultiplier;

        // 最大CPU倍数
        private double maxMultiplier;

        // 最小线程数
        private int minThreads;

        // 最大线程数
        private int maxThreads;
    }

    // 内部类：线程池指标
    @Data
    @Builder
    private static class ThreadPoolMetrics {
        private int corePoolSize;
        private int activeCount;
        private int queueSize;
        private long completedTaskCount;
        private int poolSize;
        private long taskCount;
    }
}
