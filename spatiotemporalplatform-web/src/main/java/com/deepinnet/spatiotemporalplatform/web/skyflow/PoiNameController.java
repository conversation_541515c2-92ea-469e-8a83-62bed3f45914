package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.dto.PoiNameMappingPageQueryDTO;
import com.deepinnet.spatiotemporalplatform.model.skyflow.PoiNameMapping;
import com.deepinnet.spatiotemporalplatform.skyflow.service.impl.PoiNameService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * PoiNameController
 * Author: chenkaiyang
 * Date: 2025/3/21
 */
@RestController
@RequestMapping("/api/skyflow/poiName")
@Api(tags = "POI名称映射管理")
public class PoiNameController {

    @Resource
    private PoiNameService poiNameService;

    @PostMapping("/pageQuery")
    @ApiOperation(value = "分页查询POI名称映射列表")
    public CommonPage<PoiNameMapping> pageQueryPoiNameMapping(@RequestBody PoiNameMappingPageQueryDTO queryDTO) {
        return poiNameService.pageQueryPoiNameMapping(queryDTO);
    }
}