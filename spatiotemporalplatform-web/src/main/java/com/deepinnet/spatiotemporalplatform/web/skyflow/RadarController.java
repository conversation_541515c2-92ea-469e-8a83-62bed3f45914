package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.RadarPageQueryDTO;
import com.deepinnet.spatiotemporalplatform.model.skyflow.Radar;
import com.deepinnet.spatiotemporalplatform.skyflow.service.RadarService;
import com.deepinnet.spatiotemporalplatform.skyflow.task.RadarTask;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 雷达管理控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@RestController
@RequestMapping("/stpf/skyflow/radar")
@Api(tags = "雷达管理")
public class RadarController {

    @Resource
    private RadarService radarService;

    @PostMapping("/create")
    @ApiOperation(value = "创建雷达")
    public Result<String> createRadar(@RequestBody Radar radar) {
        if (radar == null) {
            return Result.fail("ILLEGAL_PARAMS", "雷达信息不能为空");
        }
        return Result.success(radarService.createRadar(radar));
    }

    @PostMapping("/page")
    @ApiOperation(value = "分页查询雷达")
    public Result<CommonPage<Radar>> pageQueryRadar(@RequestBody RadarPageQueryDTO queryDTO) {
        if (queryDTO == null || queryDTO.getPageNum() == null || queryDTO.getPageSize() == null) {
            return Result.fail("ILLEGAL_PARAMS", "分页参数不能为空");
        }
        return Result.success(radarService.pageQueryRadar(queryDTO));
    }

    @GetMapping("/detail")
    @ApiOperation(value = "根据编码查询雷达详情")
    public Result<Radar> getRadarByCode(@RequestParam("code") String code) {
        if (code == null) {
            return Result.fail("ILLEGAL_PARAMS", "雷达编码不能为空");
        }
        return Result.success(radarService.getRadarByCode(code));
    }

} 