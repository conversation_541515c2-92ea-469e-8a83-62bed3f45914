package com.deepinnet.spatiotemporalplatform.web.smartcity.reggov.flow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.*;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 区域人流REST接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024/8/2
 */
@RestController
@RequestMapping("/stpf/area/people/flow")
@CrossOrigin
@Api(tags = "区域人流")
public class PeopleFlowRest {

    @Resource
    private CommonDataService commonDataService;

    @ApiOperation("区域实时人流查询")
    @PostMapping("/realtime")
    public Result<RealTimePeopleFlowData> getRealTimePeopleFlow(@RequestBody RealTimePeopleFlowParams realTimePeopleFlowParams) {
        RealTimePeopleFlowRequest request = new RealTimePeopleFlowRequest();
        request.setUrlParams(realTimePeopleFlowParams);
        return Result.success((RealTimePeopleFlowData) commonDataService.fetchData(request));
    }

    @ApiOperation("区域历史人流查询")
    @PostMapping("/history")
    public Result<List<HistoryPeopleFlowData>> getHistoryPeopleFlow(@RequestBody HistoryPeopleFlowParams historyPeopleFlowParams) {
        HistoryPeopleFlowRequest request = new HistoryPeopleFlowRequest();
        request.setUrlParams(historyPeopleFlowParams);
        return Result.success((List<HistoryPeopleFlowData>) commonDataService.fetchData(request));
    }

    @ApiOperation("网格历史人流和活力指数查询")
    @PostMapping("/grid/history")
    public Result<List<GridHistoryPeopleFlowData>> getGridHistoryPeopleFlow(@RequestBody GridHistoryPeopleFlowParams gridHistoryPeopleFlowParams) {
        GridHistoryPeopleFlowRequest request = new GridHistoryPeopleFlowRequest();
        request.setUrlParams(gridHistoryPeopleFlowParams);
        return Result.success((List<GridHistoryPeopleFlowData>) commonDataService.fetchData(request));
    }


    @ApiOperation("网格实时人流和活力指数查询")
    @PostMapping("/grid/realtime")
    public Result<GridRealTimePeopleFlowData> getGridRealTimePeopleFlow(@RequestBody GridRealTimePeopleFlowParams peopleFlowParams) {
        GridRealTimePeopleFlowRequest request = new GridRealTimePeopleFlowRequest();
        request.setUrlParams(peopleFlowParams);
        return Result.success((GridRealTimePeopleFlowData) commonDataService.fetchData(request));
    }

    @ApiOperation("实时人流来源地查询")
    @PostMapping("/source")
    public Result<RegionPeopleSourceFlowData> getSourcePeopleFlow(@RequestBody RegionPeopleSourceFlowParams regionPeopleSourceFlowParams) {
        RegionPeopleSourceFlowRequest request = new RegionPeopleSourceFlowRequest();
        request.setUrlParams(regionPeopleSourceFlowParams);

        return Result.success((RegionPeopleSourceFlowData) commonDataService.fetchData(request));
    }

}
