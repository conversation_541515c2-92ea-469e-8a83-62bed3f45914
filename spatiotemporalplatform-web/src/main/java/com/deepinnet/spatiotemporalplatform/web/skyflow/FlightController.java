package com.deepinnet.spatiotemporalplatform.web.skyflow;

import cn.hutool.core.util.StrUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.*;
import com.deepinnet.spatiotemporalplatform.model.skyflow.Airspace;
import com.deepinnet.spatiotemporalplatform.skyflow.service.FlightService;
import com.deepinnet.spatiotemporalplatform.vo.*;
import com.deepinnet.tenant.TenantIdUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 飞行相关接口
 *
 * <AUTHOR>
 */
@Api(tags = "[低空经济] => 飞行接口")
@RestController
@RequestMapping("/stpf/flight")
@RequiredArgsConstructor
@Validated
public class FlightController {

    private final FlightService flightService;

    /**
     * 分页查询飞行任务
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @ApiOperation(value = "[飞行接口] => 分页查询飞行任务")
    @PostMapping("/page/mission")
    public Result<CommonPage<FlightMissionVO>> pageQueryMission(@RequestBody @Valid FlightQueryDTO queryDTO) {
        CommonPage<FlightMissionVO> flightMissionVOCommonPage = flightService.pageQueryMission(queryDTO);
        return Result.success(flightMissionVOCommonPage);
    }

    /**
     * 分页查询飞行计划
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @ApiOperation(value = "[飞行接口] => 分页查询飞行计划")
    @PostMapping("/page/plan")
    public Result<CommonPage<FlightPlanVO>> pageQueryPlan(@RequestBody @Valid FlightQueryDTO queryDTO) {
        CommonPage<FlightPlanVO> flightMissionVOCommonPage = flightService.pageQueryPlan(queryDTO);
        return Result.success(flightMissionVOCommonPage);
    }

    /**
     * 查询飞行任务详情
     *
     * @param missionId 查询条件
     * @return 飞行任务详情
     */
    @ApiOperation(value = "[飞行接口] => 查询飞行计划详情")
    @GetMapping("/mission/detail")
    public Result<FlightMissionVO> queryMissionDetail(@RequestParam("missionId") String missionId) {
        return Result.success(flightService.queryMissionDetail(missionId));
    }

    /**
     * 查询飞行计划详情
     *
     * @param planId 查询条件
     * @return 飞行任务详情
     */
    @ApiOperation(value = "[飞行接口] => 查询飞行计划详情")
    @GetMapping("/plan/detail")
    public Result<FlightPlanVO> queryPlanDetail(@RequestParam("planId") String planId) {
        return Result.success(flightService.queryPlanDetail(planId));
    }

    /**
     * 查询飞行计划架次列表
     * @param planId
     * @return
     */
    @ApiOperation(value = "[飞行接口] => 查询飞行计划架次列表")
    @GetMapping("/plan/flight/list")
    public Result<List<RealTimeUavFlightVO>> queryPlanFlight(@RequestParam("planId") String planId) {
        return Result.success(flightService.queryPlanFlight(planId));
    }

    /**
     * 查询飞行计划架次详情
     * @param planId
     * @param flightId
     * @return
     */
    @ApiOperation(value = "[飞行接口] => 查询飞行计划架次详情")
    @GetMapping("/plan/flight/detail")
    public Result<FlightPlanVO> queryPlanFlightDetail(@RequestParam("planId") String planId, @RequestParam("flightId") String flightId) {
        return Result.success(flightService.queryPlanFlightDetail(planId, flightId));
    }


    /**
     * 查询飞行计划对应的航道
     *
     * @param planId 查询条件
     * @return 查询飞行计划对应的航道
     */
    @ApiOperation(value = "[飞行接口] => 查询飞行计划对应的航道")
    @GetMapping("/plan/airway")
    public Result<Airspace> queryPlanAirway(@RequestParam("planId") String planId) {
        return Result.success(flightService.queryPlanAirway(planId));
    }

    /**
     * 查询今日飞行概览
     *
     * @return 飞行概览
     */
    @ApiOperation(value = "[飞行接口] => 查询今日飞行概览")
    @GetMapping("/overview")
    public Result<FlightTodayOverviewVO> overview() {
        return Result.success(flightService.queryTodayOverview());
    }

    /**
     * 查询今日飞行概览趋势图
     */
    @ApiOperation(value = "[飞行接口] => 查询今日飞行概览趋势图")
    @GetMapping("/overview/trend")
    public Result<List> queryTrend(@RequestParam("type") String type) {
        return Result.success(flightService.queryTrend(type));
    }

    /**
     * 查询今日飞行高度趋势图
     */
    @ApiOperation(value = "[飞行接口] => 查询今日飞行高度趋势图")
    @GetMapping("/overview/height/trend")
    public Result<List> queryHeightTrend(@RequestParam(value = "startTime", required = false) Long startTime,
                                         @RequestParam(value = "endTime", required = false) Long endTime) {
        return Result.success(flightService.queryTodayHeightSorties(startTime, endTime));
    }

    /**
     * 查询今日无人机实时点位
     */
    @ApiOperation(value = "[飞行接口] => 查询今日无人机实时点位")
    @PostMapping("/realtime/uav/position")
    public Result<List<RealTimeUavFlightVO>> getUavPosition(@RequestBody PositionQueryDTO queryDTO) {
        return Result.success(flightService.queryTodayRealTimeUavFlight(queryDTO));
    }

    /**
     * 查询今日无人机轨迹
     */
    @ApiOperation(value = "[飞行接口] => 查询今日无人机轨迹")
    @PostMapping("/realtime/uav/trajectory")
    public Result<List<UavFlightTrackVO>> getUavTrajectory(@RequestBody PositionQueryDTO queryDTO) {
        return Result.success(flightService.queryTodayUavFlightTrack(queryDTO));
    }

    /**
     * 查询无人机直播地址
     */
    @ApiOperation(value = "[飞行接口] => 查询无人机直播地址")
    @GetMapping("/realtime/uav/live")
    public Result<String> getUavLiveUrl(@RequestParam("sn") String sn,
                                        @RequestParam(value = "httpProtocol", defaultValue = "http") String httpProtocol) {
        return Result.success(flightService.queryLiveUrl(sn, httpProtocol));
    }

    /**
     * 企业飞行统计
     */
    @ApiOperation(value = "[飞行接口] => 企业飞行统计")
    @PostMapping("/enterprise/statistics")
    public Result<List<FlightDailyStatisticsVO>> flightDailyStatistics(@RequestBody FlightDailyStatisticsQueryDTO queryDTO) {
        return Result.success(flightService.queryFlightDailyStatistics(queryDTO));
    }

    /**
     * 飞行统计概览
     */
    @ApiOperation(value = "[飞行接口] => 飞行统计概览")
    @PostMapping("/statistics/overview")
    public Result<FlightStatisticsOverviewVO> flightStatisticsOverview(@RequestBody FlightStatisticsOverviewQueryDTO queryDTO) {
        return Result.success(flightService.queryFlightStatisticsOverview(queryDTO));
    }

    @ApiOperation(value = "[飞行接口] => 根据需求编号批量查询飞行架次")
    @PostMapping("/planCountByBizNo")
    public Result<List<FlightCountVO>> planCountByBizNo(@RequestBody BatchQueryPlanDTO queryDTO) {
        queryDTO.setTenantId(TenantIdUtil.getTenantId());
        return Result.success(flightService.batchQueryFlightCount(queryDTO));
    }

    @ApiOperation(value = "[飞行接口] => 根据需求编号批量查询飞行计划")
    @PostMapping("/planListByBizNo")
    public Result<List<FlightPlanVO>> planListByBizNo(@RequestBody BatchQueryPlanDTO queryDTO) {
        queryDTO.setTenantId(TenantIdUtil.getTenantId());
        return Result.success(flightService.batchQueryFlightPlan(queryDTO));
    }

    /**
     * 查询今日飞行高度趋势图
     */
    @ApiOperation(value = "[飞行接口] => 批量查询起降场")
    @PostMapping("/aerodromeList")
    public Result<List<AerodromeVO>> queryAerodromeList(@RequestBody BatchQueryAerodromeDTO queryAerodromeDTO) {
        return Result.success(flightService.batchQueryAerodrome(queryAerodromeDTO.getAerodromeIds()));
    }

    /**
     * 更新飞行报告
     */
    @ApiOperation("更新飞行报告")
    @PostMapping("/update/report")
    public Result<Boolean> updateFlightReport(@RequestBody FlightReportDTO dto) {
        return Result.success(flightService.updateFlightPlan(dto));
    }

    @ApiOperation("统计近三个月内撮合平台在交警类目下的需求的计划统计排行")
    @PostMapping("/demand/plan/stat")
    public Result<List<FlightDemandPlanStatDTO>> queryFlightDemandPlanStat(@RequestBody FlightDemandPlanStatQueryDTO queryDTO) {
        if (StrUtil.isBlank(queryDTO.getTenantId())) {
            queryDTO.setTenantId(TenantIdUtil.getTenantId());
        }
        return Result.success(flightService.queryFlightDemandPlanStat(queryDTO));
    }

    @ApiOperation("提供根据计划id返回算法视频流地址")
    @GetMapping("/algorithm/monitor/url")
    public Result<String> queryAlgorithmMonitorUrl(@RequestParam("planId") String planId) {
        return Result.success(flightService.queryAlgorithmMonitorUrl(planId));
    }

    @ApiOperation("根据计划ID返回成果内容")
    @GetMapping("/achievements")
    public Result<List<FlightAchievementVO>> queryAchievementList(@RequestParam("planId") String planId) {
        return Result.success(flightService.queryAchievementList(planId));
    }

    @ApiOperation("根据计划ID对应日期的location比对上一次的成果")
    @PostMapping("/achievement/compare")
    public Result<FlightAchievementVO> queryAchievementCompareDate(@RequestBody FlightPlanAchievementQueryDTO queryDTO) {
        return Result.success(flightService.queryAchievementCompareDate(queryDTO));
    }

} 