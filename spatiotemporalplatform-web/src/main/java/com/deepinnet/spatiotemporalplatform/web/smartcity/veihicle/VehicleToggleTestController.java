package com.deepinnet.spatiotemporalplatform.web.smartcity.veihicle;

import com.deepinnet.spatiotemporalplatform.smartcity.task.VehiclePassDataSourceToggle;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 过车统计数据源开关
 */
@RestController
@RequestMapping("/test/vehicle/toggle")
@Api(tags = "过车统计数据源开关")
public class VehicleToggleTestController {
    @Resource
    private VehiclePassDataSourceToggle featureToggle;

    @GetMapping("/setFromApi")
    public void setFromApi() {
        featureToggle.setFromApi();
    }

    @GetMapping("/setFromMQ")
    public void setFromMQ() {
        featureToggle.setFromMQ();
    }

    @GetMapping("/isFromMQ")
    public boolean isFromMQ() {
        return featureToggle.isFromMQ();
    }


}
