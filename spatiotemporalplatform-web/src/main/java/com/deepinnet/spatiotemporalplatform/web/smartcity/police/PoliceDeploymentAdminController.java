package com.deepinnet.spatiotemporalplatform.web.smartcity.police;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.police.PoliceDeployment;
import com.deepinnet.spatiotemporalplatform.model.police.PoliceDeploymentDTO;
import com.deepinnet.spatiotemporalplatform.model.police.PoliceDeploymentBatchOperation;
import com.deepinnet.spatiotemporalplatform.model.police.PoliceDeploymentQueryDTO;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.PoliceDeploymentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Controller for managing PoliceDeployment entities.
 */
@RestController
@RequestMapping("/domain/police/deployment")
@Api(tags = "后台-警力部署")
public class PoliceDeploymentAdminController {

    @Resource
    private PoliceDeploymentService policeDeploymentService;


    @PostMapping("/deleteByObjCodeAndType")
    @ApiOperation("后台-预案-删除警力")
    public Result<Boolean> deleteByObjCodeAndType(@RequestParam String planCode, @RequestParam String type) {
        policeDeploymentService.deleteByObjCodeAndType(planCode, type);
        return Result.success(true);
    }

    @PostMapping("/listByAreaCodeAndPlanCodeList")
    @ApiOperation("后台-通过区域code和预案 code获取警力部署列表")
    public Result<List<PoliceDeployment>> listByAreaCodeAndPlanCodeList(@RequestParam String areaCode, @RequestBody List<String> contingencyPlanCodeList) {
        return Result.success(policeDeploymentService.listByAreaCodeAndPlanCodeList(areaCode, contingencyPlanCodeList));
    }

    @PostMapping("/list")
    @ApiOperation("后台-通过区域code和关联对象获取警力部署列表")
    public Result<List<PoliceDeployment>> list(@RequestBody PoliceDeploymentQueryDTO queryDTO) {
        Assert.notNull(queryDTO, "查询条件不能为空");
        Assert.notEmpty(queryDTO.getAreaCode(), "区域code不能为空");
        return Result.success(policeDeploymentService.list(queryDTO.getAreaCode(), queryDTO.getObjectType(), queryDTO.getObjectCode()));
    }


    @PostMapping("/assignToObject")
    public Result<Boolean> assignToObject(@RequestBody List<PoliceDeployment> policeDeploymentList, @RequestParam String objCode) {
        policeDeploymentService.assignToObject(policeDeploymentList, objCode);
        return Result.success(true);
    }

    // 获取区域当前可用的警力部署列表
    @PostMapping("/listByAreaCode")
    @ApiOperation("通过区域code获取当前可用的警力部署列表")
    public Result<List<PoliceDeployment>> listByAreaCode(@RequestBody PoliceDeploymentQueryDTO queryDTO) {
        Assert.notNull(queryDTO, "查询条件不能为空");
        Assert.notEmpty(queryDTO.getAreaCode(), "区域code不能为空");
        Assert.notEmpty(queryDTO.getObjectType(), "关联类型不能为空");
        Assert.notEmpty(queryDTO.getObjectCode(), "关联类型code不能为空");

        return Result.success(policeDeploymentService.list(queryDTO.getAreaCode(), queryDTO.getObjectType(), queryDTO.getObjectCode()));
    }


    @PostMapping("/create")
    @ApiOperation("新增警力部署")
    public Result<PoliceDeploymentDTO> add(@RequestBody PoliceDeploymentDTO policeDeployment) {
        return Result.success(policeDeploymentService.create(policeDeployment));
    }

    @PostMapping("/delete")
    @ApiOperation("删除警力部署")
    public Result<Boolean> delete(@RequestParam Integer id) {
        Assert.notNull(id, "警力部署id不能为空");
        return Result.success(policeDeploymentService.delete(id));
    }

    @PostMapping("/batch")
    @ApiOperation("批量新增或删除警力")
    @Transactional
    public Result<Boolean> batch(@RequestBody PoliceDeploymentBatchOperation batchOperation) {
        Assert.notNull(batchOperation, "请求参数不能为空");
        if (CollectionUtil.isNotEmpty(batchOperation.getBatchDeleteList())) {
            policeDeploymentService.deleteByIds(batchOperation.getBatchDeleteList());
        }
        if (CollectionUtil.isNotEmpty(batchOperation.getBatchSaveList())) {
            policeDeploymentService.batchSave(batchOperation.getBatchSaveList());
        }
        if (CollectionUtil.isNotEmpty(batchOperation.getBatchUpdateList())) {
            List<Integer> updateIdList = batchOperation.getBatchUpdateList().stream().map(PoliceDeploymentDTO::getId).collect(Collectors.toList());
            policeDeploymentService.deleteByIds(updateIdList);
            List<PoliceDeploymentDTO> updateList = batchOperation.getBatchUpdateList().stream().peek(p -> {
                p.setId(null);
                p.setObjectType(StringUtils.lowerCase(p.getObjectType()));
            }).collect(Collectors.toList());
            policeDeploymentService.batchSave(updateList);
        }

        return Result.success(true);
    }

    @PostMapping("/realtime/list")
    @ApiOperation("实时警力部署列表")
    public Result<List<PoliceDeployment>> listRealTimeByAreaCodeAndObjectCode(@RequestBody PoliceDeploymentQueryDTO queryDTO) {
        Assert.notNull(queryDTO, "查询条件不能为空");
        Assert.notEmpty(queryDTO.getAreaCode(), "区域code不能为空");
        return Result.success(policeDeploymentService.listRealTimeByAreaCodeAndObjectCode(queryDTO.getAreaCode(), queryDTO.getObjectType(), queryDTO.getObjectCode()));
    }

    @PostMapping("/listByObjCodeList")
    @ApiOperation("根据作用对象获取列表")
    public Result<List<PoliceDeployment>> listByObjCodeList(@RequestBody List<String> objCodeList) {
        return Result.success(policeDeploymentService.listByObjCodeList(objCodeList));
    }



    @PostMapping("/deleteByObjCode")
    @ApiOperation("根据作用对象删除")
    public Result<Boolean> deleteByObjCode(@RequestParam String objCode) {
        policeDeploymentService.deleteByObjCode(objCode);
        return Result.success(true);
    }




}
