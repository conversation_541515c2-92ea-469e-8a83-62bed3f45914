package com.deepinnet.spatiotemporalplatform.web.common.advice;

import com.deepinnet.digitaltwin.common.error.ErrorCode;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.core.env.Environment;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2024/8/8 09:46
 **/
@Slf4j
@RestControllerAdvice
@SuppressWarnings("all")
public class GlobalControllerAdvise {

    @Resource
    private Environment environment;

    private static final String[] SHOW_ERROR_STACK_TRACE_PROFILES = {"local", "local-inner", "test"};

    /**
     * 处理请求参数格式错误 @RequestBody上validate失败后抛出的异常是MethodArgumentNotValidException异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException e) {
        FieldError fieldError = e.getBindingResult().getFieldError();
        String message = fieldError.getField() + " " + fieldError.getDefaultMessage();
        return Result.fail(ErrorCode.ILLEGAL_PARAMS.getCode(), message);
    }

    /**
     * 处理Get请求中 使用@Valid 验证路径中请求实体校验失败后抛出的异常
     */
    @ExceptionHandler(BindException.class)
    public Result bindExceptionHandler(BindException e) {
        log.error("系统业务异常", e);
        String message = e.getBindingResult().getAllErrors().stream().map(DefaultMessageSourceResolvable::getDefaultMessage).collect(Collectors.joining(";"));
        return Result.fail(ErrorCode.ILLEGAL_PARAMS.getCode(), message);
    }

    /**
     * 处理请求参数格式错误 @RequestParam上validate失败后抛出的异常是ConstraintViolationException
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public Result constraintViolationExceptionHandler(ConstraintViolationException e) {
        log.error("系统业务异常", e);
        String message = e.getConstraintViolations().stream().map(ConstraintViolation::getMessage).collect(Collectors.joining(";"));
        return Result.fail(ErrorCode.ILLEGAL_PARAMS.getCode(), message);
    }

    /**
     * 系统具体异常提示
     *
     * @param e
     * @return
     */
    @ExceptionHandler(BizException.class)
    public Result bizExceptionHandler(BizException e) {
        LogUtil.error("系统业务异常", e);
        return Result.fail(e.getErrorCode(), e.getMessage());
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public Result illgealArgumentExceptionHander(IllegalArgumentException e) {
        if (showErrorStackTrace()) {
            return Result.fail(ErrorCode.ILLEGAL_PARAMS.getCode(), e.getMessage());
        }
        return Result.fail(ErrorCode.ILLEGAL_PARAMS.getCode(), "缺少必要参数");
    }


    /**
     * 内部兜底异常处理
     *
     * @param e
     * @return
     */
    @ExceptionHandler(Exception.class)
    public Result exceptionHandler(Exception e) {
        LogUtil.error("系统异常", e);
        if (showErrorStackTrace()) {
            return Result.fail(ErrorCode.UNKNOWN_EXCEPTION.getCode(), e.getMessage());
        }
        return Result.fail(ErrorCode.UNKNOWN_EXCEPTION.getCode(), ErrorCode.UNKNOWN_EXCEPTION.getDesc());
    }


    @ExceptionHandler(MissingServletRequestParameterException.class)
    public Result missingServletRequestParameterExceptionHandler(MissingServletRequestParameterException e) {
        if (showErrorStackTrace()) {
            return Result.fail(ErrorCode.ILLEGAL_PARAMS.getCode(), e.getMessage());
        }
        return Result.fail(ErrorCode.ILLEGAL_PARAMS.getCode(), "缺少必要参数");
    }

    private Boolean showErrorStackTrace() {
        String[] activeProfiles = environment.getActiveProfiles();
        for (String activeProfile : activeProfiles) {
            if (Stream.of(SHOW_ERROR_STACK_TRACE_PROFILES).anyMatch(profile -> Objects.equals(profile, activeProfile))) {
                return true;
            }
        }
        return false;
    }
}
