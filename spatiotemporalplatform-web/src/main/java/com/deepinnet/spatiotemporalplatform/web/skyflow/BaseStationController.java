package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.skyflow.service.BaseStationService;
import com.deepinnet.spatiotemporalplatform.vo.BaseStationVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 基站数据REST接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/stpf/base/station")
@RequiredArgsConstructor
@Api(tags = "[基站管理] => 基站数据管理")
public class BaseStationController {

    private final BaseStationService baseStationService;

    /**
     * 查询所有基站数据
     *
     * @return 基站数据列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "[基站数据接口] => 查询所有基站数据")
    public Result<List<BaseStationVO>> listAllBaseStations() {
        List<BaseStationVO> baseStationList = baseStationService.listAllBaseStations();
        return Result.success(baseStationList);
    }
} 