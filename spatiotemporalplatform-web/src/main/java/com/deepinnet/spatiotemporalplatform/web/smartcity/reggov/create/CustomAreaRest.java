package com.deepinnet.spatiotemporalplatform.web.smartcity.reggov.create;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.reggov.create.CustomAreaCreatePostBody;
import com.deepinnet.spatiotemporalplatform.model.reggov.create.CustomAreaCreateRequest;
import com.deepinnet.spatiotemporalplatform.model.reggov.create.CustomAreaCreateUrlParams;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2024-07-31
 */
@RestController
@RequestMapping("/stpf/area/custom")
@CrossOrigin
@Api(tags = "自定义区域API")
public class CustomAreaRest {
    @Resource
    private CommonDataService commonDataService;

    @Value("${digest.sign.api.key-temp}")
    private String digestSignApikeyTemp;


    @ApiOperation("创建自定义区域")
    @PostMapping("/create")
    public Result<String> createCustomArea(@RequestBody CustomAreaCreatePostBody customAreaCreatePostBody) {
        CustomAreaCreateRequest request = new CustomAreaCreateRequest();
        customAreaCreatePostBody.setUserKey(digestSignApikeyTemp);
        request.setPostBody(customAreaCreatePostBody);
        CustomAreaCreateUrlParams customAreaCreateUrlParams = new CustomAreaCreateUrlParams();
        request.setUrlParams(customAreaCreateUrlParams);
        return Result.success((String) commonDataService.fetchData(request));
    }

}
