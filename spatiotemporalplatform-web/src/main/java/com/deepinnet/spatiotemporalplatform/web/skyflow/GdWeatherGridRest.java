package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import com.deepinnet.spatiotemporalplatform.model.skyflow.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR> wong
 * @create 2025/3/11 10:23
 * @Description
 */
@RestController
@RequestMapping("/stpf/gd/weather")
public class GdWeatherGridRest {

    @Resource
    private CommonDataService commonDataService;

    @ApiOperation("获取天气网格数据")
    @PostMapping("/grid")
    public Result<WeatherGridQueryResponse> queryGdWeatherGridData(@RequestBody WeatherGridPostBody weatherGridPostBody) {
        WeatherGridRequest request = new WeatherGridRequest();
        request.setPostBody(weatherGridPostBody);
        WeatherGridUrlParams weatherGridUrlParams = new WeatherGridUrlParams();
        request.setUrlParams(weatherGridUrlParams);
        return Result.success((WeatherGridQueryResponse) commonDataService.fetchData(request));
    }
}
