package com.deepinnet.spatiotemporalplatform.web.smartcity.tour;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.tour.heat.RealHeat;
import com.deepinnet.spatiotemporalplatform.model.tour.heat.RealHeatRequest;
import com.deepinnet.spatiotemporalplatform.model.tour.heat.RealHeatUrlParams;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**

 *
 * <AUTHOR>
 * @since 2024-08-27 星期二
 **/
@RestController
@RequestMapping("/stpf/tour")
@CrossOrigin
@Api(tags = "")
public class RealHeatRest {

    @Resource
    private CommonDataService commonDataService;

    @ApiOperation("实时网格客流")
    @PostMapping("/real/heat")
    public Result<RealHeat> heat(@RequestBody RealHeatUrlParams realHeatUrlParams) {
        RealHeatRequest realHeatRequest = new RealHeatRequest();
        realHeatRequest.setUrlParams(realHeatUrlParams);
        return Result.success((RealHeat) commonDataService.fetchData(realHeatRequest));
    }

}
