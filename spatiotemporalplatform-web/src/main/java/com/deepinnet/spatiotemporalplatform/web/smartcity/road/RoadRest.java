package com.deepinnet.spatiotemporalplatform.web.smartcity.road;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.road.*;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**

 *
 * <AUTHOR>
 * @since 2024-08-27 星期二
 **/
@RestController
@RequestMapping("/stpf/road")
@CrossOrigin
@Api(tags = "")
@SuppressWarnings("all")
public class RoadRest {

    @Resource
    private CommonDataService commonDataService;

    @ApiOperation("实时网格客流")
    @PostMapping("/realRoadIndex")
    public Result<List<RealRoadIndex>> realRoadIndex(@RequestBody RealRoadIndexUrlParams realHeatUrlParams) {
        RealRoadIndexRequest realRoadIndexRequest = new RealRoadIndexRequest();
        realRoadIndexRequest.setUrlParams(realHeatUrlParams);
        return Result.success((List<RealRoadIndex>) commonDataService.fetchData(realRoadIndexRequest));
    }


    @ApiOperation("道路查询")
    @PostMapping("/search")
    public Result<List<RoadSearch>> search(@RequestBody RoadSearchUrlParams roadSearchUrlParams) {
        RoadSearchRequest roadSearchRequest = new RoadSearchRequest();
        roadSearchRequest.setUrlParams(roadSearchUrlParams);
        return Result.success((List<RoadSearch>) commonDataService.fetchData(roadSearchRequest));
    }

    @ApiOperation("道路形状查询")
    @PostMapping("/shape")
    public Result<List<RoadShape>> shape(@RequestBody RoadShapeUrlParams roadShapeUrlParams) {
        RoadShapeRequest roadShapeRequest = new RoadShapeRequest();
        roadShapeRequest.setUrlParams(roadShapeUrlParams);
        return Result.success((List<RoadShape>) commonDataService.fetchData(roadShapeRequest));
    }

    @ApiOperation("道路历史排名")
    @PostMapping("/historyRanking")
    public Result<List<RoadHistoryRanking>> historyRanking(@RequestBody RoadHistoryRankingUrlParams RoadHistoryRankingUrlParams) {
        RoadHistoryRankingRequest roadHistoryRankingRequest = new RoadHistoryRankingRequest();
        roadHistoryRankingRequest.setUrlParams(RoadHistoryRankingUrlParams);
        return Result.success((List<RoadHistoryRanking>) commonDataService.fetchData(roadHistoryRankingRequest));
    }

}
