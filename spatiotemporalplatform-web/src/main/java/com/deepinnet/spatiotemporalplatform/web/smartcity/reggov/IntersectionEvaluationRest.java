package com.deepinnet.spatiotemporalplatform.web.smartcity.reggov;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis.RealTimeIntersectionEvaluationData;
import com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis.RealTimeIntersectionEvaluationRequest;
import com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis.RealTimeIntersectionEvaluationUrlParams;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**

 *
 * <AUTHOR>
 * @since 2024-08-03 星期六
 **/
@RestController
@RequestMapping("/stpf/area/intersection")
@CrossOrigin
@Api(tags = "实时路口")
public class IntersectionEvaluationRest {

    @Resource
    private CommonDataService commonDataService;

    @ApiOperation("实时路口评价")
    @PostMapping("/realtime/evaluation")
    public Result<RealTimeIntersectionEvaluationData> realtimeEvaluation(@RequestBody RealTimeIntersectionEvaluationUrlParams realTimeIntersectionEvaluationUrlParams) {
        RealTimeIntersectionEvaluationRequest realTimeIntersectionEvaluationRequest = new RealTimeIntersectionEvaluationRequest();
        realTimeIntersectionEvaluationRequest.setUrlParams(realTimeIntersectionEvaluationUrlParams);
        return Result.success((RealTimeIntersectionEvaluationData) commonDataService.fetchData(realTimeIntersectionEvaluationRequest));
    }

}
