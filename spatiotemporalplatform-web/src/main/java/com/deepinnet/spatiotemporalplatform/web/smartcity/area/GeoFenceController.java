package com.deepinnet.spatiotemporalplatform.web.smartcity.area;

import com.alibaba.fastjson2.JSON;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import com.deepinnet.spatiotemporalplatform.model.area.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description:
 * Date: 2025/4/29
 * Author: lijunheng
 */
@Slf4j
@RestController
@RequestMapping("/stpf/fence")
@CrossOrigin
@Api(tags = "地理围栏")
public class GeoFenceController {

    @Resource
    private CommonDataService commonDataService;

    @ApiOperation("乡镇/街道界查询")
    @PostMapping("/street")
    public Result<CommonPage<StreetPolygonRespDTO>> getStreetPolygon(@RequestBody StreetPolygonQueryParams streetPolygonQueryParams) {
        // 参数校验
        if (streetPolygonQueryParams == null) {
            return Result.fail("PARAM_ERROR", "请求参数不能为空");
        }
        
        log.debug("乡镇/街道界查询接口入参: {}", JSON.toJSONString(streetPolygonQueryParams));
        
        StreetPolygonRequest streetPolygonRequest = new StreetPolygonRequest();
        streetPolygonRequest.setStreetPolygonQueryParams(streetPolygonQueryParams);
        
        // 调用API获取数据
        StreetPolygonResponse response = (StreetPolygonResponse) commonDataService.fetchData(streetPolygonRequest);

        // 检查API调用是否成功
        if (!Objects.equals(response.getCode(), "0")) {
            String message = response.getMessage() != null ? response.getMessage() : "查询街道界失败";
            log.error("乡镇/街道界查询接口调用失败: code={}, message={}", response.getCode(), message);
            return Result.fail(String.valueOf(response.getCode()), message);
        }

        StreetPolygonData data = response.getData();

        // 转换为StreetPolygonRespDTO列表
        List<StreetPolygonRespDTO> respDTOList = data.getList().stream().map(item -> {
            StreetPolygonRespDTO dto = new StreetPolygonRespDTO();
            dto.setName(item.getName());
            dto.setPolygon(item.getPolygon());
            dto.setCenter(item.getCenter());
            dto.setLevel(item.getLevel());
            dto.setStreetid(item.getStreetid());
            return dto;
        }).collect(Collectors.toList());

        // 获取分页参数
        StreetPolygonData.StreetPolygonInfo info = data.getInfo();
        Integer count = info.getCount() != null ? info.getCount() : 0;
        Integer pageNum = streetPolygonQueryParams.getPageNum();
        Integer pageSize = streetPolygonQueryParams.getPageSize();
        int totalPage = (count + pageSize - 1) / pageSize;

        // 构建分页结果
        CommonPage<StreetPolygonRespDTO> commonPage = CommonPage.buildPage(
                pageNum,
                pageSize,
                totalPage,
                Long.valueOf(count),
                respDTOList
        );

        log.debug("乡镇/街道界查询接口返回结果: 总数:{}, 当前页:{}, 页大小:{}", count, pageNum, pageSize);
        return Result.success(commonPage);
    }

    @ApiOperation("POI搜索-关键字搜索")
    @PostMapping("/poi/keyword/search")
    public Result<PoiKeywordResponse> poiKeywordSearch(@RequestBody PoiKeywordQueryParams poiKeywordQueryParams) {
        // 参数校验
        if (poiKeywordQueryParams == null) {
            return Result.fail("PARAM_ERROR", "请求参数不能为空");
        }

        log.debug("POI搜索-关键字搜索接口入参: {}", JSON.toJSONString(poiKeywordQueryParams));

        PoiKeywordRequest poiKeywordRequest = new PoiKeywordRequest();
        poiKeywordRequest.setPoiKeywordQueryParams(poiKeywordQueryParams);

        // 调用API获取数据
        PoiKeywordResponse response = (PoiKeywordResponse) commonDataService.fetchData(poiKeywordRequest);

        // 检查API调用是否成功
        if (!Objects.equals(response.getStatus(), "1")) {
            String message = response.getInfo() != null ? response.getInfo() : "POI搜索-关键字搜索失败";
            log.error("POI搜索-关键字搜索接口调用失败: code={}, message={}", response.getInfocode(), message);
            return Result.fail(String.valueOf(response.getCode()), message);
        }

        return Result.success(response);
    }
}
