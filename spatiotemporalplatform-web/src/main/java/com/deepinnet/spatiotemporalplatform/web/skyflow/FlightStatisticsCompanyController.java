package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.localdata.integration.FlightTaskNatureStatisticsClient;
import com.deepinnet.spatiotemporalplatform.dto.FlightServiceCompanyStatisticsQueryDTO;
import com.deepinnet.spatiotemporalplatform.skyflow.service.FlightStatisticsService;
import com.deepinnet.spatiotemporalplatform.vo.FlightServiceCompanyRankingVO;
import com.deepinnet.spatiotemporalplatform.vo.FlightTaskNatureStatisticsDataVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 飞行服务公司统计接口
 *
 * <AUTHOR>
 * @create 2025/6/30
 * @Description 飞行服务公司统计相关接口
 */
@Api(tags = "[低空经济] => 飞行服务公司统计")
@RestController
@RequestMapping("/stpf/flight/statistics/company")
@RequiredArgsConstructor
@Validated
public class FlightStatisticsCompanyController {

    private final FlightStatisticsService flightStatisticsService;

    @ApiOperation("获取飞行服务公司统计数据")
    @PostMapping("/query")
    public Result<List<FlightServiceCompanyRankingVO>> getFlightServiceCompanyStatistics(@RequestBody @Valid FlightServiceCompanyStatisticsQueryDTO queryDTO) {
        List<FlightServiceCompanyRankingVO> rankingList = flightStatisticsService.getFlightServiceCompanyStatistics(queryDTO);
        return Result.success(rankingList);
    }

    @ApiOperation("获取飞行任务性质统计信息")
    @PostMapping("/nature")
    public Result<List<FlightTaskNatureStatisticsDataVO>> getFlightTaskNatureStatistics() {
        List<FlightTaskNatureStatisticsDataVO> flightTaskNatureStatistics = flightStatisticsService.getFlightTaskNatureStatistics();
        return Result.success(flightTaskNatureStatistics);
    }

}