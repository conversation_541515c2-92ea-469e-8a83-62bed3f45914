package com.deepinnet.spatiotemporalplatform.web.smartcity.tour;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.tour.customer.*;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**

 *
 * <AUTHOR>
 * @since 2024-08-27 星期二
 **/
@RestController
@RequestMapping("/stpf/tour/people")
@CrossOrigin
@Api(tags = "")
public class CustomerFlowRest {

    @Resource
    private CommonDataService commonDataService;

    @ApiOperation("客流监测")
    @PostMapping("/flow/detect")
    public Result<CustomerFlowDetect> detect(@RequestBody CustomerFlowDetectUrlParams customerFlowDetectUrlParams) {
        CustomerFlowDetectRequest customerFlowDetectRequest = new CustomerFlowDetectRequest();
        customerFlowDetectRequest.setUrlParams(customerFlowDetectUrlParams);
        return Result.success((CustomerFlowDetect) commonDataService.fetchData(customerFlowDetectRequest));
    }

    @ApiOperation("客流检测网格")
    @PostMapping("/flow/detect/grid")
    public Result<CustomerFlowDetectGrid> detectGrid(@RequestBody CustomerFlowDetectGridUrlParams customerFlowDetectGridUrlParams) {
        CustomerFlowDetectGridRequest customerFlowDetectGridRequest = new CustomerFlowDetectGridRequest();
        customerFlowDetectGridRequest.setUrlParams(customerFlowDetectGridUrlParams);
        return Result.success((CustomerFlowDetectGrid) commonDataService.fetchData(customerFlowDetectGridRequest));
    }

}
