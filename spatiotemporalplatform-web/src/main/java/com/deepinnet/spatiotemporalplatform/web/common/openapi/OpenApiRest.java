package com.deepinnet.spatiotemporalplatform.web.common.openapi;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import com.deepinnet.spatiotemporalplatform.model.tour.openapi.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR> wong
 * @create 2024/9/25 16:11
 * @Description 高德开放平台接口
 */
@RestController
@RequestMapping("/stpf/openapi")
public class OpenApiRest {

    @Resource
    private CommonDataService commonDataService;

    @ApiOperation("逆地理编码")
    @PostMapping("/reverse/geocoding")
    public Result<ReverseGeocoding> getReverseGeocoding(@RequestBody ReverseGeocodingParam param) {
        ReverseGeoCodingRequest request = new ReverseGeoCodingRequest();
        request.setUrlParams(param);
        return Result.success((ReverseGeocoding) commonDataService.fetchData(request));
    }

    @ApiOperation("地理编码")
    @PostMapping("/geocode/geocoding")
    public Result<Geocoding> getGeocoding(@RequestBody GeocodingParam param) {
        GeoCodingRequest request = new GeoCodingRequest();
        request.setUrlParams(param);
        return Result.success((Geocoding) commonDataService.fetchData(request));
    }

    @ApiOperation("输入提示")
    @PostMapping("/assistant/inputtips")
    public Result<InputTips> getTips(@RequestBody InputTipsParam param) {
        InputTipsRequest request = new InputTipsRequest();
        request.setParam(param);
        return Result.success((InputTips) commonDataService.fetchData(request));
    }
}
