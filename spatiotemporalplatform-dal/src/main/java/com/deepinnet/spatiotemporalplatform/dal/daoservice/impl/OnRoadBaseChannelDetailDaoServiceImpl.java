package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.mapper.OnRoadBaseChannelDetailMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.OnRoadBaseChannelDetailDO;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.OnRoadBaseChannelDetailDaoService;
import org.springframework.stereotype.Service;

/**
 * Description:
 * Date: 2024/9/28
 * Author: lijunheng
 */
@Service
public class OnRoadBaseChannelDetailDaoServiceImpl extends ServiceImpl<OnRoadBaseChannelDetailMapper, OnRoadBaseChannelDetailDO> implements OnRoadBaseChannelDetailDaoService {

}
