package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Creator zengjuerui
 * Date 2025-08-14
 **/

@Data
@TableName("restricted_area_vehicle_access_record")
public class RestrictedAreaVehicleAccessRecordDO {

    /**
     * 主键ID (自增序列)
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 区域编码
     */
    @TableField("area_code")
    private String areaCode;

    /**
     * 车牌号
     */
    @TableField("plate_num")
    private String plateNum;

    /**
     * 流入时间
     */
    @TableField("inflow_time")
    private LocalDateTime inflowTime;

    /**
     * 驻留时间
     * */
    @TableField("duration")
    private Long duration;

    /**
     * 是否已备案车牌记录
     */
    @TableField("is_plate_record_filed")
    private Boolean isPlateRecordFiled;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 已驶离
     * */
    @TableField("is_departed")
    private Boolean isDeparted;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 流出时间
     */
    @TableField("outflow_time")
    private LocalDateTime outflowTime;

    /**
     * 车辆备案状态
     * */
    @TableField("plate_record_filed_status")
    private String plateRecordFiledStatus;
    /**
     * 备案查询次数
     * */
    @TableField("plate_record_filed_query_times")
    private int plateRecordFiledQueryTimes;
    /**
     * 车牌类型
     * */
    @TableField("plate_num_type")
    private String plateNumType;
}
