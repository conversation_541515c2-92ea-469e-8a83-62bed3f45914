package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.deepinnet.spatiotemporalplatform.dal.type.StringListTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.OffsetDateTime;
import java.util.List;

/**
 * 算法侧的事件表实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "event", autoResultMap = true)
public class AlgorithmEventDO extends Model<AlgorithmEventDO> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 飞行计划id
     */
    @TableField("flight_task_id")
    private String flightTaskId;

    /**
     * 检测任务id
     */
    @TableField("detect_task_id")
    private String detectTaskId;

    /**
     * 事件类型
     */
    @TableField("event_type")
    private String eventType;

    /**
     * 事件描述
     */
    @TableField("event_description")
    private String eventDescription;

    /**
     * 车牌号
     */
    @TableField(value = "plate_numbers", typeHandler = StringListTypeHandler.class)
    private List<String> plateNumbers;

    /**
     * 取证图片
     */
    @TableField(value = "evidence_image_urls", typeHandler = StringListTypeHandler.class)
    private List<String> evidenceImageUrls;

    /**
     * 取证视频
     */
    @TableField(value = "evidence_video_urls", typeHandler = StringListTypeHandler.class)
    private List<String> evidenceVideoUrls;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 事件状态
     */
    @TableField("event_status")
    private String eventStatus;

    @TableField("sub_task_id")
    private String subTaskId;

    @TableField("car_id")
    private String carId;

    @TableField("event_start_time")
    private OffsetDateTime eventStartTime;

    @TableField("event_end_time")
    private OffsetDateTime eventEndTime;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private OffsetDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private OffsetDateTime gmtModified;

    @TableField("flight_batch_id")
    private String flightBatchId;
} 