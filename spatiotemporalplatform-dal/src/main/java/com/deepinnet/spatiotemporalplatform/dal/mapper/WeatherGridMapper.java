package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.WeatherGridDO;
import org.apache.ibatis.annotations.Param;
import org.locationtech.jts.geom.Polygon;

import java.util.List;


/**
 * 气象网格表 Mapper 接口
 */
public interface WeatherGridMapper extends BaseMapper<WeatherGridDO> {

    int upsertBatch(List<WeatherGridDO> list);

    List<WeatherGridDO> list(@Param("factor") String factor, @Param("polygon") Polygon polygon);
}