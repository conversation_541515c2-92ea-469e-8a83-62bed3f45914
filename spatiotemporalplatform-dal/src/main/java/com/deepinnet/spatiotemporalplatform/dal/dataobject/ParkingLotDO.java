package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 停车场表
 *
 * @TableName parking_lot
 */
@TableName(value = "third_party_parking_lot", autoResultMap = true)
@Data
public class ParkingLotDO implements Serializable {
    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 停车场编号
     */
    private String parkingLotCode;

    /**
     * 名称
     */
    private String name;

    /**
     * 类型
     */
    private String type;

    /**
     * 地址
     */
    private String address;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 维度
     */
    private String latitude;

    /**
     * 总车位数
     */
    private Integer totalSpaces;

    /**
     * 空余车位数
     */
    private Integer availableSpaces;

    /**
     * 开放时间段
     */
    private String openHours;

    /**
     * 状态
     */
    private String status;

    @TableField("tenant_id")
    private String tenantId;
}