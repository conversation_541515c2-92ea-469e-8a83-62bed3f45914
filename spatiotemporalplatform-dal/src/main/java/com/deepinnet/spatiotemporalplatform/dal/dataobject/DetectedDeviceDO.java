package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 侦测定位设备数据实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("detected_device")
public class DetectedDeviceDO {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 外部系统设备ID
     */
    private String externalId;

    /**
     * 厂家
     */
    private String manufacturer;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 品牌型号
     */
    private String brandModel;

    /**
     * 序列号
     */
    private String serialNo;

    /**
     * 基站类型
     */
    private String stationType;

    /**
     * 技术参数
     */
    private String techParams;

    /**
     * 数据标准
     */
    private String dataStandard;

    /**
     * 设备用途
     */
    private String equipUsage;

    /**
     * 安装环境
     */
    private String installEnvir;

    /**
     * 安装地址
     */
    private String installAdress;

    /**
     * 天线高度
     */
    private String aerialHeight;

    /**
     * 工作频率或频段
     */
    private String operatingFrequency;

    /**
     * 安装日期
     */
    private String installDate;

    /**
     * 安装数量
     */
    private Integer installCount;

    /**
     * 安装情况
     */
    private String installSituation;

    /**
     * 所在物业单位
     */
    private String propCompany;

    /**
     * 物业联系人
     */
    private String propLinkUser;

    /**
     * 物业联系人电话
     */
    private String linkPhone;

    /**
     * 设备维护人
     */
    private String deviceManager;

    /**
     * 设备联系人电话
     */
    private String devicePhone;

    /**
     * 维护要求
     */
    private String manageRequire;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String crtUserName;

    /**
     * 创建人ID
     */
    private String crtUserId;

    /**
     * 创建时间
     */
    private String crtTime;

    /**
     * 是否删除
     */
    @TableLogic(value = "0", delval = "1")
    private String isDeleted;

    /**
     * 修改人
     */
    private String updUserName;

    /**
     * 修改人ID
     */
    private String updUserId;

    /**
     * 修改时间
     */
    private String updTime;

    /**
     * 部门id
     */
    private String departId;

    /**
     * 部门ids（20级）
     */
    private String departIds;

        /**
     * 外部系统租户ID
     */
    private String externalTenantId;
    
    /**
     * 本系统租户ID
     */
    private String tenantId;

    /**
     * 团队ID
     */
    private String teamId;

    /**
     * 接收增益
     */
    private String receiverGain;

    /**
     * 发射功率
     */
    private String transPower;

    /**
     * 脉冲波形
     */
    private String pulseWaveform;

    /**
     * 极化方式
     */
    private String polarizationMode;

    /**
     * 噪声系数
     */
    private String figure;

    /**
     * 通讯系统原始带宽
     */
    private String bandWidth;

    /**
     * 现场照片
     */
    private String sitePicUrl;

    /**
     * 经度
     */
    private Double lon;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 水平覆盖范围
     */
    private String gcoverRage;

    /**
     * 垂直覆盖范围
     */
    private String vcoverRage;

    /**
     * 批次号
     */
    private Long batchNo;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 修改时间
     */
    private Date gmtModified;
} 