package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.ThirdPartyEventRepository;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.ThirdPartyEventDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.ThirdPartyEventMapper;
import org.springframework.stereotype.Repository;

/**
 * 第三方事件仓储实现类
 *
 * <AUTHOR>
 */
@Repository
public class ThirdPartyEventRepositoryImpl extends ServiceImpl<ThirdPartyEventMapper, ThirdPartyEventDO>
        implements ThirdPartyEventRepository {
}
