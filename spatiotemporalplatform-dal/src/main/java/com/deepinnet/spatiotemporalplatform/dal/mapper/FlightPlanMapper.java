package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightPlanDO;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightDemandPlanStatDO;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightDemandPlanStatQuery;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 飞行计划表，每个飞行任务包含多个飞行计划 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
public interface FlightPlanMapper extends BaseMapper<FlightPlanDO> {

    /**
     * 根据条件查询飞行计划
     * @param flightQuery 查询条件
     * @return 查询结果
     */
    List<FlightPlanDO> listFlightPlanByCondition(@Param("query") FlightQuery flightQuery);

    List<FlightDemandPlanStatDO> queryFlightDemandPlanStat(FlightDemandPlanStatQuery query);

    List<String> queryHasAchievementPlan();
}
