package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.GdAoiDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface GdAoiMapper extends BaseMapper<GdAoiDO> {

    /**
     * @param region
     * @return
     */
    List<GdAoiDO> getAoiList(@Param("region") String region);
}




