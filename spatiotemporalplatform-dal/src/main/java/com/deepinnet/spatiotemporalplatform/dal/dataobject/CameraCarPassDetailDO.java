package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * Description: 
 * Date: 2024/9/28
 * Author: lijunheng
 */
@Data
@TableName(value = "camera_car_pass_detail", autoResultMap = true)
public class CameraCarPassDetailDO extends Model<CameraCarPassDetailDO> {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_created")
    private Date gmtCreated;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified")
    private Date gmtModified;

    /**
     * 区域编码
     */
    @TableField(value = "area_code")
    private String areaCode;

    /**
     * 摄像头编号
     */
    @TableField(value = "camera_code")
    private String cameraCode;

    /**
     * 当前小时（yyyyMMddHH）
     */
    @TableField(value = "current_hour")
    private String currentHour;

    /**
     * 开始分钟
     */
    @TableField(value = "start_minute")
    private String startMinute;

    /**
     * 结束分钟
     */
    @TableField(value = "end_minute")
    private String endMinute;

    /**
     * 车牌号
     */
    @TableField(value = "plate_num")
    private String plateNum;

    /**
     * 过车时间戳
     */
    @TableField(value = "pass_car_time")
    private Date passCarTime;

    @TableField("tenant_id")
    private String tenantId;
}