package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import org.locationtech.jts.geom.Point;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.deepinnet.spatiotemporalplatform.dal.type.PGPointTypeHandler;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Getter
@Setter
@TableName(value = "flight_plan_achievement")
public class FlightPlanAchievementDO extends Model<FlightPlanAchievementDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 计划编号(flight_plan.plan_no)
     */
    @TableField("plan_no")
    private String planNo;

    /**
     * 鸢飞成果ID
     */
    @TableField("achievement_id")
    private String achievementId;

    /**
     * 鸢飞成果名称
     */
    @TableField("achievement_name")
    private String achievementName;

    /**
     * 鸢飞成果类型(IMAGE-图片;VIDEO-视频;2d-二维图;3d-三维图;PANORAMA-全景; REPORT-其它报告)
     */
    @TableField("achievement_type")
    private String achievementType;

    /**
     * 鸢飞成果时间
     */
    @TableField("achievement_time")
    private LocalDateTime achievementTime;

    /**
     * 鸢飞成果地址
     */
    @TableField("achievement_url")
    private String achievementUrl;

    /**
     * 鸢飞成果省市区地址
     */
    @TableField("achievement_address")
    private String achievementAddress;

    @TableField(value = "achievement_location", typeHandler = PGPointTypeHandler.class)
    private Point achievementLocation;

    /**
     * 鸢飞成果经纬度
     */
    @TableField(value = "achievement_location_str")
    private String achievementLocationStr;

    /**
     * 需求编号
     */
    @TableField(value = "demand_no")
    private String demandNo;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 更新时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
