package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;

/**
 * 天气预警数据对象
 *
 * <AUTHOR>
 */
@Data
@TableName("weather_warning")
public class WeatherWarningDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 航道编号
     */
    @TableField("airspace_code")
    private String airspaceCode;

    /**
     * 航道名称
     */
    @TableField("airspace_name")
    private String airspaceName;

    /**
     * 预警类型：降雨、温度、风速
     */
    @TableField("type")
    private String type;

    /**
     * 预警时间
     */
    @TableField("warning_time")
    private Long warningTime;

    /**
     * 风险预警编号
     */
    @TableField("warning_no")
    private String warningNo;

    /**
     * 预警状态
     */
    @TableField("status")
    private String status;

    /**
     * 预警内容
     */
    @TableField("content")
    private String content;

    /**
     * 预警标题
     */
    @TableField("title")
    private String title;

    @TableField("tenant_id")
    private String tenantId;
} 