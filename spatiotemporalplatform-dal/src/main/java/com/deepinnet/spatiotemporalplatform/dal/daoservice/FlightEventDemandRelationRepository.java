package com.deepinnet.spatiotemporalplatform.dal.daoservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.DemandArea;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightEventDemandRelationDO;

import java.util.List;

/**
 * 飞行事件需求关联表仓储接口
 *
 * <AUTHOR>
 */
public interface FlightEventDemandRelationRepository extends IService<FlightEventDemandRelationDO> {

    /**
     * 根据飞行任务编号和经纬度查询原始需求和区域序号
     *
     * @param flightTaskCode 飞行任务编号
     * @param longitude 经度
     * @param latitude 纬度
     * @return 原始需求编号和区域序号的映射结果
     */
    List<DemandArea> queryDemandAreaByTaskCodeAndLocation(String flightTaskCode, Double longitude, Double latitude);
} 