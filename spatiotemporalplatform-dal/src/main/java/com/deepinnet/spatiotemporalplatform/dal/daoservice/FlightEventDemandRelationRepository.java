package com.deepinnet.spatiotemporalplatform.dal.daoservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.DemandArea;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightEventDemandRelationDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.PlanDemandDO;

import java.util.List;

/**
 * 飞行事件需求关联表仓储接口
 *
 * <AUTHOR>
 */
public interface FlightEventDemandRelationRepository extends IService<FlightEventDemandRelationDO> {

    /**
     * 根据飞行任务编号和经纬度查询原始需求和区域序号
     *
     * @param flightTaskCode 飞行任务编号
     * @param longitude 经度
     * @param latitude 纬度
     * @param distance 如果是点位需要拓展的距离
     * @return 原始需求编号和区域序号的映射结果
     */
    List<DemandArea> queryDemandAreaByTaskCodeAndLocation(String flightTaskCode, Double longitude, Double latitude, Integer distance);

    /**
     * 根据计划编号列表查询计划需求关系
     *
     * @param planIdList 计划编号列表
     * @return 计划需求列表
     */
    List<PlanDemandDO> queryPlanListOfEmergencyDemand(List<String> planIdList);
}