package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.mapper.SurveillanceCameraMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.SurveillanceCameraConditionDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.SurveillanceCameraDO;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.SurveillanceCameraIService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【surveillance_video(监控视频表)】的数据库操作Service实现
 * @createDate 2024-07-26 17:13:05
 */
@Service
public class SurveillanceCameraIServiceImpl extends ServiceImpl<SurveillanceCameraMapper, SurveillanceCameraDO>
        implements SurveillanceCameraIService {

    @Override
    public List<SurveillanceCameraDO> queryCameraWithInArea(String areaCode, String type) {
        return baseMapper.queryCameraWithInArea(areaCode, type);
    }

    @Override
    public List<SurveillanceCameraDO> queryLineNearCamera(SurveillanceCameraConditionDO condition) {
        return baseMapper.queryLineNearCamera(condition);
    }

    @Override
    public List<SurveillanceCameraDO> queryAreaNearCamera(SurveillanceCameraConditionDO condition) {
        return baseMapper.queryAreaNearCamera(condition);
    }

    @Override
    public List<SurveillanceCameraDO> queryCameraListByIndex(Date afterCreateDataTime, int startIndex, int size) {
        LambdaQueryWrapper<SurveillanceCameraDO> queryWrapper = new LambdaQueryWrapper<>();
        if (afterCreateDataTime != null) {
            queryWrapper.gt(SurveillanceCameraDO::getGmtCreated, afterCreateDataTime);
        }
        queryWrapper.orderByAsc(SurveillanceCameraDO::getId);
        if (startIndex > 0) {
            queryWrapper.gt(SurveillanceCameraDO::getId, startIndex);
        }
        queryWrapper.last("limit " + size);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<SurveillanceCameraDO> findAreaCarCamerasWithExtends(String areaCode, int extendDistance) {
        return baseMapper.findAreaCarCamerasWithExtends(areaCode, extendDistance);
    }

    @Override
    public boolean isCameraInArea(double lng, double lat, String areaCode) {
        return baseMapper.isCameraInArea(lng, lat, areaCode);
    }

    @Override
    public List<SurveillanceCameraDO> queryWithGis(SurveillanceCameraConditionDO condition) {
        return baseMapper.queryWithGis(condition);
    }

    @Override
    public int upsertBatch(List<SurveillanceCameraDO> cameraList) {
        if(CollectionUtils.isEmpty(cameraList)) {
            return 0;
        }

        return baseMapper.upsertBatch(cameraList);
    }
}