package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.PoiNameMappingService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.PoiNameMappingDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.PoiNameMappingMapper;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PoiNameMappingServiceImpl extends ServiceImpl<PoiNameMappingMapper, PoiNameMappingDO> implements PoiNameMappingService {

    @Override
    public List<PoiNameMappingDO> findByName(String name) {
        return this.lambdaQuery()
                .eq(PoiNameMappingDO::getName, name)
                .list();
    }

    @Override
    public List<PoiNameMappingDO> findByIsDeleted(Boolean isDeleted) {
        return this.lambdaQuery()
                .eq(PoiNameMappingDO::getIsDeleted, isDeleted)
                .list();
    }

    @Override
    public List<PoiNameMappingDO> findByNameAndIsDeleted(String name, Boolean isDeleted) {
        return this.lambdaQuery()
                .eq(PoiNameMappingDO::getName, name)
                .eq(PoiNameMappingDO::getIsDeleted, isDeleted)
                .list();
    }
}