package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deepinnet.spatiotemporalplatform.dal.type.PGPointTypeHandler;
import lombok.Data;
import org.locationtech.jts.geom.Point;

import java.io.Serializable;
import java.util.Date;

/**
 * 警力部署表
 * @TableName police_deployment
 */
@TableName(value ="police_deployment", autoResultMap = true)
@Data
public class PoliceDeploymentDO implements Serializable {
    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 警员姓名
     */
    private String officerName;

    /**
     * 警员编号
     */
    private String officerId;

    /**
     * 警力类型
     */
    private String policeType;

    /**
     * 经纬度
     */
    @TableField(value = "coordinates", typeHandler = PGPointTypeHandler.class)
    private Point coordinates;

    /**
     * 所属区域code
     */
    private String areaCode;

    private String objectType;

    private String objectCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 设置人数
     */
    private Integer count;


    @TableField(value = "is_deleted")
    @TableLogic(value = "false", delval = "true")
    private Boolean isDeleted;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField("tenant_id")
    private String tenantId;

}