package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;

/**
 * 领航车gps消息
 */
@Data
@TableName(value = "gps_device", autoResultMap = true)
public class GpsDeviceDO implements Serializable {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private String deviceId;

    @TableField("tenant_id")
    private String tenantId;
}