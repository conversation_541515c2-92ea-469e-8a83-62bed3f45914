package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deepinnet.spatiotemporalplatform.dal.type.PGPolygonTypeHandler;
import lombok.Data;
import org.locationtech.jts.geom.Geometry;

/**
 * @TableName gd_ground_feature 高德地物人流
 */
@TableName(value = "gd_ground_feature", autoResultMap = true)
@Data
public class GdGroundFeatureDO {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 网格码
     */
    private String gridCode;

    /**
     * 地物客流
     */
    private String flow;

    /**
     * 当前网格对应的空间信息Polygon
     */
    @TableField(value = "grid_geometry", typeHandler = PGPolygonTypeHandler.class)
    private Geometry gridGeometry;

    @TableField("tenant_id")
    private String tenantId;
}