package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 基站数据表
 *
 * @TableName base_station
 */
@TableName(value = "base_station")
@Data
public class BaseStationDO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 外部系统的基站ID（对应BaseStationDataDTO中的id）
     */
    private String externalId;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 创建人
     */
    private String crtUserName;
    
    /**
     * 创建人ID
     */
    private String crtUserId;
    
    /**
     * 创建时间
     */
    private String crtTime;
    
    /**
     * 是否删除
     */
    private String isDeleted;
    
    /**
     * 修改人
     */
    private String updUserName;
    
    /**
     * 修改人ID
     */
    private String updUserId;
    
    /**
     * 修改时间
     */
    private String updTime;
    
    /**
     * 部门id
     */
    private String departId;
    
    /**
     * 部门ids（20级）
     */
    private String departIds;
    
    /**
     * 团队ID
     */
    private String teamId;
    
    /**
     * 小区ID（CI）
     */
    private String communityCi;
    
    /**
     * 小区名/aau名
     */
    private String aau;
    
    /**
     * 所属基站id
     */
    private String baseStationId;
    
    /**
     * 所属基站名称
     */
    private String baseStationName;
    
    /**
     * 所属省份
     */
    private String province;
    
    /**
     * 所属地市
     */
    private String city;
    
    /**
     * 所属区县
     */
    private String county;
    
    /**
     * 供应商
     */
    private String supplier;
    
    /**
     * 是否已接入AF
     */
    private String ifAf;
    
    /**
     * 测试场
     */
    private String testField;
    
    /**
     * 基站经度
     */
    private Double baseLon;
    
    /**
     * 基站纬度
     */
    private Double baseLat;
    
    /**
     * aau经度
     */
    private Double aauLon;
    
    /**
     * aau纬度
     */
    private Double aauLat;
    
    /**
     * 挂高
     */
    private String handingHeigh;
    
    /**
     * 方位
     */
    private String direction;
    
    /**
     * 俯仰角
     */
    private String pitch;
    
    /**
     * 数据批次号（用于标识同一批数据）
     */
    private Long batchNo;
    
    /**
     * 创建时间
     */
    private Date gmtCreated;
    
    /**
     * 修改时间
     */
    private Date gmtModified;
    
    /**
     * 外部系统租户ID
     */
    private String externalTenantId;
    
    /**
     * 本系统租户ID
     */
    private String tenantId;
} 