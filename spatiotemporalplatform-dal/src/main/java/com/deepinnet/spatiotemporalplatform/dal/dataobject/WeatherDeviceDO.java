package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 天气设备信息
 *
 * @TableName weather_device
 */
@TableName(value = "weather_device")
@Data
public class WeatherDeviceDO {
    
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 厂家
     */
    private String manufacturer;
    
    /**
     * 设备名称
     */
    private String deviceName;
    
    /**
     * 品牌型号
     */
    private String brandModel;
    
    /**
     * 序列号
     */
    private String serialNo;
    
    /**
     * 技术参数
     */
    private String techParams;
    
    /**
     * 数据标准
     */
    private String dataStandard;
    
    /**
     * 设备用途
     */
    private String equipUsage;
    
    /**
     * 安装环境
     */
    private String installEnvir;
    
    /**
     * 安装地址
     */
    private String installAdress;
    
    /**
     * 安装日期
     */
    private String installDate;
    
    /**
     * 安装数量
     */
    private Integer installCount;
    
    /**
     * 安装情况
     */
    private String installSituation;
    
    /**
     * 所在物业单位
     */
    private String propCompany;
    
    /**
     * 物业联系人
     */
    private String propLinkUser;
    
    /**
     * 设备维护人
     */
    private String deviceManager;
    
    /**
     * 经度
     */
    private Double lon;
    
    /**
     * 纬度
     */
    private Double lat;
    
    /**
     * 物业联系人电话
     */
    private String linkPhone;
    
    /**
     * 设备联系人电话
     */
    private String devicePhone;
    
    /**
     * 创建时间
     */
    private Date gmtCreated;
    
    /**
     * 修改时间
     */
    private Date gmtModified;

    private String tenantId;
} 