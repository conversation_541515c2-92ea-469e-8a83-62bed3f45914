package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.OssFileInfoService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.OssFileInfoDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.OssFileInfoMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【oss_file_info】的数据库操作Service实现
* @createDate 2025-04-02 16:50:41
*/
@Service
public class OssFileInfoServiceImpl extends ServiceImpl<OssFileInfoMapper, OssFileInfoDO>
    implements OssFileInfoService {

}




