package com.deepinnet.spatiotemporalplatform.dal.daoservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.PoiNameMappingDO;

import java.util.List;

public interface PoiNameMappingService extends IService<PoiNameMappingDO> {
    /**
     * 根据名称查询POI信息
     * @param name 名称
     * @return 查询结果
     */
    List<PoiNameMappingDO> findByName(String name);

    /**
     * 根据是否删除状态查询POI信息
     * @param isDeleted 是否删除
     * @return 查询结果
     */
    List<PoiNameMappingDO> findByIsDeleted(Boolean isDeleted);

    /**
     * 根据名称和是否删除状态查询POI信息
     * @param name 名称
     * @param isDeleted 是否删除
     * @return 查询结果
     */
    List<PoiNameMappingDO> findByNameAndIsDeleted(String name, Boolean isDeleted);
}