package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * <AUTHOR> wong
 * @create 2025/3/12 16:52
 * @Description
 */
@Data
public class IntelligenceDO {
    /**
     * 业务编号
     */
    private String bizNo;

    /**
     * 空域编号
     */
    private String airspaceCode;

    /**
     * 空域名称
     */
    private String airspaceName;

    /**
     * 预警类型：降雨、温度、风速
     */
    private String type;

    /**
     * 预警时间
     */
    private Long warningTime;

    /**
     * 预警状态
     */
    private String status;

    /**
     * 预警内容
     */
    private String content;

    /**
     * 预警标题
     */
    private String title;

    @TableField("tenant_id")
    private String tenantId;
}
