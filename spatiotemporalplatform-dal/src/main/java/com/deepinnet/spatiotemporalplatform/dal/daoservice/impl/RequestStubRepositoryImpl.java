package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.RequestStubDO;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.RequestStubRepository;
import com.deepinnet.spatiotemporalplatform.dal.mapper.RequestStubMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【request_stub(请求数据存根表)】的数据库操作Service实现
 * @createDate 2024-09-04 15:28:42
 */
@Service
public class RequestStubRepositoryImpl extends ServiceImpl<RequestStubMapper, RequestStubDO> implements RequestStubRepository {

}




