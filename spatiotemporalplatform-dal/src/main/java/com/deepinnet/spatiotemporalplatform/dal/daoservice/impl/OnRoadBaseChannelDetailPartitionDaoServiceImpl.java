package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.mapper.OnRoadBaseChannelDetailPartitionMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.OnRoadBaseChannelDetailPartitionDO;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.OnRoadBaseChannelDetailPartitionDaoService;
import org.springframework.stereotype.Service;

@Service
public class OnRoadBaseChannelDetailPartitionDaoServiceImpl 
    extends ServiceImpl<OnRoadBaseChannelDetailPartitionMapper, OnRoadBaseChannelDetailPartitionDO>
    implements OnRoadBaseChannelDetailPartitionDaoService {
} 