package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.mapper.LockObjectDOMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.LockObjectDO;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.LockObjectDaoService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Description:
 * Date: 2024/9/27
 * Author: lijunheng
 */
@Service
public class LockObjectDaoServiceImpl extends ServiceImpl<LockObjectDOMapper, LockObjectDO> implements LockObjectDaoService {
    @Override
    public int updateReentrantLock(LockObjectDO lockObjectDO) {
        return baseMapper.updateReentrantLock(lockObjectDO);
    }

    @Override
    public int updateReleaseLock(LockObjectDO lockObjectDO) {
        return baseMapper.updateReleaseLock(lockObjectDO);
    }

    @Override
    public int updateRenewExpirationLock(LockObjectDO lockObjectDO) {
        return baseMapper.updateRenewExpirationLock(lockObjectDO);
    }

    @Override
    public List<String> queryAllowObtainLockList(List<String> lockNameList) {
        if (CollectionUtils.isEmpty(lockNameList)) {
            return new ArrayList<>();
        }
        return baseMapper.queryAllowObtainLockList(lockNameList);
    }

}
