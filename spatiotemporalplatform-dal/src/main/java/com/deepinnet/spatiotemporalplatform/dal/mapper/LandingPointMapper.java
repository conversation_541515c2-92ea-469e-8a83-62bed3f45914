package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.LandingPointDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 起降点数据Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface LandingPointMapper extends BaseMapper<LandingPointDO> {

    /**
     * 根据外部ID查询起降点数据
     *
     * @param externalId 外部ID
     * @return 起降点数据
     */
    LandingPointDO selectByExternalId(@Param("externalId") String externalId);
} 