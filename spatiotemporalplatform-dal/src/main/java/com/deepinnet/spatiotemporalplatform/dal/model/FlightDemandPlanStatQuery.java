package com.deepinnet.spatiotemporalplatform.dal.model;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 飞行事件统计查询DTO
 *
 * <AUTHOR>
 */
@Data
public class FlightDemandPlanStatQuery implements Serializable {

    /**
     * 需求创建的时间
     */
    private LocalDateTime demandCreateTime;

    /**
     * 需求来源场景
     */
    private String demandScene;

    /**
     * 统计的需求类目
     */
    private List<String> categoryCodeList;

    /**
     * 统计的需求类型
     */
    private String demandType;

    private String userNo;

    private String tenantId;

    private String account;
} 