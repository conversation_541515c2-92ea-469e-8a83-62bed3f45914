package com.deepinnet.spatiotemporalplatform.dal.model;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 飞行事件查询DTO
 *
 * <AUTHOR>
 */
@Data
public class FlightEventsQuery {

    private static final long serialVersionUID = 1L;

    /**
     * 事件id
     */
    private String id;

    /**
     * 事件类型，例如违停、闯入禁区等
     */
    private String eventType;

    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 事件状态
     */
    private String status;

    /**
     * 事件发生地点
     */
    private String eventLocation;

    /**
     * 飞行任务id
     */
    private String flightTaskCode;

    /**
     * 事件发生开始时间
     */
    private LocalDateTime startTime;

    /**
     * 事件发生结束时间
     */
    private LocalDateTime endTime;

    private String userNo;

    private String demandType;

    private String tenantId;

    private LocalDateTime demandStartTime;

    private LocalDateTime demandEndTime;

    private List<String> accountList;

    private List<String> userNoList;

    private LocalDateTime planedTakeoffStartTime;

    private LocalDateTime planedTakeoffEndTime;

    private String demandScene;
} 