package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Creator zengjuerui
 * Date 2024-07-31
 **/

@EqualsAndHashCode(callSuper = true)
@Data
public class SurveillanceCameraConditionDO extends PageQueryDO {

    private String wkt;

    private Integer distance;

    private List<String> typeList;

    private String keyword;

    @TableField("tenant_id")
    private String tenantId;
}
