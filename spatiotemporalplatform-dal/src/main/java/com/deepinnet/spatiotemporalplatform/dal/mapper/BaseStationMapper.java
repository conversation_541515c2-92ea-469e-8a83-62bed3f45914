package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.BaseStationDO;
import org.apache.ibatis.annotations.Param;

/**
 * 基站数据Mapper接口
 *
 * <AUTHOR>
 */
public interface BaseStationMapper extends BaseMapper<BaseStationDO> {
    void deleteByTenantId(@Param("tenantId") String tenantId);
} 