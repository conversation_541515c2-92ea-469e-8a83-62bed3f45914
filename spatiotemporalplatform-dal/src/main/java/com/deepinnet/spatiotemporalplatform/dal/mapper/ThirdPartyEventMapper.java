package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.constant.DataSourceConstants;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.ThirdPartyEventDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 第三方事件 Mapper 接口
 *
 * <AUTHOR>
 */
@DS(DataSourceConstants.THIRD_PARTY_ALGORITHM)
@Mapper
public interface ThirdPartyEventMapper extends BaseMapper<ThirdPartyEventDO> {
}
