package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightDetectionDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 无人机数据上报表 Dao 接口
 */
public interface FlightDetectionMapper extends BaseMapper<FlightDetectionDO> {
    /**
     * 查询不在飞行计划内的无人机（使用 PageHelper 进行分页）
     */
    List<FlightDetectionDO> getUnplannedFlights(@Param("minId") Long minId);

    /**
     * 查询每个osid最新的一条数据
     * @param osIds
     * @return
     */
    List<FlightDetectionDO> getOsIdLatestFlight(@Param("osIds") List<String> osIds);
}