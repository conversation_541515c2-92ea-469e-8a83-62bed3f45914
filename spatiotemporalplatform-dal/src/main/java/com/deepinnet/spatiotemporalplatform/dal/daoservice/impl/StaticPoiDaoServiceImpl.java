package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.StaticPoiDO;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.StaticPoiDaoService;
import com.deepinnet.spatiotemporalplatform.dal.mapper.StaticPoiMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 静态核心POI表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-10
 */
@Service
public class StaticPoiDaoServiceImpl extends ServiceImpl<StaticPoiMapper, StaticPoiDO>
        implements StaticPoiDaoService {

    @Override
    public StaticPoiDO getByPoiId(String poiId) {
        return getOne(Wrappers.lambdaQuery(StaticPoiDO.class)
                .eq(StaticPoiDO::getPoiId, poiId));
    }
} 