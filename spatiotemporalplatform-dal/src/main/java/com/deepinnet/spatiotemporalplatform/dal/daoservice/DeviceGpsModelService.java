package com.deepinnet.spatiotemporalplatform.dal.daoservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.DeviceGpsModelDO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【device_gps_model】的数据库操作Service
* @createDate 2024-08-30 10:58:25
*/
public interface DeviceGpsModelService extends IService<DeviceGpsModelDO> {

    List<DeviceGpsModelDO> getByDeviceIds(List<String> deviceIds);

}
