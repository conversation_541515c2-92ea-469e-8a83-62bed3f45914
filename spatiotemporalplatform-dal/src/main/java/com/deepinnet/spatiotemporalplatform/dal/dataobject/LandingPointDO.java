package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deepinnet.spatiotemporalplatform.dal.type.PGPointTypeHandler;
import lombok.Data;
import org.locationtech.jts.geom.Point;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 起降点数据DO
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "landing_point", autoResultMap = true)
public class LandingPointDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 外部系统ID
     */
    @TableField("external_id")
    private String externalId;

    /**
     * 编号
     */
    @TableField("code")
    private String code;

    /**
     * 类型
     */
    @TableField("type")
    private String type;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 海拔高度
     */
    @TableField("alt")
    private Integer alt;

    /**
     * 高度
     */
    @TableField("height")
    private Integer height;

    /**
     * 半径
     */
    @TableField("radius")
    private Integer radius;

    /**
     * 坐标点
     */
    @TableField("point")
    private String point;

    /**
     * GCJ02坐标系的地理位置点
     */
    @TableField(value = "point_gcj02", typeHandler = PGPointTypeHandler.class)
    private Point pointGcj02;

    /**
     * 详细地址
     */
    @TableField("address")
    private String address;

    /**
     * 点或名称
     */
    @TableField("point_or_name")
    private String pointOrName;

    /**
     * 团队ID
     */
    @TableField("team_id")
    private String teamId;

    /**
     * 创建类型
     */
    @TableField("crt_type")
    private String crtType;

    /**
     * 创建人ID
     */
    @TableField("crt_user_id")
    private String crtUserId;

    /**
     * 创建时间
     */
    @TableField("crt_time")
    private Long crtTime;

    /**
     * 创建人名称
     */
    @TableField("crt_user_name")
    private String crtUserName;

    /**
     * 修改人ID
     */
    @TableField("upd_user_id")
    private String updUserId;

    /**
     * 修改时间
     */
    @TableField("upd_time")
    private String updTime;

    /**
     * 修改人名称
     */
    @TableField("upd_user_name")
    private String updUserName;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 是否禁用
     */
    @TableField("is_disabled")
    private String isDisabled;

    /**
     * 部门ID
     */
    @TableField("depart_id")
    private String departId;

    /**
     * 部门IDs
     */
    @TableField("depart_ids")
    private String departIds;

    /**
     * 外部系统租户ID
     */
    @TableField("external_tenant_id")
    private String externalTenantId;

    /**
     * 本系统租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 所有人
     */
    @TableField("owner")
    private String owner;

    /**
     * 运营人
     */
    @TableField("operator")
    private String operator;

    /**
     * 运营人联系方式
     */
    @TableField("phone_number")
    private String phoneNumber;

    /**
     * 业务类型
     */
    @TableField("busi_type")
    private String busiType;

    /**
     * 终端ID
     */
    @TableField("terminal_id")
    private String terminalId;

    /**
     * 摄像头编号
     */
    @TableField("camera_no")
    private String cameraNo;

    /**
     * 摄像头URL
     */
    @TableField("camera_url")
    private String cameraUrl;

    /**
     * 所属街道
     */
    @TableField("street")
    private String street;

    /**
     * 准确度
     */
    @TableField("accuracy")
    private String accuracy;

    /**
     * 起降场类型
     */
    @TableField("air_type")
    private String airType;

    /**
     * 分页开始
     */
    @TableField("page_start")
    private String pageStart;

    /**
     * 分页大小
     */
    @TableField("page_size")
    private String pageSize;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;
} 