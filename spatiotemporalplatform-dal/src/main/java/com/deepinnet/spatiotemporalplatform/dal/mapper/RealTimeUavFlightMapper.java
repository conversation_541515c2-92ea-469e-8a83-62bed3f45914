package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.RealTimeUavFlightDO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 实时无人机飞行数据表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
public interface RealTimeUavFlightMapper extends BaseMapper<RealTimeUavFlightDO> {
    // 高性能版本
    int countDistinctFlightsFast(
            @Param("planIds") List<String> planIds,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    List<RealTimeUavFlightDO> queryLatestFlights(@Param("planIds") List<String> planIds, @Param("uavIds") List<String> uavIds, @Param("warningPlanIds") List<String> warningPlanIds);

    List<RealTimeUavFlightDO> queryPlanFlights(@Param("planIds") List<String> planIds, @Param("pageNo") Integer pageNo, @Param("pageSize") Integer pageSize);

    RealTimeUavFlightDO queryLatestFlightsByReportTime(@Param("reportTime") LocalDateTime reportTime, @Param("planId") String planId);

    List<RealTimeUavFlightDO> queryFlightPlanIdLastFlightId(@Param("planIds") List<String> planIds);

    List<RealTimeUavFlightDO> queryFlightPlanLastStatus(@Param("planIds") List<String> planIds);
}
