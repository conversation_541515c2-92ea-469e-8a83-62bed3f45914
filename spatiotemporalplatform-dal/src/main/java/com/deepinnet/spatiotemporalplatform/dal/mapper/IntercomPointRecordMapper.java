package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.IntercomPointRecordDO;
import org.apache.ibatis.annotations.Param;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;

import java.util.Date;
import java.util.List;

/**
 * Description: 
 * Date: 2024/11/7
 * Author: lijunheng
 */
public interface IntercomPointRecordMapper extends BaseMapper<IntercomPointRecordDO> {

    List<IntercomPointRecordDO>  queryNearIntercomPoint(@Param("point") Point point, @Param("distance") Double distance);


    void saveOrUpdateBatchCustom(List<IntercomPointRecordDO> list);

    List<IntercomPointRecordDO> queryPolygonContainsAndBeforeTime(@Param("areaWkt") String areaWkt, @Param("time") Date time);

    List<IntercomPointRecordDO> queryLatestRecordByCode(List<String> codeList);

}