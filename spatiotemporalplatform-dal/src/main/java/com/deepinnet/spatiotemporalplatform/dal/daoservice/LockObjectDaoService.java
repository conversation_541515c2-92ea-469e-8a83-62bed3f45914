package com.deepinnet.spatiotemporalplatform.dal.daoservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.LockObjectDO;

import java.util.List;

/**
 * Description:
 * Date: 2024/9/27
 * Author: lijunheng
 */
public interface LockObjectDaoService extends IService<LockObjectDO> {

    int updateReentrantLock(LockObjectDO lockObjectDO);

    int updateReleaseLock(LockObjectDO lockObjectDO);

    int updateRenewExpirationLock(LockObjectDO lockObjectDO);

    List<String> queryAllowObtainLockList(List<String> lockNameList);
}
