package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deepinnet.spatiotemporalplatform.dal.type.PGPolygonTypeHandler;
import lombok.Data;
import org.locationtech.jts.geom.Polygon;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 区域表
 * @TableName area
 */
@TableName(value ="area", autoResultMap = true)
@Data
public class AreaDO implements Serializable {
    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    @TableField(value = "id", insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Integer id;

    /**
     * 城市code
     */
    private String adCode;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 区域类型
     */
    private String areaType;

    /**
     * 人流区域code
     */
    private String geoAreaCode;

    /**
     * 车流区域code
     */
    private String geoBigAreaCode;

    /**
     * 人流区域
     */
    @TableField(value = "geo", typeHandler = PGPolygonTypeHandler.class)
    private Polygon geo;

    /**
     * 车流区域
     */
    @TableField(value = "geo_big", typeHandler = PGPolygonTypeHandler.class)
    private Polygon geoBig;

    /**
     * 描述
     */
    private String description;

    /**
     * 面积（平方公里）
     */
    private BigDecimal areaSize;

    /**
     * 负责人
     */
    private String responsiblePerson;

    /**
     * 上级区域ID
     */
    private Integer superiorAreaId;

    /**
     * 是否重点监测区域
     */
    private Boolean isKeyMonitorArea;

    /**
     * 负责人电话
     */
    private String responsiblePersonPhone;

    /**
     * 安保开始时间
     */
    private Date securityPeriodStart;

    /**
     * 安保结束时间
     */
    private Date securityPeriodEnd;

    @TableField(value = "is_deleted")
    @TableLogic(value = "false", delval = "true")
    private Boolean deleted;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField("tenant_id")
    private String tenantId;

    /**
     * 是否是车辆限制区域
     * */
    private Boolean inVehicleRestrictedArea;
}