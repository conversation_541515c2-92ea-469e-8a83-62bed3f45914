package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.GdGroundFeatureDO;
import org.apache.ibatis.annotations.Param;
import org.locationtech.jts.geom.Point;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2025-01-14 16:02:48
 */
public interface GdGroundFeatureMapper extends BaseMapper<GdGroundFeatureDO> {

    List<GdGroundFeatureDO> getGroundFeatureList(@Param("pointList") List<Point> pointList, @Param("region") String region);
}




