package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * @TableName weather_grid
 */
@TableName(value = "weather_warning_data")
@Data
public class WeatherWarningDataDO {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     * 天气预警编号
     */
    private String warningNo;

    /**
     * 气象文件的链接
     */
    private String url;

    /**
     * 请求时间
     */
    private Date requestTime;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 更新时间
     */
    private Date gmtModified;

    @TableField("tenant_id")
    private String tenantId;
}