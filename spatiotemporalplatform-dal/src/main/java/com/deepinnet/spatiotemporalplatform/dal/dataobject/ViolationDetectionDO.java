package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deepinnet.spatiotemporalplatform.dal.type.PGPointTypeHandler;
import lombok.Data;
import org.locationtech.jts.geom.Geometry;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 违规检测表数据对象
 * 存储飞行器违规行为的检测记录
 *
 * <AUTHOR>
 */
@Data
@TableName("violation_detection")
public class ViolationDetectionDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID，自1开始自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 违规时间：违规行为发生的时间点
     */
    private LocalDateTime violationTime;

    /**
     * 违规类型：违规行为的分类，如超高飞行、禁飞区飞行等
     */
    private String violationType;

    /**
     * 运营主体：违规飞行器的运营方或责任单位
     */
    private String operatingEntity;

    /**
     * 飞行器SN码：违规飞行器的序列号或唯一标识
     */
    private String aircraftSn;

    /**
     * 违规详情：违规行为的详细描述
     */
    private String violationDetails;

    /**
     * 坐标：违规行为发生位置的地理坐标
     */
    @TableField(value = "coordinates", typeHandler = PGPointTypeHandler.class)
    private Geometry coordinates;

    /**
     * 高度：违规行为发生的高度，单位可为米
     */
    private String altitude;

    /**
     * 创建时间：记录创建的时间戳
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间：记录最后修改的时间戳
     */
    private LocalDateTime gmtModified;

    @TableField("tenant_id")
    private String tenantId;
} 