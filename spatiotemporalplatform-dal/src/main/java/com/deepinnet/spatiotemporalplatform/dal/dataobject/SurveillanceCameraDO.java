package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deepinnet.spatiotemporalplatform.dal.type.PGPointTypeHandler;
import lombok.Data;
import org.locationtech.jts.geom.Point;

import java.io.Serializable;
import java.util.Date;

/**
 * 监控视频表
 * @TableName surveillance_video
 */

@Data
@TableName(value ="surveillance_cameras", autoResultMap = true)
public class SurveillanceCameraDO implements Serializable {
    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 监控视频编号
     */
    private String cameraCode;

    /**
     * 名称
     */
    private String name;

    /**
     * 类型
     */
    private String type;

    /**
     * 安装位置
     */
    private String installationLocation;

    /**
     * 经纬度
     */
    @TableField(value = "coordinates", typeHandler = PGPointTypeHandler.class)
    private Point coordinates;

    /**
     * 经度
     */
    private String lng;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 视频地址（本地mock用）
     */
    private String videoStreamUrl;

    /**
     * 有无过车数据
     */
    private Boolean hasPassCarData;

    /**
     * 摄像头在线状态
     */
    private String status;

    /**
     * 摄像头上报来源
     */
    private String source;

    private Boolean isDeleted;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField("tenant_id")
    private String tenantId;
}