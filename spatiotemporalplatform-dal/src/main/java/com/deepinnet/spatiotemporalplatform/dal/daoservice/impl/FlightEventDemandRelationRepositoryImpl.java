package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.FlightEventDemandRelationRepository;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.DemandArea;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightEventDemandRelationDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.PlanDemandDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.FlightEventDemandRelationMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 飞行事件需求关联表仓储实现类
 *
 * <AUTHOR>
 */
@Repository
public class FlightEventDemandRelationRepositoryImpl extends ServiceImpl<FlightEventDemandRelationMapper, FlightEventDemandRelationDO>
        implements FlightEventDemandRelationRepository {

    @Override
    public List<DemandArea> queryDemandAreaByTaskCodeAndLocation(String flightTaskCode, Double longitude, Double latitude, Integer distance) {
        return baseMapper.queryDemandAreaByTaskCodeAndLocation(flightTaskCode, longitude, latitude, distance);
    }

    @Override
    public List<PlanDemandDO> queryPlanListOfEmergencyDemand(List<String> planIdList) {
        return baseMapper.queryPlanListOfEmergencyDemand(planIdList);
    }
} 