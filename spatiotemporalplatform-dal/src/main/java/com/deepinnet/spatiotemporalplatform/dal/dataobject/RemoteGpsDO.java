package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * GpsModel
 * Author: chenkaiyang
 * Date: 2024/11/22
 */
@Data
@TableName(value = "view_qzjj", autoResultMap = true)
public class RemoteGpsDO {
    @TableField(value = "k02_user_account")
    private String userAccount;

    @TableField(value = "f01_device_username")
    private String userName;

    private String longitude;

    private String latitude;

    private String lng;

    private String lat;

    private Date gpsArriveTime;

    @TableField(value = "lastUpdateTime")
    private Date lastUpdateTime;

    @TableField(value = "is_online")
    private Integer online;

    private Date onlineTime;

    private Date offlineTime;

    @TableField(value = "k01_enterprise_id")
    private String enterpriseId;

    @TableField(value = "f03_enterprise_name")
    private String enterpriseName;

    @TableField("tenant_id")
    private String tenantId;
}
