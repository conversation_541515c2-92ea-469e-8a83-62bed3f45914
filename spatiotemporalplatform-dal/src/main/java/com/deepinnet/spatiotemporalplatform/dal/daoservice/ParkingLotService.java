package com.deepinnet.spatiotemporalplatform.dal.daoservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.ParkingLotDO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【parking_lot(停车场表)】的数据库操作Service
 * @createDate 2024-07-26 17:01:25
 */
public interface ParkingLotService extends IService<ParkingLotDO> {

    void saveOrUpdateParkingLot(ParkingLotDO parkingLotDO);

    List<ParkingLotDO> listByGeoAndType(String geoWKT, String type);

    ParkingLotDO getByParkingLotCode(String s);
}
