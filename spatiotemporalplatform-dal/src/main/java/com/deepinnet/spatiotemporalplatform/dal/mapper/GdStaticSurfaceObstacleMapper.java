package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.GdStaticSurfaceObstacleDO;
import org.apache.ibatis.annotations.Param;
import org.locationtech.jts.geom.Point;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
public interface GdStaticSurfaceObstacleMapper extends BaseMapper<GdStaticSurfaceObstacleDO> {

    /**
     * 根据区域和类型查询POI
     * @param region 区域
     * @param pointList 坐标
     * @return POI列表
     */
    List<GdStaticSurfaceObstacleDO> getStaticSurfaceObstacles(@Param("gridCodes") List<String> bd2DGridLocationCodes, @Param("pointList") List<Point> pointList, @Param("region") String region);
}
