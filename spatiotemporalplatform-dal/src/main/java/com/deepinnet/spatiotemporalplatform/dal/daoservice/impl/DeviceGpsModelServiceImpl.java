package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.mapper.DeviceGpsModelMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.DeviceGpsModelDO;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.DeviceGpsModelService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【device_gps_model】的数据库操作Service实现
* @createDate 2024-08-30 10:58:25
*/
@Service
public class DeviceGpsModelServiceImpl extends ServiceImpl<DeviceGpsModelMapper, DeviceGpsModelDO>
    implements DeviceGpsModelService {

    @Override
    public List<DeviceGpsModelDO> getByDeviceIds(List<String> deviceIds) {
        LambdaQueryWrapper<DeviceGpsModelDO> wrappers = Wrappers.lambdaQuery(DeviceGpsModelDO.class)
                .in(DeviceGpsModelDO::getDeviceId, deviceIds);
        return super.list(wrappers);
    }
}