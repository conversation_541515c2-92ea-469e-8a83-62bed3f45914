package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.NavigateEquipmentDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 导航设备数据Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface NavigateEquipmentMapper extends BaseMapper<NavigateEquipmentDO> {

    /**
     * 根据外部ID查询导航设备
     *
     * @param externalId 外部ID
     * @return 导航设备数据
     */
    NavigateEquipmentDO selectByExternalId(@Param("externalId") String externalId);
} 