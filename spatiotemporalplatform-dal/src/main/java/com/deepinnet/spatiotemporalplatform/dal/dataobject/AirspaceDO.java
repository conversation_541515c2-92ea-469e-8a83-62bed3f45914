package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.deepinnet.spatiotemporalplatform.dal.type.*;
import lombok.*;
import org.locationtech.jts.geom.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 空域数据对象
 * 航道和空域现在都在 airspace 表
 * rangeType 1-航道，2-空域。暂时只有 1,2 的数据
 * width 宽度，单位千米
 * wkt1 线，rangeType 为1时有值，业务理解为航道的线
 * wkt2 面，rangeType 为 1,2 时都有值，业务理解为航道或空域的面，为 1 时是通过 wkt1 加上 width 缓冲区获得
 * origin_wkt 存原始点数据，业务上不用这个字段
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@Getter
@Setter
@TableName(value = "airspace", autoResultMap = true)
public class AirspaceDO extends Model<AirspaceDO> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 空域id
     */
    @TableField(value = "airspace_id")
    private String airspaceId;
    /**
     * 空域代码
     */
    @TableField("code")
    private String code;

    /**
     * 空域id
     */
    @TableField("business_code")
    private String businessCode;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 使用状态，开放、关闭、限制
     */
    @TableField("status")
    private String status;

    /**
     * 空间三维
     */
    @TableField(value = "wkt_3", typeHandler = PGLineStringTypeHandler.class)
    private LineString wkt3;

    /**
     * 二维WKT
     */
    @TableField(value = "wkt_2", typeHandler = PGPolygonTypeHandler.class)
    private Polygon wkt2;

    /**
     * @see com.deepinnet.spatiotemporalplatform.skyflow.enums.YuanFeiAirSpaceRangeTypeEnum
     */
    @TableField("range_type")
    private String rangeType;
    /**
     * 原始航道线
     */
    @TableField(value = "wkt_1", typeHandler = PGLineStringTypeHandler.class)
    private LineString wkt1;

    /**
     * 原始 wkt
     */
    @TableField("origin_wkt")
    private String originWkt;

    /**
     * 航线长度
     */
    @TableField("length")
    private Integer length;

    /**
     * 起飞地坐标
     */
    @TableField(value = "begin_addr", typeHandler = PGPointTypeHandler.class)
    private Point beginAddr;

    /**
     * 降落地坐标
     */
    @TableField(value = "end_addr", typeHandler = PGPointTypeHandler.class)
    private Point endAddr;

    /**
     * 航线宽度
     */
    @TableField("width")
    private Integer width;

    /**
     * 航线高度
     */
    @TableField("height")
    private Integer height;

    /**
     * 半径
     */
    @TableField("radius")
    private Integer radius;

    /**
     * 最小高度
     */
    @TableField("min_height")
    private Integer minHeight;

    /**
     * 最大高度
     */
    @TableField("max_height")
    private Integer maxHeight;

    /**
     * 形状、长方体、圆柱体、不规则体
     */
    @TableField("shape_type")
    private Integer shapeType;

    /**
     * 1.公有空域2.私有空域
     */
    @TableField("private_type")
    private String privateType;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @TableField("tenant_id")
    private String tenantId;

    @TableField("source")
    private String source;

    /**
     * 面积，平方千米
     * */
    @TableField("area")
    private Double area;
} 