package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightPlanSyncLogDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.FlightPlanSyncLogDao;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.FlightPlanSyncLogRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-07
 */
@Service
public class FlightPlanSyncLogRepositoryImpl extends ServiceImpl<FlightPlanSyncLogDao, FlightPlanSyncLogDO> implements FlightPlanSyncLogRepository {

}
