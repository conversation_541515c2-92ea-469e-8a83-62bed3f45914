package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.RestrictedAreaVehicleAccessRecordDO;
import com.deepinnet.spatiotemporalplatform.dal.model.RestrictedAreaVehiclePassageRecordStat;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import static com.deepinnet.spatiotemporalplatform.dal.constant.DataSourceConstants.CAR_PASS_STATICS;

/**
 * Creator zengjuerui
 * Date 2025-08-14
 **/
@DS(CAR_PASS_STATICS)
public interface RestrictedAreaVehicleAccessRecordDOMapper extends BaseMapper<RestrictedAreaVehicleAccessRecordDO> {

    List<RestrictedAreaVehicleAccessRecordDO> listLastOneByPlateNumList(@Param("areaCode") String areaCode, @Param("plateNumList") List<String> plateNumList);

    RestrictedAreaVehiclePassageRecordStat listAreaStat(@Param("areaCode") String areaCode);

    List<RestrictedAreaVehicleAccessRecordDO> listByAreaCode(@Param("areaCode") String areaCode, @Param("isIllegal") Boolean isIllegal);
}
