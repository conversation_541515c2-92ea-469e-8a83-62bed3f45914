package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deepinnet.spatiotemporalplatform.dal.type.PGMultiPolygonTypeHandler;
import lombok.Data;
import org.locationtech.jts.geom.Geometry;

import java.io.Serializable;

/**
 * 领航车gps消息
 */
@Data
@TableName(value = "gd_aoi", autoResultMap = true)
public class GdAoiDO implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField(value = "wkt", typeHandler = PGMultiPolygonTypeHandler.class)
    private Geometry wkt;

    private String poiId;

    private String name;

    private String adCode;

    private String center;

    private String area;

    private String aoiId;

    @TableField("tenant_id")
    private String tenantId;
}