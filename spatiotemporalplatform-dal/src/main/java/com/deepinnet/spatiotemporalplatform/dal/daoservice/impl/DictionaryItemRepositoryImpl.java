package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.DictionaryItemRepository;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.DictionaryItemDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.DictionaryItemMapper;
import org.springframework.stereotype.Repository;

/**
 * 字典项仓储实现类
 *
 * <AUTHOR>
 */
@Repository
public class DictionaryItemRepositoryImpl extends ServiceImpl<DictionaryItemMapper, DictionaryItemDO>
        implements DictionaryItemRepository {
} 