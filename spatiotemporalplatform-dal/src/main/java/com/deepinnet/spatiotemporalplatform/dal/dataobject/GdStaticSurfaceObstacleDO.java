package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deepinnet.spatiotemporalplatform.dal.type.PGPolygonTypeHandler;
import lombok.Data;
import org.locationtech.jts.geom.Geometry;

import java.time.LocalDateTime;

/**
 * <p>
 *     静态地表障碍物表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Data
@TableName(value = "gd_static_surface_obstacle", autoResultMap = true)
public class GdStaticSurfaceObstacleDO {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 网格码
     */
    @TableField("grid_code")
    private String gridCode;

    /**
     * 静态障碍物
     */
    @TableField("static_obstacle")
    private String staticObstacle;

    /**
     * 当前网格对应的空间信息Polygon
     */
    @TableField(value = "grid_geometry", typeHandler = PGPolygonTypeHandler.class)
    private Geometry gridGeometry;

    @TableField(value = "center")
    private String center;

    @TableField(value = "grid_area")
    private String gridArea;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 创建时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    @TableField("tenant_id")
    private String tenantId;
}