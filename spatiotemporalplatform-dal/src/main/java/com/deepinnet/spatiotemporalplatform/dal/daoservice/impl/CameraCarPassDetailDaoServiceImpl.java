package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.mapper.CameraCarPassDetailMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.CameraCarPassDetailDO;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.CameraCarPassDetailDaoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;

/**
 * Description:
 * Date: 2024/9/28
 * Author: lijunheng
 */
@Service
@Slf4j
public class CameraCarPassDetailDaoServiceImpl extends ServiceImpl<CameraCarPassDetailMapper, CameraCarPassDetailDO> implements CameraCarPassDetailDaoService {


    @Override
    public List<CameraCarPassDetailDO> queryLatestCarPassDetail(String areaCode, String currentHour) {
        if (StringUtils.isBlank(currentHour) || StringUtils.isBlank(areaCode)) {
            return Arrays.asList();
        }

        LambdaQueryWrapper<CameraCarPassDetailDO> wrapper = Wrappers.lambdaQuery(CameraCarPassDetailDO.class)
                .eq(StringUtils.isNotBlank(areaCode), CameraCarPassDetailDO::getAreaCode, areaCode)
                .eq(CameraCarPassDetailDO::getCurrentHour, currentHour)
                .orderByDesc(CameraCarPassDetailDO::getStartMinute).last("limit 1");

        List<CameraCarPassDetailDO> list = super.list(wrapper);

        if (CollectionUtils.isEmpty(list)) {
            log.info("currentHour car pass detail is empty:{},{}", currentHour, areaCode);
            return Arrays.asList();
        }

        String startMinute = list.get(0).getStartMinute();
        wrapper = Wrappers.lambdaQuery(CameraCarPassDetailDO.class)
                .eq(CameraCarPassDetailDO::getAreaCode, areaCode)
                .eq(CameraCarPassDetailDO::getCurrentHour, currentHour)
                .eq(CameraCarPassDetailDO::getStartMinute, startMinute);
        return super.list(wrapper);
    }

}
