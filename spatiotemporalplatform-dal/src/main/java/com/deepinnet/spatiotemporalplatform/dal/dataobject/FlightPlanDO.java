package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 飞行计划表，每个飞行任务包含多个飞行计划
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
@Getter
@Setter
@TableName("flight_plan")
public class FlightPlanDO extends Model<FlightPlanDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 外部计划编号（服务商计划编号）
     */
    @TableField("out_plan_no")
    private String outPlanNo;

    /**
     * 计划名称
     */
    @TableField("plan_name")
    private String planName;

    /**
     * 需求名称
     */
    @TableField("requirement_name")
    private String requirementName;

    /**
     * 场景码
     */
    @TableField("scence_code")
    private String scenceCode;

    /**
     * 业务编号
     */
    @TableField("biz_no")
    private String bizNo;

    @TableField("mission_id")
    private String missionId;

    /**
     * 飞行计划唯一标识
     */
    @TableField("plan_id")
    private String planId;

    /**
     * 申请计划类型:1-长期;2-次日;3-当日
     */
    @TableField("apply_type")
    private Integer applyType;

    /**
     * 关联长期计划的ID
     */
    @TableField("related_id")
    private String relatedId;

    /**
     * 无人机ID
     */
    @TableField("uav_id")
    private String uavId;

    /**
     * 无人机型号
     */
    @TableField("uav_model")
    private String uavModel;

    /**
     * 申请人名称
     */
    @TableField("apply_user_name")
    private String applyUserName;

    /**
     * 飞行计划状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 计划起飞时间
     */
    @TableField("planed_takeoff_time")
    private Long planedTakeoffTime;

    /**
     * 计划降落时间
     */
    @TableField("planed_landing_time")
    private Long planedLandingTime;

    /**
     * 是否实名
     */
    @TableField("real_ame_verification")
    private Boolean realNameVerification;

    /**
     * 飞手（操作员）
     */
    @TableField("operator_user")
    private String operatorUser;

    /**
     * 飞手联系电话
     */
    @TableField("operator_phone")
    private String operatorPhone;

    /**
     * 起飞机场
     */
    @TableField("landing_aerodrome_id")
    private String landingAerodromeId;

    /**
     * 降落机场
     */
    @TableField("arrival_aerodrome_id")
    private String arrivalAerodromeId;

    /**
     * 备降机场
     */
    @TableField("alternate_aerodrome_id")
    private String alternateAerodromeId;

    /**
     * 飞行区域ID
     */
    @TableField("airspace_id")
    private String airspaceId;

    /**
     * 计划飞行高度
     */
    @TableField("planed_altitude")
    private Long planedAltitude;

    /**
     * 飞行单位
     */
    @TableField("flight_unit")
    private String flightUnit;

    /**
     * 飞行单位ID
     */
    @TableField("flight_unit_id")
    private String flightUnitId;

    @TableField("plan_create_time")
    private LocalDateTime planCreateTime;

    @TableField("crt_user_name")
    private String crtUserName;

    @TableField("crt_user_id")
    private String crtUserId;

    @TableField("flight_report")
    private String flightReport;

    @TableField("monitor_url")
    private String monitorUrl;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
