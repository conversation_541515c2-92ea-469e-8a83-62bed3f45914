package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.deepinnet.spatiotemporalplatform.dal.type.StringListTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.OffsetDateTime;
import java.util.List;

/**
 * 第三方事件表实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "third_party_event", autoResultMap = true)
public class ThirdPartyEventDO extends Model<ThirdPartyEventDO> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 检测任务id
     */
    @TableField("task_id")
    private Long taskId;

    /**
     * 飞行任务ID
     */
    @TableField("flight_task_id")
    private String flightTaskId;

    /**
     * 架次ID
     */
    @TableField("flight_sortie_id")
    private String flightSortieId;

    /**
     * 事件类型代码
     */
    @TableField("event_type")
    private String eventType;

    /**
     * 事件描述
     */
    @TableField("event_description")
    private String eventDescription;

    /**
     * 事件开始时间
     */
    @TableField("event_start_time")
    private OffsetDateTime eventStartTime;

    /**
     * 事件结束时间
     */
    @TableField("event_end_time")
    private OffsetDateTime eventEndTime;

    /**
     * 取证图片
     */
    @TableField(value = "evidence_image_urls", typeHandler = StringListTypeHandler.class)
    private List<String> evidenceImageUrls;

    /**
     * 取证视频
     */
    @TableField(value = "evidence_video_urls", typeHandler = StringListTypeHandler.class)
    private List<String> evidenceVideoUrls;

    /**
     * 事件状态
     */
    @TableField("event_status")
    private String eventStatus;

    /**
     * 事件数据
     */
    @TableField("event_data")
    private String eventData;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 位置字符串
     */
    @TableField("location_str")
    private String locationStr;

    /**
     * 地址
     */
    @TableField("address")
    private String address;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private OffsetDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private OffsetDateTime gmtModified;
}
