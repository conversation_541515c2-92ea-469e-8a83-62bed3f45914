package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 机场（起降场地）信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
@Getter
@Setter
@TableName("aerodrome")
public class AerodromeDO extends Model<AerodromeDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 机场唯一标识
     */
    @TableField("uav_port_id")
    private String uavPortId;

    /**
     * 机场编号
     */
    @TableField("code")
    private String code;

    /**
     * 高度
     */
    @TableField("height")
    private String height;

    /**
     * 海拔高度
     */
    @TableField("alt")
    private String alt;

    /**
     * 机场名称
     */
    @TableField("name")
    private String name;

    /**
     * 机场AOI区域
     */
    @TableField("aoi")
    private String aoi;

    /**
     * 机场坐标
     */
    @TableField("position")
    private String position;

    /**
     * 机场状态
     */
    @TableField("status")
    private String status;

    /**
     * 半径，单位千米
     */
    @TableField("radius")
    private String radius;

    /**
     * 地址
     */
    @TableField("address")
    private String address;
    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @TableField("tenant_id")
    private String tenantId;
}
