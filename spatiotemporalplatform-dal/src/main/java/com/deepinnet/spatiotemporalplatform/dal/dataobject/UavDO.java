package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 无人机信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
@Getter
@Setter
@TableName("uav")
public class UavDO extends Model<UavDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 无人机唯一标识
     */
    @TableField("uav_id")
    private String uavId;

    /**
     * 无人机名称
     */
    @TableField("uav_name")
    private String uavName;

    /**
     * 无人机厂商
     */
    @TableField("uav_company")
    private String uavCompany;

    /**
     * 无人机登记主体
     */
    @TableField("uav_registration_entity")
    private String uavRegistrationEntity;

    /**
     * 航空器类别
     */
    @TableField("aircraft_type")
    private String aircraftType;

    /**
     * USA码
     */
    @TableField("usa")
    private String usa;

    /**
     * 无人机序列号（SN码）
     */
    @TableField("sn")
    private String sn;

    /**
     * 无人机型号
     */
    @TableField("model")
    private String model;

    /**
     * 是否购买保险
     */
    @TableField("insurance_status")
    private Boolean insuranceStatus;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @TableField("tenant_id")
    private String tenantId;
}
