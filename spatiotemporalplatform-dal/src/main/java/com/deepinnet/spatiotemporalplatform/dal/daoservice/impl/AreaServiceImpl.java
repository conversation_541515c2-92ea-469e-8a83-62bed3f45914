package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.mapper.AreaMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.AreaDO;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.AreaService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【area(区域表)】的数据库操作Service实现
* @createDate 2024-07-26 17:13:05
*/
@Service
public class AreaServiceImpl extends ServiceImpl<AreaMapper, AreaDO>
    implements AreaService {

    @Override
    public void updatebByAreaCode(AreaDO aDo) {
        update(aDo, Wrappers.lambdaQuery(AreaDO.class).eq(AreaDO::getAreaCode, aDo.getAreaCode()));
    }
}




