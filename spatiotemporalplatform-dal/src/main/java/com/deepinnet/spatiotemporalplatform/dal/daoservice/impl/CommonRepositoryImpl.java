package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.deepinnet.spatiotemporalplatform.dal.mapper.CommonMapper;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.CommonRepository;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Map;

/**

 *
 * <AUTHOR>
 * @since 2024-10-31 星期四
 **/
@Repository
public class CommonRepositoryImpl implements CommonRepository {

    @Resource
    private CommonMapper commonMapper;

    @Override
    public Integer insertByMap(String tableName, Map<String, Object> map) {
        return commonMapper.insertByMap(tableName, map);
    }
}
