package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 
 * @TableName vehicle_pass_record
 */
@TableName(value ="vehicle_pass_record", autoResultMap = true)
@Data
public class VehiclePassRecordDO implements Serializable {
    /**
     * 
     */
    @TableId
    private String recordId;

    /**
     * 
     */
    private String plateNum;

    /**
     * 
     */
    private String carNumType;

    /**
     * 
     */
    private String carNumTypeStr;

    /**
     * 
     */
    private Long capTime;

    /**
     * 
     */
    private String capTimeStr;

    /**
     * 
     */
    private String channelCode;

    /**
     * 
     */
    private String channelName;

    /**
     * 
     */
    private String orgName;

    /**
     * 
     */
    private Long createTime;

    /**
     * 
     */
    private String createTimeStr;

    /**
     * 
     */
    private Integer carWayCode;

    /**
     * 
     */
    private String carWayCodeStr;

    /**
     * 
     */
    private Integer carSpeed;

    /**
     * 
     */
    private Integer carColor;

    /**
     * 
     */
    private String carColorStr;

    /**
     * 
     */
    private String plateNumUrl;

    /**
     * 
     */
    private String carImgUrl;

    /**
     * 
     */
    private String carBrand;

    /**
     * 
     */
    private String carBrandStr;

    /**
     * 
     */
    private Integer carDirect;

    /**
     * 
     */
    private String carDirectStr;

    /**
     * 
     */
    private String channelId;

    /**
     * 
     */
    private String lng;

    /**
     * 
     */
    private String lat;

    /**
     * 
     */
    private String plateColor;

    /**
     * 
     */
    private String plateColorStr;

    private LocalDateTime localCreateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField("tenant_id")
    private String tenantId;
}