package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.DictionaryRepository;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.DictionaryDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.DictionaryMapper;
import org.springframework.stereotype.Repository;

/**
 * 字典仓储实现类
 *
 * <AUTHOR>
 */
@Repository
public class DictionaryRepositoryImpl extends ServiceImpl<DictionaryMapper, DictionaryDO>
        implements DictionaryRepository {
} 