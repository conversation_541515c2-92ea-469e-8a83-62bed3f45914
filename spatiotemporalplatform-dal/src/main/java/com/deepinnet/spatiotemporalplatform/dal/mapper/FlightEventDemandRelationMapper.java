package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.DemandArea;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightEventDemandRelationDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.PlanDemandDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 飞行事件需求关联表 Mapper 接口
 *
 * <AUTHOR>
 */
@Mapper
public interface FlightEventDemandRelationMapper extends BaseMapper<FlightEventDemandRelationDO> {

    /**
     * 根据飞行任务编号和经纬度查询原始需求和区域序号
     *
     * @param flightTaskCode 飞行任务编号
     * @param longitude 经度
     * @param latitude 纬度
     * @param distance 如果是点位需要拓展的距离
     * @return 原始需求编号和区域序号的映射结果
     */
    List<DemandArea> queryDemandAreaByTaskCodeAndLocation(@Param("flightTaskCode") String flightTaskCode,
                                                          @Param("longitude") Double longitude,
                                                          @Param("latitude") Double latitude,
                                                          @Param("distance") Integer distance);

    List<PlanDemandDO> queryPlanListOfEmergencyDemand(List<String> planIdList);
}