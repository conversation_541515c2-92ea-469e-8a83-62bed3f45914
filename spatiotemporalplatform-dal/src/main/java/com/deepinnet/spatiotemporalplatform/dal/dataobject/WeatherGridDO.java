package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import com.deepinnet.spatiotemporalplatform.dal.type.*;
import lombok.Data;
import org.locationtech.jts.geom.*;
import org.springframework.util.DigestUtils;

import java.io.Serializable;

/**
 * 气象网格表数据对象
 */
@Data
@TableName("weather_grid")
public class WeatherGridDO implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String gridCode;

    private String weather;

    @TableField(value = "grid_geometry", typeHandler = PGPolygonTypeHandler.class)
    private Geometry gridGeometry;

    @TableField(typeHandler = PGPointTypeHandler.class)
    private Point centerPoint;

    private String coordinateSystem;
    /**
     * 云底高（米）
     */
    private double cloudBaseHeight;

    /**
     * 能见度（米）
     */
    private double visibility;

    /**
     * 地面降雨强度
     */
    private double precipitationIntensity;

    /**
     * 地面大气气压
     */
    private double airpressure;

    /**
     * 大气湿度
     */
    private double humidity;

    /**
     * 地面温度（摄氏度）
     */
    private double temperature;

    @TableField("md5_hash")
    private String md5Hash;

    private String weatherLayers;

    public void computeMd5() {
        String rawData = String.format("%s|%.3f|%.3f|%.3f|%.3f|%.3f|%.3f|%s",
                gridCode,
                cloudBaseHeight,
                visibility,
                precipitationIntensity,
                airpressure,
                temperature,
                humidity,
                weatherLayers);

        this.md5Hash = DigestUtils.md5DigestAsHex(rawData.getBytes());
    }

    @TableField("tenant_id")
    private String tenantId;
}