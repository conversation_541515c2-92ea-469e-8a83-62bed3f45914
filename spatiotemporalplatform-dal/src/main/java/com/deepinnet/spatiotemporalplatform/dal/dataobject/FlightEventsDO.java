package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.deepinnet.spatiotemporalplatform.dal.type.PGPointTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.locationtech.jts.geom.Point;

import java.time.LocalDateTime;

/**
 * 飞行事件记录表实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "flight_events", autoResultMap = true)
public class FlightEventsDO extends Model<FlightEventsDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 事件唯一标识
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 算法侧事件唯一id
     */
    @TableField("algorithm_event_id")
    private String algorithmEventId;

    /**
     * 事件类型，例如违停、闯入禁区等
     */
    @TableField("event_type")
    private String eventType;

    /**
     * 事件名称
     */
    @TableField("event_name")
    private String eventName;

    /**
     * 事件描述，详细说明事件内容
     */
    @TableField("description")
    private String description;

    /**
     * 事件发生地点的经纬度坐标，WGS84坐标系
     */
    @TableField(value = "event_point", typeHandler = PGPointTypeHandler.class)
    private Point eventPoint;

    /**
     * 事件发生地点的文字描述（如：某某街口）
     */
    @TableField("event_location")
    private String eventLocation;

    /**
     * 事件持续时长，单位：秒
     */
    @TableField("duration")
    private Long duration;

    /**
     * 事件状态，例如未处理、处理中、已处理
     */
    @TableField("status")
    private String status;

    /**
     * 涉事车辆的车牌号
     */
    @TableField("license_plate")
    private String licensePlate;

    /**
     * 取证图片的 JSON 数组，包含图片地址和描述
     */
    @TableField("evidence_images")
    private String evidenceImages;

    /**
     * 取证视频的 JSON 数组，包含视频地址和描述
     */
    @TableField("evidence_videos")
    private String evidenceVideos;

    /**
     * 飞行任务id
     */
    @TableField("flight_task_code")
    private String flightTaskCode;

    @TableField("event_start_time")
    private LocalDateTime eventStartTime;

    @TableField("event_end_time")
    private LocalDateTime eventEndTime;

    @TableField("algorithm_event_gmt_modified")
    private LocalDateTime algorithmEventGmtModified;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 更新时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 飞行批次 id
     * */
    @TableField("flight_id")
    private String flightId;
} 