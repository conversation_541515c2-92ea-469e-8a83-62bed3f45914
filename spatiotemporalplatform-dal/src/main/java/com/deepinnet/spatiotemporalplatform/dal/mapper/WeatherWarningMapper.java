package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.WeatherWarningDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.IntelligenceDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 天气预警Dao接口
 * 针对表【weather_warning】的数据库操作
 *
 * <AUTHOR>
 */
@Mapper
public interface WeatherWarningMapper extends BaseMapper<WeatherWarningDO> {

    List<IntelligenceDO> getIntelligenceList(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    List<IntelligenceDO> getHistoryIntelligenceList(@Param("startTime") Long startTime, @Param("endTime") Long endTime);
}