package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deepinnet.spatiotemporalplatform.dal.type.PGPolygonTypeHandler;
import lombok.Data;
import org.locationtech.jts.geom.Polygon;

import java.io.Serializable;
import java.util.Date;

/**
 * 圈层表
 * @TableName circle_layer
 */
@TableName(value ="circle_layer", autoResultMap = true)
@Data
public class CircleLayerDO implements Serializable {

    @TableField(value = "is_deleted")
    @TableLogic(value = "false", delval = "true")
    private Boolean deleted;
    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    @TableField(value = "id", insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Integer id;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 所属区域code
     */
    private String areaCode;

    /**
     * 圈层ID
     */
    private String layerId;

    /**
     * 圈层名称
     */
    private String layerName;

    /**
     * 圈层类型
     */
    private String layerType;

    /**
     * 边界经纬度
     */
    @TableField(value = "boundary_coordinates", typeHandler = PGPolygonTypeHandler.class)
    private Polygon boundaryCoordinates;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField("tenant_id")
    private String tenantId;
}