package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.ParkingLotDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【parking_lot(停车场表)】的数据库操作Mapper
* @createDate 2024-07-26 17:01:25
* @Entity com.deepinnet.smarttrafficpolice.regionaltrafficassurance.dal.ParkingLot
*/
public interface ParkingLotMapper extends BaseMapper<ParkingLotDO> {
    /**
     * 区域内的停车场，有个distance的缓存区，过滤与区域相交或者包含的停车场
     *
     * @param geoWkt
     * @param type
     * @param distance
     * @return
     */
    List<ParkingLotDO> listByGeoAndType(@Param("geoWkt") String geoWkt,
                                        @Param("type") String type,
                                        @Param("distance") Integer distance
    );
}




