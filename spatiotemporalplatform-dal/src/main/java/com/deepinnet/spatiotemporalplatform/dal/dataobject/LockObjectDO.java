package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * Description: 
 * Date: 2024/9/27
 * Author: lijunheng
 */
@Data
@TableName(value = "distributed_lock", autoResultMap = true)
public class LockObjectDO extends Model<LockObjectDO> {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 锁名称
     */
    @TableField(value = "lock_name")
    private String lockName;

    /**
     * 目前获取锁的服务器id
     */
    @TableField(value = "machine_id")
    private String machineId;

    /**
     * 锁过期时间
     */
    @TableField(value = "expire_time")
    private Date expireTime;

    /**
     * 是否被锁定
     */
    @TableField(value = "is_locked")
    private Boolean isLocked;

    /**
     * 锁定数量
     */
    @TableField(value = "state")
    private Integer state;

    /**
     * 锁定线程id
     */
    @TableField(value = "thread_id")
    private String threadId;

    @TableField(value = "gmt_create")
    private Date gmtCreate;

    @TableField(value = "gmt_modified")
    private Date gmtModified;

    @TableField(value = "is_deleted")
    @TableLogic(value = "false", delval = "true")
    private Boolean isDeleted;
}