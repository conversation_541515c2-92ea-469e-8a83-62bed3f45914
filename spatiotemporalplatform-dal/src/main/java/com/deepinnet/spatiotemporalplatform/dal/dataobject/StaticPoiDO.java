package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Date;

/**
 * <p>
 * 静态核心POI表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-10
 */
@Getter
@Setter
@TableName(value = "static_poi", autoResultMap = true)
public class StaticPoiDO extends Model<StaticPoiDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * POI ID
     */
    @TableField("poi_id")
    private String poiId;

    /**
     * POI名称
     */
    @TableField("poi_name")
    private String poiName;

    /**
     * 经度
     */
    @TableField("longitude")
    private String longitude;

    /**
     * 纬度
     */
    @TableField("latitude")
    private String latitude;

    /**
     * 坐标系
     */
    @TableField("coordinate_system")
    private String coordinateSystem;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private Date gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private Date gmtModified;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @TableField("tenant_id")
    private String tenantId;
} 