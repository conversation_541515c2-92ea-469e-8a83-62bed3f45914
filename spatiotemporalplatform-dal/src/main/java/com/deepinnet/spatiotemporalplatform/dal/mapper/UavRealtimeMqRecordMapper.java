package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.UavRealtimeMqRecordDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 从消息队列接收的无人机实时数据 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
public interface UavRealtimeMqRecordMapper extends BaseMapper<UavRealtimeMqRecordDO> {

    /**
     * 查询指定无人机ID列表中每个无人机的最新记录
     *
     * @param uavIds 无人机ID列表
     * @param planIds 计划ID列表，可为空
     * @return 最新记录列表
     */
    List<UavRealtimeMqRecordDO> selectLatestByUavIds(@Param("uavIds") List<String> uavIds, @Param("planIds") List<String> planIds);
}
