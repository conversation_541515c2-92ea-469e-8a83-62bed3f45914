package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.mapper.CircleLayerMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.CircleLayerDO;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.CircleLayerService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【circle_layer(圈层表)】的数据库操作Service实现
 * @createDate 2024-07-26 17:13:05
 */
@Service
public class CircleLayerServiceImpl extends ServiceImpl<CircleLayerMapper, CircleLayerDO>
        implements CircleLayerService {

    @Override
    public List<CircleLayerDO> listByAreaeCodeList(List<String> areaCodeList) {
        return list(Wrappers.lambdaQuery(CircleLayerDO.class).in(CircleLayerDO::getAreaCode, areaCodeList).orderByDesc(CircleLayerDO::getGmtCreated));
    }


}




