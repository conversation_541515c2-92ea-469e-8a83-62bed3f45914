package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 飞行任务表，记录飞行任务的基本信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
@Getter
@Setter
@TableName("flight_mission")
public class FlightMissionDO extends Model<FlightMissionDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务唯一标识
     */
    @TableField("mission_id")
    private String missionId;

    /**
     * 飞行任务状态
     */
    @TableField("status")
    private String status;

    /**
     * 预计起飞时间，取该任务最早的计划起飞时间
     */
    @TableField("planed_takeoff_time")
    private Long planedTakeoffTime;

    /**
     * 预计降落时间，取该任务最晚的计划降落时间
     */
    @TableField("planed_landing_time")
    private Long planedLandingTime;

    /**
     * 执行飞行任务的单位
     */
    @TableField("flight_unit")
    private String flightUnit;

    /**
     * 飞行单位ID
     */
    @TableField("flight_unit_id")
    private String flightUnitId;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @TableField("tenant_id")
    private String tenantId;
}
