package com.deepinnet.spatiotemporalplatform.dal.daoservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightEventsDO;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightEventsQuery;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightEventsStatDO;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightEventsStatQuery;

import java.util.List;

/**
 * 飞行事件记录仓储接口
 *
 * <AUTHOR>
 */
public interface FlightEventsRepository extends IService<FlightEventsDO> {
    /**
     * 查询飞行事件统计
     *
     * @param query 查询参数
     * @return List<FlightEventsStatDO>
     */
    List<FlightEventsStatDO> queryFlightEventsStat(FlightEventsStatQuery query);

    /**
     * 查询用户需求下的飞行事件
     *
     * @param query
     * @return
     */
    List<FlightEventsDO> queryUserDemandEvents(FlightEventsQuery query);

    /**
     * 查询flight_events表中最新事件
     *
     * @return
     */
    FlightEventsDO queryLatestEvent();
}