package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.GdPoiDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
public interface GdPoiMapper extends BaseMapper<GdPoiDO> {

    /**
     * 根据区域和类型查询POI
     * @param region 区域
     * @param types 类型
     * @return POI列表
     */
    List<GdPoiDO> getPoiByRegionAndType(@Param("region") String region, @Param("types") List<String> types);
}
