package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightPlanAchievementDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.FlightPlanAchievementDao;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.FlightPlanAchievementRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Service
public class FlightPlanAchievementRepositoryImpl extends ServiceImpl<FlightPlanAchievementDao, FlightPlanAchievementDO> implements FlightPlanAchievementRepository {

    @Override
    public FlightPlanAchievementDO getLastAchievement(String planNo, LocalDate compareDate, LocalDate currentAchievementDate, String typeCode, String achievementLocationStr) {
        return baseMapper.getLastAchievement(planNo, compareDate, currentAchievementDate, typeCode, achievementLocationStr);
    }

    @Override
    public List<FlightPlanAchievementDO> getAchievementList(LocalDate currentAchievementDate, String typeCode, String achievementLocationStr) {
        return baseMapper.getAchievementList(currentAchievementDate, typeCode, achievementLocationStr);
    }
}
