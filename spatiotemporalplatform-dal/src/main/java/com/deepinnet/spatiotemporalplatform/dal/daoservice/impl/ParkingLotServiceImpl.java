package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.ParkingLotService;
import com.deepinnet.spatiotemporalplatform.dal.mapper.ParkingLotMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.ParkingLotDO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【parking_lot(停车场表)】的数据库操作Service实现
 * @createDate 2024-07-26 17:13:05
 */
@Service
public class ParkingLotServiceImpl extends ServiceImpl<ParkingLotMapper, ParkingLotDO> implements ParkingLotService {

    @Override
    public void saveOrUpdateParkingLot(ParkingLotDO parkingLotDO) {
        // 判断是否存在
        QueryWrapper<ParkingLotDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parking_lot_code", parkingLotDO.getParkingLotCode());

        ParkingLotDO exist = this.getOne(queryWrapper);
        if (exist != null) {
            parkingLotDO.setId(exist.getId());
            parkingLotDO.setGmtCreated(exist.getGmtCreated());
            this.updateById(parkingLotDO);
        } else {
            this.save(parkingLotDO);
        }
    }


    @Override
    public List<ParkingLotDO> listByGeoAndType(String geoWKT, String type) {
        return baseMapper.listByGeoAndType(geoWKT, type, 500);
    }

    @Override
    public ParkingLotDO getByParkingLotCode(String parkingLotCode) {
        return getOne(Wrappers.lambdaQuery(ParkingLotDO.class).eq(ParkingLotDO::getParkingLotCode, parkingLotCode));
    }
}




