package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightDailyStatisticsDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.FlightDailyStatisticsDao;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.FlightDailyStatisticsRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-10
 */
@Service
public class FlightDailyStatisticsRepositoryImpl extends ServiceImpl<FlightDailyStatisticsDao, FlightDailyStatisticsDO> implements FlightDailyStatisticsRepository {

}
