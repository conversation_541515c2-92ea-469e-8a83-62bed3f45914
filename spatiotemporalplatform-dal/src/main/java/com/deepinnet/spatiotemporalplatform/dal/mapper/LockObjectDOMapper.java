package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.LockObjectDO;

import java.util.List;

/**
 * Description: 
 * Date: 2024/9/27
 * Author: lijunheng
 */
public interface LockObjectDOMapper extends BaseMapper<LockObjectDO> {

    int updateReentrantLock(LockObjectDO lockObjectDO);

    int updateReleaseLock(LockObjectDO lockObjectDO);

    int updateRenewExpirationLock(LockObjectDO lockObjectDO);

    List<String> queryAllowObtainLockList(List<String> lockNameList);
}