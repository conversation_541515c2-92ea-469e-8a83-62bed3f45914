package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.RestrictedAreaVehicleAccessRecordDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.RestrictedAreaVehicleAccessRecordDOMapper;
import com.deepinnet.spatiotemporalplatform.dal.model.RestrictedAreaVehiclePassageRecordQuery;
import com.deepinnet.spatiotemporalplatform.dal.model.RestrictedAreaVehiclePassageRecordStat;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static com.deepinnet.spatiotemporalplatform.dal.constant.DataSourceConstants.CAR_PASS_STATICS;

/**
 * C<PERSON> zengjuerui
 * Date 2025-08-14
 **/

@DS(CAR_PASS_STATICS)
@Component
public class RestrictedAreaVehicleAccessRecordRepository
        extends ServiceImpl<RestrictedAreaVehicleAccessRecordDOMapper, RestrictedAreaVehicleAccessRecordDO>
        implements IService<RestrictedAreaVehicleAccessRecordDO> {


    public List<RestrictedAreaVehicleAccessRecordDO> listLastOneByPlateNumList(String areaCode, List<String> plateNums) {
        if(CollectionUtils.isEmpty(plateNums)) return List.of();

        return getBaseMapper().listLastOneByPlateNumList(areaCode, plateNums);
    }

    public RestrictedAreaVehiclePassageRecordStat listRestrictedAreaVehiclePassageRecordStat(String araCode) {
        return getBaseMapper().listAreaStat(araCode);
    }

}
