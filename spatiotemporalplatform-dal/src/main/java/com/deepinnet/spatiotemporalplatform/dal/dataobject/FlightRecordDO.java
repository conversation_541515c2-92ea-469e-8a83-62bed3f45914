package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
@Getter
@Setter
@TableName("flight_record")
public class FlightRecordDO extends Model<FlightRecordDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 计划ID
     */
    @TableField("plan_id")
    private String planId;

    /**
     * 起飞时间
     */
    @TableField("takeoff_time")
    private Long takeoffTime;

    /**
     * 落地时间
     */
    @TableField("landing_time")
    private Long landingTime;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 更新时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @TableField("tenant_id")
    private String tenantId;
}
