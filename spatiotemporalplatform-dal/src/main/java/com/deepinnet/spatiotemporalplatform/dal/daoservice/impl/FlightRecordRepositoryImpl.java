package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightRecordDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.FlightRecordDao;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.FlightRecordRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
@Service
public class FlightRecordRepositoryImpl extends ServiceImpl<FlightRecordDao, FlightRecordDO> implements FlightRecordRepository {

}
