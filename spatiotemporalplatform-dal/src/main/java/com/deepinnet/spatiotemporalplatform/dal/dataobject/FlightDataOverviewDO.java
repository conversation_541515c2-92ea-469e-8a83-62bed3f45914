package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;


/**
 * 飞行数据概览数据
 *
 * <AUTHOR> wong
 * @TableName flight_data_overview
 * @create 2025/01/14
 */
@TableName(value = "flight_data_overview")
@Data
public class FlightDataOverviewDO {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 近一月飞行架次
     */
    private Integer lastMonthFlightCount;

    /**
     * 近一月飞行架次同比增长（百分比）
     */
    private Double lastMonthFlightCountGrowth;

    /**
     * 最近一周飞行架次
     */
    private Integer lastWeekFlightCount;

    /**
     * 最近一周飞行架次同比增长（百分比）
     */
    private Double lastWeekFlightCountGrowth;

    /**
     * 近一年飞行架次
     */
    private Integer lastYearFlightCount;

    /**
     * 近一年飞行架次同比
     */
    private Double lastYearFlightCountGrowth;

    /**
     * 近一年飞行里程（公里）
     */
    private Double lastYearDistance;

    /**
     * 近一年飞行里程同比增长（百分比）
     */
    private Double lastYearDistanceGrowth;

    /**
     * 近一年飞行时长（小时）
     */
    private Double lastYearFlightDuration;

    /**
     * 近一年飞行时长同比增长（%）
     */
    private Double lastYearFlightDurationGrowth;

    /**
     * 近一月日均架次（架次/天）
     */
    private Double lastMonthDailyAverageFlightCount;

    /**
     * 近一月日均架次同比
     */
    private Double lastMonthDailyAverageFlightCountGrowth;

    /**
     * 数据批次号（用于标识同一批数据）
     */
    private Long batchNo;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 修改时间
     */
    private Date gmtModified;

    private String tenantId;


}