package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightMissionDO;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 飞行任务表，记录飞行任务的基本信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
public interface FlightMissionMapper extends BaseMapper<FlightMissionDO> {

    /**
     * 根据条件查询飞行任务
     * @param flightQuery 查询条件
     * @return 查询结果
     */
    List<FlightMissionDO> getFlightMissionByCondition(@Param("queryDTO") FlightQuery flightQuery);
}
