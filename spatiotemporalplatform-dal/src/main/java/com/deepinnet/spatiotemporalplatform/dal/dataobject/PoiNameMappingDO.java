package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName(value = "poi_name_mapping", autoResultMap = true)
public class PoiNameMappingDO {
    @TableId(value = "id")
    private String id;

    @TableField("name")
    private String name;

    @TableField("min_height")
    private Float minHeight;

    @TableField("max_height")
    private Float maxHeight;

    @TableField("point")
    private String point;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    @TableField("is_deleted")
    private Boolean isDeleted;

    @TableField("deleted_at")
    private LocalDateTime deletedAt;

    @TableField("tenant_id")
    private String tenantId;
}