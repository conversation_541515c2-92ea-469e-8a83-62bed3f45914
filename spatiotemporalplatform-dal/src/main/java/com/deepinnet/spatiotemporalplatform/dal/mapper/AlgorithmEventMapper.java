package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.constant.DataSourceConstants;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.AlgorithmEventDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 算法侧事件 Mapper 接口
 *
 * <AUTHOR>
 */
@DS(DataSourceConstants.ALGORITHM)
@Mapper
public interface AlgorithmEventMapper extends BaseMapper<AlgorithmEventDO> {
} 