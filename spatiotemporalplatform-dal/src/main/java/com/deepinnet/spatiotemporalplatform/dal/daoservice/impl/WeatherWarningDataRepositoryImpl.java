package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.WeatherWarningDataDO;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.WeatherWarningDataRepository;
import com.deepinnet.spatiotemporalplatform.dal.mapper.WeatherWarningDataMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【weather_grid】的数据库操作Service实现
 * @createDate 2025-04-10 10:13:11
 */
@Service
public class WeatherWarningDataRepositoryImpl extends ServiceImpl<WeatherWarningDataMapper, WeatherWarningDataDO>
        implements WeatherWarningDataRepository {

}




