package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 禁飞区/管制区数据对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@Getter
@Setter
@TableName("fence_space")
public class FenceSpaceDO extends Model<FenceSpaceDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 管制区ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 地区代码
     */
    @TableField("address_code")
    private String addressCode;

    /**
     * 管制区名称
     */
    @TableField("name")
    private String name;

    /**
     * 名称拼音
     */
    @TableField("name_pinyin")
    private String namePinyin;

    /**
     * 管制区编号
     */
    @TableField("fence_num")
    private String fenceNum;

    /**
     * 删除标识
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 类型 1.净空区 2.禁飞区 3.适飞区 4.超低空航路
     */
    @TableField("fence_type")
    private Integer fenceType;

    /**
     * 航路宽度
     */
    @TableField("buffer_width")
    private String bufferWidth;

    /**
     * 管制高度
     */
    @TableField("height")
    private String height;

    /**
     * 基准海拔高
     */
    @TableField("alt")
    private String alt;

    /**
     * 开始时间
     */
    @TableField("begin_time")
    private Long beginTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private Long endTime;

    /**
     * 地址
     */
    @TableField("address")
    private String address;

    /**
     * 详细坐标
     */
    @TableField("spatial_detail_points")
    private String spatialDetailPoints;

    /**
     * 豁免机型
     */
    @TableField("free_uav_type")
    private String freeUavType;

    /**
     * 划分类型 1-行政区域划分 2-手动划分
     */
    @TableField("zone_type")
    private String zoneType;

    /**
     * 生效类型 0-永久 1临时
     */
    @TableField("limit_mode")
    private String limitMode;

    /**
     * 状态 1.停用 0.启用
     */
    @TableField("status")
    private String status;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @TableField("tenant_id")
    private String tenantId;
} 