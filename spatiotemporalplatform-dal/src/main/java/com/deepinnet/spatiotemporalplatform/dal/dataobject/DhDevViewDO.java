package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * Creator zengjuerui
 * Date 2025-04-01
 * 衢州交警大屏摄像头视图
 **/

@Data
@TableName("dh_dev_view")
public class DhDevViewDO {

    private String id;
    /**
     * 设备编号
     * */
    private String deviceCode;
    /**
     * 设备名称
     * */
    private String deviceName;
    /**
     * 设备序列号
     * */
    private String devSn;

    private String gpsX;

    private String gpsY;

    private String updateTime;

    @TableField("tenant_id")
    private String tenantId;
}
