package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 请求数据存根表
 *
 * @TableName request_stub
 */
@TableName(value = "request_stub")
@Data
public class RequestStubDO implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 请求接口api
     */
    private String api;

    /**
     * 请求路径
     */
    @TableField(value = "url")
    private String url;

    /**
     * 请求方式
     */
    @TableField(value = "method")
    private String method;

    /**
     * 请求参数
     */
    @TableField(value = "param")
    private String param;

    /**
     * 请求体参数
     */
    @TableField(value = "body")
    private String body;
    /**
     * 状态 成功 失败
     */
    @TableField(value = "status")
    private String status;

    /**
     * 返回结果
     */
    @TableField(value = "response")
    private String response;

    /**
     * 请求时间
     */
    @TableField(value = "request_time")
    private Long requestTime;

    /**
     * 响应时间
     */
    @TableField(value = "response_time")
    private Long responseTime;

    /**
     *
     */
    @TableField(value = "gmt_create")
    private LocalDateTime gmtCreate;

    /**
     *
     */
    @TableField(value = "gmt_modify")
    private LocalDateTime gmtModify;

    /**
     * 错误信息
     */
    @TableField(value = "error_message")
    private String errorMessage;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField("tenant_id")
    private String tenantId;
}