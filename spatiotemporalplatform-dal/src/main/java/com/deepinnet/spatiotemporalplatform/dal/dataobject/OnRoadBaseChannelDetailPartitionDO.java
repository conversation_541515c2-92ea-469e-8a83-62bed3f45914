package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "on_road_base_channel_detail_partition", autoResultMap = true)
public class OnRoadBaseChannelDetailPartitionDO extends Model<OnRoadBaseChannelDetailPartitionDO> {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_created")
    private Date gmtCreated;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified")
    private Date gmtModified;

    /**
     * 区域编码
     */
    @TableField(value = "area_code")
    private String areaCode;

    /**
     * 车牌号
     */
    @TableField(value = "plate_num")
    private String plateNum;

    /**
     * 过车时间
     */
    @TableField(value = "cap_time")
    private Date capTime;

    /**
     * recordId
     */
    @TableField(value = "record_id")
    private String recordId;

    /**
     * 城市Code
     */
    @TableField(value = "tenant_id")
    private String tenantId;

    /**
     * 区域名称
     */
    @TableField(value = "area_name")
    private String areaName;

    /**
     * 摄像头编号
     */
    @TableField(value = "channel_id")
    private String channelId;

    /**
     * 是否在区域内
     */
    @TableField(value = "inside_area")
    private Integer insideArea;

    /**
     * 分区字段
     */
    @TableField(value = "hash_key")
    private Integer hashKey;
} 