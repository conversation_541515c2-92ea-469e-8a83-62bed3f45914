package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 风险预警表数据对象
 * 存储各类风险预警信息
 *
 * <AUTHOR>
 */
@Data
@TableName("risk_warning")
public class RiskWarningDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID，自1开始自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 预警时间：风险预警发布的时间点
     */
    private LocalDateTime warningTime;

    /**
     * 预警编号
     */
    private String warningNo;

    /**
     * 架次id
     */
    private String flightId;

    /**
     * 飞行计划id
     */
    private String planId;

    /**
     * 无人机id
     */
    private String uavId;

    /**
     * 飞手ID
     */
    private String userId;

    /**
     * 运营主体：负责处理该预警的实体或组织
     */
    private String operatingEntityId;

    /**
     * 运营主体
     */
    private String operatingEntityName;

    /**
     * 预警或告警类型 1-预警 3-告警
     */
    private String warningType;

    /**
     * super_scope-超域，weather-天气，conflict-冲突，impact-障碍物碰撞，no_fly-闯禁飞区，around-周边安全隐患，black-黑飞
     */
    private String subType;

    /**
     * 预警状态：预警中、预警结束
     */
    private String warningStatus;

    /**
     * 预警详情：预警的详细描述信息
     */
    private String warningDetails;

    /**
     * 经度
     */
    private String longitude;
    /**
     * 纬度
     */
    private String latitude;

    /**
     * 真高
     */
    private String height;

    /**
     * 海拔高度
     */
    private String altitude;

    /**
     * 无人机速度
     */
    private String flightSpeed;

    /**
     * 飞行数据开始id
     */
    private Long startId;

    /**
     * 飞行数据结束id
     */
    private Long endId;

    /**
     * 创建时间：记录创建的时间戳
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间：记录最后修改的时间戳
     */
    private LocalDateTime gmtModified;

    @TableField("tenant_id")
    private String tenantId;
} 