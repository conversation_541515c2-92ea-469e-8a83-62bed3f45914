package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightEventsDO;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightEventsQuery;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightEventsStatDO;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightEventsStatQuery;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightPlanExistEventQueryDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 飞行事件记录 Mapper 接口
 *
 * <AUTHOR>
 */
@Mapper
public interface FlightEventsMapper extends BaseMapper<FlightEventsDO> {

    List<FlightEventsStatDO> queryFlightEventsStat(FlightEventsStatQuery query);

    List<FlightEventsDO> queryUserDemandEvents(FlightEventsQuery query);

    List<String> queryPlanExistEvent(FlightPlanExistEventQueryDO queryDTO);
}