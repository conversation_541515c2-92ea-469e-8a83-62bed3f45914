package com.deepinnet.spatiotemporalplatform.dal.daoservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.StaticPoiDO;

import java.util.List;

/**
 * <p>
 * 静态核心POI表 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-10
 */
public interface StaticPoiDaoService extends IService<StaticPoiDO> {

    /**
     * 根据POI ID查询静态POI
     *
     * @param poiId POI ID
     * @return 静态POI
     */
    StaticPoiDO getByPoiId(String poiId);

} 