package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.mapper.*;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.*;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.*;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【device_gps_model】的数据库操作Service实现
 * @createDate 2024-08-30 10:58:25
 */
@Service
@DS("slave")
public class RemoteGpsRepositoryImpl extends ServiceImpl<RemoteGpsMapper, RemoteGpsDO>
        implements RemoteGpsRepository {

    @Override
    public List<RemoteGpsDO> getFullDataList() {
        return list();
    }
}




