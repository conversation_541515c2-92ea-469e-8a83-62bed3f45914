package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.SurveillanceCameraConditionDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.SurveillanceCameraDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【surveillance_video(监控视频表)】的数据库操作Mapper
 * @createDate 2024-07-26 17:01:25
 * @Entity com.deepinnet.smarttrafficpolice.regionaltrafficassurance.dal.SurveillanceVideo
 */
public interface SurveillanceCameraMapper extends BaseMapper<SurveillanceCameraDO> {

    List<SurveillanceCameraDO> queryCameraWithInArea(@Param("areaCode") String areaCode,
                                                     @Param("type") String type);

    List<SurveillanceCameraDO> queryLineNearCamera(SurveillanceCameraConditionDO condition);

    List<SurveillanceCameraDO> queryAreaNearCamera(SurveillanceCameraConditionDO condition);

    /**
     * 获取区域指定外展范围内的卡口摄像头
     *
     * @param areaCode
     * @param extendDistance
     * @return
     */
    List<SurveillanceCameraDO> findAreaCarCamerasWithExtends(@Param("areaCode") String areaCode, @Param("extendDistance") int extendDistance);

    boolean isCameraInArea(@Param("lng") double lng, @Param("lat") double lat, @Param("areaCode") String areaCode);

    List<SurveillanceCameraDO> queryWithGis(SurveillanceCameraConditionDO condition);

    int upsertBatch(List<SurveillanceCameraDO> list);
}