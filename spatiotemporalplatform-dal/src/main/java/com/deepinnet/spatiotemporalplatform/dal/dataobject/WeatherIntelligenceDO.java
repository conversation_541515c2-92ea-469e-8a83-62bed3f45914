package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 气象情报表数据对象
 * 存储气象相关的数据信息
 *
 * <AUTHOR>
 */
@Data
@TableName("weather_intelligence")
public class WeatherIntelligenceDO implements Serializable {
    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID，自1开始自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 北斗网格码
     */
    private String gridCode;

    /**
     * 航道编号
     */
    private String airwayNo;

    /**
     * 航道名称
     */
    private String airwayName;

    /**
     * 高度
     */
    private String height;

    /**
     * 水平风速
     */
    private String hspeed;

    /**
     * ⼤于等于0，⼩于360
     */
    private String hdir;

    /**
     * 垂直风速(m/s)
     */
    private String vspeed;

    /**
     * 云底⾼
     */
    private String cloudbase;

    /**
     * 能见度
     */
    private String visibility;

    /**
     * 地⾯降⾬强度
     */
    private String rainfall;

    /**
     * 地⾯⼤⽓⽓压
     */
    private String airpressure;

    /**
     * 地⾯⼤⽓湿度
     */
    private String humidity;

    /**
     * 地⾯⼤⽓温度
     */
    private String temp;

    /**
     * 创建时间：记录创建的时间戳
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间：记录最后修改的时间戳
     */
    private LocalDateTime gmtModified;

    @TableField("tenant_id")
    private String tenantId;
}