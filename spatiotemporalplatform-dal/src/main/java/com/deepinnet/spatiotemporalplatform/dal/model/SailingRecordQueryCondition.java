package com.deepinnet.spatiotemporalplatform.dal.model;

import lombok.Data;

import java.time.*;

/**
 * <AUTHOR> wong
 * @create 2025/8/19 17:54
 * @Description
 *
 */
@Data
public class SailingRecordQueryCondition {

    private String shipName;

    private String portName;

    private LocalDateTime departureStartDate;

    private LocalDateTime departureEndDate;

    private String returnStatus;

    private String warningType;
}
