package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.DhDevViewDO;

import static com.deepinnet.spatiotemporalplatform.dal.constant.DataSourceConstants.QZJJ_CAMERA_VIEW;

/**
 * C<PERSON> zeng<PERSON>
 * Date 2025-04-01
 **/

@DS(QZJJ_CAMERA_VIEW)
public interface DhDevViewMapper extends BaseMapper<DhDevViewDO> {
}
