package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-07
 */
@Getter
@Setter
@TableName("flight_plan_sync_log")
public class FlightPlanSyncLogDO extends Model<FlightPlanSyncLogDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 计划编号
     */
    @TableField("plan_no")
    private String planNo;

    /**
     * 同步日期
     */
    @TableField("sync_date")
    private LocalDate syncDate;

    /**
     * 同步时间
     */
    @TableField("sync_time")
    private LocalTime syncTime;

    /**
     * 同步时间点
     */
    @TableField("sync_date_time")
    private LocalDateTime syncDateTime;

    /**
     * 同步数据
     */
    @TableField("sync_data")
    private String syncData;


    @TableField("tenant_id")
    private String tenantId;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 更新时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
