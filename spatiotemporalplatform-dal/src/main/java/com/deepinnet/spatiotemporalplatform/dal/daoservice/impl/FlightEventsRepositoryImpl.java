package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.FlightEventsRepository;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightEventsDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.FlightEventsMapper;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightEventsQuery;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightEventsStatDO;
import com.deepinnet.spatiotemporalplatform.dal.model.FlightEventsStatQuery;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;

/**
 * 飞行事件记录仓储实现类
 *
 * <AUTHOR>
 */
@Repository
public class FlightEventsRepositoryImpl extends ServiceImpl<FlightEventsMapper, FlightEventsDO>
        implements FlightEventsRepository {
    @Override
    public List<FlightEventsStatDO> queryFlightEventsStat(FlightEventsStatQuery query) {
        return baseMapper.queryFlightEventsStat(query);
    }

    @Override
    public List<FlightEventsDO> queryUserDemandEvents(FlightEventsQuery query) {
        return baseMapper.queryUserDemandEvents(query);
    }

    @Override
    public FlightEventsDO queryLatestEvent() {
        LambdaQueryWrapper<FlightEventsDO> wrapper = Wrappers.lambdaQuery(FlightEventsDO.class)
                .orderByDesc(FlightEventsDO::getAlgorithmEventGmtModified)
                .last("LIMIT 1");
        
        return baseMapper.selectOne(wrapper);
    }
}