package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 雷达数据对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@Data
@TableName("radar")
public class RadarDO extends Model<RadarDO> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 代码
     */
    @TableField("code")
    private String code;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 海拔位置
     */
    @TableField("alt")
    private Integer alt;

    /**
     * 探测高度
     */
    @TableField("height")
    private Integer height;

    /**
     * 探测半径
     */
    @TableField("radius")
    private Integer radius;

    /**
     * 坐标
     */
    @TableField("point")
    private String point;

    /**
     * 地址
     */
    @TableField("address")
    private String address;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 更新时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 是否停用
     */
    @TableField("is_disabled")
    private String isDisabled;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 部门IDs
     */
    @TableField("depart_ids")
    private String departIds;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @TableField("tenant_id")
    private String tenantId;
} 