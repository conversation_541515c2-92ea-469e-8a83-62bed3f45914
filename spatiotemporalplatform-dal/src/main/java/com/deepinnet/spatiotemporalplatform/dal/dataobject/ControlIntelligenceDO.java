package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 管控情报表数据对象
 * 存储管控相关的情报信息
 *
 * <AUTHOR>
 */
@Data
@TableName("control_intelligence")
public class ControlIntelligenceDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID，自1开始自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    private String controlIntelligenceNo;

    /**
     * 情报来源：情报的发布者或来源渠道
     */
    private String source;

    /**
     * 发布时间：情报发布的时间点
     */
    private LocalDateTime publishTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 管控内容：情报的具体内容描述
     */
    private String content;

    /**
     * 状态：0启用、1停用
     */
    private String status;

    /**
     * 创建时间：记录创建的时间戳
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间：记录最后修改的时间戳
     */
    private LocalDateTime gmtModified;

    @TableField("tenant_id")
    private String tenantId;
} 