package com.deepinnet.spatiotemporalplatform.dal.daoservice;

import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightPlanAchievementDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
public interface FlightPlanAchievementRepository extends IService<FlightPlanAchievementDO> {

    /**
     * 上一次成果
     * @param planNo 计划编号
     * @param compareDate 对比日期
     * @param currentAchievementDate 当前成果日期
     * @param typeCode 类型编码
     * @param achievementLocationStr 位置信息
     * @return 上一次成果数据
     */
    FlightPlanAchievementDO getLastAchievement(String planNo, LocalDate compareDate, LocalDate currentAchievementDate, String typeCode, String achievementLocationStr);

    /**
     * 查询范围内的成果列表
     * @param currentAchievementDate 当前成果日期
     * @param typeCode 类型编码
     * @param achievementLocationStr 位置信息
     * @return 成果数据
     */
    List<FlightPlanAchievementDO> getAchievementList(LocalDate currentAchievementDate, String typeCode, String achievementLocationStr);
}
