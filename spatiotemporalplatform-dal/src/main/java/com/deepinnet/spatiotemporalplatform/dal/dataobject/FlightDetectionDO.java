package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deepinnet.spatiotemporalplatform.dal.type.PGPointTypeHandler;
import lombok.Data;
import org.locationtech.jts.geom.Geometry;

import java.io.Serializable;
import java.util.Date;

/**
 * 无人机数据上报表
 *
 * @TableName flight_detection
 */
@TableName(value = "flight_detection", autoResultMap = true)
@Data
public class FlightDetectionDO implements Serializable {
    /**
     * 主键，自增 ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 基站编号
     */
    private String devId;

    /**
     * 无人机型号（通过 osid 匹配类型映射库，未匹配则为 unknown）
     */
    private String model;

    private String bigType;

    private String middleType;

    /**
     * 无人机编号
     */
    private String osid;

    /**
     * 无人机类型
     */
    private Integer uaType;

    /**
     * 起飞状态: 0=未声明, 1=地面, 2=空中, 3=紧急
     */
    private Integer status;

    /**
     * 航迹角 (0-360, 361 表示未知)
     */
    private Integer direction;

    /**
     * 水平速度 (m/s)
     */
    private Integer speedHorizontal;

    /**
     * 垂直速度 (m/s)
     */
    private Integer speedVertical;

    /**
     * 无人机经度
     */
    private Double longitude;

    /**
     * 无人机纬度
     */
    private Double latitude;

    /**
     * 气压高度 (Ref 29.92 inHg, 1013.24 mb), -1000 表示无效值
     */
    private Integer altitudeBaro;

    /**
     * 真高
     */
    private Double height;

    /**
     * 海拔高度 (WGS84-HAE), -1000 表示无效值
     */
    private Double altitude;

    /**
     * 飞手经度
     */
    private Double operatorLongitude;

    /**
     * 飞手纬度
     */
    private Double operatorLatitude;

    /**
     * 飞手海拔高度
     */
    private Integer operatorAltitudeGeo;

    /**
     * 无线信号强度 (取两根天线的最大值)
     */
    private Integer rssi;

    /**
     * 右上角天线信号强度
     */
    private Integer rssi0;

    /**
     * 左上角天线信号强度
     */
    private Integer rssi1;

    /**
     * 设备温度 (摄氏度)
     */
    private Double temperature;

    /**
     * 基站设备 MAC 地址
     */
    private String mac;

    /**
     * 无人机中心频点
     */
    private String frequency;

    /**
     * 无人机带宽
     */
    private String bandwidth;

    /**
     * 上报 Unix 时间戳 (秒)
     */
    private Long time;

    /**
     * 坐标
     */
    @TableField(value = "coordinate", typeHandler = PGPointTypeHandler.class)
    private Geometry coordinate;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 修改时间
     */
    private Date gmtModified;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField("tenant_id")
    private String tenantId;
}