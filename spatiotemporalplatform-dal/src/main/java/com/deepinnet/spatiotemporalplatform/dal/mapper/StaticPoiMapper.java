package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.StaticPoiDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 静态核心POI Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-10
 */
public interface StaticPoiMapper extends BaseMapper<StaticPoiDO> {

    /**
     * 根据坐标系查询POI
     * @param coordinateSystem 坐标系
     * @return POI列表
     */
    List<StaticPoiDO> findByCoordinateSystem(@Param("coordinateSystem") String coordinateSystem);
} 