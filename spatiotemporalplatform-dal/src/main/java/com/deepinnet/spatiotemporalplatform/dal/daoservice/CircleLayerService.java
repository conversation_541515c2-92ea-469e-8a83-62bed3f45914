package com.deepinnet.spatiotemporalplatform.dal.daoservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.CircleLayerDO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【circle_layer(圈层表)】的数据库操作Service
* @createDate 2024-07-26 17:01:25
*/
public interface CircleLayerService extends IService<CircleLayerDO> {

    List<CircleLayerDO> listByAreaeCodeList(List<String> areaCodeList);

}
