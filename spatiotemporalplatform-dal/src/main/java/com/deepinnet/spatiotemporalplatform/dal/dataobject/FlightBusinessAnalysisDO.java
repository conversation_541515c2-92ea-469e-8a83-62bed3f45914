package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 飞行业务分析数据
 *
 * @TableName flight_business_analysis
 */
@TableName(value = "flight_business_analysis")
@Data
public class FlightBusinessAnalysisDO {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 统计数量
     */
    private Integer statisticsCount;

    /**
     * 数据批次号（用于标识同一批数据）
     */
    private Long batchNo;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 修改时间
     */
    private Date gmtModified;

    private String tenantId;
} 