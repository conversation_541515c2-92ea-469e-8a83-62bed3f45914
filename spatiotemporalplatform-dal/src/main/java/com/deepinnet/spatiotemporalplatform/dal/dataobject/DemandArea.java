package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import lombok.Data;

/**
 * 飞行需求区域表实体类
 *
 * <AUTHOR>
 */
@Data
public class DemandArea {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 需求编号
     */
    private String demandCode;

    /**
     * 需求名称
     */
    private String demandName;

    /**
     * 区域的序号
     */
    private Integer sequence;

    /**
     * 中心点坐标-经度
     */
    private String centerPointLongitude;

    /**
     * 中心点坐标-纬度
     */
    private String centerPointLatitude;

    /**
     * 区域坐标，WKT字符串
     */
    private String areaCoordinate;

    /**
     * 区域面积，平方公里
     */
    private Double area;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 是否删除
     */
    private Boolean isDeleted;
}
