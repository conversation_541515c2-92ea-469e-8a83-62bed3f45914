package com.deepinnet.spatiotemporalplatform.dal.model;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * 飞行查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
@Data
public class FlightQuery {
    
    /**
     * 当前页码
     */
    private Integer pageNum = 1;
    
    /**
     * 每页数量
     */
    private Integer pageSize = 10;
    
    /**
     * 飞行任务ID
     */
    private String keyword;
    
    /**
     * 飞行任务状态
     */
    private List<Integer> status;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;

    private List<String> planIdList;
} 