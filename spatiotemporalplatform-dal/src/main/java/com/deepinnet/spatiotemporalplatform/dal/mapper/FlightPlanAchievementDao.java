package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightPlanAchievementDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
public interface FlightPlanAchievementDao extends BaseMapper<FlightPlanAchievementDO> {

    /**
     * 上一次的成果数据
     * @param planNo 计划编码
     * @param compareDate 对比日期
     * @param typeCode 对比类型
     * @param achievementLocationStr 最新一次的成果经纬度
     * @return 上一次成果数据
     */
    FlightPlanAchievementDO getLastAchievement(@Param("planNo") String planNo
            , @Param("compareDate") LocalDate compareDate
            , @Param("currentAchievementDate") LocalDate currentAchievementDate
            , @Param("typeCode") String typeCode
            , @Param("location") String achievementLocationStr);

    /**
     * 根据类型与坐标获取一定范围内的成果列表数据
     * @param currentAchievementDate 计划编码
     * @param typeCode 类型编码
     * @param achievementLocationStr 最新一次的成果经纬度
     * @return 列表数据
     */
    List<FlightPlanAchievementDO> getAchievementList(@Param("currentAchievementDate") LocalDate currentAchievementDate, @Param("typeCode") String typeCode
            , @Param("location") String achievementLocationStr);
}
