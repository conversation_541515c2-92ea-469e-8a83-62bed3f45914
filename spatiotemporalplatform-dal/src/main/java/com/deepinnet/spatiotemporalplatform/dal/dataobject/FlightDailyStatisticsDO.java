package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-10
 */
@Getter
@Setter
@TableName("flight_daily_statistics")
public class FlightDailyStatisticsDO extends Model<FlightDailyStatisticsDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 飞行单位ID
     */
    @TableField("flight_unit_id")
    private String flightUnitId;

    /**
     * 飞行单位
     */
    @TableField("flight_unit")
    private String flightUnit;

    /**
     * 飞行架次
     */
    @TableField("flight_count")
    private Integer flightCount;

    /**
     * 飞行时长
     */
    @TableField("flight_duration")
    private Integer flightDuration;

    /**
     * 飞行里程
     */
    @TableField("flight_miles")
    private String flightMiles;

    /**
     * 统计日期
     */
    @TableField("date")
    private String date;

    /**
     * 统计日期-时间戳
     */
    @TableField("timestamp")
    private Long timestamp;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @TableField("tenant_id")
    private String tenantId;
}
