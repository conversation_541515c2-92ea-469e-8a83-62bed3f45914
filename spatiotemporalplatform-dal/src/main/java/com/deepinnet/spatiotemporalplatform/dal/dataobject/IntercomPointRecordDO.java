package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.deepinnet.spatiotemporalplatform.dal.type.PGPointTypeHandler;
import lombok.Data;
import org.locationtech.jts.geom.Point;

import java.util.Date;

/**
 * Description: 
 * Date: 2024/11/7
 * Author: lijunheng
 */

/**
 * 对讲机位置记录表
 */
@Data
@TableName(value = "intercom_point_record", autoResultMap = true)
public class IntercomPointRecordDO extends Model<IntercomPointRecordDO> {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 对讲机编号
     */
    @TableField(value = "code")
    private String code;

    /**
     * 用户账号
     */
    @TableField(value = "user_account")
    private String userAccount;

    /**
     * 用户名
     */
    @TableField(value = "user_name")
    private String userName;

    /**
     * 经度
     */
    @TableField(value = "lng")
    private String lng;

    /**
     * 纬度
     */
    @TableField(value = "lat")
    private String lat;

    /**
     * 最近一次位置更新时间
     */
    @TableField(value = "last_update_time")
    private Date lastUpdateTime;

    /**
     * 所在区域编号
     */
    @TableField(value = "area_code")
    private String areaCode;

    /**
     * 经纬度坐标
     */
    @TableField(value = "coordinate", typeHandler = PGPointTypeHandler.class)
    private Point coordinate;

    @TableField(value = "gps_arrive_time")
    private Date gpsArriveTime;

    @TableField(value = "is_online")
    private Boolean online;

    @TableField(value = "online_time")
    private Date onlineTime;

    @TableField(value = "offline_time")
    private Date offlineTime;

    @TableField(value = "enterprise_id")
    private String enterpriseId;

    @TableField(value = "enterprise_name")
    private String enterpriseName;

    @TableField("tenant_id")
    private String tenantId;

    @TableField("contact")
    private String contact;
}