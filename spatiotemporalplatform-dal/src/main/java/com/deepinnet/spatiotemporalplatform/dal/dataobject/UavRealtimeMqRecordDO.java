package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 从消息队列接收的无人机实时数据
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Getter
@Setter
@TableName("uav_realtime_mq_record")
public class UavRealtimeMqRecordDO extends Model<UavRealtimeMqRecordDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * CPU唯一标识符
     */
    @TableField("cpuid")
    private String cpuid;

    /**
     * 经度坐标（可能是某种比例格式）
     */
    @TableField("lon")
    private Long lon;

    /**
     * 纬度坐标（可能是某种比例格式）
     */
    @TableField("lat")
    private Long lat;

    /**
     * 海拔高度，单位：米
     */
    @TableField("alt")
    private Integer alt;

    /**
     * 高度值，单位：米
     */
    @TableField("height")
    private Integer height;

    /**
     * 相对起飞点的高度，单位：米
     */
    @TableField("relative_height")
    private Integer relativeHeight;

    /**
     * GPS导出的偏航角，单位：度
     */
    @TableField("gps_yaw")
    private Integer gpsYaw;

    /**
     * 偏航角（航向），单位：度
     */
    @TableField("yaw")
    private Integer yaw;

    /**
     * 横滚角，单位：度
     */
    @TableField("roll")
    private Integer roll;

    /**
     * 速度，单位：厘米/秒
     */
    @TableField("speed")
    private Integer speed;

    /**
     * 时间戳，毫秒（从1970年1月1日起）
     */
    @TableField("\"timestamp\"")
    private Long timestamp;

    /**
     * 无人机状态码
     */
    @TableField("state")
    private Integer state;

    /**
     * 航线总里程，单位：米
     */
    @TableField("wp_distance")
    private Integer wpDistance;

    /**
     * 完成航线里程，单位：米
     */
    @TableField("finish_distance")
    private Integer finishDistance;

    /**
     * 剩余航线里程
     */
    @TableField("surplus_circle")
    private Integer surplusCircle;

    /**
     * 距离起飞点距离，单位：米
     */
    @TableField("home_distance")
    private Integer homeDistance;

    /**
     * 飞行持续时间，单位：秒
     */
    @TableField("flight_duration")
    private Integer flightDuration;

    /**
     * 偏差值
     */
    @TableField("leeway")
    private Integer leeway;

    /**
     * GPS可见卫星数量
     */
    @TableField("gps_stars")
    private Integer gpsStars;

    /**
     * GPS PDOP值（位置精度因子）
     */
    @TableField("gps_pdop")
    private Integer gpsPdop;

    /**
     * GPS状态码
     */
    @TableField("gps_state")
    private Integer gpsState;

    /**
     * 锁定状态（1表示锁定，0表示未锁定）
     */
    @TableField("lock")
    private Integer lock;

    /**
     * 电池电压，单位：毫伏
     */
    @TableField("voltage")
    private Integer voltage;

    /**
     * 电量百分比
     */
    @TableField("soc")
    private Integer soc;

    /**
     * 标识是否为关键记录（1表示是，0表示否）
     */
    @TableField("is_key")
    private Integer isKey;

    @TableField("user_id")
    private String userId;

    @TableField("task_id")
    private String taskId;

    @TableField("plan_id")
    private String planId;

    @TableField("uav_id")
    private String uavId;

    @TableField("relation_id")
    private String relationId;

    @TableField("flight_id")
    private String flightId;

    @TableField("warning")
    private String warning;

    @TableField("error")
    private String error;

    @TableField("load")
    private String load;

    /**
     * 记录创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 记录最后修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @TableField("tenant_id")
    private String tenantId;
}
