package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 未在飞行计划内的无人机检测数据对象
 */
@Data
@TableName("unplanned_flight_detection")
public class UnplannedFlightDetectionDO implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 无人机SN码（唯一标识）
     */
    private String sn;

    /**
     * 确认黑飞时间（Unix 时间戳，秒）
     */
    private Long confirmedIllegalTime;

    /**
     * 上报时间（Unix 时间戳，秒）
     */
    private Long reportedTime;

    /**
     * 确认黑飞关联的飞行数据id
     */
    private Long startFlightDetectionId;

    private Long endFlightDetectionId;

    private String status;

    private Date gmtCreated;

    private Date gmtModified;

    @TableField("tenant_id")
    private String tenantId;
}