package com.deepinnet.spatiotemporalplatform.dal.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description:
 * Date: 2025/8/14
 * Author: lijunheng
 */
@Data
public class FlightPlanExistEventQueryDO implements Serializable {

    /**
     * 计划ID列表
     */
    private List<String> planIdList;

    /**
     * 算法编码列表
     */
    private List<String> algorithmCodeList;
}
