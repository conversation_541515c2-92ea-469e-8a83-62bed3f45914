package com.deepinnet.spatiotemporalplatform.dal.daoservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.AlgorithmEventRepository;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.AlgorithmEventDO;
import com.deepinnet.spatiotemporalplatform.dal.mapper.AlgorithmEventMapper;
import org.springframework.stereotype.Repository;

/**
 * 算法侧事件仓储实现类
 *
 * <AUTHOR>
 */
@Repository
public class AlgorithmEventRepositoryImpl extends ServiceImpl<AlgorithmEventMapper, AlgorithmEventDO>
        implements AlgorithmEventRepository {
} 