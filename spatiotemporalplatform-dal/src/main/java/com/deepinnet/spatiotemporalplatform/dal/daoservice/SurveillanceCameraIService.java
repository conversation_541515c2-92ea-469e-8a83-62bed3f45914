package com.deepinnet.spatiotemporalplatform.dal.daoservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.SurveillanceCameraConditionDO;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.SurveillanceCameraDO;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【surveillance_video(监控视频表)】的数据库操作Service
* @createDate 2024-07-26 17:01:25
*/
public interface SurveillanceCameraIService extends IService<SurveillanceCameraDO> {

    List<SurveillanceCameraDO> queryCameraWithInArea(String areaCode, String type);

    List<SurveillanceCameraDO> queryLineNearCamera(SurveillanceCameraConditionDO condition);

    List<SurveillanceCameraDO> queryAreaNearCamera(SurveillanceCameraConditionDO condition);

    List<SurveillanceCameraDO> queryCameraListByIndex(Date afterCreateDataTime, int startIndex, int size);

    List<SurveillanceCameraDO> findAreaCarCamerasWithExtends(String areaCode, int extendDistance);

    boolean isCameraInArea(double lng, double lat, String areaCode);

    List<SurveillanceCameraDO> queryWithGis(SurveillanceCameraConditionDO condition);

    int upsertBatch(List<SurveillanceCameraDO> cameraList);
}
