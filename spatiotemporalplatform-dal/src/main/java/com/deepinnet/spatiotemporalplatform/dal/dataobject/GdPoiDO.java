package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.deepinnet.spatiotemporalplatform.dal.type.PGPointTypeHandler;
import lombok.Getter;
import lombok.Setter;
import org.locationtech.jts.geom.Geometry;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Getter
@Setter
@TableName(value = "gd_poi", autoResultMap = true)
public class GdPoiDO extends Model<GdPoiDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * poi_id
     */
    @TableField("poi_id")
    private String poiId;

    /**
     * poi名称
     */
    @TableField("poi_name")
    private String poiName;

    /**
     * poi经度
     */
    @TableField("poi_lng")
    private String poiLng;

    /**
     * poi纬度
     */
    @TableField("poi_lat")
    private String poiLat;

    /**
     * 行政区编码
     */
    @TableField("district_code")
    private String districtCode;

    /**
     * 行政区名称
     */
    @TableField("district_name")
    private String districtName;

    /**
     * poi一级类型名称
     */
    @TableField("poi_first_type_name")
    private String poiFirstTypeName;

    /**
     * poi二级类型名称
     */
    @TableField("poi_second_type_name")
    private String poiSecondTypeName;

    /**
     * poi三级类型名称
     */
    @TableField("poi_third_type_name")
    private String poiThirdTypeName;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * poi三级类型
     */
    @TableField("poi_third_type")
    private String poiThirdType;

    /**
     * poi
     */
    @TableField(value = "poi", typeHandler = PGPointTypeHandler.class)
    private Geometry poi;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @TableField("tenant_id")
    private String tenantId;
}
