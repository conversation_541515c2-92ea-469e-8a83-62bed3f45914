package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 实时无人机飞行数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
@Getter
@Setter
@TableName("real_time_uav_flight")
public class RealTimeUavFlightDO extends Model<RealTimeUavFlightDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属飞行计划ID
     */
    @TableField("plan_id")
    private String planId;

    /**
     * 无人机ID
     */
    @TableField("uav_id")
    private String uavId;

    /**
     * 架次ID
     */
    @TableField("flight_id")
    private String flightId;

    /**
     * 无人机当前状态
     */
    @TableField("status")
    private String status;

    /**
     * 飞手位置（经纬度）
     */
    @TableField("operator_position")
    private String operatorPosition;

    /**
     * 飞手当前高度
     */
    @TableField("operator_altitude")
    private String operatorAltitude;

    /**
     * 无人机飞行速度
     */
    @TableField("flight_speed")
    private String flightSpeed;

    /**
     * 无人机当前位置
     */
    @TableField("uav_position")
    private String uavPosition;

    /**
     * 无人机当前高度
     */
    @TableField("uav_altitude")
    private String uavAltitude;

    /**
     * 海拔高度
     */
    @TableField("elevation")
    private String elevation;

    /**
     * 飞行时长
     */
    @TableField("flight_duration")
    private String flightDuration;

    /**
     * 飞行里程
     */
    @TableField("flight_miles")
    private String flightMiles;

    /**
     * 电池电压
     */
    @TableField("voltage")
    private String voltage;

    /**
     * 电量百分比
     */
    @TableField("soc")
    private Integer soc;

    /**
     * 上报时间
     */
    @TableField("report_time")
    private LocalDateTime reportTime;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @TableField("tenant_id")
    private String tenantId;
}
