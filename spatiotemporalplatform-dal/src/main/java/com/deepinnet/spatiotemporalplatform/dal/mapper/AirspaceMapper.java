package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.AirspaceDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 空域 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
public interface AirspaceMapper extends BaseMapper<AirspaceDO> {
    List<AirspaceDO> listByCodes(@Param("codes") List<String> codes);

} 