package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * Description: 
 * Date: 2024/8/5
 * Author: lijunheng
 */

/**
 * 标记监控摄像头
 */
@Data
@TableName(value = "surveillance_cameras_mark", autoResultMap = true)
public class SurveillanceCamerasMarkDO extends Model<SurveillanceCamerasMarkDO> {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 摄像头编码
     */
    private String cameraCode;

    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 作用对象，比如某某租户
     */
    private String objectCode;

    private Boolean isDeleted;

    private Date gmtCreated;

    private Date gmtModified;

    /**
     * 摄像头类型，普通摄像头、重点摄像头
     */
    private String cameraType;

    @TableField("tenant_id")
    private String tenantId;
}