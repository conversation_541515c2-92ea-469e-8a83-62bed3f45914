package com.deepinnet.spatiotemporalplatform.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.RiskWarningDO;
import com.deepinnet.spatiotemporalplatform.dal.dto.*;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 风险预警表Mapper接口
 * 针对表【risk_warning】的数据库操作
 *
 * <AUTHOR>
 */
@Mapper
public interface RiskWarningMapper extends BaseMapper<RiskWarningDO> {

    List<SubTypeCountDTO> getSubTypeCountList(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("needWarningTypeList") List<String> needWarningTypeList);

    List<StatusCountDTO> getStatusCountList(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("needWarningTypeList") List<String> needWarningTypeList);
    
    /**
     * 查询每个planId的最新10条风险预警记录
     *
     * @param planIds 计划ID列表
     * @return 风险预警列表
     */
    List<RiskWarningDO> listTopRiskWarningsByPlanIds(@Param("planIds") List<String> planIds, @Param("limit") Integer limit);
}