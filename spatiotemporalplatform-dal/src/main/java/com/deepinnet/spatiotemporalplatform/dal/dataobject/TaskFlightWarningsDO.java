package com.deepinnet.spatiotemporalplatform.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Getter
@Setter
@TableName("task_flight_warnings")
public class TaskFlightWarningsDO extends Model<TaskFlightWarningsDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableField("id")
    private Long id;

    /**
     * 统计时间
     */
    @TableField("statistical_date")
    private LocalDate statisticalDate;

    /**
     * 统计时刻
     */
    @TableField("statistical_time")
    private String statisticalTime;

    /**
     * 统计数量
     */
    @TableField("statistical_count")
    private Integer statisticalCount;

    /**
     * 统计该时刻历史平均
     */
    @TableField("statistical_history_avg_count")
    private String statisticalHistoryAvgCount;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 更新时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    @Override
    public Serializable pkVal() {
        return null;
    }

    @TableField("tenant_id")
    private String tenantId;
}
