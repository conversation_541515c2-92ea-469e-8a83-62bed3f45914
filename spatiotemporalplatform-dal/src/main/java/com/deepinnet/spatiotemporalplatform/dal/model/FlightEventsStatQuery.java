package com.deepinnet.spatiotemporalplatform.dal.model;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 飞行事件统计查询DTO
 *
 * <AUTHOR>
 */
@Data
public class FlightEventsStatQuery implements Serializable {

    private LocalDateTime startTime;

    private String userNo;

    private String demandType;

    private String tenantId;

    private String account;

    private List<String> userNoList;

    private String startTimeType;

    private String demandScene;
} 