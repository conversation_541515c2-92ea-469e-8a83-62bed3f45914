package com.deepinnet.spatiotemporalplatform.dal.model;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 飞行事件统计查询DTO
 *
 * <AUTHOR>
 */
@Data
public class FlightEventsStatQuery implements Serializable {

    private LocalDateTime startTime;

    private String userNo;

    private String demandType;

    private String tenantId;

    private String account;
} 