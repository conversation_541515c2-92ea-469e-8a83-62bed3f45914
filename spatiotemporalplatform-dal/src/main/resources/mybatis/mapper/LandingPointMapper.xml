<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.LandingPointMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.LandingPointDO">
        <id column="id" property="id"/>
        <result column="external_id" property="externalId"/>
        <result column="code" property="code"/>
        <result column="type" property="type"/>
        <result column="name" property="name"/>
        <result column="alt" property="alt"/>
        <result column="height" property="height"/>
        <result column="radius" property="radius"/>
        <result column="point" property="point"/>
        <result column="point_gcj02" property="pointGcj02" jdbcType="OTHER" typeHandler="com.deepinnet.spatiotemporalplatform.dal.type.PGPointTypeHandler"/>
        <result column="address" property="address"/>
        <result column="point_or_name" property="pointOrName"/>
        <result column="team_id" property="teamId"/>
        <result column="crt_type" property="crtType"/>
        <result column="crt_user_id" property="crtUserId"/>
        <result column="crt_time" property="crtTime"/>
        <result column="crt_user_name" property="crtUserName"/>
        <result column="upd_user_id" property="updUserId"/>
        <result column="upd_time" property="updTime"/>
        <result column="upd_user_name" property="updUserName"/>
        <result column="remark" property="remark"/>
        <result column="is_disabled" property="isDisabled"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="depart_id" property="departId"/>
        <result column="depart_ids" property="departIds"/>
        <result column="external_tenant_id" property="externalTenantId"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="owner" property="owner"/>
        <result column="operator" property="operator"/>
        <result column="phone_number" property="phoneNumber"/>
        <result column="busi_type" property="busiType"/>
        <result column="terminal_id" property="terminalId"/>
        <result column="camera_no" property="cameraNo"/>
        <result column="camera_url" property="cameraUrl"/>
        <result column="street" property="street"/>
        <result column="accuracy" property="accuracy"/>
        <result column="air_type" property="airType"/>
        <result column="page_start" property="pageStart"/>
        <result column="page_size" property="pageSize"/>
        <result column="gmt_created" property="gmtCreated"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, external_id, code, type, name, alt, height, radius, point, point_gcj02, address, point_or_name,
        team_id, crt_type, crt_user_id, crt_time, crt_user_name, upd_user_id, upd_time, upd_user_name,
        remark, is_disabled, is_deleted, depart_id, depart_ids, external_tenant_id, tenant_id, owner, operator, phone_number,
        busi_type, terminal_id, camera_no, camera_url, street, accuracy, air_type, page_start, page_size,
        gmt_created, gmt_modified
    </sql>

    <!-- 根据外部ID查询 -->
    <select id="selectByExternalId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM landing_point
        WHERE external_id = #{externalId}
    </select>

</mapper> 