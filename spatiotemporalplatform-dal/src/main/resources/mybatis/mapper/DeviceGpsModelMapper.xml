<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.DeviceGpsModelMapper">
    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.DeviceGpsModelDO">
        <!--@mbg.generated-->
        <!--@Table device_gps_model-->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_modify" jdbcType="TIMESTAMP" property="gmtModify" />
        <result column="orientation_status" jdbcType="INTEGER" property="orientationStatus" />
        <result column="device_code" jdbcType="VARCHAR" property="deviceCode" />
        <result column="device_id" jdbcType="VARCHAR" property="deviceId" />
        <result column="speed" jdbcType="VARCHAR" property="speed" />
        <result column="user_device_code" jdbcType="VARCHAR" property="userDeviceCode" />
        <result column="uid" jdbcType="VARCHAR" property="uid" />
        <result column="star_count" jdbcType="INTEGER" property="starCount" />
        <result column="antenna_status" jdbcType="INTEGER" property="antennaStatus" />
        <result column="angle" jdbcType="VARCHAR" property="angle" />
        <result column="event" jdbcType="VARCHAR" property="event" />
        <result column="cap_time" jdbcType="VARCHAR" property="capTime" />
        <result column="gps_x" jdbcType="VARCHAR" property="gpsX" />
        <result column="gps_y" jdbcType="VARCHAR" property="gpsY" />
        <result column="height" jdbcType="VARCHAR" property="height" />
        <result column="biz_data" jdbcType="VARCHAR" property="bizData" />
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, gmt_create, gmt_modify, orientation_status, device_code, device_id, speed, user_device_code,
        uid, star_count, antenna_status, angle, event, cap_time, gps_x, gps_y, height,
        biz_data, is_deleted
    </sql>
</mapper>
