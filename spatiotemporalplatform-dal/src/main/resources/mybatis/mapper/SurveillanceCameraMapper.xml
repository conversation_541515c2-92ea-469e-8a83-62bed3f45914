<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.SurveillanceCameraMapper">
    <resultMap id="BaseResultMap"
               type="com.deepinnet.spatiotemporalplatform.dal.dataobject.SurveillanceCameraDO">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="gmtCreated" column="gmt_created" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="cameraCode" column="camera_code" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="installationLocation" column="installation_location" jdbcType="VARCHAR"/>
        <result property="coordinates" column="coordinates" jdbcType="OTHER"
                typeHandler="com.deepinnet.spatiotemporalplatform.dal.type.PGPointTypeHandler"/>
        <result property="lat" column="lat" jdbcType="VARCHAR"/>
        <result property="lng" column="lng" jdbcType="VARCHAR"/>
        <result property="videoStreamUrl" column="video_stream_url" jdbcType="VARCHAR"/>
        <result property="hasPassCarData" column="has_pass_car_data" jdbcType="BOOLEAN"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="source" column="source" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        gmt_created,
        gmt_modified,
        camera_code,
        name,
        type,
        installation_location,
        coordinates,
        is_deleted,
        lat,
        lng,
        video_stream_url,
        has_pass_car_data,
        status,
        source
    </sql>

    <select id="queryCameraWithInArea" resultMap="BaseResultMap">
        select s.camera_code,
               s.name,
               s.type,
               s.installation_location,
               s.coordinates,
               s.lat,
               s.lng,
               s.video_stream_url,
               s.status,
               s.source
        from surveillance_cameras s
                 join area c on st_within(s.coordinates, c.geo_big)
        where c.area_code = #{areaCode,jdbcType=VARCHAR}
          and s.type = #{type,jdbcType=VARCHAR}
          and s.is_deleted = false
          and s.status = 'online'
        order by s.gmt_created desc
    </select>

    <select id="queryLineNearCamera" resultMap="BaseResultMap">
        with route_buffer as (
            select st_buffer(ST_GeomFromText(#{wkt,jdbcType=VARCHAR}, 4326)::geography, #{distance,jdbcType=INTEGER})::geometry as geom
        ) , ranked_cameras AS (
            SELECT s.*,
            ST_LineLocatePoint(ST_GeomFromText(#{wkt,jdbcType=VARCHAR}, 4326), s.coordinates) AS relative_position,
            ROW_NUMBER() OVER (PARTITION BY camera_code ORDER BY type) AS row_num
            FROM surveillance_cameras s
            INNER JOIN route_buffer r ON ST_Within(s.coordinates, r.geom)
            WHERE s.is_deleted = false
            AND s.status = 'online'
            <if test="typeList != null and typeList.size() != 0">
                AND s.type IN
                <foreach collection="typeList" item="type" separator="," open="(" close=")">
                    #{type,jdbcType=VARCHAR}
                </foreach>
            </if>
        )
        SELECT *
        FROM ranked_cameras
        WHERE row_num = 1
        ORDER BY relative_position
    </select>

    <select id="queryAreaNearCamera" resultMap="BaseResultMap">
        with route_buffer as (select st_buffer(ST_GeomFromText(#{wkt,jdbcType=VARCHAR}, 4326)::geography, #{distance,jdbcType=INTEGER})::geometry as geom)
        SELECT *
        FROM surveillance_cameras s
                 inner join route_buffer r on st_within(s.coordinates, r.geom)
        WHERE s.is_deleted = false
        and s.status = 'online'
        <if test="typeList != null and typeList.size() != 0">
            and s.type in
            <foreach collection="typeList" item="type" separator="," open="(" close=")">
                #{type,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <select id="findAreaCarCamerasWithExtends" resultMap="BaseResultMap">
        with route_buffer as (select st_buffer(geo_big::geography, #{extendDistance})::geometry as geom
                              from area
                              where area_code = #{areaCode})
        select s.camera_code,
               s.name,
               s.type,
               s.installation_location,
               s.coordinates,
               s.lat,
               s.lng,
               s.video_stream_url,
               s.status,
               s.source
        from surveillance_cameras s
                 inner join route_buffer r on st_within(s.coordinates, r.geom)
        where s.is_deleted = false
          and s.has_pass_car_data = true
          and s.type = '1'
          and s.status = 'online'
    </select>

    <!-- 判断一个坐标是否在区域范围内 -->
    <select id="isCameraInArea" resultType="boolean">
        select st_within(st_setsrid(st_makepoint(#{lng}, #{lat}), 4326), geo_big)
        from area
        where area_code = #{areaCode}
    </select>

    <select id="queryWithGis" resultMap="BaseResultMap">
        With ranked_cameras AS (
            SELECT s.*,
            ROW_NUMBER() OVER (PARTITION BY camera_code ORDER BY type) AS row_num
            FROM surveillance_cameras s
            WHERE s.is_deleted = false
            AND s.status = 'online'
            <if test="typeList != null and typeList.size() != 0">
                AND s.type IN
                <foreach collection="typeList" item="type" separator="," open="(" close=")">
                    #{type,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="keyword != null and keyword != ''">
                and (s.name like concat('%', #{keyword,jdbcType=VARCHAR},'%') or s.camera_code like concat('%', #{keyword,jdbcType=VARCHAR})
                or s.camera_code like concat(#{keyword,jdbcType=VARCHAR}, '%'))
             </if>
            order by name
        )
        SELECT *
        FROM ranked_cameras
        WHERE row_num = 1
    </select>

    <insert id="upsertBatch">
            INSERT INTO surveillance_cameras (camera_code, name, type, coordinates, lat, lng, status)
            VALUES
            <foreach collection="list" item="item" separator=",">
                (#{item.cameraCode},
                 #{item.name},
                 #{item.type},
                 #{item.coordinates,typeHandler=com.deepinnet.spatiotemporalplatform.dal.type.PGPointTypeHandler},
                 #{item.lat},
                 #{item.lng},
                 #{item.status})
            </foreach>
            ON CONFLICT (camera_code, type) DO UPDATE
            SET
            lat = EXCLUDED.lat,
            lng = EXCLUDED.lng,
            coordinates = EXCLUDED.coordinates,
            gmt_modified = now()
    </insert>
</mapper>
