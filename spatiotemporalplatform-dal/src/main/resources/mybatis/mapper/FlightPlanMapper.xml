<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.FlightPlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightPlanDO">
        <id column="id" property="id" />
        <result column="out_plan_no" property="outPlanNo" />
        <result column="scence_code" property="scenceCode" />
        <result column="biz_no" property="bizNo" />
        <result column="mission_id" property="missionId" />
        <result column="plan_id" property="planId" />
        <result column="plan_name" property="planName" />
        <result column="requirement_name" property="requirementName" />
        <result column="related_id" property="relatedId" />
        <result column="apply_type" property="applyType" />
        <result column="uav_id" property="uavId" />
        <result column="uav_model" property="uavModel" />
        <result column="apply_user_name" property="applyUserName" />
        <result column="status" property="status" />
        <result column="planed_takeoff_time" property="planedTakeoffTime" />
        <result column="planed_landing_time" property="planedLandingTime" />
        <result column="real_ame_verification" property="realNameVerification" />
        <result column="operator_user" property="operatorUser" />
        <result column="operator_phone" property="operatorPhone" />
        <result column="landing_aerodrome_id" property="landingAerodromeId" />
        <result column="arrival_aerodrome_id" property="arrivalAerodromeId" />
        <result column="alternate_aerodrome_id" property="alternateAerodromeId" />
        <result column="planed_altitude" property="planedAltitude" />
        <result column="flight_unit" property="flightUnit" />
        <result column="flight_unit_id" property="flightUnitId" />
        <result column="plan_create_time" property="planCreateTime" />
        <result column="crt_user_id" property="crtUserId" />
        <result column="crt_user_name" property="crtUserName" />
        <result column="flight_report" property="flightReport" />
        <result column="sync_status" property="syncStatus" />
        <result column="sync_start" property="syncStart" />
        <result column="sync_end" property="syncEnd" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, out_plan_no, plan_name, requirement_name, scence_code, biz_no, mission_id, plan_id, related_id, apply_type, uav_id, uav_model, apply_user_name, status, planed_takeoff_time, planed_landing_time, real_ame_verification, operator_user, operator_phone, landing_aerodrome_id, arrival_aerodrome_id, alternate_aerodrome_id, planed_altitude, flight_unit, flight_unit_id, tenant_id, plan_create_time, crt_user_id, crt_user_name, flight_report, sync_status, sync_start, sync_end, gmt_created, gmt_modified
    </sql>
    <select id="listFlightPlanByCondition"
            resultType="com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightPlanDO">
        select
            <include refid="Base_Column_List" />
        from
            flight_plan
        <where>
            <if test="query.startTime == null or query.endTime == null">
                (DATE(to_timestamp(planed_takeoff_time / 1000)) = CURRENT_DATE OR DATE(to_timestamp(planed_landing_time / 1000)) = CURRENT_DATE)
            </if>
            <if test="query.startTime != null and query.endTime != null">
                planed_takeoff_time &gt;= #{query.startTime}
                and planed_takeoff_time &lt;= #{query.endTime}
            </if>
            <if test="query.status != null and query.status.size() > 0">
                AND status in
                <foreach collection="query.status" item="state" open="(" close=")" separator=",">
                    #{state}
                </foreach>
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                and (flight_unit like concat('%', #{query.keyword}, '%') or uav_id like concat('%', #{query.keyword}, '%'))
            </if>
            <if test="query.planIdList != null and query.planIdList.size() > 0">
                and plan_id in
                <foreach collection="query.planIdList" item="planId" separator="," open="(" close=")">
                    #{planId}
                </foreach>
            </if>
        </where>
        ORDER BY planed_takeoff_time DESC, id DESC
    </select>

    <select id="queryFlightDemandPlanStat" resultType="com.deepinnet.spatiotemporalplatform.dal.model.FlightDemandPlanStatDO">
        select
        fd.category_no as  demand_category_code,
        count(fp.id) as plan_num
        from flight_plan as fp
        left join flight_merge_demand_relation fmdr on fmdr.target_demand_code = fp.biz_no
        left join flight_demand as fd on fd.demand_no = fmdr.original_demand_code
        where
            fd.tenant_id = #{tenantId}
            and fd.publish_time >= #{demandCreateTime}
        <if test="demandScene != null">
            and fd.scene = #{demandScene}
        </if>
        <if test="demandScene != null and demandScene == 'ONE_NET_UNIFIED_FLY'">
            <!-- 一网统飞的数据需要过滤状态 -->
            and fd.match_status in ('MATCHED','PARTIAL_MATCHED')
        </if>
        <if test="userNo != null">
            and fd.publisher_no = #{userNo}
        </if>
        <if test="userNoList != null and userNoList.size() > 0">
            and fd.publisher_no in
            <foreach collection="userNoList" item="userNo" open="(" close=")" separator=",">
                #{userNo}
            </foreach>
        </if>
        <if test="account != null">
            and (fd.biz_data::json->>'account') = #{account}
        </if>
          <if test="demandType != null">
              and fd.type = #{demandType}
          </if>
          <if test="categoryCodeList != null">
              and fd.category_no in
              <foreach collection="categoryCodeList" item="categoryCode" open="(" close=")" separator=",">
                  #{categoryCode}
              </foreach>
          </if>

        group by fd.category_no

    </select>

</mapper>
