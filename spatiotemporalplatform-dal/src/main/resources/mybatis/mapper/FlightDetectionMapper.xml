<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.FlightDetectionMapper">

    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightDetectionDO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="devId" column="dev_id" jdbcType="VARCHAR"/>
        <result property="model" column="model" jdbcType="VARCHAR"/>
        <result property="osid" column="osid" jdbcType="VARCHAR"/>
        <result property="uaType" column="ua_type" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="direction" column="direction" jdbcType="INTEGER"/>
        <result property="speedHorizontal" column="speed_horizontal" jdbcType="INTEGER"/>
        <result property="speedVertical" column="speed_vertical" jdbcType="INTEGER"/>
        <result property="longitude" column="longitude" jdbcType="DOUBLE"/>
        <result property="latitude" column="latitude" jdbcType="DOUBLE"/>
        <result property="altitudeGeo" column="altitude_geo" jdbcType="INTEGER"/>
        <result property="altitudeBaro" column="altitude_baro" jdbcType="INTEGER"/>
        <result property="height" column="height" jdbcType="INTEGER"/>
        <result property="operatorLongitude" column="operator_longitude" jdbcType="DOUBLE"/>
        <result property="operatorLatitude" column="operator_latitude" jdbcType="DOUBLE"/>
        <result property="operatorAltitudeGeo" column="operator_altitude_geo" jdbcType="INTEGER"/>
        <result property="rssi" column="rssi" jdbcType="INTEGER"/>
        <result property="rssi0" column="rssi0" jdbcType="INTEGER"/>
        <result property="rssi1" column="rssi1" jdbcType="INTEGER"/>
        <result property="temperature" column="temperature" jdbcType="DOUBLE"/>
        <result property="mac" column="mac" jdbcType="VARCHAR"/>
        <result property="frequency" column="frequency" jdbcType="VARCHAR"/>
        <result property="bandwidth" column="bandwidth" jdbcType="VARCHAR"/>
        <result property="time" column="time" jdbcType="BIGINT"/>
        <result property="coordinate" column="coordinate"
                typeHandler="com.deepinnet.spatiotemporalplatform.dal.type.PGPointTypeHandler"/>
        <result property="gmtCreated" column="gmt_created" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,dev_id,model,
        osid,ua_type,status,
        direction,speed_horizontal,speed_vertical,
        longitude,latitude,altitude_geo,
        altitude_baro,height,operator_longitude,
        operator_latitude,operator_altitude_geo,rssi,
        rssi0,rssi1,temperature,
        mac,frequency,bandwidth,
        `time`,coordinate,gmt_created,
        gmt_modified
    </sql>

    <!-- 查询不在飞行计划内的无人机 -->
    <select id="getUnplannedFlights" resultType="com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightDetectionDO">
        WITH today_flights AS (SELECT fd.*
                               FROM public.flight_detection fd
                               WHERE id >= #{minId}
                                 AND
            DATE (to_timestamp(fd.time/1000)) = CURRENT_DATE
            )
           , flights_with_plan AS (
        SELECT
            tf.*, fp.plan_id, fp.planed_takeoff_time AS planned_takeoff, fp.planed_landing_time AS planned_landing
        FROM
            today_flights tf
            LEFT JOIN
            public.flight_plan fp
        ON
            tf.osid = fp.uav_id
            )

        SELECT fd.*
        FROM public.flight_detection fd
        WHERE fd.id IN (SELECT fwp.id
                        FROM flights_with_plan fwp
                        WHERE fwp.plan_id IS NULL
                           OR (fwp.time &lt; fwp.planned_takeoff - 900000
                            OR
                               fwp.time &gt; fwp.planned_landing + 900000))
        ORDER BY fd.time
    </select>

    <select id="getOsIdLatestFlight"
            resultType="com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightDetectionDO"
            parameterType="java.util.List">
        SELECT DISTINCT ON (osid) *
        FROM flight_detection
        WHERE osid IN
        <foreach collection="osIds" item="osid" open="(" separator="," close=")">
            #{osid}
        </foreach>
        ORDER BY osid, time DESC
    </select>

</mapper>
