<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.GdPoiMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.GdPoiDO">
        <id column="id" property="id" />
        <result column="poi_id" property="poiId" />
        <result column="poi_name" property="poiName" />
        <result column="poi_lng" property="poiLng" />
        <result column="poi_lat" property="poiLat" />
        <result column="district_code" property="districtCode" />
        <result column="district_name" property="districtName" />
        <result column="poi_first_type_name" property="poiFirstTypeName" />
        <result column="poi_second_type_name" property="poiSecondTypeName" />
        <result column="poi_third_type_name" property="poiThirdTypeName" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="poi_third_type" property="poiThirdType" />
        <result column="poi" property="poi" jdbcType="OTHER" typeHandler="com.deepinnet.spatiotemporalplatform.dal.type.PGPointTypeHandler" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, poi_id, poi_name, poi_lng, poi_lat, district_code, district_name, poi_first_type_name, poi_second_type_name, poi_third_type_name, gmt_created, gmt_modified, poi_third_type, poi
    </sql>
    <select id="getPoiByRegionAndType"
            parameterType="java.util.Map"
            resultType="com.deepinnet.spatiotemporalplatform.dal.dataobject.GdPoiDO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            gd_poi
        WHERE
            ST_Within(poi, ST_SetSRID(ST_GeomFromGeoJSON(#{region,jdbcType=VARCHAR}), 4326))
        <if test="types != null and types.size() > 0">
            AND poi_third_type IN
            <foreach item="type" collection="types" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
    </select>

</mapper>
