<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.FlightDailyStatisticsDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightDailyStatisticsDO">
        <id column="id" property="id" />
        <result column="flight_unit_id" property="flightUnitId" />
        <result column="flight_unit" property="flightUnit" />
        <result column="flight_count" property="flightCount" />
        <result column="flight_duration" property="flightDuration" />
        <result column="flight_miles" property="flightMiles" />
        <result column="date" property="date" />
        <result column="timestamp" property="timestamp" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, flight_unit_id, flight_unit, flight_count, flight_duration, flight_miles, date, timestamp, gmt_created, gmt_modified
    </sql>

</mapper>
