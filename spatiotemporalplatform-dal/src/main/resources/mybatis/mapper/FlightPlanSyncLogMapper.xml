<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.FlightPlanSyncLogDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightPlanSyncLogDO">
        <id column="id" property="id" />
        <result column="plan_no" property="planNo" />
        <result column="sync_date" property="syncDate" />
        <result column="sync_time" property="syncTime" />
        <result column="sync_date_time" property="syncDateTime" />
        <result column="sync_data" property="syncData" />
        <result column="tenant_id" property="tenantId" />
        <result column="is_deleted" property="isDeleted" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, plan_no, sync_date, sync_time, sync_date_time, sync_data, tenant_id, is_deleted, gmt_created, gmt_modified
    </sql>

</mapper>
