<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.FlightMissionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightMissionDO">
        <id column="id" property="id" />
        <result column="mission_id" property="missionId" />
        <result column="status" property="status" />
        <result column="planed_takeoff_time" property="planedTakeoffTime" />
        <result column="planed_landing_time" property="planedLandingTime" />
        <result column="flight_unit" property="flightUnit" />
        <result column="flight_unit_id" property="flightUnitId" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, mission_id, status, planed_takeoff_time, planed_landing_time, flight_unit, flight_unit_id, gmt_created, gmt_modified
    </sql>

    <select id="getFlightMissionByCondition"
            resultType="com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightMissionDO">
        SELECT
            fm.*
        FROM flight_mission fm
        LEFT JOIN flight_plan  fp ON fm.mission_id = fp.mission_id
        WHERE
            (DATE(to_timestamp(fm.planed_takeoff_time / 1000)) = CURRENT_DATE OR DATE(to_timestamp(fm.planed_landing_time / 1000)) = CURRENT_DATE)
            <if test="queryDTO.keyword != null and queryDTO.keyword != ''">
                and (fm.flight_unit like concat('%', #{queryDTO.keyword}, '%') or fp.uav_id like concat('%', #{queryDTO.keyword}, '%'))
            </if>
            <if test="queryDTO.status != null and queryDTO.status != ''">
                and fm.status = #{queryDTO.status}
            </if>
            <if test="queryDTO.startTime != null and queryDTO.startTime != ''">
                and fm.planed_takeoff_time &gt;= #{queryDTO.startTime}
            </if>
            <if test="queryDTO.endTime != null and queryDTO.endTime != ''">
                and fm.planed_landing_time &gt;= #{queryDTO.endTime}
            </if>
        ORDER BY fm.planed_takeoff_time DESC
    </select>

</mapper>
