<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.RequestStubMapper">

    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.RequestStubDO">
            <result property="id" column="id" jdbcType="BIGINT"/>
            <result property="url" column="url" jdbcType="VARCHAR"/>
            <result property="method" column="method" jdbcType="VARCHAR"/>
            <result property="param" column="param" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="response" column="response" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtModify" column="gmt_modify" jdbcType="TIMESTAMP"/>
            <result property="errorMessage" column="error_message" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,url,method,
        param,status,response,
        gmt_create,gmt_modify,error_message
    </sql>
</mapper>
