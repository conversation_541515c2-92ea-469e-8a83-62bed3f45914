<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.RiskWarningMapper">

    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.RiskWarningDO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="warningTime" column="warning_time" jdbcType="TIMESTAMP"/>
        <result property="warningNo" column="warning_no" jdbcType="VARCHAR"/>
        <result property="flightId" column="flight_id" jdbcType="VARCHAR"/>
        <result property="planId" column="plan_id" jdbcType="VARCHAR"/>
        <result property="uavId" column="uav_id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="operatingEntityId" column="operating_entity_id" jdbcType="VARCHAR"/>
        <result property="warningType" column="warning_type" jdbcType="VARCHAR"/>
        <result property="subType" column="sub_type" jdbcType="VARCHAR"/>
        <result property="warningStatus" column="warning_status" jdbcType="VARCHAR"/>
        <result property="warningDetails" column="warning_details" jdbcType="VARCHAR"/>
        <result property="gmtCreated" column="gmt_created" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="height" column="height" jdbcType="VARCHAR"/>
        <result property="longitude" column="longitude" jdbcType="VARCHAR"/>
        <result property="latitude" column="latitude" jdbcType="VARCHAR"/>
        <result property="operatingEntityName" column="operating_entity_name" jdbcType="VARCHAR"/>
        <result property="altitude" column="altitude" jdbcType="VARCHAR"/>
        <result property="flightSpeed" column="flight_speed" jdbcType="VARCHAR"/>
        <result property="startId" column="start_id" jdbcType="BIGINT"/>
        <result property="endId" column="end_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,warning_time,warning_no,
        flight_id,plan_id,uav_id,
        user_id,operating_entity_id,warning_type,
        sub_type,warning_status,warning_details,
        gmt_created,gmt_modified,height,
        longitude,latitude,operating_entity_name,
        altitude,flight_speed,start_id,
        end_id
    </sql>

    <select id="getSubTypeCountList" resultType="com.deepinnet.spatiotemporalplatform.dal.dto.SubTypeCountDTO">
        SELECT sub_type AS subType, COUNT(*) AS count
        FROM risk_warning
        <where>
            <if test="startTime != null">
                AND warning_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND warning_time &lt;= #{endTime}
            </if>
            <if test="needWarningTypeList != null and needWarningTypeList.size() > 0">
                AND sub_type IN
                <foreach collection="needWarningTypeList" item="warningType" open="(" close=")" separator=",">
                    #{warningType}
                </foreach>
            </if>
        </where>
        GROUP BY sub_type
        ORDER BY count DESC
    </select>

    <select id="getStatusCountList" resultType="com.deepinnet.spatiotemporalplatform.dal.dto.StatusCountDTO">
        SELECT warning_status AS status, COUNT(*) AS count
        FROM risk_warning
        <where>
            <if test="startTime != null">
                AND warning_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND warning_time &lt;= #{endTime}
            </if>
            <if test="needWarningTypeList != null and needWarningTypeList.size() > 0">
                AND sub_type IN
                <foreach collection="needWarningTypeList" item="warningType" open="(" close=")" separator=",">
                    #{warningType}
                </foreach>
            </if>
        </where>
        GROUP BY warning_status
        ORDER BY count DESC
    </select>
    
    <select id="listTopRiskWarningsByPlanIds" resultMap="BaseResultMap">
        WITH ranked_warnings AS (
            SELECT 
                *,
                ROW_NUMBER() OVER (PARTITION BY plan_id ORDER BY id DESC) AS rn
            FROM 
                risk_warning
            WHERE 
                plan_id IN
                <foreach collection="planIds" item="planId" open="(" close=")" separator=",">
                    #{planId}
                </foreach>
        )
        SELECT 
            <include refid="Base_Column_List"/>
        FROM 
            ranked_warnings
        WHERE 
            rn &lt;= #{limit}
        ORDER BY 
            id DESC
    </select>
</mapper>
