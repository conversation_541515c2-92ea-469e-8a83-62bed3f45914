<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.UavMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.UavDO">
        <id column="id" property="id" />
        <result column="uav_id" property="uavId" />
        <result column="uav_name" property="uavName" />
        <result column="uav_company" property="uavCompany" />
        <result column="uav_registration_entity" property="uavRegistrationEntity" />
        <result column="aircraft_type" property="aircraftType" />
        <result column="usa" property="usa" />
        <result column="sn" property="sn" />
        <result column="model" property="model" />
        <result column="insurance_status" property="insuranceStatus" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, uav_id, uav_name, uav_company, uav_registration_entity, aircraft_type, usa, sn, model, insurance_status, gmt_created, gmt_modified
    </sql>

</mapper>
