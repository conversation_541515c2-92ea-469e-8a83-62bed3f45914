<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.AirspaceMapper">

    <select id="listByCodes" resultType="com.deepinnet.spatiotemporalplatform.dal.dataobject.AirspaceDO">
        select * from airspace where code in <foreach item="item" collection="codes" open="(" separator="," close=")">#{item}</foreach>
    </select>




</mapper>
