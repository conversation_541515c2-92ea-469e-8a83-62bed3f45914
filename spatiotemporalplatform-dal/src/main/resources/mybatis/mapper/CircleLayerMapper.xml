<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.CircleLayerMapper">

    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.CircleLayerDO">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="gmtCreated" column="gmt_created" jdbcType="TIMESTAMP"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="areaCode" column="area_code" jdbcType="VARCHAR"/>
            <result property="layerId" column="layer_id" jdbcType="VARCHAR"/>
            <result property="layerName" column="layer_name" jdbcType="VARCHAR"/>
            <result property="layerType" column="layer_type" jdbcType="VARCHAR"/>
            <result property="boundaryCoordinates" column="boundary_coordinates" jdbcType="OTHER" typeHandler="com.deepinnet.spatiotemporalplatform.dal.type.PGPolygonTypeHandler"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,gmt_created,gmt_modified,
        area_code,layer_id,layer_name,
        layer_type,boundary_coordinates
    </sql>
</mapper>
