<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.FlightPlanAchievementDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightPlanAchievementDO">
        <id column="id" property="id" />
        <result column="plan_no" property="planNo" />
        <result column="achievement_id" property="achievementId" />
        <result column="achievement_name" property="achievementName" />
        <result column="achievement_type" property="achievementType" />
        <result column="achievement_time" property="achievementTime" />
        <result column="achievement_url" property="achievementUrl" />
        <result column="achievement_address" property="achievementAddress" />
        <result column="achievement_location" property="achievementLocation" typeHandler="com.deepinnet.spatiotemporalplatform.dal.type.PGPointTypeHandler"/>
        <result column="achievement_location_str" property="achievementLocationStr"/>
        <result column="tenant_id" property="tenantId" />
        <result column="is_deleted" property="isDeleted" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, plan_no, achievement_id, achievement_name, achievement_type, achievement_time, achievement_url, achievement_address, achievement_location, achievement_location_str, tenant_id, is_deleted, gmt_created, gmt_modified
    </sql>

    <select id="getLastAchievement"
            resultType="com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightPlanAchievementDO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            flight_plan_achievement
        WHERE
            achievement_location IS NOT NULL
            AND plan_no != #{planNo}
            AND ST_DWithin(
                achievement_location::geography,
                ST_SetSRID(ST_MakePoint(
                    split_part(#{location}, ',', 1)::float8,
                    split_part(#{location}, ',', 2)::float8
                ), 4326)::geography,
                20
            )
            <if test="compareDate != null">
                AND achievement_time::date = #{compareDate}
            </if>
        ORDER BY achievement_time DESC
        LIMIT 1
    </select>

</mapper>
