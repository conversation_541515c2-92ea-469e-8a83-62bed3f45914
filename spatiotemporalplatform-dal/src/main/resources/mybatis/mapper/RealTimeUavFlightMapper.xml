<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.RealTimeUavFlightMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.RealTimeUavFlightDO">
        <id column="id" property="id"/>
        <result column="plan_id" property="planId"/>
        <result column="uav_id" property="uavId"/>
        <result column="flight_id" property="flightId"/>
        <result column="status" property="status"/>
        <result column="operator_position" property="operatorPosition"/>
        <result column="operator_altitude" property="operatorAltitude"/>
        <result column="flight_speed" property="flightSpeed"/>
        <result column="uav_position" property="uavPosition"/>
        <result column="uav_altitude" property="uavAltitude"/>
        <result column="elevation" property="elevation"/>
        <result column="flight_duration" property="flightDuration"/>
        <result column="flight_miles" property="flightMiles"/>
        <result column="voltage" property="voltage"/>
        <result column="soc" property="soc"/>
        <result column="report_time" property="reportTime"/>
        <result column="gmt_created" property="gmtCreated"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , plan_id, uav_id, flight_id, status, operator_position, operator_altitude, flight_speed, uav_position, uav_altitude, elevation, flight_duration, flight_miles, voltage, soc, report_time, gmt_created, gmt_modified
    </sql>

    <select id="countDistinctFlightsFast" resultType="int">
        SELECT COUNT(*) FROM (
        SELECT 1
        FROM real_time_uav_flight
        <where>
            plan_id IN
            <foreach collection="planIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            <if test="startTime != null and endTime != null">
                AND report_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        GROUP BY flight_id
        ) AS tmp
    </select>

    <select id="queryLatestFlights"
            resultType="com.deepinnet.spatiotemporalplatform.dal.dataobject.RealTimeUavFlightDO">
        WITH ids(plan_id) AS (
        VALUES
            <foreach collection="planIds" item="id" separator=",">
                (#{id})
            </foreach>
        )
        SELECT *
        FROM ids
        CROSS JOIN LATERAL (
            SELECT DISTINCT ON (uav_id)
                id, uav_id, flight_id, report_time,
                status, flight_speed, uav_position,
                uav_altitude, elevation, flight_duration,
                flight_miles, voltage, soc
            FROM   real_time_uav_flight
            WHERE  plan_id = ids.plan_id
            ORDER  BY uav_id, report_time DESC
        ) t
    </select>

    <select id="queryPlanFlights"
            resultType="com.deepinnet.spatiotemporalplatform.dal.dataobject.RealTimeUavFlightDO">
        SELECT
        DISTINCT ON (plan_id, flight_id) *
        FROM real_time_uav_flight
        WHERE
        plan_id IN
        <foreach collection="planIds" item="planId" open="(" separator="," close=")">
            #{planId}
        </foreach>
        ORDER BY plan_id, flight_id,report_time DESC
        LIMIT #{pageSize} OFFSET (#{pageNo}-1) * #{pageSize}
    </select>

    <select id="queryLatestFlightsByReportTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from real_time_uav_flight
        where plan_id = #{planId}
        order by abs(extract(epoch from (report_time - #{reportTime})))
        limit 1
    </select>

    <select id="queryFlightPlanIdLastFlightId"
            resultType="com.deepinnet.spatiotemporalplatform.dal.dataobject.RealTimeUavFlightDO">
        SELECT DISTINCT ON (plan_id)
        plan_id,
        flight_id
        FROM real_time_uav_flight
        WHERE flight_id IS NOT NULL
        AND plan_id in
        <foreach collection="planIds" item="planId" open="(" separator="," close=")">
            #{planId}
        </foreach>
        ORDER BY plan_id, gmt_created DESC;
    </select>

    <select id="queryFlightPlanLastStatus"
            resultType="com.deepinnet.spatiotemporalplatform.dal.dataobject.RealTimeUavFlightDO">
        WITH ids(plan_id) AS (
        VALUES
            <foreach collection="planIds" item="id" separator=",">
                (#{id})
            </foreach>
        )
        SELECT t.id, ids.plan_id, t.flight_id, t.report_time, t.status, t.flight_speed, t.uav_position, t.uav_altitude, t.elevation, t.flight_duration, t.flight_miles, t.voltage, t.soc
        FROM ids
        CROSS JOIN LATERAL (
            SELECT DISTINCT ON (plan_id)
                id, flight_id, report_time,
                status, flight_speed, uav_position,
                uav_altitude, elevation, flight_duration,
                flight_miles, voltage, soc
            FROM
                real_time_uav_flight
            WHERE
                plan_id = ids.plan_id
            ORDER  BY
                plan_id, report_time DESC
        ) t
    </select>

</mapper>
