<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.TaskInFlightMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.TaskInFlightDO">
        <id column="id" property="id" />
        <result column="statistical_date" property="statisticalDate" />
        <result column="statistical_time" property="statisticalTime" />
        <result column="statistical_count" property="statisticalCount" />
        <result column="statistical_history_avg_count" property="statisticalHistoryAvgCount" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, statistical_date, statistical_time, statistical_count, statistical_history_avg_count, gmt_created, gmt_modified
    </sql>

</mapper>
