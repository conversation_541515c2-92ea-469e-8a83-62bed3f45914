<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.GdGroundFeatureMapper">

    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.GdGroundFeatureDO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="gridCode" column="grid_code" jdbcType="VARCHAR"/>
        <result property="flow" column="flow" jdbcType="VARCHAR"/>
        <result property="gridGeometry" column="grid_geometry" jdbcType="OTHER"
                typeHandler="com.deepinnet.spatiotemporalplatform.dal.type.PGPolygonTypeHandler"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,grid_code,flow,grid_geometry
    </sql>
    <select id="getGroundFeatureList"
            resultMap="BaseResultMap">
        SELECT
        id,
        grid_code,
        flow,
        grid_geometry
        FROM gd_ground_feature
        WHERE
        <if test="pointList != null and pointList.size > 0">
            <foreach collection="pointList" item="point" separator="OR">
                ST_Intersects(
                grid_geometry,
                ST_SetSRID(ST_MakePoint(#{point.x}, #{point.y}), 4326)
                )
            </foreach>
        </if>
        <if test="region != null and region != ''">
            ST_Intersects(ST_SetSRID(grid_geometry, 4326),
            ST_SetSRID(ST_GeomFromGeoJSON(#{region,jdbcType=VARCHAR}), 4326)) OR
            ST_Contains(ST_SetSRID(grid_geometry, 4326),
            ST_SetSRID(ST_GeomFromGeoJSON(#{region,jdbcType=VARCHAR}), 4326)) OR
            ST_Within(ST_SetSRID(grid_geometry, 4326), ST_SetSRID(ST_GeomFromGeoJSON(#{region,jdbcType=VARCHAR}), 4326)) OR
            ST_Equals(ST_SetSRID(grid_geometry, 4326), ST_SetSRID(ST_GeomFromGeoJSON(#{region,jdbcType=VARCHAR}), 4326))
        </if>
    </select>
</mapper>
