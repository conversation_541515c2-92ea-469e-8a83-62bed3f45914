<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.GdStaticSurfaceObstacleMapper">

    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.GdStaticSurfaceObstacleDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="gridCode" column="grid_code" jdbcType="VARCHAR"/>
            <result property="staticObstacle" column="static_obstacle" jdbcType="VARCHAR"/>
            <result property="center" column="center" jdbcType="VARCHAR"/>
            <result property="gridArea" column="grid_area" jdbcType="VARCHAR"/>
            <result property="gridGeometry" column="grid_geometry" jdbcType="OTHER"
                    typeHandler="com.deepinnet.spatiotemporalplatform.dal.type.PGPolygonTypeHandler"/>
            <result column="gmt_created" property="gmtCreated" />
            <result column="gmt_modified" property="gmtModified" />
    </resultMap>

    <sql id="Base_Column_List">
        id, grid_code, static_obstacle, grid_geometry, center, grid_area, gmt_created, gmt_modified
    </sql>
    <select id="getStaticSurfaceObstacles" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM gd_static_surface_obstacle
        <where>
            <if test="gridCodes != null and gridCodes.size > 0">
                grid_code in
                <foreach collection="gridCodes" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
            <if test="pointList != null and pointList.size > 0">
                AND
                <foreach collection="pointList" item="point" separator="or">
                    ST_Intersects(
                    grid_geometry,
                    ST_SetSRID(ST_MakePoint(#{point.x}, #{point.y}), 4326)
                    )
                </foreach>
            </if>
            <if test="region != null and region != ''">
                AND (ST_Intersects(ST_SetSRID(grid_geometry, 4326), ST_SetSRID(ST_GeomFromGeoJSON(#{region,jdbcType=VARCHAR}),
                4326)) OR
                ST_Contains(ST_SetSRID(grid_geometry, 4326), ST_SetSRID(ST_GeomFromGeoJSON(#{region,jdbcType=VARCHAR}),
                4326)) OR
                ST_Within(ST_SetSRID(grid_geometry, 4326), ST_SetSRID(ST_GeomFromGeoJSON(#{region,jdbcType=VARCHAR}), 4326)) OR
                ST_Equals(ST_SetSRID(grid_geometry, 4326), ST_SetSRID(ST_GeomFromGeoJSON(#{region,jdbcType=VARCHAR}), 4326)))
            </if>
        </where>
    </select>
</mapper>
