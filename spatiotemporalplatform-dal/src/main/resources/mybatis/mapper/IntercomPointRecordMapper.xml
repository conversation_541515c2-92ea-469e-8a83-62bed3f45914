<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.IntercomPointRecordMapper">
    <resultMap id="BaseResultMap"
               type="com.deepinnet.spatiotemporalplatform.dal.dataobject.IntercomPointRecordDO">
        <!--@mbg.generated-->
        <!--@Table intercom_point_record-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="user_account" jdbcType="VARCHAR" property="userAccount"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="lng" jdbcType="VARCHAR" property="lng"/>
        <result column="lat" jdbcType="VARCHAR" property="lat"/>
        <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="area_code" jdbcType="VARCHAR" property="areaCode"/>
        <result column="coordinate" jdbcType="OTHER" property="coordinate"
                typeHandler="com.deepinnet.spatiotemporalplatform.dal.type.PGPointTypeHandler"/>
        <result column="gps_arrive_time" jdbcType="TIMESTAMP" property="gpsArriveTime"/>
        <result column="is_online" jdbcType="BOOLEAN" property="online"/>
        <result column="online_time" jdbcType="TIMESTAMP" property="onlineTime"/>
        <result column="offline_time" jdbcType="TIMESTAMP" property="offlineTime"/>
        <result column="enterprise_id" jdbcType="VARCHAR" property="enterpriseId"/>
        <result column="enterprise_name" jdbcType="VARCHAR" property="enterpriseName"/>
        <result column="contact" jdbcType="VARCHAR" property="contact"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>

    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        code,
        user_account,
        user_name,
        lng,
        lat,
        last_update_time,
        area_code,
        coordinate,
        gps_arrive_time,
        is_online,
        online_time,
        offline_time,
        enterprise_id,
        enterprise_name,
        tenant_id
        contact
    </sql>

    <select id="saveOrUpdateBatchCustom">
        insert into intercom_point_record(code, user_account, user_name, lng, lat, last_update_time, area_code,
                                          coordinate, gps_arrive_time, is_online, online_time, offline_time,
                                          enterprise_id, enterprise_name)
        values
        <foreach collection="list" separator="," item="record">
            (#{record.code,jdbcType=VARCHAR}, #{record.userAccount,jdbcType=VARCHAR},
             #{record.userName,jdbcType=VARCHAR}, #{record.lng,jdbcType=VARCHAR}, #{record.lat,jdbcType=VARCHAR},
             #{record.lastUpdateTime,jdbcType=TIMESTAMP}, #{record.areaCode,jdbcType=VARCHAR},
             #{record.coordinate, typeHandler=com.deepinnet.spatiotemporalplatform.dal.type.PGPointTypeHandler},
             #{record.gpsArriveTime,jdbcType=TIMESTAMP}, #{record.online,jdbcType=BOOLEAN},
             #{record.onlineTime,jdbcType=TIMESTAMP},
             #{record.offlineTime,jdbcType=TIMESTAMP}, #{record.enterpriseId,jdbcType=VARCHAR},
             #{record.enterpriseName,jdbcType=VARCHAR})
        </foreach>
        on conflict (code, area_code)
        do update set
            user_account = excluded.user_account,
            user_name = excluded.user_name,
            lng = excluded.lng,
            lat = excluded.lat,
            last_update_time = excluded.last_update_time,
            area_code = excluded.area_code,
            coordinate = excluded.coordinate,
            gps_arrive_time = excluded.gps_arrive_time,
            is_online = excluded.is_online,
            online_time = excluded.online_time,
            offline_time = excluded.offline_time,
            enterprise_id = excluded.enterprise_id,
            enterprise_name = excluded.enterprise_name
    </select>

    <select id="queryNearIntercomPoint" resultMap="BaseResultMap">
        select * from intercom_point_record
        where st_contains(ST_Buffer(#{point,typeHandler=com.deepinnet.spatiotemporalplatform.dal.type.PGPointTypeHandler}::geography,#{distance})::geometry, coordinate)
    </select>

    <select id="queryPolygonContainsAndBeforeTime" resultMap="BaseResultMap">
        WITH ranked_records AS (
            SELECT code,
                    user_account,
                    user_name,
                    lng,
                    lat,
                    last_update_time,
                    area_code,
                    coordinate,
                    gps_arrive_time,
                    is_online,
                    online_time,
                    offline_time,
                    enterprise_id,
                    enterprise_name,
                    contact,
                   ROW_NUMBER() OVER (PARTITION BY code, user_account ORDER BY gps_arrive_time DESC) AS rank
        FROM intercom_point_record
        WHERE st_contains(st_geomfromtext(#{areaWkt}, 4326), coordinate)
          AND gps_arrive_time >= #{time, jdbcType=TIMESTAMP})
        SELECT code, user_account, user_name, lng, lat, last_update_time, area_code, coordinate, contact
        FROM ranked_records
        WHERE rank = 1;
    </select>

    <select id="queryLatestRecordByCode" resultMap="BaseResultMap">
        select distinct on (code)
        <include refid="Base_Column_List" />
        from intercom_point_record
        <if test="collection != null and collection.size() != 0">
            where code in
            <foreach collection="list" item="code" separator="," open="(" close=")">
                #{code,jdbcType=VARCHAR}
            </foreach>
        </if>
        order by code, gps_arrive_time desc
    </select>
</mapper>