<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.DetectedDeviceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.DetectedDeviceDO">
        <id column="id" property="id" />
        <result column="external_id" property="externalId" />
        <result column="manufacturer" property="manufacturer" />
        <result column="device_name" property="deviceName" />
        <result column="brand_model" property="brandModel" />
        <result column="serial_no" property="serialNo" />
        <result column="station_type" property="stationType" />
        <result column="tech_params" property="techParams" />
        <result column="data_standard" property="dataStandard" />
        <result column="equip_usage" property="equipUsage" />
        <result column="install_envir" property="installEnvir" />
        <result column="install_adress" property="installAdress" />
        <result column="aerial_height" property="aerialHeight" />
        <result column="operating_frequency" property="operatingFrequency" />
        <result column="install_date" property="installDate" />
        <result column="install_count" property="installCount" />
        <result column="install_situation" property="installSituation" />
        <result column="prop_company" property="propCompany" />
        <result column="prop_link_user" property="propLinkUser" />
        <result column="link_phone" property="linkPhone" />
        <result column="device_manager" property="deviceManager" />
        <result column="device_phone" property="devicePhone" />
        <result column="manage_require" property="manageRequire" />
        <result column="remark" property="remark" />
        <result column="crt_user_name" property="crtUserName" />
        <result column="crt_user_id" property="crtUserId" />
        <result column="crt_time" property="crtTime" />
        <result column="is_deleted" property="isDeleted" />
        <result column="upd_user_name" property="updUserName" />
        <result column="upd_user_id" property="updUserId" />
        <result column="upd_time" property="updTime" />
        <result column="depart_id" property="departId" />
        <result column="depart_ids" property="departIds" />
        <result column="external_tenant_id" property="externalTenantId" />
        <result column="tenant_id" property="tenantId" />
        <result column="team_id" property="teamId" />
        <result column="receiver_gain" property="receiverGain" />
        <result column="trans_power" property="transPower" />
        <result column="pulse_waveform" property="pulseWaveform" />
        <result column="polarization_mode" property="polarizationMode" />
        <result column="figure" property="figure" />
        <result column="band_width" property="bandWidth" />
        <result column="site_pic_url" property="sitePicUrl" />
        <result column="lon" property="lon" />
        <result column="lat" property="lat" />
        <result column="gcover_rage" property="gcoverRage" />
        <result column="vcover_rage" property="vcoverRage" />
        <result column="batch_no" property="batchNo" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>

    <sql id="Base_Column_List">
        id, external_id, manufacturer, device_name, brand_model, serial_no, station_type,
        tech_params, data_standard, equip_usage, install_envir, install_adress, aerial_height,
        operating_frequency, install_date, install_count, install_situation, prop_company,
        prop_link_user, link_phone, device_manager, device_phone, manage_require, remark,
        crt_user_name, crt_user_id, crt_time, is_deleted, upd_user_name, upd_user_id, upd_time,
        depart_id, depart_ids, external_tenant_id, tenant_id, team_id, receiver_gain, trans_power, pulse_waveform,
        polarization_mode, figure, band_width, site_pic_url, lon, lat, gcover_rage, vcover_rage,
        batch_no, gmt_created, gmt_modified
    </sql>

    <!-- 根据外部ID查询侦测定位设备 -->
    <select id="selectByExternalId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM detected_device
        WHERE external_id = #{externalId}
        LIMIT 1
    </select>

</mapper> 