<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.BaseStationMapper">

    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.BaseStationDO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="externalId" column="external_id" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="crtUserName" column="crt_user_name" jdbcType="VARCHAR"/>
        <result property="crtUserId" column="crt_user_id" jdbcType="VARCHAR"/>
        <result property="crtTime" column="crt_time" jdbcType="VARCHAR"/>
        <result property="isDeleted" column="is_deleted" jdbcType="VARCHAR"/>
        <result property="updUserName" column="upd_user_name" jdbcType="VARCHAR"/>
        <result property="updUserId" column="upd_user_id" jdbcType="VARCHAR"/>
        <result property="updTime" column="upd_time" jdbcType="VARCHAR"/>
        <result property="departId" column="depart_id" jdbcType="VARCHAR"/>
        <result property="departIds" column="depart_ids" jdbcType="VARCHAR"/>
        <result property="teamId" column="team_id" jdbcType="VARCHAR"/>
        <result property="communityCi" column="community_ci" jdbcType="VARCHAR"/>
        <result property="aau" column="aau" jdbcType="VARCHAR"/>
        <result property="baseStationId" column="base_station_id" jdbcType="VARCHAR"/>
        <result property="baseStationName" column="base_station_name" jdbcType="VARCHAR"/>
        <result property="province" column="province" jdbcType="VARCHAR"/>
        <result property="city" column="city" jdbcType="VARCHAR"/>
        <result property="county" column="county" jdbcType="VARCHAR"/>
        <result property="supplier" column="supplier" jdbcType="VARCHAR"/>
        <result property="ifAf" column="if_af" jdbcType="VARCHAR"/>
        <result property="testField" column="test_field" jdbcType="VARCHAR"/>
        <result property="baseLon" column="base_lon" jdbcType="DOUBLE"/>
        <result property="baseLat" column="base_lat" jdbcType="DOUBLE"/>
        <result property="aauLon" column="aau_lon" jdbcType="DOUBLE"/>
        <result property="aauLat" column="aau_lat" jdbcType="DOUBLE"/>
        <result property="handingHeigh" column="handing_heigh" jdbcType="VARCHAR"/>
        <result property="direction" column="direction" jdbcType="VARCHAR"/>
        <result property="pitch" column="pitch" jdbcType="VARCHAR"/>
        <result property="batchNo" column="batch_no" jdbcType="BIGINT"/>
        <result property="gmtCreated" column="gmt_created" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="externalTenantId" column="external_tenant_id" jdbcType="VARCHAR"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, external_id, remark, crt_user_name, crt_user_id, crt_time, is_deleted, upd_user_name, upd_user_id, 
        upd_time, depart_id, depart_ids, team_id, community_ci, aau, base_station_id, base_station_name, 
        province, city, county, supplier, if_af, test_field, base_lon, base_lat, aau_lon, aau_lat, 
        handing_heigh, direction, pitch, batch_no, gmt_created, gmt_modified, external_tenant_id, tenant_id
    </sql>

    <delete id="deleteByTenantId">
        delete from base_station where tenant_id = #{tenantId}
    </delete>

</mapper> 