<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.SurveillanceCamerasMarkMapper">
  <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.SurveillanceCamerasMarkDO">
    <!--@mbg.generated-->
    <!--@Table surveillance_cameras_mark-->
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="camera_code" jdbcType="VARCHAR" property="cameraCode" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="object_code" jdbcType="VARCHAR" property="objectCode" />
    <result column="is_deleted" jdbcType="BOOLEAN" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="camera_type" jdbcType="VARCHAR" property="cameraType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, camera_code, area_code, object_code, is_deleted, gmt_created, gmt_modified, camera_type
  </sql>
</mapper>