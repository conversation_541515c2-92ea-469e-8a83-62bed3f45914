<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.FlightEventDemandRelationMapper">

    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightEventDemandRelationDO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="algorithmEventId" column="algorithm_event_id" jdbcType="VARCHAR"/>
        <result property="flightOriginDemandCode" column="flight_origin_demand_code" jdbcType="VARCHAR"/>
        <result property="areaSequence" column="area_sequence" jdbcType="INTEGER"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="isDeleted" column="is_deleted" jdbcType="BOOLEAN"/>
        <result property="gmtCreated" column="gmt_created" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, algorithm_event_id, flight_origin_demand_code, area_sequence,
        tenant_id, is_deleted, gmt_created, gmt_modified
    </sql>

    <!-- 根据飞行任务编号和经纬度查询原始需求和区域序号 -->
    <select id="queryDemandAreaByTaskCodeAndLocation" resultType="com.deepinnet.spatiotemporalplatform.dal.dataobject.DemandArea">
        WITH geo_input AS (
            SELECT
                ST_SetSRID(ST_MakePoint(#{longitude}, #{latitude}), 4326) AS input_point,
                ST_Buffer(
                        ST_SetSRID(ST_MakePoint(#{longitude}, #{latitude}), 4326)::geography,
                        #{distance,jdbcType=INTEGER}
                )::geometry AS input_buffer
        )
        SELECT DISTINCT fda.*
        FROM flight_plan fp
                 JOIN flight_merge_demand_relation fmdr
                      ON fmdr.target_demand_code = fp.biz_no
                 JOIN flight_demand_area fda
                      ON fda.demand_code = fmdr.original_demand_code
                 CROSS JOIN geo_input
        WHERE fp.plan_id = #{flightTaskCode}
          AND (
                (
                    fda.type = 'AREA'
                    AND ST_Within(geo_input.input_point, ST_GeomFromText(fda.area_coordinate, 4326))
                )
                OR
                (
                    fda.type = 'POINT'
                    AND ST_Within(
                      ST_SetSRID(
                        ST_MakePoint(fda.center_point_longitude::double precision, fda.center_point_latitude::double precision),
                        4326
                      ),
                      geo_input.input_buffer
                    )
                )
              )
    </select>


    <select id="queryPlanListOfEmergencyDemand"
            resultType="com.deepinnet.spatiotemporalplatform.dal.dataobject.PlanDemandDO">
        select fp.plan_id, fd.demand_no
        from flight_plan fp
        join flight_demand fd on fd.demand_no = fp.biz_no and fd.type = 'EMERGENCY_RESPONSE'
        where fp.plan_id in
        <foreach collection="planIdList" item="planId" open="(" close=")" separator=",">
            #{planId}
        </foreach>
    </select>
</mapper> 