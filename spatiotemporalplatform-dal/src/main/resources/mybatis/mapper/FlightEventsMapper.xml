<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.FlightEventsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.FlightEventsDO">
        <id column="id" property="id" />
        <result column="algorithm_event_id" property="algorithmEventId" />
        <result column="event_type" property="eventType" />
        <result column="event_name" property="eventName" />
        <result column="description" property="description" />
        <result column="event_point" property="eventPoint" typeHandler="com.deepinnet.spatiotemporalplatform.dal.type.PGPointTypeHandler"/>
        <result column="event_location" property="eventLocation" />
        <result column="duration" property="duration" />
        <result column="status" property="status" />
        <result column="license_plate" property="licensePlate" />
        <result column="evidence_images" property="evidenceImages" />
        <result column="evidence_videos" property="evidenceVideos" />
        <result column="flight_task_code" property="flightTaskCode" />
        <result column="tenant_id" property="tenantId" />
        <result column="event_start_time" property="eventStartTime" />
        <result column="event_end_time" property="eventEndTime" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="algorithm_event_gmt_modified" property="algorithmEventGmtModified" />
    </resultMap>
    
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, algorithm_event_id, event_type, event_name, description, 
        event_point, event_location, duration,
        status, license_plate, evidence_images, evidence_videos,
        flight_task_code, tenant_id, event_start_time, event_end_time,
        gmt_created, gmt_modified, algorithm_event_gmt_modified
    </sql>

    <select id="queryFlightEventsStat" resultType="com.deepinnet.spatiotemporalplatform.dal.model.FlightEventsStatDO">
        select
            fe.event_type as eventType,
            count(distinct fe.id) as eventNum
        from flight_events as fe
        join flight_plan as fp on fp.plan_id = fe.flight_task_code
        join flight_merge_demand_relation fmdr on fmdr.target_demand_code = fp.biz_no
        join flight_event_demand_relation fedr on fedr.flight_origin_demand_code = fmdr.original_demand_code and fe.algorithm_event_id = fedr.algorithm_event_id
        join flight_demand as fd on fd.demand_no = fedr.flight_origin_demand_code
        where fd.tenant_id = #{tenantId}
        <if test="userNo != null">
            and fd.publisher_no = #{userNo}
        </if>
        <if test="account != null">
            and (fd.biz_data::json->>'account') = #{account}
        </if>
        <if test="demandType != null">
            and fd.type = #{demandType}
        </if>
        <if test="userNoList != null and userNoList.size() > 0">
            and fd.publisher_no in
            <foreach collection="userNoList" item="userNo" open="(" close=")" separator=",">
                #{userNo}
            </foreach>
        </if>
        and fd.publish_time >= #{startTime}
        and fe.algorithm_event_id = fedr.algorithm_event_id
        group by fe.event_type
    </select>

    <select id="queryUserDemandEvents" resultMap="BaseResultMap">
        select distinct fe.*
        from flight_events as fe
        <if test="userNo != null or accountList != null
            or (demandStartTime != null and demandEndTime != null)">
            join flight_plan as fp on fp.plan_id = fe.flight_task_code
            join flight_merge_demand_relation fmdr on fmdr.target_demand_code = fp.biz_no
            join flight_event_demand_relation fedr on fedr.flight_origin_demand_code = fmdr.original_demand_code and fe.algorithm_event_id = fedr.algorithm_event_id
            join flight_demand as fd on fd.demand_no = fedr.flight_origin_demand_code
                <if test="demandType != null">
                and fd.type = #{demandType}
                </if>
        </if>
        <where>
            <if test="id != null">
                and fe.id = #{id}
            </if>
            <if test="flightTaskCode != null and flightTaskCode != ''">
                and fe.flight_task_code = #{flightTaskCode}
            </if>
            <if test="tenantId != null and tenantId != ''">
                and fe.tenant_id = #{tenantId}
            </if>
            <if test="startTime != null">
                and fe.event_start_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and fe.event_start_time &lt;= #{endTime}
            </if>
            <if test="eventType != null and eventType != ''">
                and fe.event_type = #{eventType}
            </if>
            <if test="eventName != null and eventName != ''">
                and fe.event_name like concat('%', #{eventName}, '%')
            </if>
            <if test="status != null and status != ''">
                and fe.status = #{status}
            </if>
            <if test="eventLocation != null and eventLocation != ''">
                and fe.event_location like concat('%', #{eventLocation}, '%')
            </if>
            <if test="userNo != null">
                and  fd.publisher_no = #{userNo}
            </if>
            <if test="demandStartTime != null and demandEndTime != null">
                AND (fd.publish_time &gt;= #{demandStartTime} AND fd.publish_time &lt;= #{demandEndTime})
            </if>
            <if test="accountList != null and accountList.size() > 0">
                and fd.biz_data::json->>'account' in
                <foreach collection="accountList" item="account" open="(" close=")" separator=",">
                    #{account}
                </foreach>
            </if>
            <if test="userNoList != null and userNoList.size() > 0">
                and fd.publisher_no in
                <foreach collection="userNoList" item="userNo" open="(" close=")" separator=",">
                    #{userNo}
                </foreach>
            </if>
        </where>
        order by fe.event_start_time desc, fe.id desc
    </select>

</mapper>
