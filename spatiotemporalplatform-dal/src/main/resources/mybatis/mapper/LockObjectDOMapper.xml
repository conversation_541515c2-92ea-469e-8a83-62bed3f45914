<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.LockObjectDOMapper">
    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.LockObjectDO">
        <!--@mbg.generated-->
        <!--@Table distributed_lock-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="lock_name" jdbcType="VARCHAR" property="lockName"/>
        <result column="machine_id" jdbcType="VARCHAR" property="machineId"/>
        <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime"/>
        <result column="is_locked" jdbcType="BOOLEAN" property="isLocked"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="thread_id" jdbcType="VARCHAR" property="threadId"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="isDeleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        lock_name,
        machine_id,
        expire_time,
        is_locked,
        `state`,
        thread_id,
        gmt_create,
        gmt_modified,
        is_deleted
    </sql>

    <update id="updateReentrantLock">
        update distributed_lock
        <set>
            is_locked   = true,
            machine_id   = #{machineId,jdbcType=VARCHAR},
            thread_id   = #{threadId,jdbcType=VARCHAR},
            state       = case when expire_time &lt; NOW() then 1 else state + 1 end,
            expire_time = #{expireTime,jdbcType=TIMESTAMP}
        </set>
        where is_deleted = false
          and lock_name = #{lockName,jdbcType=VARCHAR}
          and (
                expire_time &lt; NOW()
                or is_locked = false
                or (machine_id = #{machineId,jdbcType=VARCHAR}
                and thread_id = #{threadId,jdbcType=VARCHAR})
            )
    </update>

    <update id="updateReleaseLock">
        update distributed_lock
        <set>
            state       = state - 1,
            expire_time = case when state=1 then null else expire_time end,
            is_locked   = case when state=1 then false else true end,
            machine_id   = case when state=1 then null else machine_id end,
            thread_id   = case when state=1 then null else thread_id end,
        </set>
        where is_deleted = false
        and lock_name = #{lockName,jdbcType=VARCHAR}
        and machine_id   = #{machineId,jdbcType=VARCHAR}
        and thread_id   = #{threadId,jdbcType=VARCHAR}
        and expire_time &gt; NOW()
        and is_locked = true
    </update>

    <update id="updateRenewExpirationLock">
        update distributed_lock
        set expire_time = #{expireTime,jdbcType=TIMESTAMP}
        where is_deleted = false
        and is_locked = true
        and lock_name = #{lockName,jdbcType=VARCHAR}
        and machine_id   = #{machineId,jdbcType=VARCHAR}
        and thread_id   = #{threadId,jdbcType=VARCHAR}
        and expire_time &gt; NOW()
    </update>

    <select id="queryAllowObtainLockList" resultType="java.lang.String">
        select lock_name
        from distributed_lock
        where is_deleted = false
          and lock_name in
        <foreach collection="list" item="lockName" open="(" close=")" separator=",">
            #{lockName,jdbcType=VARCHAR}
        </foreach>
        and (
                        is_locked = false
                    or expire_time &lt; NOW()
                )
    </select>
</mapper>