<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.WeatherWarningMapper">

    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.WeatherWarningDO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="airspaceCode" column="airspace_code" jdbcType="VARCHAR"/>
        <result property="airspaceName" column="airspace_name" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="warningTime" column="warning_time" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,airspace_code,airspace_name,
        type,status,content,
        title,warning_time
    </sql>

    <select id="getIntelligenceList" resultType="com.deepinnet.spatiotemporalplatform.dal.dataobject.IntelligenceDO">
        SELECT
            'weather_warning' as source_type,
            ww.id,
            ww.warning_no as biz_no,
            ww.airspace_code,
            ww.airspace_name,
            ww.type,
            ww.status,
            ww.content,
            ww.title,
            ww.warning_time as warning_time
        FROM weather_warning ww
        <where>
            <choose>
                <when test="startTime != null and endTime != null">
                    ww.warning_time &gt;= #{startTime}
                    AND ww.warning_time &lt;= #{endTime}
                </when>
                <otherwise>
                    DATE(to_timestamp(ww.warning_time / 1000)) = CURRENT_DATE
                </otherwise>
            </choose>
        </where>

        UNION ALL

        SELECT
            'control_intelligence' as source_type,
            ci.id,
            ci.control_intelligence_no as biz_no, -- 调整此字段位置
            '' as airspace_code,
            '' as airspace_name,
            'control' as type,
            'active' as status,
            ci.content,
            ci.content as title,
            EXTRACT(EPOCH FROM ci.publish_time) * 1000 as warning_time
        FROM control_intelligence ci
        WHERE ci.status = '0'
        ORDER BY warning_time DESC
    </select>

    <select id="getHistoryIntelligenceList" resultType="com.deepinnet.spatiotemporalplatform.dal.dataobject.IntelligenceDO">
        SELECT 'weather_warning' as source_type,
               ww.id,
               ww.warning_no     as biz_no,
               ww.airspace_code,
               ww.airspace_name,
               ww.type,
               ww.status,
               ww.content,
               ww.title,
               ww.warning_time   as warning_time
        FROM weather_warning ww
        WHERE ww.warning_time &gt;= #{startTime}
          AND ww.warning_time &lt;= #{endTime}

        UNION ALL

        SELECT 'control_intelligence'                     as source_type,
               ci.id,
               ci.control_intelligence_no                 as biz_no,
               ''                                         as airspace_code,
               ''                                         as airspace_name,
               'control'                                  as type,
               ci.status                                   as status,
               ci.content,
               ci.content                                 as title,
               EXTRACT(EPOCH FROM ci.publish_time AT TIME ZONE 'Asia/Shanghai') * 1000 as warning_time
        FROM control_intelligence ci
        WHERE EXTRACT(EPOCH FROM ci.publish_time AT TIME ZONE 'Asia/Shanghai') * 1000 >= #{startTime}
          AND EXTRACT(EPOCH FROM ci.publish_time AT TIME ZONE 'Asia/Shanghai') * 1000 &lt;= #{endTime}
        ORDER BY warning_time DESC
    </select>
</mapper>
