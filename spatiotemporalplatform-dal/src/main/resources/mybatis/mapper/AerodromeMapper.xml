<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.AerodromeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.AerodromeDO">
        <id column="id" property="id" />
        <result column="uav_port_id" property="uavPortId" />
        <result column="name" property="name" />
        <result column="aoi" property="aoi" />
        <result column="position" property="position" />
        <result column="status" property="status" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, uav_port_id, name, aoi, position, status, gmt_created, gmt_modified
    </sql>

</mapper>
