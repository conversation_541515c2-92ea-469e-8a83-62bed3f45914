<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.WeatherGridMapper">

    <resultMap id="ResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.WeatherGridDO">
        <!-- 基础字段 -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="grid_code" property="gridCode" jdbcType="VARCHAR"/>
        <result column="weather" property="weather" jdbcType="VARCHAR"/>
        <result column="coordinate_system" property="coordinateSystem" jdbcType="VARCHAR"/>
        <result column="cloud_base_height" property="cloudBaseHeight" jdbcType="DECIMAL"/>
        <result column="visibility" property="visibility" jdbcType="DECIMAL"/>
        <result column="precipitation_intensity" property="precipitationIntensity" jdbcType="DECIMAL"/>
        <result column="airpressure" property="airpressure" jdbcType="DECIMAL"/>
        <result column="humidity" property="humidity" jdbcType="DECIMAL"/>
        <result column="temperature" property="temperature" jdbcType="DECIMAL"/>
        <result column="md5_hash" property="md5Hash" jdbcType="VARCHAR"/>
        <result column="weather_layers" property="weatherLayers" jdbcType="VARCHAR"/>

        <result column="grid_geometry" property="gridGeometry"
                typeHandler="com.deepinnet.spatiotemporalplatform.dal.type.PGPolygonTypeHandler"/>
        <result column="center_point" property="centerPoint"
                typeHandler="com.deepinnet.spatiotemporalplatform.dal.type.PGPointTypeHandler"/>
    </resultMap>

    <insert id="upsertBatch">
        INSERT INTO weather_grid
        (grid_code, center_point, cloud_base_height, visibility, precipitation_intensity, airpressure, temperature, humidity, weather_layers, md5_hash)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.gridCode},
            #{item.centerPoint,typeHandler=com.deepinnet.spatiotemporalplatform.dal.type.PGPointTypeHandler},
            #{item.cloudBaseHeight},
            #{item.visibility},
            #{item.precipitationIntensity},
            #{item.airpressure},
            #{item.temperature},
            #{item.humidity},
            #{item.weatherLayers},
            #{item.md5Hash})
        </foreach>
        ON CONFLICT (grid_code)
        DO UPDATE SET
            center_point = EXCLUDED.center_point,
            cloud_base_height = EXCLUDED.cloud_base_height,
            visibility = EXCLUDED.visibility,
            precipitation_intensity = EXCLUDED.precipitation_intensity,
            airpressure = EXCLUDED.airpressure,
            temperature = EXCLUDED.temperature,
            humidity = EXCLUDED.humidity,
            weather_layers = EXCLUDED.weather_layers,
            md5_hash = EXCLUDED.md5_hash
        WHERE
            weather_grid.md5_hash != EXCLUDED.md5_hash
    </insert>

    <select id="list" resultMap="ResultMap">
        select grid_code, center_point
        <if test="factor == 'TEMPERATURE'">
            , temperature
        </if>
        <if test="factor == 'PRECIPITATION'">
            , precipitation_intensity
        </if>
        <if test="factor == 'WIND_FORCE'">
            , weather_layers
        </if>
        from weather_grid
        where st_contains(#{polygon, typeHandler=com.deepinnet.spatiotemporalplatform.dal.type.PGPolygonTypeHandler}, center_point)
        <!-- 暂时先限制数量 -->
        limit 5000
    </select>
</mapper>
