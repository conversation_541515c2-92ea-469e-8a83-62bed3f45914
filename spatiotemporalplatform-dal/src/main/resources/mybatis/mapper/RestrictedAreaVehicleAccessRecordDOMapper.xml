<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.RestrictedAreaVehicleAccessRecordDOMapper">
    <!-- 基本结果映射 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.RestrictedAreaVehicleAccessRecordDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="area_code" property="areaCode" jdbcType="VARCHAR"/>
        <result column="plate_num" property="plateNum" jdbcType="VARCHAR"/>
        <result column="inflow_time" property="inflowTime" jdbcType="TIMESTAMP"/>
        <result column="duration" property="duration" jdbcType="BIGINT"/>
        <result column="is_plate_record_filed" property="isPlateRecordFiled" jdbcType="BOOLEAN"/>
        <result column="gmt_created" property="gmtCreated" jdbcType="TIMESTAMP"/>
        <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP"/>
        <result column="is_departed" property="isDeparted" jdbcType="BOOLEAN"/>
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
    </resultMap>


    <select id="listLastOneByPlateNumList" resultMap="BaseResultMap">
        SELECT DISTINCT ON (plate_num) *
        FROM restricted_area_vehicle_access_record
        WHERE plate_num IN
        <foreach collection="plateNumList" item="plateNum" open="(" separator="," close=")">
            #{plateNum}
        </foreach>
        AND area_code = #{areaCode}
        AND inflow_time::date = CURRENT_DATE
        ORDER BY plate_num, inflow_time DESC
    </select>

    <select id="listAreaStat" resultType="com.deepinnet.spatiotemporalplatform.dal.model.RestrictedAreaVehiclePassageRecordStat">
        SELECT
        -- 今日流入量（今天所有数据条数）
        COUNT(*) AS todayInflowVolume,

        -- 已备案车辆数（今天已备案车牌去重数量）
        COUNT(DISTINCT CASE WHEN is_plate_record_filed = true THEN plate_num END) AS recordFiledVehicleNum,

        -- 今日违法车辆（今天未备案且流入超过20分钟）
        COUNT(DISTINCT CASE WHEN is_plate_record_filed = false
        AND ((inflow_time &lt; (NOW() - INTERVAL '20 minutes') AND is_departed = false) OR (is_departed = true and duration > 1200))
        THEN plate_num END) AS todayViolatingVehiclesNum,

        -- 当前违法车辆（今日违法车辆中未驶离的）
        COUNT(DISTINCT CASE WHEN is_plate_record_filed = false
        AND inflow_time &lt; (NOW() - INTERVAL '20 minutes')
        AND is_departed = false
        THEN plate_num END) AS currentViolatingVehiclesNum
        FROM
        restricted_area_vehicle_access_record
        WHERE inflow_time::date = CURRENT_DATE and area_code = #{areaCode}
    </select>

    <select id="listByAreaCode" resultMap="BaseResultMap">
        SELECT *
        FROM restricted_area_vehicle_access_record
        WHERE inflow_time::date = CURRENT_DATE and area_code = #{areaCode}
        <if test="isIllegal != null">
            <choose>
                <!-- 查询违法记录：未备案且超过20分钟 -->
                <when test="isIllegal == true">
                    AND is_plate_record_filed = false
                    AND ((inflow_time &lt; (NOW() - INTERVAL '20 minutes') AND is_departed = false) OR (is_departed = true and duration > 1200))
                </when>
                <!-- 查询非违法记录：已备案或未超时 -->
                <when test="isIllegal == false">
                    AND (
                    is_plate_record_filed = true
                    OR (
                    is_plate_record_filed = false
                    AND ((inflow_time &gt;= (NOW() - INTERVAL '20 minutes') AND is_departed = false) OR (is_departed = true and duration &lt;= 1200))
                    )
                    )
                </when>
                <!-- 默认查询全部记录 -->
            </choose>
        </if>
        ORDER BY inflow_time DESC
    </select>
</mapper>