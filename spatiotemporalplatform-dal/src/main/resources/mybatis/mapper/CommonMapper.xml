<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.CommonMapper">

    <!-- 根据map插入数据 -->
    <insert id="insertByMap" parameterType="java.lang.String">
        INSERT INTO ${tableName}
        <foreach collection="map.entrySet()" index="key" separator="," open="(" close=")">
            ${key}
        </foreach>
        VALUES
        <foreach collection="map.entrySet()" item="val" separator="," open="(" close=")">
            #{val}
        </foreach>
    </insert>

</mapper>