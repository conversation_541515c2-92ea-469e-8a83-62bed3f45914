<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.ParkingLotMapper">
    <resultMap id="BaseResultMap"
               type="com.deepinnet.spatiotemporalplatform.dal.dataobject.ParkingLotDO">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="gmtCreated" column="gmt_created" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="parkingLotCode" column="parking_lot_code" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="address" column="address" jdbcType="VARCHAR"/>
        <result property="longitude" column="longitude" jdbcType="VARCHAR"/>
        <result property="latitude" column="latitude" jdbcType="VARCHAR"/>
        <result property="totalSpaces" column="total_spaces" jdbcType="INTEGER"/>
        <result property="availableSpaces" column="available_spaces" jdbcType="INTEGER"/>
        <result property="openHours" column="open_hours" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        gmt_created,
        gmt_modified,
        parking_lot_code,
        name,
        type,
        address,
        longitude,
        latitude,
        total_spaces,
        available_spaces,
        open_hours,
        status
    </sql>

    <select id="listByGeoAndType" resultMap="BaseResultMap">
        WITH area_buffer AS (SELECT st_buffer(ST_Transform(ST_GeomFromText(#{geoWkt,jdbcType=VARCHAR}, 4326), 3857),
                                              #{distance,jdbcType=INTEGER}) AS geom)
        SELECT s.parking_lot_code,
               s.name,
               s.type,
               s.address,
               s.longitude,
               s.latitude,
               s.total_spaces,
               s.available_spaces,
               s.open_hours,
               s.status
        FROM third_party_parking_lot s
                 INNER JOIN area_buffer r
                            ON st_within(ST_Transform(ST_SetSRID(ST_MakePoint(cast(s.longitude as float), cast(s.latitude as float)), 4326), 3857), r.geom)
        WHERE s.is_deleted = '0'
          and s.type = #{type,jdbcType=VARCHAR}
        ORDER BY s.gmt_created DESC
    </select>
</mapper>
