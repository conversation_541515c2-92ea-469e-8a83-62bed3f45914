<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.UavRealtimeMqRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.UavRealtimeMqRecordDO">
        <id column="id" property="id" />
        <result column="cpuid" property="cpuid" />
        <result column="lon" property="lon" />
        <result column="lat" property="lat" />
        <result column="alt" property="alt" />
        <result column="height" property="height" />
        <result column="relative_height" property="relativeHeight" />
        <result column="gps_yaw" property="gpsYaw" />
        <result column="yaw" property="yaw" />
        <result column="pitch" property="pitch" />
        <result column="roll" property="roll" />
        <result column="speed" property="speed" />
        <result column="timestamp" property="timestamp" />
        <result column="state" property="state" />
        <result column="wp_distance" property="wpDistance" />
        <result column="finish_distance" property="finishDistance" />
        <result column="surplus_circle" property="surplusCircle" />
        <result column="home_distance" property="homeDistance" />
        <result column="flight_duration" property="flightDuration" />
        <result column="leeway" property="leeway" />
        <result column="gps_stars" property="gpsStars" />
        <result column="gps_pdop" property="gpsPdop" />
        <result column="gps_state" property="gpsState" />
        <result column="lock" property="lock" />
        <result column="voltage" property="voltage" />
        <result column="soc" property="soc" />
        <result column="is_key" property="isKey" />
        <result column="user_id" property="userId" />
        <result column="task_id" property="taskId" />
        <result column="plan_id" property="planId" />
        <result column="uav_id" property="uavId" />
        <result column="relation_id" property="relationId" />
        <result column="flight_id" property="flightId" />
        <result column="warning" property="warning" />
        <result column="error" property="error" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, cpuid, lon, lat, alt, height, relative_height, gps_yaw, yaw, pitch, roll, speed, timestamp, state, wp_distance, finish_distance, surplus_circle, home_distance, flight_duration, leeway, gps_stars, gps_pdop, gps_state, lock, voltage, soc, is_key, user_id, task_id, plan_id, uav_id, relation_id, flight_id, warning, error, gmt_created, gmt_modified
    </sql>

    <!-- 查询每个无人机ID的最新记录 -->
    <select id="selectLatestByUavIds" resultMap="BaseResultMap">
        WITH latest_records AS (
            SELECT uav_id, MAX(timestamp) as max_timestamp
            FROM uav_realtime_mq_record
            WHERE uav_id IN
            <foreach item="item" collection="uavIds" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="planIds != null and planIds.size() > 0">
                AND plan_id IN
                <foreach item="item" collection="planIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            GROUP BY uav_id
        )
        SELECT ur.*
        FROM uav_realtime_mq_record ur
        JOIN latest_records lr ON ur.uav_id = lr.uav_id AND ur.timestamp = lr.max_timestamp
        ORDER BY ur.timestamp DESC
    </select>
</mapper>
