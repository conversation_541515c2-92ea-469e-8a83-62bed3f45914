<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.WeatherIntelligenceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.WeatherIntelligenceDO">
        <id column="id" property="id"/>
        <result column="wind_speed" property="windSpeed"/>
        <result column="wind_direction" property="windDirection"/>
        <result column="temperature" property="temperature"/>
        <result column="air_pressure" property="airPressure"/>
        <result column="visibility" property="visibility"/>
        <result column="precipitation" property="precipitation"/>
        <result column="weather_time" property="weatherTime"/>
        <result column="gmt_created" property="gmtCreated"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, wind_speed, wind_direction, temperature, air_pressure, visibility, precipitation,
        weather_time, gmt_created, gmt_modified
    </sql>

</mapper> 