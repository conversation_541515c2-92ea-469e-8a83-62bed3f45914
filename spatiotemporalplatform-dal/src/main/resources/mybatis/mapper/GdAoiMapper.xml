<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.spatiotemporalplatform.dal.mapper.GdAoiMapper">

    <resultMap id="BaseResultMap" type="com.deepinnet.spatiotemporalplatform.dal.dataobject.GdAoiDO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="wkt" column="wkt" jdbcType="OTHER"
                typeHandler="com.deepinnet.spatiotemporalplatform.dal.type.PGMultiPolygonTypeHandler"/>
        <result property="poiId" column="poi_id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="adCode" column="ad_code" jdbcType="VARCHAR"/>
        <result property="center" column="center" jdbcType="VARCHAR"/>
        <result property="area" column="area" jdbcType="VARCHAR"/>
        <result property="aoiId" column="aoi_id" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,wkt,poi_id,
        `name`,ad_code,center,
        area,aoi_id
    </sql>

    <select id="getAoiList"
            resultMap="BaseResultMap">
        SELECT id,
               wkt,
               poi_id,
               name,
               ad_code,
               center,
               area,
               aoi_id
        FROM gd_aoi
        WHERE (
                  ST_Intersects(ST_SetSRID(wkt, 4326),
                                ST_SetSRID(ST_GeomFromGeoJSON(#{region,jdbcType=VARCHAR}), 4326)) OR
                  ST_Contains(ST_SetSRID(wkt, 4326),
                              ST_SetSRID(ST_GeomFromGeoJSON(#{region,jdbcType=VARCHAR}), 4326)) OR
                  ST_Within(ST_SetSRID(wkt, 4326), ST_SetSRID(ST_GeomFromGeoJSON(#{region,jdbcType=VARCHAR}), 4326)) OR
                  ST_Equals(ST_SetSRID(wkt, 4326), ST_SetSRID(ST_GeomFromGeoJSON(#{region,jdbcType=VARCHAR}), 4326))
                  )
    </select>

</mapper>
