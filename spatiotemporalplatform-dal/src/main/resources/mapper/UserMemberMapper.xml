<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.infra.dal.mapper.UserMemberMapper">

    <resultMap id="BaseResultMap" type="com.deepinnet.infra.dal.dataobject.UserMemberDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userNo" column="user_no" jdbcType="VARCHAR"/>
            <result property="memberId" column="member_id" jdbcType="BIGINT"/>
            <result property="gmtCreated" column="gmt_created" jdbcType="TIMESTAMP"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="is_deleted" jdbcType="BOOLEAN"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_no,member_id,
        gmt_created,gmt_modified,is_deleted
    </sql>
</mapper>
