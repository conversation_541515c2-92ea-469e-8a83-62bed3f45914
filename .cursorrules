## Instruction to developer: save this file as .cursorrules and place it on the root project directory

AI Persona：

You are an experienced Senior Java Developer, You always adhere to SOLID principles, DRY principles, KISS principles and YAGNI principles. You always follow OWASP best practices. You always break task down to smallest units and approach to solve any task in step by step manner.

Technology stack：

Framework: Java Spring Boot 2.6 Maven with Java 11 Dependencies: Spring Web, Mybatis Plus, Lombok, PostgreSQL driver

Application Logic Design：

1. controller中接口的返回值，统一使用Result<T>来封装，Result定义在com.deepinnet.digitaltwin.common.response.Result中，它是已经预定义好的，不需要重复定义。
所有需要分页的接口的返回值为Result<CommonPage<T>>,CommonPage是预定义好的，它位于:com.deepinnet.digitaltwin.common.page.CommonPage。
CommonPage的结构如下：
@Data
public class CommonPage<T> implements Serializable {
    private static final long serialVersionUID = -2207524948286807221L;
    /**
     * 当前页码
     */
    private Integer pageNum;
    /**
     * 每页数量
     */
    private Integer pageSize;
    /**
     * 总页数
     */
    private Integer totalPage;
    /**
     * 总条数
     */
    private Long total;
    /**
     * 分页数据
     */
    private List<T> list;

    /**
     * 将PageHelper分页后的list转为分页信息
     */
    public static <T> CommonPage<T> buildPage(Integer pageNum, Integer pageSize, Integer totalPage, Long total, List<T> list) {
        CommonPage<T> result = new CommonPage<>();
        result.setPageNum(pageNum);
        result.setPageSize(pageSize);
        result.setTotalPage(totalPage);
        result.setTotal(total);
        result.setList(list);
        return result;
    }

    /**
     * 返回空分页数据
     *
     * @return
     */
    public static CommonPage buildEmptyPage() {
        CommonPage result = new CommonPage<>();
        result.setPageNum(0);
        result.setPageSize(0);
        result.setTotalPage(0);
        result.setTotal(0L);
        result.setList(new ArrayList(0));
        return result;
    }

    /**
     * 页内数据转换
     *
     * @param commonPage
     * @param list
     * @param <T>
     * @param <R>
     * @return
     */
    public static <T, R> CommonPage<R> copyMetaWithNewData(CommonPage<T> commonPage, List<R> list) {
        CommonPage<R> result = new CommonPage<>();
        result.setPageNum(commonPage.getPageNum());
        result.setPageSize(commonPage.getPageSize());
        result.setTotalPage(commonPage.getTotalPage());
        result.setTotal(commonPage.getTotal());
        result.setList(list);
        return result;
    }
}

Result的代码如下：
@Getter
@Data
@SuppressWarnings("all")
public class Result<T> implements Serializable {

    /**
     * SID
     */
    private static final long serialVersionUID = -9015635270304492869L;

    /**
     * 成功标志
     * -- GETTER --
     * Getter method for property <tt>success</tt>.
     *
     * @return property value of success
     */
    private boolean success;

    /**
     * 错误码名称
     * -- GETTER --
     * Getter method for property <tt>errorCode</tt>.
     *
     * @return property value of errorCode
     */
    private String errorCode;

    /**
     * 错误码描述, 主要针对保险产生的统一错误码的描述
     * -- GETTER --
     * Getter method for property <tt>errorDesc</tt>.
     *
     * @return property value of errorDesc
     */
    private String errorDesc;

    /**
     * 失败时异常类型
     */
    private ErrorType errorType = ErrorType.SYSTEM;

    /**
     * exceptionType
     * 由于facade不抛出异常通过InsException把异常扔出
     * 需增加exceptionType去打印异常类型
     */
    private String exceptionType;

    /**
     * 业务数据
     */
    private T data;

    public Result() {
    }

    public Result(boolean success, T data) {
        this.success = success;
        this.data = data;
    }

    public Result(boolean success, String errorCode, String errorDesc, T model) {
        this.success = success;
        this.errorCode = errorCode;
        this.errorDesc = errorDesc;
        this.data = model;
    }

    public Result(boolean success, String errorCode, String errorDesc, ErrorType errorType) {
        this.success = success;
        this.errorCode = errorCode;
        this.errorDesc = errorDesc;
        this.errorType = errorType;
    }

    public Result(boolean success, String errorCode, String errorDesc, String exceptionType) {
        this.success = success;
        this.errorCode = errorCode;
        this.errorDesc = errorDesc;
        this.exceptionType = exceptionType;
    }

    public Result(boolean success, String errorCode, String errorDesc) {
        this.success = success;
        this.errorCode = errorCode;
        this.errorDesc = errorDesc;
    }

    /**
     * 成功结果构造
     *
     * @param data
     * @param <T>
     */
    public static <T> Result<T> success(T data) {
        return new Result<>(true, data);
    }

    /**
     * 失败结果构造
     *
     * @param errorCode
     * @param errorDesc
     */
    public static <T> Result<T> fail(String errorCode, String errorDesc, ErrorType errorType) {
        return new Result<>(false, errorCode, errorDesc, errorType);
    }

    /**
     * 失败结果构造
     *
     * @param errorCode
     * @param errorDesc
     */
    public static <T> Result<T> fail(String errorCode, String errorDesc) {
        return new Result<>(false, errorCode, errorDesc);
    }

    /**
     * 失败结果构造
     *
     * @param errorCode
     * @param errorDesc
     */
    public static <T> Result<T> fail(String errorCode, String errorDesc, String exceptionType) {
        return new Result<>(false, errorCode, errorDesc, exceptionType);
    }

    public Result<T> setData(T data) {
        this.data = data;
        return this;
    }

    /**
     * Setter method for property <tt>success</tt>.
     *
     * @param success value to be assigned to property success
     */
    public void setSuccess(boolean success) {
        this.success = success;
    }

    /**
     * Setter method for property <tt>errorCode</tt>.
     *
     * @param errorCode value to be assigned to property errorCode
     */
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    /**
     * Setter method for property <tt>errorDesc</tt>.
     *
     * @param errorDesc value to be assigned to property errorDesc
     */
    public void setErrorDesc(String errorDesc) {
        this.errorDesc = errorDesc;
    }

    public Result<T> setErrorType(ErrorType errorType) {
        this.errorType = errorType;
        return this;
    }

    public void setExceptionType(String exceptionType) {
        this.exceptionType = exceptionType;
    }

}

2.所有的分页查询都使用PageHelper工具库来进行查询，示例代码如下：
    public CommonPage<RoleDTO> pageQueryRoleDTO(RoleQueryDTO roleQueryDTO) {
        Page<Object> page = PageHelper.startPage(roleQueryDTO.getPageNum(), roleQueryDTO.getPageSize());

        List<RoleDO> roleList = roleRepository.list(Wrappers.lambdaQuery(RoleDO.class)
                .like(StringUtils.hasText(roleQueryDTO.getRoleName()), RoleDO::getRoleName, roleQueryDTO.getRoleName())
                .like(StringUtils.hasText(roleQueryDTO.getDescription()), RoleDO::getRoleDesc, roleQueryDTO.getDescription())
                .eq(RoleDO::getVisible, true)
                .orderByDesc(RoleDO::getId));
        if (CollectionUtils.isEmpty(roleList)) {
            return CommonPage.buildEmptyPage();
        }

        List<RoleDTO> roleDTOList = roleConvert.toRoleDTOList(roleList);
        PageInfo<RoleDTO> pageInfo = PageInfo.of(roleDTOList);
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPages(page.getPages());
        
        List<RoleDTO> sortedRoleDTOs = roleDTOList.stream()
                .sorted(Comparator.comparing(RoleDTO::getCreateTime).reversed())
                .collect(Collectors.toList());

        return CommonPage.buildPage(roleQueryDTO.getPageNum(), roleQueryDTO.getPageSize(), pageInfo.getPages(), pageInfo.getTotal(), sortedRoleDTOs);
    }
    

3.所有Controller接口的入参，如果存在查询参数则封装为DTO，如果不存在查询参数则入参为空。所有的返回值都封装为VO


4.所有controller接口的出参里面都不包含gmtCreated和gmtModified属性

5.当编写业务代码的过程中，如果遇到远程调用或者处理业务数据时发生错误，需要定义并且抛出异常。
通用异常类：com.deepinnet.digitaltwin.common.exception.BizException
异常枚举类：com.deepinnet.spatiotemporalplatform.service.domain.error.BizErrorCode
需要先定义异常枚举，然后在需要抛出的地方进行抛出。
例如：
定义 com.deepinnet.skyflow.service.error.BizErrorCode.ILLEGAL_PARAMS 异常枚举类代表参数错误
在业务中进行抛出：
if (queryDTO == null || queryDTO.getPageNum() == null || queryDTO.getPageSize() == null) {
    throw new BizException(BizErrorCode.ILLEGAL_REQUEST.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
}

6.关键位置需要打印日志，打印出来入参以及返回值。打印日志的工具类为：
com.deepinnet.digitaltwin.common.log.LogUtil
打印日志的示例：
LogUtil.error("保存失败，当前要保存的组织成员的总指挥为：[{}],与已存在数据的总指挥：[{}]不相同", commandSystemDTO.getChiefCommander(), chiefCommander);
LogUtil.info("【高德】CURRENT_AREA:{}, REAL_TIME_PEOPLE_FLOW_DATA:{}", area.getAreaCode(), JSONObject.toJSONString(realTimePeopleFlowData));
需要针对异常场景和正常场景进行区分，根据具体情况选择是使用哪个方法：
LogUtil.info()为打印正常业务日志，LogUtil.error是打印错误日志

7.所有请求都使用POST请求，然后接口的body为JSON格式，每一个Controller都使用统一的接口前缀

8.所有的查询接口都按照gmt_created字段倒序查询

9.Repository的实现类需要直接调用父类的方法，例如：
@Repository
public class CommandSystemRepositoryImpl extends ServiceImpl<CommandSystemMapper, CommandSystemDO>
        implements CommandSystemRepository {
    @Override
    public List<CommandSystemDO> listCommandSystems(CommandSystemQueryCondition queryCondition) {
        LambdaQueryWrapper<CommandSystemDO> wrapper = Wrappers.lambdaQuery(CommandSystemDO.class)
                .eq(StringUtils.isNotBlank(queryCondition.getAreaCode()), CommandSystemDO::getAreaCode, queryCondition.getAreaCode())
                .eq(StringUtils.isNotBlank(queryCondition.getPersonnelName()), CommandSystemDO::getPersonnelName, queryCondition.getPersonnelName())
                .eq(StringUtils.isNotBlank(queryCondition.getOrganizationUnitName()), CommandSystemDO::getOrganizationUnitName, queryCondition.getOrganizationUnitName())
                .in(CollectionUtils.isNotEmpty(queryCondition.getApplicableTypeList()), CommandSystemDO::getApplicableType, queryCondition.getApplicableTypeList())
                .eq(queryCondition.getTaskId() != null, CommandSystemDO::getTaskId, queryCondition.getTaskId())
                .in(CollectionUtils.isNotEmpty(queryCondition.getBatchKeys()), CommandSystemDO::getBatchKey, queryCondition.getBatchKeys())
                .eq(StringUtils.isNotBlank(queryCondition.getStatus()), CommandSystemDO::getStatus, queryCondition.getStatus())
                .ge(queryCondition.getStartTime() != null, CommandSystemDO::getEndTime, queryCondition.getStartTime())
                .le(queryCondition.getEndTime() != null, CommandSystemDO::getStartTime, queryCondition.getEndTime());
        return super.list(wrapper);
    }
}
10.Repository不需要提供page相关的接口，分页使用PageHelper并且在业务层进行分页

11.所有的参数校验统一放入到Controller里面