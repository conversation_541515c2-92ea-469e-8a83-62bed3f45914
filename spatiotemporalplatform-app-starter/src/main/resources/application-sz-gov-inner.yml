
spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: **********************************************
          username: postgres
          password: sdzl123
          driver-class-name: org.postgresql.Driver

mybatis-plus:
  mapper-locations: classpath*:mybatis/mapper/*Mapper.xml


application:
  deployment:
    env: sz-gov-inner

network:
  edge:
    url: http://***********:9879/services/gaodeDataBridge?wsdl

digest:
  sign:
    api:
      key: 934c26e878d8eeb0253705d153c97334
      secret: 39b4a34f0b17e8ef0b4cdfa3a6b273e9
      key-temp: d05d7417d6f70eb5f6cbf8a72a824844
      secret-temp: b6c8e84fc756d898ccf3897c5a1625a0

token:
  sign:
    api:
      - key: 517d6aa3f7daec6a8efbd6d22b501f20
        secret: cd7bdd471d4192dbeeb833617de53b99
        appName: dz-amap-runproject
        adcode: 440300  # 深圳
      - key: d05d7417d6f70eb5f6cbf8a72a824844
        secret: 6eaa4cc617ebdcbb1546f989b3dd363b
        appName: dz-amap-sdzl
        adcode: 330800  # 衢州

request:
  stub:
    persistence: true

ftp:
  host: ***********
  port: 12121
  username: deepinnet
  password: wqpuycwcKwtEiSUU

city:
  adcode: 330800