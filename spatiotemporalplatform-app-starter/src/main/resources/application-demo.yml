
spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: jdbc:postgresql://************:5432/digitaltwin?currentSchema=public&reWriteBatchedInserts=true&userTimezone=Asia/Shanghai
          username: postgres
          password: sdzl123
          driver-class-name: org.postgresql.Driver

        car-pass-statics:
          url: jdbc:postgresql://************:5432/digitaltwin?currentSchema=public&reWriteBatchedInserts=true&userTimezone=Asia/Shanghai
          username: postgres
          password: sdzl123
          driver-class-name: org.postgresql.Driver

        qzjj-camera-view:
          url: ************************************
          username: root
          password: sdzl123
          driver-class-name: com.mysql.cj.jdbc.Driver

mybatis-plus:
  mapper-locations: classpath*:mybatis/mapper/*Mapper.xml

logging:
  level:
    com:
      baomidou: debug
    java:
      sql:
        Connection: debug
        Statement: debug
        PreparedStatement: debug

application:
  deployment:
    env: demo

data:
  service:
    url: http://************:60206

network:
  edge:
    url: http://************:60206/services/gaodeDataBridge?wsdl

digest:
  sign:
    api:
      key: 934c26e878d8eeb0253705d153c97334
      secret: 39b4a34f0b17e8ef0b4cdfa3a6b273e9
      key-temp: d05d7417d6f70eb5f6cbf8a72a824844
      secret-temp: b6c8e84fc756d898ccf3897c5a1625a0

token:
  sign:
    api:
      - key: 517d6aa3f7daec6a8efbd6d22b501f20
        secret: cd7bdd471d4192dbeeb833617de53b99
        appName: dz-amap-runproject
        adcode: 440300  # 深圳
      - key: d05d7417d6f70eb5f6cbf8a72a824844
        secret: 6eaa4cc617ebdcbb1546f989b3dd363b
        appName: dz-amap-sdzl
        adcode: 330800  # 衢州

request:
  stub:
    persistence: false


city:
  adcode: 330800

out-api:
  team-id: 99fae838557b4c2e952468932b1db4a2
  yuan-fei:
    username: 13904014313
    password: zhjg2023
    serverUrl: https://jt-gk.kitegogo.cn

weather:
  node:
    url: http://************:9002/decode

mqtt:
  protocol: ws
  host: mqtest.kitegogo.cn
  port: 2416
  username: admin
  password: public

file:
  attachment:
    dir: /home/<USER>/web-storage/html/web-storage
    url: http://live-storage.deepinnet.com/