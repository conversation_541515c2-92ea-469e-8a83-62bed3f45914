spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: ************************************************************************************************************************************
          username: <PERSON><PERSON>(sVMyCam15Oox9BCvuJvd6VqXcXdCEzzb)
          password: E<PERSON>(miyi4KUBRWgcx4NhYmMqOq3lFb20I9o+)
          driver-class-name: org.postgresql.Driver
          hikari:
            leak-detection-threshold: 20000  # 超过 20 秒未关闭连接，打印日志
            max-pool-size: 25
            min-idle: 5
            idle-timeout: 30000
            connection-timeout: 30000
        car-pass-statics:
          url: ************************************************************************************************************************************
          username: ENC(sVMyCam15Oox9BCvuJvd6VqXcXdCEzzb)
          password: <PERSON><PERSON>(miyi4KUBRWgcx4NhYmMqOq3lFb20I9o+)
          driver-class-name: org.postgresql.Driver
          hikari:
            leak-detection-threshold: 20000  # 超过 20 秒未关闭连接，打印日志
            max-pool-size: 25
            min-idle: 5
            idle-timeout: 30000
            connection-timeout: 30000


        qzjj-camera-view:
          url: ************************************
          username: root
          password: sdzl123
          driver-class-name: com.mysql.cj.jdbc.Driver
          hikari:
            leak-detection-threshold: 20000  # 超过 20 秒未关闭连接，打印日志
            max-pool-size: 25
            min-idle: 5
            idle-timeout: 30000
            connection-timeout: 30000

        algorithm:
          url: ************************************************************************************************************************************
          username: postgres
          password: Shendu188
          driver-class-name: org.postgresql.Driver
          hikari:
            leak-detection-threshold: 20000  # 超过 20 秒未关闭连接，打印日志
            max-pool-size: 25
            min-idle: 5
            idle-timeout: 30000
            connection-timeout: 30000

  # Redis配置
  redis:
    # Redis数据库索引（默认为0）
    database: 6
    # Redis服务器地址
    host: *************
    # Redis服务器连接端口
    port: 6379
    # Redis服务器连接密码（默认为空）
    password: ENC(ao+A54rgdnsZ/wIydpBlnxYqM34u5B4O)
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池最大连接数
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0

mybatis-plus:
  mapper-locations: classpath*:mybatis/mapper/*Mapper.xml

#logging:
#  level:
#    com:
#      baomidou: debug
#    org:
#      springframework:
#        web: debug
#    java:
#      sql:
#        Connection: debug
#        Statement: debug
#        PreparedStatement: debug

application:
  deployment:
    env: local

data:
  service:
    url: http://*************:60149
    #url: http://localhost:8080

network:
  edge:
    url: http://*************:9879/services/gaodeDataBridge?wsdl

digest:
  sign:
    api:
      key: 934c26e878d8eeb0253705d153c97334
      secret: 39b4a34f0b17e8ef0b4cdfa3a6b273e9
      key-temp: d05d7417d6f70eb5f6cbf8a72a824844
      secret-temp: b6c8e84fc756d898ccf3897c5a1625a0

token:
  sign:
    api:
      - key: 517d6aa3f7daec6a8efbd6d22b501f20
        secret: cd7bdd471d4192dbeeb833617de53b99
        appName: dz-amap-runproject
        adcode: 440300  # 深圳
      - key: d05d7417d6f70eb5f6cbf8a72a824844
        secret: 6eaa4cc617ebdcbb1546f989b3dd363b
        appName: dz-amap-sdzl
        adcode: 330800  # 衢州

request:
  stub:
    persistence: false


city:
  adcode: 330800

out-api:
  yuan-fei:
    team-id: 99fae838557b4c2e952468932b1db4a2
    #    管控
    control:
      username: 13904014313
      password: zhjg2023
      serverUrl: https://lg-gk.kitegogo.cn
    #      serverUrl: http://*************:60207

    #    运营端
    op:
      username: 17647654305
      password: 123456aA!
      serverUrl: https://lg-yy.kitegogo.cn

inner-api:
  algorithm:
    #    serverUrl: http://localhost:8080
    serverUrl: http://***************:32073
    monitor-video:
      url: http://***************:32265

weather:
  node:
    url: http://*************:9002/decode

mqtt:
  protocol: ws
  host: mqtest.kitegogo.cn
  port: 2416
  username: admin
  password: public

file:
  attachment:
    dir: /usr/local/nginx/html/picture
    url: http://*************:8052/

# 低空运营平台地址
sfoc:
  service:
    url: http://*************:60207/

tenant-id: sz_unifly

# MinIO配置
minio:
  #  endpoint: http://***************:32179
  endpoint: https://minio.deepinnet.com:9000
  accessKey: cosskJEYUP9ffuNO3ruv
  secretKey: QwKVrdyLGu89xIfRimlLnf0WDkF3JTBdoi1oaJDZ
  bucketName: deepinnet

gaode:
  enterprise:
    map:
      ak: ec85d3648154874552835438ac6a02b2
      server-url: http://*************:35001

chrome:
  driver:
    path: /usr/local/bin/chromedriver

#    routine: /Users/<USER>/IdeaProjects/spatiotemporalplatform/spatiotemporalplatform-app-starter/src/main/resources/temp/flight_event_report_routine_template.docx
#    emergency: /Users/<USER>/IdeaProjects/spatiotemporalplatform/spatiotemporalplatform-app-starter/src/main/resources/temp/flight_event_report_emergency_template.docx

word:
  url:
    front_screenshot: http://*************:8056/screenshot?planId=%s&step=%s&%s=%s
  flight:
    output: /var/word/flight/output
    image: /var/word/flight/image
    routine:
      template: classpath:word/flight_event_report_routine_template.docx
    emergency:
      template: classpath:word/flight_event_report_emergency_template.docx

mock:
  # 算法
  sf: true