spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: **************************************************************************
          username: postgres
          password: sdzl123
          driver-class-name: org.postgresql.Driver

        car-pass-statics:
          url: **************************************************************************
          username: postgres
          password: sdzl123
          driver-class-name: org.postgresql.Driver

        qzjj-camera-view:
          url: ***********************************
          username: qsd
          password: qsd_node_view!@#
          driver-class-name: com.mysql.cj.jdbc.Driver

  session:
    store-type: none


mybatis-plus:
  mapper-locations: classpath*:mybatis/mapper/*Mapper.xml


application:
  deployment:
    env: qz-gov-inner-network

network:
  edge:
    url: http://41.236.1.204:13201/vss/3.0/dzxy/service?wsdl

digest:
  sign:
    api:
      key: 934c26e878d8eeb0253705d153c97334
      secret: 39b4a34f0b17e8ef0b4cdfa3a6b273e9
      key-temp: d05d7417d6f70eb5f6cbf8a72a824844
      secret-temp: b6c8e84fc756d898ccf3897c5a1625a0

token:
  sign:
    api:
      - key: d05d7417d6f70eb5f6cbf8a72a824844
        secret: 6eaa4cc617ebdcbb1546f989b3dd363b
        appName: dz-amap-sdzl
        adcode: 330800  # 衢州

  # Redis配置
  redis:
    # Redis数据库索引（默认为0）
    database: 6
    # Redis服务器地址
    host: ************
    # Redis服务器连接端口
    port: 6379
    # Redis服务器连接密码（默认为空）
    password: shendu188
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池最大连接数
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0

city:
  adcode: 330800

out-api:
  haikang:
    appKey: 26656738
    appSecret: hpWNOLzE6WuGsmHVbfwL
    ip: **************
  dahua:
    server:
      ip: ***********
      port: 8320
    username: hzsdzl
    password: hzsdzl@123
    app-id: 1830867666382999553

sfoc:
  service:
    url: http://127.0.0.1:60207/