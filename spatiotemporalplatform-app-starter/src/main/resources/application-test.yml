spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: ***************************************************************************
          #url: *************************************************************************
          username: <PERSON><PERSON>(sVMyCam15Oox9BCvuJvd6VqXcXdCEzzb)
          password: E<PERSON>(miyi4KUBRWgcx4NhYmMqOq3lFb20I9o+)
          driver-class-name: org.postgresql.Driver

  session:
    store-type: none


mybatis-plus:
  mapper-locations: classpath*:mybatis/mapper/*Mapper.xml


application:
  deployment:
    env: test

network:
  edge:
    url: http://localhost:60106/services/gaodeDataBridge?wsdl

data:
  service:
    url: http://***********:60109

digest:
  sign:
    api:
      key: 934c26e878d8eeb0253705d153c97334
      secret: 39b4a34f0b17e8ef0b4cdfa3a6b273e9
      key-temp: d05d7417d6f70eb5f6cbf8a72a824844
      secret-temp: b6c8e84fc756d898ccf3897c5a1625a0

token:
  sign:
    api:
      - key: 517d6aa3f7daec6a8efbd6d22b501f20
        secret: cd7bdd471d4192dbeeb833617de53b99
        appName: dz-amap-runproject
        adcode: 440300  # 深圳
      - key: d05d7417d6f70eb5f6cbf8a72a824844
        secret: 6eaa4cc617ebdcbb1546f989b3dd363b
        appName: dz-amap-sdzl
        adcode: 330800  # 衢州

request:
  stub:
    persistence: true

city:
  adcode: 330800

out-api:
  yuan-fei:
    #    管控
    control:
      username: 13904014313
      password: zhjg2023
      serverUrl: https://jt-gk.kitegogo.cn
    #      serverUrl: http://*************:60207

    #    运营端
    op:
      username: 17647654305
      password: 123456aA!
      serverUrl: https://lg-yy.kitegogo.cn

weather:
  node:
    url: http://***********:9002/decode

mqtt:
  protocol: ws
  host: mqtest.kitegogo.cn
  port: 2416
  username: admin
  password: public

file:
  attachment:
    dir: /usr/local/nginx/html/picture
    url: http://*************:8052/
# MinIO配置
minio:
  #  endpoint: http://***************:32179
  endpoint: https://minio.deepinnet.com:9000
  accessKey: cosskJEYUP9ffuNO3ruv
  secretKey: QwKVrdyLGu89xIfRimlLnf0WDkF3JTBdoi1oaJDZ
  bucketName: deepinnet

# 低空运营平台地址
sfoc:
  service:
    url: http://*************:60207/
chrome:
  driver:
    path: /usr/local/bin/chromedriver
word:
  url:
    front_screenshot: http://*************:8053/screenshot?planId=%s&step=%s&%s=%s
  flight:
    output: /var/word/flight/output
    image: /var/word/flight/image
    routine:
      template: classpath:word/flight_event_report_routine_template.docx
    emergency:
      template: classpath:word/flight_event_report_emergency_template.docx
inner-api:
  third-party:
    algorithm:
      serverUrl: http://***************:32354
  algorithm:
    #    serverUrl: http://localhost:8080
    serverUrl: http://***************:32192
    monitor-video:
      url: http://***************:32265