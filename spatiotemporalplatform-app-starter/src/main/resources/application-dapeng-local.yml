
spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: ***************************************************************************
          username: <PERSON><PERSON>(sVMyCam15Oox9BCvuJvd6VqXcXdCEzzb)
          password: <PERSON><PERSON>(miyi4KUBRWgcx4NhYmMqOq3lFb20I9o+)
          driver-class-name: org.postgresql.Driver

mybatis-plus:
  mapper-locations: classpath*:mybatis/mapper/*Mapper.xml


application:
  deployment:
    env: local

network:
  edge:
    url: http://localhost:60106/services/gaodeDataBridge?wsdl

digest:
  sign:
    api:
      key: 517d6aa3f7daec6a8efbd6d22b501f20
      secret: 1d9da8b945fc813912b3182e5ea822a8
      key-temp: 517d6aa3f7daec6a8efbd6d22b501f20
      secret-temp: 1d9da8b945fc813912b3182e5ea822a8

token:
  sign:
    api:
      - key: 517d6aa3f7daec6a8efbd6d22b501f20
        secret: cd7bdd471d4192dbeeb833617de53b99
        appName: dz-amap-runproject
        adcode: 440300  # 深圳
      - key: d05d7417d6f70eb5f6cbf8a72a824844
        secret: 6eaa4cc617ebdcbb1546f989b3dd363b
        appName: dz-amap-sdzl
        adcode: 330800  # 衢州

ftp:
  host: *************
  port: 21
  username: deepinnet
  password: deepinnet


city:
  adcode: 440300
