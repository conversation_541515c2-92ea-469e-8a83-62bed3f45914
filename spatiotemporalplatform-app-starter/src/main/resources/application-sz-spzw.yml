
spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: *********************************************
          username: postgres
          password: sdzl123
          driver-class-name: org.postgresql.Driver
  # Redis配置
  redis:
    # Redis数据库索引（默认为0）
    database: 8
    # Redis服务器地址
    host: **********
    # Redis服务器连接端口
    port: 6379
    # Redis服务器连接密码（默认为空）
    password: shendu188
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池最大连接数
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0

mybatis-plus:
  mapper-locations: classpath*:mybatis/mapper/*Mapper.xml

digest:
  sign:
    api:
      key: 934c26e878d8eeb0253705d153c97334
      secret: 39b4a34f0b17e8ef0b4cdfa3a6b273e9
      key-temp: d05d7417d6f70eb5f6cbf8a72a824844
      secret-temp: b6c8e84fc756d898ccf3897c5a1625a0

data:
  service:
    url: http://*************

file:
  attachment:
    dir: /home/<USER>/web-storage/html/web-storage
    url: http://**********:8066/

weather:
  node:
    url: http://**********:9002/decode

application:
  deployment:
    env: sz-spzw

#视频专网FTP
ftp:
  host: **********
  port: 21
  username: deepinnet
  password: wqpuycwcKwtEiSUU

city:
  adcode: 440300


# 低空运营平台地址
sfoc:
  service:
    url: http://**********:60108/

tenant-id: sz_ft

# MinIO配置
minio:
  #  endpoint: http://***************:32179
  endpoint: http://**********:9000
  accessKey: cosskJEYUP9ffuNO3ruv
  secretKey: QwKVrdyLGu89xIfRimlLnf0WDkF3JTBdoi1oaJDZ
  bucketName: deepinnet

inner-api:
  algorithm:
    serverUrl: http://**********:32177
    monitor-video:
      url: http://**********:32029

gaode:
  enterprise:
    map:
      ak: ec85d3648154874552835438ac6a02b2
      server-url: http://**********:35001

chrome:
  driver:
    path: /usr/local/bin/chromedriver

#    routine: /Users/<USER>/IdeaProjects/spatiotemporalplatform/spatiotemporalplatform-app-starter/src/main/resources/temp/flight_event_report_routine_template.docx
#    emergency: /Users/<USER>/IdeaProjects/spatiotemporalplatform/spatiotemporalplatform-app-starter/src/main/resources/temp/flight_event_report_emergency_template.docx

word:
  url:
    front_screenshot: http://**********:8053/screenshot?planId=%s&step=%s&%s=%s
  flight:
    output: /var/word/flight/output
    image: /var/word/flight/image
    routine:
      template: classpath:word/flight_event_report_routine_template.docx
    emergency:
      template: classpath:word/flight_event_report_emergency_template.docx

mock:
  # 算法
  sf: true