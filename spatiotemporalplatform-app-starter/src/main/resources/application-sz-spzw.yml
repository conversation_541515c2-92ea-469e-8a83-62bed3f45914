
spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: *********************************************
          username: postgres
          password: sdzl123
          driver-class-name: org.postgresql.Driver

mybatis-plus:
  mapper-locations: classpath*:mybatis/mapper/*Mapper.xml

data:
  service:
    url: http://*************

file:
  attachment:
    dir: /home/<USER>/web-storage/html/web-storage
    url: http://**********:8066/

weather:
  node:
    url: http://**********:9002/decode

application:
  deployment:
    env: sz-spzw

#中控的视频专网FTP
ftp:
  host: ***********
  port: 9029
  username: jtzlftp
  password: Cd7FQH0y

city:
  adcode: 440300


# 低空运营平台地址
sfoc:
  service:
    url: http://**********:60108/

tenant-id: sz_ft

# MinIO配置
minio:
  #  endpoint: http://***************:32179
  endpoint: http://**********:9000
  accessKey: cosskJEYUP9ffuNO3ruv
  secretKey: QwKVrdyLGu89xIfRimlLnf0WDkF3JTBdoi1oaJDZ
  bucketName: deepinnet

inner-api:
  algorithm:
    serverUrl: http://**********:32177
    monitor-video:
      url: http://**********:32029

gaode:
  enterprise:
    map:
      ak: ec85d3648154874552835438ac6a02b2
      server-url: http://**********:35001

chrome:
  driver:
    path: /usr/local/bin/chromedriver

#    routine: /Users/<USER>/IdeaProjects/spatiotemporalplatform/spatiotemporalplatform-app-starter/src/main/resources/temp/flight_event_report_routine_template.docx
#    emergency: /Users/<USER>/IdeaProjects/spatiotemporalplatform/spatiotemporalplatform-app-starter/src/main/resources/temp/flight_event_report_emergency_template.docx

word:
  url:
    front_screenshot: http://**********:8053/screenshot?planId=%s&step=%s&%s=%s
  flight:
    output: /var/word/flight/output
    image: /var/word/flight/image
    routine:
      template: classpath:word/flight_event_report_routine_template.docx
    emergency:
      template: classpath:word/flight_event_report_emergency_template.docx

mock:
  # 算法
  sf: true