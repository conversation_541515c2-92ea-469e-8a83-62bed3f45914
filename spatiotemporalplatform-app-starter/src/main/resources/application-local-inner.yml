
spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: ***************************************************************************
          username: <PERSON><PERSON>(sVMyCam15Oox9BCvuJvd6VqXcXdCEzzb)
          password: E<PERSON>(miyi4KUBRWgcx4NhYmMqOq3lFb20I9o+)
          driver-class-name: org.postgresql.Driver
          hikari:
            # 根据实际情况设置合适的最大连接数
            max-pool-size: 20
            # 保持一部分空闲连接供突发流量使用
            min-idle: 5
            # 获取连接的超时时间（单位：毫秒）
            connection-timeout: 30000
            # 空闲连接在池中保留的时间（单位：毫秒）
            idle-timeout: 600000
            # 连接泄露检测（单位：毫秒），帮助发现未正常关闭的连接
            leak-detection-threshold: 15000
        qzjj-camera-view:
          url: ************************************
          username: root
          password: sdzl123
          driver-class-name: com.mysql.cj.jdbc.Driver

  # Redis配置
  redis:
    # Redis数据库索引（默认为0）
    database: 6
    # Redis服务器地址
    host: *************
    # Redis服务器连接端口
    port: 6379
    # Redis服务器连接密码（默认为空）
    password: shendu188
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池最大连接数
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0

mybatis-plus:
  mapper-locations: classpath*:mybatis/mapper/*Mapper.xml


application:
  deployment:
    env: local-inner

network:
  edge:
    url: http://*************:9879/services/gaodeDataBridge?wsdl

digest:
  sign:
    api:
      key: 934c26e878d8eeb0253705d153c97334
      secret: 39b4a34f0b17e8ef0b4cdfa3a6b273e9
      key-temp: d05d7417d6f70eb5f6cbf8a72a824844
      secret-temp: b6c8e84fc756d898ccf3897c5a1625a0

weather:
  node:
    url: http://*************:9002/decode

token:
  sign:
    api:
      - key: 517d6aa3f7daec6a8efbd6d22b501f20
        secret: cd7bdd471d4192dbeeb833617de53b99
        appName: dz-amap-runproject
        adcode: 440300  # 深圳
      - key: d05d7417d6f70eb5f6cbf8a72a824844
        secret: 6eaa4cc617ebdcbb1546f989b3dd363b
        appName: dz-amap-sdzl
        adcode: 330800  # 衢州

request:
  stub:
    persistence: true

city:
  adcode: 330800

file:
  attachment:
    dir: /usr/local/nginx/html/picture
    url: http://*************:8052/

# 低空运营平台地址
sfoc:
  service:
    url: http://*************:60316/

out-api:
  yuan-fei:
    team-id: 99fae838557b4c2e952468932b1db4a2

    #    管控
    control:
      username: 13904014313
      password: zhjg2023
      serverUrl: https://lg-gk.kitegogo.cn
    #      serverUrl: http://*************:60207

    #    运营端
    op:
      username: 17647654305
      password: 123456aA!
      serverUrl: https://lg-yy.kitegogo.cn

inner-api:
  algorithm:
    #    serverUrl: http://localhost:8080
    serverUrl: http://***************:32177
    monitor-video:
      url: http://***************:32029

mqtt:
  protocol: ws
  host: mqtest.kitegogo.cn
  port: 2416
  username: admin
  password: public

tenant-id: sz_unifly

# MinIO配置
minio:
  #  endpoint: http://***************:32179
  endpoint: https://minio.deepinnet.com:9000
  accessKey: cosskJEYUP9ffuNO3ruv
  secretKey: QwKVrdyLGu89xIfRimlLnf0WDkF3JTBdoi1oaJDZ
  bucketName: deepinnet

gaode:
  enterprise:
    map:
      ak: ec85d3648154874552835438ac6a02b2
      server-url: http://*************:35001

mock:
  # 算法
  sf: true

chrome:
  driver:
    path: /usr/local/bin/chromedriver

word:
  url:
    front_screenshot: http://*************:8053/screenshot?planId=%s&step=%s&%s=%s
  flight:
    output: /var/word/flight/output
    image: /var/word/flight/image
    routine:
      template: classpath:word/flight_event_report_routine_template.docx
    emergency:
      template: classpath:word/flight_event_report_emergency_template.docx
