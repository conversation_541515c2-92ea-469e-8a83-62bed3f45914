server:
  port: 60106

feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 5000

mybatis-plus:
  mapper-locations: classpath*:mybatis/mapper/*Mapper.xml
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#  sql:
#    print: true
  global-config:
    db-config:
      logic-delete-field: isDeleted
      logic-delete-value: true
      logic-not-delete-value: false

jasypt:
  encryptor:
    # 自定义加密盐值(密钥)
    password: shendu188
    # 加密算法设置
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator

# oss配置
oss:
  endpoint: oss-cn-hangzhou.aliyuncs.com
  accessSecret: ENC(ys+mbFnF71afPL22YUWw0P0B/RSRMh8vgOswK7KhFIq5tq3/SAZQQg==)
  accessKey: ENC(NaC4iCnLX+bUXi5ifbwjJQXenTTsKE5xpt7QXQs0nMPxzCfgxzQfVQ==)
  # 文件过期时间 3600 * 1000 默认1个小时 (毫秒)
  expireMilliSecond: 3600000

spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  dinger:
    project-id: ${spring.application.name}
    dingers:
      # 使用钉钉机器人, 机器人配置信息
      dingtalk:
        tokenId: e659d2df0edb37e6be87df2430d526fd14046bc25f4080084f0791ef2b93ddd4
        secret: SEC644f154a9768e8bd137d6f801387bc71d7539b116cdf3a45729fed9dcb1f86bb

  task:
    scheduling:
      pool:
        size: 20

  main:
    allow-circular-references: true
  servlet:
    multipart:
      # 文件上传的最大大小
      max-file-size: 200MB
      # 请求的最大大小（包含表单数据）
      max-request-size: 250MB
request:
  stub:
    persistence: false


area:
  map:
    "330800" : "315,390,391"

net:
  transfer:
    list: [local-inner, qz-gov-inner-network]

salt: shendu188

sa-token:
  # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  active-timeout: -1
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # 是否尝试从请求体里读取token
  is-read-body: true
  # 是否尝试从header里读取token
  is-read-header: true
  # 是否尝试从cookie里读取token
  is-read-cookie: true
  # token风格
  token-style: jwt
  # jwt秘钥
  jwt-secret-key: shendu188
  timeout: 172800
#sa-token:
#  # token 有效期（单位：秒），默认30天，-1代表永不过期
#  timeout: 172800
#  # jwt秘钥
#  jwt-secret-key: shendu188

ftp:
  env:
    list: [sz-gov-inner]