spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: ***********************************************
          username: postgres
          password: sdzl123
          driver-class-name: org.postgresql.Driver

  session:
    store-type: none


mybatis-plus:
  mapper-locations: classpath*:mybatis/mapper/*Mapper.xml


application:
  deployment:
    env: hs-network

network:
  edge:
    url: http://41.236.1.204:13201/vss/3.0/dzxy/service?wsdl

data:
  service:
    url: http://*************:60109

digest:
  sign:
    api:
      key: 934c26e878d8eeb0253705d153c97334
      secret: 39b4a34f0b17e8ef0b4cdfa3a6b273e9
      key-temp: d05d7417d6f70eb5f6cbf8a72a824844
      secret-temp: b6c8e84fc756d898ccf3897c5a1625a0

token:
  sign:
    api:
      - key: d05d7417d6f70eb5f6cbf8a72a824844
        secret: 6eaa4cc617ebdcbb1546f989b3dd363b
        appName: dz-amap-sdzl
        adcode: 330800  # 衢州

city:
  adcode: 330800

out-api:
  haikang:
    appKey: none
    appSecret: none
    ip: **************
  dahua:
    server:
      ip: ***********
      port: 8320
    username: none
    password: none
    app-id: none
