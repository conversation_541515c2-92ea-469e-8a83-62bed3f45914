spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: *************************************************************************
          #    url: ***************************************************************************
          username: postgres
          password: Shendu188
          driver-class-name: org.postgresql.Driver

  session:
    store-type: none

mybatis-plus:
  mapper-locations: classpath*:mybatis/mapper/*Mapper.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  sql:
    print: false

application:
  deployment:
    env: dapeng-test

data:
  service:
    url: http://***********:62109

network:
  edge:
    url: http://localhost:60106/services/gaodeDataBridge?wsdl

digest:
  sign:
    api:
      key: 517d6aa3f7daec6a8efbd6d22b501f20
      secret: 1d9da8b945fc813912b3182e5ea822a8
      key-temp: 517d6aa3f7daec6a8efbd6d22b501f20
      secret-temp: 1d9da8b945fc813912b3182e5ea822a8

token:
  sign:
    api:
      - key: 517d6aa3f7daec6a8efbd6d22b501f20
        secret: cd7bdd471d4192dbeeb833617de53b99
        appName: dz-amap-runproject
        adcode: 440300  # 深圳

city:
  adcode: 440300
