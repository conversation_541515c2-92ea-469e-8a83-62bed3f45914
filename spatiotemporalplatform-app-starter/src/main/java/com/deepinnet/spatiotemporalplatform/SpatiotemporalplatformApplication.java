package com.deepinnet.spatiotemporalplatform;

import cn.hutool.extra.spring.EnableSpringUtil;
import com.github.jaemon.dinger.core.annatations.DingerScan;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@ComponentScan(basePackages = "com.deepinnet")
@MapperScan(basePackages = {"com.deepinnet.spatiotemporalplatform.dal.mapper", "com.deepinnet.infra.dal.mapper"})
@EnableSpringUtil
@EnableScheduling
@EnableAsync(proxyTargetClass = true)
@DingerScan(basePackages = {"com.deepinnet.dingding"})
@EnableFeignClients(basePackages = "com.deepinnet")
public class SpatiotemporalplatformApplication {

    public static void main(String[] args) {
        SpringApplication.run(SpatiotemporalplatformApplication.class, args);
    }

}
