package com.deepinnet.spatiotemporalplatform.config;

import com.deepinnet.spatiotemporalplatform.base.config.TenantConfig;
import feign.RequestInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <p>
 *    为所有Feign请求添加Tenant_Id请求头
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/12
 */

@Configuration
public class FeignTenantIdConfig {

    private static final String TENANT_ID_HEADER = "Tenant-Id";

    /**
     * Feign请求拦截器配置
     */
    @Bean
    public RequestInterceptor tenantIdInterceptor(TenantConfig tenantConfig) {
        return template -> template.header(TENANT_ID_HEADER, tenantConfig.getTenantId());
    }

}
