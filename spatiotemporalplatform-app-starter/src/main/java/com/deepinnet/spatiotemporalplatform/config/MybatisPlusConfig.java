package com.deepinnet.spatiotemporalplatform.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.*;
import com.deepinnet.spatiotemporalplatform.dal.type.StringListTypeHandler;
import org.springframework.context.annotation.*;

import javax.annotation.Resource;

/**
 * Creator zengjuerui
 * Date 2024-07-29
 **/

@Configuration
public class MybatisPlusConfig {

    @Resource
    private DeepinnetTenantLineHandler tenantLineHandler;

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor mybatisPlusInterceptor = new MybatisPlusInterceptor();
        mybatisPlusInterceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.POSTGRE_SQL));

        // 注册租户行拦截器
        mybatisPlusInterceptor.addInnerInterceptor(new TenantLineInnerInterceptor(tenantLineHandler));

        return mybatisPlusInterceptor;
    }

    @Bean
    public ConfigurationCustomizer configurationCustomizer() {
        return configuration -> {
            configuration.getTypeHandlerRegistry().register(StringListTypeHandler.class);
        };
    }

}
