package com.deepinnet.spatiotemporalplatform.config;

import cn.hutool.core.util.StrUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.context.annotation.Configuration;

import java.util.stream.Stream;

/**
 * <p>
 *  仅在调用 FlightDemandClient 时添加 X-Auth-Model: bypass 头
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/16
 */

@Configuration
public class FlightDemandSelectiveInterceptor implements RequestInterceptor {

    private static final String[] IGNORE_URL = new String[]{"/demand/pass/get"
            , "/flight/order/orderUsage"
            , "/demand/get/by/plan/id"
            , "/demand/get/demand/increment/service/list"};

    @Override
    public void apply(RequestTemplate template) {
        String requestUrl = template.url();
        if (StrUtil.isBlank(requestUrl)) {
            return;
        }

        if (Stream.of(IGNORE_URL).anyMatch(url -> requestUrl.contains(url))) {
            template.header("X-Auth-Mode", "bypass");
        }
    }
}
