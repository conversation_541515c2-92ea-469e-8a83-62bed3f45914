package com.deepinnet.spatiotemporalplatform.config;

import org.springframework.beans.factory.ObjectFactory;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.*;

/**
 * <AUTHOR> wong
 * @create 2025/4/19 17:07
 * @Description
 */
@Configuration
public class OpenFeignConfig {

    @Bean
    public SpringEncoder springEncoder(ObjectFactory<HttpMessageConverters> messageConverters) {
        return new SpringEncoder(messageConverters);
    }
}
