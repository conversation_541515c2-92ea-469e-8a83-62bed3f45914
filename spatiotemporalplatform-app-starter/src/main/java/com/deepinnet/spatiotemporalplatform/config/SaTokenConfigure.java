package com.deepinnet.spatiotemporalplatform.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.jwt.StpLogicJwtForSimple;
import cn.dev33.satoken.stp.StpLogic;
import org.springframework.context.annotation.*;
import org.springframework.web.servlet.config.annotation.*;

import javax.annotation.Resource;

@Configuration
public class SaTokenConfigure implements WebMvcConfigurer {

    @Resource
    private SaInterceptor saInterceptor;

    /**
     * 放行目录
     */
    private static final String[] EXCLUDE_PATH_PATTERNS = {
            "/user/login",
            // Swagger
            "**/swagger-ui.html",
            "/swagger-resources/**",
            "/webjars/**",
            "/v2/**",
            "/swagger-ui.html/**",
            "/doc.html/**",
            "/error",
            "/favicon.ico",
            "sso/auth",
            "/parkingtest/**",
            "/admin/contingencyPlan/files/download",
            "/roadInflowAggregation/exportHourly",
            "/csrf",
            "/stpf/**",
            "//dictionary/**",
            "/admin/police/**",
            "/admin/area/**",
            "/domain/**",
            "/domain/intercom/**",
            "/admin/circle-layer/**",
            "/aoi/**",
            "/api/oss-file-info/**",
            "/api/poi/**",
            "/api/aoi/**",
            "/poi/**"

    };

    /**
     * 注册 Sa-Token 拦截器，打开注解式鉴权功能
     *
     * @param registry
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册 Sa-Token 拦截器，校验规则为 StpUtil.checkLogin() 登录校验。
        registry.addInterceptor(saInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(EXCLUDE_PATH_PATTERNS);
    }

    /**
     * Sa-Token 整合 jwt (服务器完全无状态)
     *
     * @return
     */
    @Bean
    public StpLogic getStpLogicJwt() {
        // 使用Redis存储，不再使用无状态模式
        return new StpLogicJwtForSimple();
    }
}