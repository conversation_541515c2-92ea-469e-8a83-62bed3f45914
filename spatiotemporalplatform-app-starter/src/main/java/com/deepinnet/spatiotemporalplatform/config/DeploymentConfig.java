package com.deepinnet.spatiotemporalplatform.config;

import com.deepinnet.spatiotemporalplatform.base.env.DeploymentEnv;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 部署相关配置
 *
 * <AUTHOR>
 * @version 2024-08-05
 */
@Configuration
public class DeploymentConfig {
    /**
     * 部署环境
     */
    @Value("${application.deployment.env:prod}")
    private String deploymentEnv;

    @Bean
    public DeploymentEnv getDeploymentEnv() {
        DeploymentEnv deployEnv = new DeploymentEnv();
        deployEnv.setDeploymentEnv(this.deploymentEnv);
        return deployEnv;
    }

}
