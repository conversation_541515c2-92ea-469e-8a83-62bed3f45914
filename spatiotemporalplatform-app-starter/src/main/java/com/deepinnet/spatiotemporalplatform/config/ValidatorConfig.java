package com.deepinnet.spatiotemporalplatform.config;

import org.hibernate.validator.HibernateValidator;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.*;
import org.springframework.validation.beanvalidation.SpringConstraintValidatorFactory;

import javax.validation.*;


/**
 * <AUTHOR>
 * @since 2024/7/29 16:40
 **/
@Configuration
public class ValidatorConfig {

    @Bean
    public Validator validator(AutowireCapableBeanFactory beanFactory) {
        ValidatorFactory validatorFactory = Validation.byProvider(HibernateValidator.class)
                .configure()
                // 配置快速失败
                .failFast(true)
                .constraintValidatorFactory(new SpringConstraintValidatorFactory(beanFactory))
                .buildValidatorFactory();
        return validatorFactory.getValidator();
    }

}
