package com.deepinnet.spatiotemporalplatform.config;

import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.spatiotemporalplatform.skyflow.mqtt.YuanFeiMessageHandler;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.*;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.integration.mqtt.support.DefaultPahoMessageConverter;
import org.springframework.messaging.MessageHandler;

@Configuration
@Profile({"local", "alicloud", "sz-gov-cloud"})
public class YuanfeiMqttConfig {

    @Value("${mqtt.protocol}")
    private String protocol;

    @Value("${mqtt.host}")
    private String host;

    @Value("${mqtt.port}")
    private String port;

    @Value("${mqtt.username}")
    private String username;

    @Value("${mqtt.password}")
    private String password;

    @Value("${mqtt.client:deepinnet}")
    private String clientId;

    @Bean
    public MqttPahoClientFactory mqttClientFactory() {
        DefaultMqttPahoClientFactory factory = new DefaultMqttPahoClientFactory();
        MqttConnectOptions options = new MqttConnectOptions();
        options.setServerURIs(new String[]{protocol + "://" + host + ":" + port});
        options.setUserName(username);
        options.setPassword(password.toCharArray());
        factory.setConnectionOptions(options);
        LogUtil.info("[MQTT] 初始化 ClientFactory，Broker = {}://{}:{}", protocol, host, port);
        return factory;
    }

    @Bean
    public MqttPahoMessageDrivenChannelAdapter inbound() {
        // var clientId = "deepinnet";
        MqttPahoMessageDrivenChannelAdapter adapter =
                new MqttPahoMessageDrivenChannelAdapter(clientId, mqttClientFactory(), YuanFeiMessageHandler.REALTIME_FLY_DATA_TOPIC,
                        YuanFeiMessageHandler.WARNING_TOPIC, YuanFeiMessageHandler.AIRSPACE_STATUS_UPDATE_TOPIC);
        adapter.setCompletionTimeout(5000);
        adapter.setConverter(new DefaultPahoMessageConverter());
        adapter.setQos(0);
        adapter.setOutputChannelName("mqttInputChannel");
        LogUtil.info("[MQTT] 创建 Inbound Adapter，clientId = {}", clientId);
        return adapter;
    }

    @Bean
    @ServiceActivator(inputChannel = "mqttInputChannel")
    public MessageHandler mqttMessageHandler(YuanFeiMessageHandler yuanFeiMessageHandler) {
        return yuanFeiMessageHandler;
    }
}