package com.deepinnet.spatiotemporalplatform.config;

import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.spatiotemporalplatform.skyflow.mqtt.YuanFeiMessageHandler;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.persist.MqttDefaultFilePersistence;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.*;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.ExecutorChannel;
import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.integration.mqtt.support.DefaultPahoMessageConverter;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHandler;

import java.net.URL;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.Executors;

@Configuration
@Profile({"local", "alicloud", "sz-gov-cloud"})
public class YuanfeiMqttConfig {

    @Value("${mqtt.protocol}")
    private String protocol;

    @Value("${mqtt.host}")
    private String host;

    @Value("${mqtt.port}")
    private String port;

    @Value("${mqtt.username}")
    private String username;

    @Value("${mqtt.password}")
    private String password;

    @Value("${mqtt.client:deepinnet}")
    private String clientId;

    @Value("${mqtt.store.path:/tmp/mqtt-store")
    private String mqttStorePath;

    @Bean
    public MqttPahoClientFactory mqttClientFactory() {
        DefaultMqttPahoClientFactory factory = new DefaultMqttPahoClientFactory();
        MqttConnectOptions options = new MqttConnectOptions();
        options.setServerURIs(new String[]{protocol + "://" + host + ":" + port});
        options.setUserName(username);
        options.setPassword(password.toCharArray());

        // ★ 保持持久会话，断线期间的 QoS1/2 消息会保留
        options.setCleanSession(false);

        // 自动重连
        options.setAutomaticReconnect(true);

        // 保活间隔（秒）
        options.setKeepAliveInterval(30);

        // 本地持久化存储 inflight 消息
        factory.setPersistence(new MqttDefaultFilePersistence(mqttStorePath));

        factory.setConnectionOptions(options);
        LogUtil.info("[MQTT] 初始化 ClientFactory，Broker = {}://{}:{}", protocol, host, port);
        return factory;
    }

    @Bean
    public MqttPahoMessageDrivenChannelAdapter inbound() {
        String uniqueClientId = clientId + "-" + UUID.randomUUID();
        // 增加share来均摊当前mqtt消息
        String MQTT_SHARE = "$share" + "/" + "deepinnet";
        MqttPahoMessageDrivenChannelAdapter adapter =
                new MqttPahoMessageDrivenChannelAdapter(uniqueClientId, mqttClientFactory(),
                        MQTT_SHARE + YuanFeiMessageHandler.REALTIME_FLY_DATA_TOPIC,
                        MQTT_SHARE + YuanFeiMessageHandler.WARNING_TOPIC,
                        MQTT_SHARE + YuanFeiMessageHandler.AIRSPACE_STATUS_UPDATE_TOPIC);

        adapter.setCompletionTimeout(5000);
        adapter.setConverter(new DefaultPahoMessageConverter());

        // set QOS 1
        adapter.setQos(1);

        // 输出到我们自定义的异步 Channel
        adapter.setOutputChannelName("mqttInputChannel");

        LogUtil.info("[MQTT] 创建 Inbound Adapter，clientId = {}", uniqueClientId);
        return adapter;
    }

    @Bean
    @ServiceActivator(inputChannel = "mqttInputChannel")
    public MessageHandler mqttMessageHandler(YuanFeiMessageHandler yuanFeiMessageHandler) {
        return yuanFeiMessageHandler;
    }

    @Bean(name = "mqttInputChannel")
    public MessageChannel mqttInputChannel() {
        // 8 个线程并发处理消息
        return new ExecutorChannel(Executors.newFixedThreadPool(8));
    }
}