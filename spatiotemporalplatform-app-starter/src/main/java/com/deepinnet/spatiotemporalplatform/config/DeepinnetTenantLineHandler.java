package com.deepinnet.spatiotemporalplatform.config;

import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.util.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.deepinnet.infra.service.context.TenantContext;
import com.deepinnet.tenant.TenantIdUtil;
import net.sf.jsqlparser.expression.*;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wong
 * @create 2025/4/21 10:09
 * @Description
 */
@Component
public class DeepinnetTenantLineHandler implements TenantLineHandler, InitializingBean {

    List<String> needFilterTenantIdTableNames = new ArrayList<>();

    @Override
    public void afterPropertiesSet() {
        Set<String> infraTableNames = getTableNames("com.deepinnet.infra.dal.dataobject");
        Set<String> normalTableNames = getTableNames("com.deepinnet.spatiotemporalplatform.dal.dataobject");
        needFilterTenantIdTableNames.addAll(infraTableNames);
        needFilterTenantIdTableNames.addAll(normalTableNames);
        needFilterTenantIdTableNames.remove("dictionary");
        needFilterTenantIdTableNames.remove("dictionary_item");
    }

    @Override
    public Expression getTenantId() {
        String tenantId = TenantIdUtil.getTenantId();
        if (StrUtil.isNotBlank(tenantId)) {
            return new StringValue(tenantId);
        }
        return null;
    }

    @Override
    public String getTenantIdColumn() {
        return "tenant_id";
    }

    @Override
    public boolean ignoreTable(String tableName) {
        // 兼容老的逻辑，如果租户id为空则不拼接租户id
        if (StrUtil.isBlank(TenantIdUtil.getTenantId())) {
            return true;
        }

        // 如果开启了禁用租户id则直接放行，不拼接租户id，适用场景：定时任务
        if (TenantContext.isTenantLineDisabled()) {
            return true;
        }

        return !needFilterTenantIdTableNames.contains(tableName);
    }

    public static Set<String> getTableNames(String basePackage) {
        // 扫描包下所有类（非抽象）
        Set<Class<?>> classSet = ClassUtil.scanPackage(basePackage);

        // 过滤出有 @TableName 注解的类，提取表名
        return classSet.stream()
                .filter(clazz -> AnnotationUtil.hasAnnotation(clazz, TableName.class))
                .map(clazz -> AnnotationUtil.getAnnotationValue(clazz, TableName.class, "value"))
                .map(String::valueOf)
                .collect(Collectors.toSet());
    }
}
