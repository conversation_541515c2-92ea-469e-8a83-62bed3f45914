package com.deepinnet.spatiotemporalplatform.web.skyflow;

import cn.hutool.core.collection.ListUtil;
import com.deepinnet.digitaltwin.common.util.JsonConvertUtil;
import com.deepinnet.digitaltwin.common.util.PointCoordinate;
import com.deepinnet.localdata.integration.error.RepeatExecException;
import com.deepinnet.localdata.integration.model.outsidebean.FlightMonitorTaskResp;
import com.deepinnet.spatiotemporalplatform.SpatiotemporalplatformApplication;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.AlgorithmEventRepository;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.AlgorithmEventDO;
import com.deepinnet.spatiotemporalplatform.dto.*;
import com.deepinnet.spatiotemporalplatform.enums.FlightEventStatusEnum;
import com.deepinnet.spatiotemporalplatform.enums.FlightEventTypeEnum;
import com.deepinnet.spatiotemporalplatform.skyflow.algorithm.EventMonitorService;
import com.deepinnet.spatiotemporalplatform.skyflow.client.FlightTaskNotifyClient;
import com.google.common.collect.ImmutableMap;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.MDC;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Description: 动态SQL调用和供应商匹配测试
 * Date: 2025/4/22
 * Author: lijunheng
 */
@ActiveProfiles("local")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpatiotemporalplatformApplication.class)
public class EventMonitorTest {

    @Resource
    private EventMonitorService eventMonitorService;

    @Resource
    private AlgorithmEventRepository algorithmEventRepository;

    @Resource
    private FlightTaskNotifyClient flightTaskNotifyClient;

    @Before
    public void setup() {
        MDC.put("Tenant-Id", "deepinnet");
    }

    @Test
    public void testQueryFlightEventsStat() {
        FlightEventsStatQueryDTO queryDTO = new FlightEventsStatQueryDTO();
        queryDTO.setTenantId("1");
        queryDTO.setStartTime(LocalDateTime.now().withDayOfMonth(8));
        List<FlightEventsStatDTO> flightEventsStatDTOS = eventMonitorService.queryFlightEventsStat(queryDTO);
        System.out.println(flightEventsStatDTOS);
    }

    @Test
    public void testQueryListByFlightTaskCode() {
        List<FlightEventsDTO> flightEventsDTOS = eventMonitorService.queryListByFlightTaskCode("PLAN_d74d09cd5f654d38bcd44eaf6ddb0d66", List.of());
        System.out.println(flightEventsDTOS);
    }

    @Test
    public void testSaveEvent() {
        FlightEventsDTO flightEventsDTO = new FlightEventsDTO();
        flightEventsDTO.setAlgorithmEventId("3");
        flightEventsDTO.setEventType(FlightEventTypeEnum.TURN_VIOLATION);
        flightEventsDTO.setEventStartTime(LocalDateTime.now());
        flightEventsDTO.setEventName(FlightEventTypeEnum.TURN_VIOLATION.getName());
        flightEventsDTO.setDescription("1");
        flightEventsDTO.setEventPoint(new PointCoordinate(122.0, 25.0));
        flightEventsDTO.setEventLocation("xxx街道xxx店门口");
        flightEventsDTO.setStatus(FlightEventStatusEnum.ONGOING);
        flightEventsDTO.setLicensePlate("1");
        Map<String, String> imageMap = ImmutableMap.of("url", "xxxx");
        flightEventsDTO.setEvidenceImages(JsonConvertUtil.toJsonStr(imageMap));
        Map<String, String> videoMap = ImmutableMap.of("url", "xxxx");
        flightEventsDTO.setEvidenceVideos(JsonConvertUtil.toJsonStr(videoMap));
        flightEventsDTO.setFlightTaskCode("1");
        flightEventsDTO.setTenantId("1");
        eventMonitorService.saveEvent(flightEventsDTO);
    }

    @Test
    public void testGetEventById() {
        FlightEventsDTO flightEventsDTO = eventMonitorService.getEventById("2");
        System.out.println(flightEventsDTO);
    }

    @Test
    public void testPageQuery() {
        FlightEventsQueryDTO queryDTO = new FlightEventsQueryDTO();
        queryDTO.setTenantId("deepinnet");
        queryDTO.setDemandStartTime(LocalDateTime.now().minusMonths(3));
        queryDTO.setDemandEndTime(LocalDateTime.now());
        queryDTO.setUserNo("uid_1920791444604985344");
        queryDTO.setPageSize(200);
        List<FlightEventsDTO> flightEventsDTOS = eventMonitorService.pageQuery(queryDTO).getList();
        System.out.println(flightEventsDTOS);
    }

    @Test
    public void testSelectListEvent() {
        List<AlgorithmEventDO> list = algorithmEventRepository.list();
        System.out.println();
    }

    @Test
    public void testStartFlightTask() throws RepeatExecException {
        FlightAlgorithmMonitorCreateDTO createDTO = new FlightAlgorithmMonitorCreateDTO();
        createDTO.setFlightTaskId("cb0762bc54e14469a9651043038e39f6");
        createDTO.setEventTypeList(ListUtil.of(FlightEventTypeEnum.PARKING_VIOLATION));
        createDTO.setVideoUrl("rtsp://122.227.105.154:32321/live/playlist/b6eb4604");
        createDTO.setTenantId("sz_unifly");
        FlightMonitorTaskResp flightMonitorTaskResp = flightTaskNotifyClient.startFlightTask(createDTO);
        System.out.println(flightMonitorTaskResp);
    }
}
