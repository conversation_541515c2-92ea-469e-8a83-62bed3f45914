package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.staticpoi.StaticPoi;
import com.deepinnet.spatiotemporalplatform.model.staticpoi.StaticPoiQueryDTO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * StaticPoiController集成测试
 * 使用现有数据库中的数据进行测试
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("local")
public class StaticPoiControllerIntegrationTest {

    @LocalServerPort
    private int port;

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    private HttpHeaders headers;
    private String baseUrl;

    @BeforeEach
    void setUp() {
        baseUrl = "http://localhost:" + port + "/stpf/poi/static";
        headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Cookie", "bypass");
    }

    /**
     * 测试分页查询接口
     * 直接使用数据库中现有的数据进行测试
     */
    @Test
    void pageStaticPoi_ShouldReturnPaginatedResults() throws Exception {
        // 创建分页查询参数
        StaticPoiQueryDTO queryDTO = new StaticPoiQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(100);

        // 发送HTTP请求
        HttpEntity<StaticPoiQueryDTO> request = new HttpEntity<>(queryDTO, headers);
        ResponseEntity<String> response = restTemplate.postForEntity(
                baseUrl + "/page", request, String.class);

        // 验证HTTP响应状态码
        Assertions.assertThat(response.getStatusCode().is2xxSuccessful()).isTrue();

        // 解析响应内容
        Result<CommonPage<StaticPoi>> result = objectMapper.readValue(
                response.getBody(),
                new TypeReference<Result<CommonPage<StaticPoi>>>() {});

        // 验证响应内容
        Assertions.assertThat(result.isSuccess()).isTrue();
        Assertions.assertThat(result.getData()).isNotNull();
        Assertions.assertThat(result.getData().getList()).isNotEmpty(); // 确保返回了数据
        Assertions.assertThat(result.getData().getTotal()).isGreaterThan(0); // 确保总数大于0
    }

    /**
     * 测试按POI名称搜索功能
     */
    @Test
    void pageStaticPoi_WithPoiNameFilter_ShouldReturnFilteredResults() throws Exception {
        // 从数据库已有数据中选取一个POI名称进行测试
        // 这里假设数据库中存在名为"天安门广场"的POI数据
        String testPoiName = "天安门广场";

        StaticPoiQueryDTO queryDTO = new StaticPoiQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);
        queryDTO.setPoiName(testPoiName);

        // 发送HTTP请求
        HttpEntity<StaticPoiQueryDTO> request = new HttpEntity<>(queryDTO, headers);
        ResponseEntity<String> response = restTemplate.postForEntity(
                baseUrl + "/page", request, String.class);

        // 验证HTTP响应状态码
        Assertions.assertThat(response.getStatusCode().is2xxSuccessful()).isTrue();

        // 解析响应内容
        Result<CommonPage<StaticPoi>> result = objectMapper.readValue(
                response.getBody(),
                new TypeReference<Result<CommonPage<StaticPoi>>>() {});

        // 验证响应内容
        Assertions.assertThat(result.isSuccess()).isTrue();
        Assertions.assertThat(result.getData()).isNotNull();

        // 验证所有返回的POI名称中包含搜索的关键词
        result.getData().getList().forEach(poi ->
                Assertions.assertThat(poi.getPoiName()).containsIgnoringCase(testPoiName)
        );
    }

    /**
     * 测试根据POI ID查询接口
     * 使用数据库中已有的一个POI ID进行测试
     */
    @Test
    void getByPoiId_ShouldReturnCorrectPoi() throws Exception {
        // 假设数据库中存在ID为"BJ001"的POI
        String existingPoiId = "BJ001";

        // 创建查询参数
        StaticPoi request = new StaticPoi();
        request.setPoiId(existingPoiId);

        // 发送HTTP请求
        HttpEntity<StaticPoi> httpEntity = new HttpEntity<>(request, headers);

        ResponseEntity<String> response = restTemplate.postForEntity(
                baseUrl + "/detail?poiId=" + existingPoiId, httpEntity, String.class);

        // 验证HTTP响应状态码
        Assertions.assertThat(response.getStatusCode().is2xxSuccessful()).isTrue();

        // 解析响应内容
        Result<StaticPoi> result = objectMapper.readValue(
                response.getBody(),
                new TypeReference<Result<StaticPoi>>() {});

        // 验证响应内容
        Assertions.assertThat(result.isSuccess()).isTrue();
        Assertions.assertThat(result.getData()).isNotNull();
        Assertions.assertThat(result.getData().getPoiId()).isEqualTo(existingPoiId);
    }

    /**
     * 测试根据不存在的POI ID查询接口
     */
    @Test
    void getByPoiId_WithNonExistentId_ShouldReturnNull() throws Exception {
        // 使用一个不存在的POI ID
        String nonExistentPoiId = "NONEXISTENT_ID_999999";

        // 创建查询参数
        StaticPoi request = new StaticPoi();
        request.setPoiId(nonExistentPoiId);

        // 发送HTTP请求
        HttpEntity<StaticPoi> httpEntity = new HttpEntity<>(request, headers);

        ResponseEntity<String> response = restTemplate.postForEntity(
                baseUrl + "/detail?poiId=" + nonExistentPoiId, httpEntity, String.class);

        // 验证HTTP响应状态码
        Assertions.assertThat(response.getStatusCode().is2xxSuccessful()).isTrue();

        // 解析响应内容
        Result<StaticPoi> result = objectMapper.readValue(
                response.getBody(),
                new TypeReference<Result<StaticPoi>>() {});

        // 验证响应内容
        Assertions.assertThat(result.isSuccess()).isTrue();
        Assertions.assertThat(result.getData()).isNull();  // 不存在的POI应该返回null
    }
}