package com.deepinnet.spatiotemporalplatform.web.skyflow;

import com.deepinnet.infra.api.dto.FileInfoDTO;
import com.deepinnet.spatiotemporalplatform.SpatiotemporalplatformApplication;
import com.deepinnet.spatiotemporalplatform.dto.FlightReportGenerateDTO;
import com.deepinnet.spatiotemporalplatform.skyflow.service.FlightReportService;
import com.deepinnet.spatiotemporalplatform.skyflow.service.impl.FlightReportServiceImpl;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.data.Pictures;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Map;

/**
 * Description: 动态SQL调用和供应商匹配测试
 * Date: 2025/4/22
 * Author: lijunheng
 */
@ActiveProfiles("local")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpatiotemporalplatformApplication.class)
public class FlightReportTest {

    @Resource
    private FlightReportService flightReportService;

    @Resource
    private FlightReportServiceImpl flightReportServiceImpl;

    @Value("${xxx:classpath:word/demo.docx}")
    private org.springframework.core.io.Resource resource;

    @Test
    public void generateFlightReport() {
        FlightReportGenerateDTO generateDTO = new FlightReportGenerateDTO();
        generateDTO.setPlanId("08d3e1c5f521465897a8955b110aa27e");
        FileInfoDTO fileInfoDTO = flightReportService.generateFlightReportDocument(generateDTO);
        System.out.println(fileInfoDTO);
    }

    // A4纸的宽度，单位：EMU（可根据实际需要调整）
    // 16cm，每厘米360000emu,保持宽高比为 16：10
    private static final int PIC_WIDTH = 16;
    // 10cm，每厘米360000emu,保持宽高比为 16：10
    private static final int PIC_HEIGHT = 8;
    @Test
    public void generateFlightReport2() {
        String image = "http://122.227.105.154:32179/deepinnet/cedb1694-dc64-42d0-9622-1f86ed69c25e/illegal_stop/images/1311_illegal_stop_id_2_e68b9936-daa0-4f7e-a36d-c3d41958273e.jpg";
        PictureRenderData pictureRenderData = Pictures.ofUrl(image).fitSize().create();
        Map<String, PictureRenderData> generateDTO = Map.of("image", pictureRenderData);
        FileInfoDTO fileInfoDTO = flightReportServiceImpl.generateFlightReportDocument(generateDTO, resource);
        System.out.println(fileInfoDTO);
    }
}
