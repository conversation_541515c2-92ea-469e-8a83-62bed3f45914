package com.deepinnet.spatiotemporalplatform.smartcity.task;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * C<PERSON> zengjuerui
 * Date 2025-04-03
 **/

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class QzjjCameraCoordinateCorrectionTaskTest {

    @Resource
    private QzjjCameraCoordinateCorrectionTask task;


    @Test
    public void testCorrect() {
        task.correct();
    }
}
