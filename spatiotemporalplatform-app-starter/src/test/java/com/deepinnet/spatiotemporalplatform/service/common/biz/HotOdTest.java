package com.deepinnet.spatiotemporalplatform.service.common.biz;

import cn.hutool.core.thread.ThreadUtil;
import com.deepinnet.spatiotemporalplatform.model.hotod.*;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import com.deepinnet.spatiotemporalplatform.smartcity.service.dto.OnRoadBaseChannelDetail;
import com.deepinnet.spatiotemporalplatform.smartcity.service.repository.OnRoadBaseChannelDetailRepository;
import com.deepinnet.spatiotemporalplatform.smartcity.service.repository.OnRoadBaseChannelDetailRepositorySlave;
import org.apache.commons.compress.utils.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @since 2024-11-18 星期一
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class HotOdTest {

    @Resource
    private CommonDataService commonDataService;

    @Resource
    private OnRoadBaseChannelDetailRepository onRoadBaseChannelDetailRepository;

    @Resource
    private OnRoadBaseChannelDetailRepositorySlave onRoadBaseChannelDetailRepositorySlave;

    @Test
    public void testInsert() {
        OnRoadBaseChannelDetail onRoadBaseChannelDetail = new OnRoadBaseChannelDetail();
        onRoadBaseChannelDetail.setRecordId("test-record-id-1");
        onRoadBaseChannelDetail.setTenantId("test-tenant-id-1");
        onRoadBaseChannelDetail.setAreaCode("330800");
        onRoadBaseChannelDetail.setAreaName("测试区域");
        onRoadBaseChannelDetail.setGmtCreated(new Date());
        onRoadBaseChannelDetail.setGmtModified(new Date());
        onRoadBaseChannelDetail.setChannelId("test-channel-id");
        onRoadBaseChannelDetail.setPlateNum("test-plate-num");
        onRoadBaseChannelDetail.setCapTime(new Date());
        onRoadBaseChannelDetail.setInsideArea(1);
        onRoadBaseChannelDetailRepository.saveBatch(Arrays.asList(onRoadBaseChannelDetail));
        onRoadBaseChannelDetailRepositorySlave.saveBatch(Arrays.asList(onRoadBaseChannelDetail));
    }

    @Test
    public void areaArriveCarPredict() {
        AreaArriveCarPredictRequest areaArriveCarPredictRequest = new AreaArriveCarPredictRequest();
        AreaArriveCarPredictUrlParams areaArriveCarPredictUrlParams = new AreaArriveCarPredictUrlParams();
        areaArriveCarPredictUrlParams.setAdcode("330800");
        areaArriveCarPredictUrlParams.setType("0");
        areaArriveCarPredictUrlParams.setIds("315");
        areaArriveCarPredictRequest.setUrlParams(areaArriveCarPredictUrlParams);
        List<AreaArriveCarPredict> areaArriveCarPredicts = (List<AreaArriveCarPredict>) commonDataService.fetchData(areaArriveCarPredictRequest);
        System.out.println(areaArriveCarPredicts);
        ThreadUtil.safeSleep(5000);

    }

    @Test
    public void driverDestinationRanking() {
        DriverDestinationRankingRequest driverDestinationRankingRequest = new DriverDestinationRankingRequest();
        DriverDestinationRankingUrlParams driverDestinationRankingUrlParams = new DriverDestinationRankingUrlParams();
        driverDestinationRankingUrlParams.setAdcode("330800");
        driverDestinationRankingUrlParams.setTypes("0,1,2");
        //driverDestinationRankingUrlParams.setIds("315");
        driverDestinationRankingUrlParams.setPageNum(1);
        driverDestinationRankingUrlParams.setPageSize(200);
        driverDestinationRankingRequest.setUrlParams(driverDestinationRankingUrlParams);
        DriverDestinationRanking driverDestinationRanking = (DriverDestinationRanking) commonDataService.fetchData(driverDestinationRankingRequest);
        System.out.println(driverDestinationRanking);
        ThreadUtil.safeSleep(5000);
    }

    @Test
    public void sourceDestinationDetail() {
        SourceDestinationDetailRequest sourceDestinationDetailRequest = new SourceDestinationDetailRequest();
        SourceDestinationDetailUrlParams sourceDestinationDetailUrlParams = new SourceDestinationDetailUrlParams();
        sourceDestinationDetailUrlParams.setType("0");
        sourceDestinationDetailUrlParams.setId("391");
        sourceDestinationDetailUrlParams.setInOut(1);
        sourceDestinationDetailUrlParams.setSourceType("CITY");
        sourceDestinationDetailUrlParams.setAdcode("330800");
        sourceDestinationDetailRequest.setUrlParams(sourceDestinationDetailUrlParams);
        SourceDestinationDetail sourceDestinationDetail = (SourceDestinationDetail) commonDataService.fetchData(sourceDestinationDetailRequest);
        System.out.println(sourceDestinationDetail);
        ThreadUtil.safeSleep(5000);
    }

}
