package com.deepinnet.spatiotemporalplatform.service.common.biz;

import com.deepinnet.spatiotemporalplatform.model.road.*;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.*;

/**
 * @since 2024-08-28 星期三
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class RoadServiceTest {

    @Resource
    private CommonDataService commonDataService;

    @Test
    public void testRoadHistoryRanking() {
        RoadHistoryRankingRequest roadHistoryRankingRequest = new RoadHistoryRankingRequest();
        RoadHistoryRankingUrlParams roadHistoryRankingUrlParams = new RoadHistoryRankingUrlParams();
        roadHistoryRankingUrlParams.setDateRangeList("2024-12-01@2024-12-10");
        roadHistoryRankingUrlParams.setTimePeriodList("all");
        roadHistoryRankingUrlParams.setDayType("workday,weekend");
        roadHistoryRankingUrlParams.setTimeGrading("hour");
        roadHistoryRankingUrlParams.setType("2");
        roadHistoryRankingUrlParams.setIds("603,539,538,443,499,566,575,511,12147,565,446,574,591,447,498,442,501,509,590,2028,448,593,2045,444");
        roadHistoryRankingUrlParams.setSize(200);
        roadHistoryRankingUrlParams.setAdcode("330800");
        roadHistoryRankingUrlParams.setSize(10);
        roadHistoryRankingRequest.setUrlParams(roadHistoryRankingUrlParams);
        List<RoadHistoryRanking> roadHistoryRankings = (List<RoadHistoryRanking>) commonDataService.fetchData(roadHistoryRankingRequest);
        System.out.println(roadHistoryRankings);

    }

//    @Test
//    public void testRoadFlow() {
//        RoadFlowRequest roadFlowRequest = new RoadFlowRequest();
//        RoadFlowPostBody roadFlowPostBody = new RoadFlowPostBody();
//        roadFlowPostBody.setAdcode(330800);
//        roadFlowPostBody.setTimeType("HH");
//        roadFlowPostBody.setRoadids(Lists.newArrayList(3049,3050,4687,4689,2554,2555,3106,4758,404,407,5812,5816,6900,6901,6995,6996,7306,7307,10802,11824,11835,3241,3242,3244,3246,1377,1414,3323,3324,10248,10249,4211,4214,3457,3458,357,367,5031,5317,1655,1659,5312,5315,5311,5314,1016,1017,9993,10003,11509,11510,5483,5484,5507,5508));
//        //roadFlowPostBody.setRoadids(Lists.newArrayList(3049));
//        //roadFlowPostBody.setHh("10");
//        roadFlowPostBody.setDs("20240905");
//        //roadFlowPostBody.setMm("");
//
//        roadFlowRequest.setPostBody(roadFlowPostBody);
//        RoadFlow roadFlow = (RoadFlow) commonDataService.fetchData(roadFlowRequest);
//        System.out.println(roadFlow);
//    }


    @Test
    public void testRoadSearch() {

        //List<String> nameList = Arrays.asList("G2052", "徐齐线", "开马线", "桃下线", "城华线", "常文线", "沿江公路", "朱富线", "府前路", "常河线", "沟直线", "苦狮线", "杜板线", "杜西线", "云十线", "湖童公路", "龙翔东路", "学士路", "方渡线", "龙灵线", "全石线", "石安线", "鹿溪北路", "江滨路", "兰贺线", "水张线", "通景公路");

        List<String> nameList = Arrays.asList("府前路");

        Map<String, String> map = new HashMap<>();

        Set<String> ids = Sets.newHashSet();

        List<RoadSearch> roadSearches = Lists.newArrayList();
        for (String name : nameList) {
            RoadSearchRequest roadSearchRequest = new RoadSearchRequest();
            RoadSearchUrlParams roadSearchUrlParams = new RoadSearchUrlParams();
            roadSearchUrlParams.setAdcode("330800");
            roadSearchUrlParams.setTypes("2");
            roadSearchUrlParams.setName(name);
            roadSearchRequest.setUrlParams(roadSearchUrlParams);
            roadSearches.addAll((List<RoadSearch>) commonDataService.fetchData(roadSearchRequest));

            // System.out.println(roadSearches.stream().map(e -> e.getId() + "").collect(Collectors.toList()).stream().distinct().collect(Collectors.joining(",")));

//            for (RoadSearch roadSearch : roadSearches) {
//                ThreadUtil.safeSleep(100);
//                //System.out.println(roadSearch.getName() + ":" + roadSearch.getDescription());
//                MultiLineString multiLineString = WktUtil.toMultiLineString(roadSearch.getGeo());
//
//                List<Coordinate> list = Arrays.asList(multiLineString.getCoordinate());
//
//                //LineString lineString = (LineString) multiLineString.getGeometryN(0);
//                //System.out.println("起点：" + list.get(0));
//                //System.out.println("终点：" + list.get(list.size() - 1));
//                map.put(roadSearch.getName() + ":" + roadSearch.getDescription() + roadSearch.getId() + "起点-" + list.get(0).x + "," + list.get(0).y, list.get(0).x + "," + list.get(0).y);
//                map.put(roadSearch.getName() + ":" + roadSearch.getDescription() + roadSearch.getId() + "终点-" + list.get(list.size() - 1).x + "," + list.get(list.size() - 1).y, list.get(list.size() - 1).x + "," + list.get(list.size() - 1).y);
//            }
//        }
//
//        map.forEach((name, point) -> {
//            ThreadUtil.safeSleep(100);
//            RoadFlowPredictRequest roadFlowPredictRequest = new RoadFlowPredictRequest();
//            RoadFlowPredictUrlParams roadFlowPredictUrlParams = new RoadFlowPredictUrlParams();
//            roadFlowPredictUrlParams.setAdcode("330800");
//            roadFlowPredictUrlParams.setStartPoint(point);
//            roadFlowPredictUrlParams.setExtensions("all");
//            roadFlowPredictUrlParams.setSaturationFlag(1);
//            roadFlowPredictRequest.setUrlParams(roadFlowPredictUrlParams);
//            RoadFlowPredict roadFlowPredict = (RoadFlowPredict) commonDataService.fetchData(roadFlowPredictRequest);
//            System.out.println(name + ":" + roadFlowPredict);
//        });
        }

        //System.out.println(String.join(",", ids));

        System.out.println(roadSearches);
    }

    @Test
    public void testRoadLinkFlowQuery() {
        RoadLinkFlowRequest roadLinkFlowRequest = new RoadLinkFlowRequest();
        RoadLinkFlowUrlParams roadLinkFlowUrlParams = new RoadLinkFlowUrlParams();
        roadLinkFlowUrlParams.setAdcode("330800");
        roadLinkFlowUrlParams.setPubname("934c26e878d8eeb0253705d153c97334");
        roadLinkFlowRequest.setUrlParams(roadLinkFlowUrlParams);
        String s = (String) commonDataService.fetchData(roadLinkFlowRequest);
        System.out.println(s);
    }

    @Test
    public void testRealRoadIndex() {
        RealRoadIndexRequest realRoadIndexRequest = new RealRoadIndexRequest();
        RealRoadIndexUrlParams realRoadIndexUrlParams = new RealRoadIndexUrlParams();
        realRoadIndexUrlParams.setAdcode("440300");
        realRoadIndexUrlParams.setType(2);
        //realRoadIndexUrlParams.setIds("5340,6995,4211,3244,3242,3241,5816,1414,1413,1655,1017,11824,1016,1378,1377,357,11509,5973,6900,5812,6901,1659,9993,5031,5033,5311,4784,5312,3050,5508,1347,2556,11835,2555,367,3049,2554,3324,3246,3323,404,888,845,6996,4214,1708,407,10543,11510,10544,5507,5483,5484,6773,6971,6774,4826,3338,3337,3458,3457,1355,5314,5315,5316,5317,5515,4825,6969,8365,5295,5053,5296,5054,5450,5972,10249,10802,3106,10847,12388,7307,7306,5449,10003,4758,10248");
        //realRoadIndexUrlParams.setDistrict("");
        realRoadIndexUrlParams.setSize(200);

        realRoadIndexRequest.setUrlParams(realRoadIndexUrlParams);
        List<RealRoadIndex> realRoadIndices = (List<RealRoadIndex>) commonDataService.fetchData(realRoadIndexRequest);
        System.out.println(realRoadIndices);
    }

    @Test
    public void testRoadFlowPredict() {
        RoadFlowPredictRequest roadFlowPredictRequest = new RoadFlowPredictRequest();
        RoadFlowPredictUrlParams roadFlowPredictUrlParams = new RoadFlowPredictUrlParams();
        roadFlowPredictUrlParams.setAdcode("330800");
        roadFlowPredictUrlParams.setStartPoint("118.38611304759979,29.153047800064087");
        roadFlowPredictUrlParams.setExtensions("all");
        roadFlowPredictUrlParams.setSaturationFlag(1);

        roadFlowPredictRequest.setUrlParams(roadFlowPredictUrlParams);
        RoadFlowPredict roadFlowPredict = (RoadFlowPredict) commonDataService.fetchData(roadFlowPredictRequest);
        System.out.println(roadFlowPredict);

//         roadFlowPredictRequest = new RoadFlowPredictRequest();
//         roadFlowPredictUrlParams = new RoadFlowPredictUrlParams();
//        roadFlowPredictUrlParams.setAdcode("330800");
//        roadFlowPredictUrlParams.setStartPoint("118.4944635629654, 28.267430663108826");
//        //roadFlowPredictUrlParams.setExtensions();
//        //roadFlowPredictUrlParams.setSaturationFlag();
//
//        roadFlowPredictRequest.setUrlParams(roadFlowPredictUrlParams);
//        roadFlowPredict = (RoadFlowPredict) commonDataService.fetchData(roadFlowPredictRequest);
//        System.out.println(roadFlowPredict);
    }
}
