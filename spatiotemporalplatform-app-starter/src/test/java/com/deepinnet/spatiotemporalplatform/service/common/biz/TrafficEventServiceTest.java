package com.deepinnet.spatiotemporalplatform.service.common.biz;

import com.deepinnet.spatiotemporalplatform.common.constants.GlobalConstant;
import com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis.RealTimeDistrictIndexRankingRequest;
import com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis.RealTimeDistrictIndexRankingUrlParams;
import com.deepinnet.spatiotemporalplatform.model.traffic.event.TrafficEventRequest;
import com.deepinnet.spatiotemporalplatform.model.traffic.event.TrafficEventUrlParams;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class TrafficEventServiceTest {
    @Resource
    private CommonDataService commonDataService;

    @Test
    public void testService() {

        // 交通事件查询
        TrafficEventRequest trafficEventRequest = new TrafficEventRequest();
        TrafficEventUrlParams trafficEventUrlParams = new TrafficEventUrlParams();
        trafficEventUrlParams.setAdcode(GlobalConstant.QZ_AD_CODE);
        //trafficEventUrlParams.setClientKey(trafficEventRequest.apikey());
        //trafficEventUrlParams.setTimestamp(System.currentTimeMillis() / 1000);
        trafficEventRequest.setUrlParams(trafficEventUrlParams);
        Object trafficEvents = commonDataService.fetchData(trafficEventRequest);
        System.out.println(trafficEvents);

    }


    @Test
    public void testRanking() {
        RealTimeDistrictIndexRankingRequest request = new RealTimeDistrictIndexRankingRequest();
        RealTimeDistrictIndexRankingUrlParams rankingUrlParams = new RealTimeDistrictIndexRankingUrlParams();
        rankingUrlParams.setAdcode("330800");
        rankingUrlParams.setTypes("28");
        rankingUrlParams.setWithArea(true);
        request.setUrlParams(rankingUrlParams);

        System.out.println(commonDataService.fetchData(request));

    }
}