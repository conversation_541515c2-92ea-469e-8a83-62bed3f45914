package com.deepinnet.spatiotemporalplatform.service.common.biz;

import cn.hutool.extra.spring.SpringUtil;
import com.deepinnet.spatiotemporalplatform.model.tour.customer.*;
import com.deepinnet.spatiotemporalplatform.model.tour.enums.FlowTypeEnum;
import com.deepinnet.spatiotemporalplatform.model.tour.enums.ProfileEnum;
import com.deepinnet.spatiotemporalplatform.model.tour.enums.TimeTypeEnum;
import com.deepinnet.spatiotemporalplatform.model.tour.flow.PassengerFlow;
import com.deepinnet.spatiotemporalplatform.model.tour.flow.PassengerFlowRequest;
import com.deepinnet.spatiotemporalplatform.model.tour.flow.PassengerFlowUrlParams;
import com.deepinnet.spatiotemporalplatform.model.tour.group.GroupFlowDetect;
import com.deepinnet.spatiotemporalplatform.model.tour.group.GroupFlowDetectRequest;
import com.deepinnet.spatiotemporalplatform.model.tour.group.GroupFlowDetectUrlParams;
import com.deepinnet.spatiotemporalplatform.model.tour.heat.RealHeat;
import com.deepinnet.spatiotemporalplatform.model.tour.heat.RealHeatRequest;
import com.deepinnet.spatiotemporalplatform.model.tour.heat.RealHeatUrlParams;
import com.deepinnet.spatiotemporalplatform.model.tour.portrait.RealPortrait;
import com.deepinnet.spatiotemporalplatform.model.tour.portrait.RealPortraitRequest;
import com.deepinnet.spatiotemporalplatform.model.tour.portrait.RealPortraitUrlParams;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * @since 2024-09-09 星期一
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class TourTest {

    @Resource
    private CommonDataService commonDataService;

    @Test
    public void te() {
        System.out.println(SpringUtil.getApplicationName());
    }


    @Test
    public void RealPortraitTest() {
        RealPortraitRequest realPortraitRequest = new RealPortraitRequest();
        RealPortraitUrlParams realPortraitUrlParams = new RealPortraitUrlParams();
        realPortraitUrlParams.setAreaId("WY00018AKC");
        realPortraitUrlParams.setFlowType(FlowTypeEnum.TOTAL_FLOW.getValue());
        realPortraitUrlParams.setTimeType("60m");
        realPortraitUrlParams.setDs(202308311800L);
        realPortraitUrlParams.setProfile("u_sex");
        realPortraitUrlParams.setAdcode("330800");
        realPortraitRequest.setUrlParams(realPortraitUrlParams);
        RealPortrait realPortrait = (RealPortrait) commonDataService.fetchData(realPortraitRequest);
        System.out.println(realPortrait);
    }

    @Test
    public void RealHeatTest() {
        RealHeatRequest realHeatRequest = new RealHeatRequest();
        RealHeatUrlParams realHeatUrlParams = new RealHeatUrlParams();
        realHeatUrlParams.setAreaId("WY00018AKC");
        realHeatUrlParams.setGeoLength("1000");
        realHeatUrlParams.setFlowType(FlowTypeEnum.TOTAL_FLOW.getValue());
        realHeatUrlParams.setTimeType("60m");
        realHeatUrlParams.setDs(202308311800L);
        realHeatUrlParams.setAdcode("330800");
        realHeatRequest.setUrlParams(realHeatUrlParams);
        RealHeat realHeat = (RealHeat) commonDataService.fetchData(realHeatRequest);
        System.out.println(realHeat);
    }

    @Test
    public void PassengerFlowTest() {
        PassengerFlowRequest passengerFlowRequest = new PassengerFlowRequest();
        PassengerFlowUrlParams passengerFlowUrlParams = new PassengerFlowUrlParams();
        passengerFlowUrlParams.setAreaId("WY00018AKC");
        passengerFlowUrlParams.setFlowType(FlowTypeEnum.TOTAL_FLOW.getValue());
        passengerFlowUrlParams.setTimeType("15m");
        passengerFlowUrlParams.setDs("202409100900");
        passengerFlowUrlParams.setAdcode("330800");
        passengerFlowRequest.setUrlParams(passengerFlowUrlParams);
        PassengerFlow passengerFlow = (PassengerFlow) commonDataService.fetchData(passengerFlowRequest);
        System.out.println(passengerFlow);
    }

    @Test
    public void GroupFlowDetectTest() {
        GroupFlowDetectRequest groupFlowDetectRequest = new GroupFlowDetectRequest();
        GroupFlowDetectUrlParams groupFlowDetectUrlParams = new GroupFlowDetectUrlParams();
        groupFlowDetectUrlParams.setAreaId("WY00018AKC");
        groupFlowDetectUrlParams.setStartDate(20240901);
        groupFlowDetectUrlParams.setEndDate(20240901);
        groupFlowDetectUrlParams.setFlowType(FlowTypeEnum.TOTAL_FLOW.getValue());
        groupFlowDetectUrlParams.setTimeType(TimeTypeEnum.DAY.getValue());
        groupFlowDetectUrlParams.setProfile(ProfileEnum.AGE_DIST.getValue());
        groupFlowDetectUrlParams.setAdcode("330800");

        groupFlowDetectRequest.setUrlParams(groupFlowDetectUrlParams);
        GroupFlowDetect groupFlowDetect = (GroupFlowDetect) commonDataService.fetchData(groupFlowDetectRequest);
        System.out.println(groupFlowDetect);
    }

    @Test
    public void CustomerFlowDetectTest() {
        CustomerFlowDetectRequest customerFlowDetectRequest = new CustomerFlowDetectRequest();
        CustomerFlowDetectUrlParams customerFlowDetectUrlParams = new CustomerFlowDetectUrlParams();
        customerFlowDetectUrlParams.setAreaId("WY00018AKD");
        customerFlowDetectUrlParams.setStartDate(20240906);
        customerFlowDetectUrlParams.setEndDate(20240908);
        customerFlowDetectUrlParams.setFlowType(FlowTypeEnum.TOTAL_FLOW.getValue());
        customerFlowDetectUrlParams.setTimeType(TimeTypeEnum.HOUR.getValue());
        customerFlowDetectUrlParams.setAdcode("330800");
        customerFlowDetectRequest.setUrlParams(customerFlowDetectUrlParams);
        CustomerFlowDetect customerFlowDetect = (CustomerFlowDetect) commonDataService.fetchData(customerFlowDetectRequest);
        System.out.println(customerFlowDetect);
    }

    @Test
    public void CustomerFlowDetectGridTest() {

        CustomerFlowDetectGridRequest customerFlowDetectGridRequest = new CustomerFlowDetectGridRequest();
        CustomerFlowDetectGridUrlParams customerFlowDetectGridUrlParams = new CustomerFlowDetectGridUrlParams();
        customerFlowDetectGridUrlParams.setAreaId("WY00018AKD");
        customerFlowDetectGridUrlParams.setGeoLength(100);
        customerFlowDetectGridUrlParams.setStartDate(20240901);
        customerFlowDetectGridUrlParams.setEndDate(20240901);
        customerFlowDetectGridUrlParams.setAdcode("330800");
        customerFlowDetectGridUrlParams.setFlowType(FlowTypeEnum.TOTAL_FLOW.getValue());
        customerFlowDetectGridUrlParams.setTimeType(TimeTypeEnum.DAY.getValue());
        customerFlowDetectGridRequest.setUrlParams(customerFlowDetectGridUrlParams);
        System.out.println(commonDataService.fetchData(customerFlowDetectGridRequest));
    }

}
