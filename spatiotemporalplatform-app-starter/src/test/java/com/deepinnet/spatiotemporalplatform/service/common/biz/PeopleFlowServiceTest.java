package com.deepinnet.spatiotemporalplatform.service.common.biz;

import com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.*;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class PeopleFlowServiceTest {

    @Resource
    private CommonDataService commonDataService;

    @Test
    public void testGridRealTimeService() {
        GridRealTimePeopleFlowRequest peopleFlowRequest = new GridRealTimePeopleFlowRequest();
        GridRealTimePeopleFlowParams peopleFlowParams =  new GridRealTimePeopleFlowParams();
        peopleFlowParams.setCustomAreaId("315");
        peopleFlowRequest.setUrlParams(peopleFlowParams);
        RealTimePeopleFlowData dataRes = (RealTimePeopleFlowData) commonDataService.fetchData(peopleFlowRequest);
        System.out.println(dataRes);
    }

    @Test
    public void testRealTimeService() {
        RealTimePeopleFlowRequest peopleFlowRequest = new RealTimePeopleFlowRequest();
        RealTimePeopleFlowParams peopleFlowParams =  new RealTimePeopleFlowParams();
        peopleFlowParams.setCustomAreaId("315");
        peopleFlowRequest.setUrlParams(peopleFlowParams);
        RealTimePeopleFlowData dataRes = (RealTimePeopleFlowData) commonDataService.fetchData(peopleFlowRequest);
        System.out.println(dataRes);
    }

    @Test
    public void testSourceFlow() {
        RegionPeopleSourceFlowRequest peopleSourceFlowRequest = new RegionPeopleSourceFlowRequest();
        RegionPeopleSourceFlowParams params = new RegionPeopleSourceFlowParams();
        params.setAdcode("315");
        params.setType(3);
        RegionPeopleSourceFlowData regionPeopleSourceFlowData = (RegionPeopleSourceFlowData) commonDataService.fetchData(peopleSourceFlowRequest);
        System.out.println(regionPeopleSourceFlowData);

    }

}