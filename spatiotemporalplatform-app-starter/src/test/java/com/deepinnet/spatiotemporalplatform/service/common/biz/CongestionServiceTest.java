package com.deepinnet.spatiotemporalplatform.service.common.biz;

import com.alibaba.fastjson2.util.DateUtils;
import com.deepinnet.spatiotemporalplatform.common.constants.GlobalConstant;
import com.deepinnet.spatiotemporalplatform.model.congestion.*;
import com.deepinnet.spatiotemporalplatform.base.http.CommonDataService;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class CongestionServiceTest {
    @Resource
    private CommonDataService commonDataService;

    @Test
    public void testRealTimeCongestionService() {
        // 实时拥堵预警
        RealTimeCongestionPostBody realTimeCongestionPostBody = new RealTimeCongestionPostBody();
        List<String> adCodes = new ArrayList<>();
        adCodes.add(GlobalConstant.QZ_AD_CODE);
        realTimeCongestionPostBody.setAdCodes(adCodes);
        realTimeCongestionPostBody.setAreas(Lists.newArrayList("POLYGON((118.868371 28.969764, 118.873675 28.967623, 118.873301 28.959949, 118.874355 28.953226, 118.871227 28.952839, 118.869935 28.953434, 118.868065 28.953791, 118.863238 28.95626, 118.864598 28.959681, 118.866128 28.965035, 118.867997 28.969021, 118.867997 28.969021, 118.868099 28.969199, 118.868371 28.969764)) | 4326"));
        List<RealTimeCongestionPostBody.RealTimeCongestionQuery> queryList = new ArrayList<>();
        RealTimeCongestionPostBody.RealTimeCongestionQuery congestionQuery = new RealTimeCongestionPostBody.RealTimeCongestionQuery();
//        congestionQuery.setDuration(4);
//        congestionQuery.setRoadType(0);
//        congestionQuery.setDistance(200);
//        congestionQuery.setCongestRate("0");
//        congestionQuery.setCongestType(4);
//        queryList.add(congestionQuery);
        realTimeCongestionPostBody.setQueryList(queryList);

        RealTimeCongestionRequest realTimeCongestionRequest = new RealTimeCongestionRequest();
        realTimeCongestionRequest.setPostBody(realTimeCongestionPostBody);
        CongestionUrlParams urlParams = new CongestionUrlParams();
        // urlParams.setClientKey(realTimeCongestionRequest.apikey());
        //urlParams.setTimestamp(System.currentTimeMillis() / 1000);
        realTimeCongestionRequest.setUrlParams(urlParams);
        CongestionData data = (CongestionData) commonDataService.fetchData(realTimeCongestionRequest);
        System.out.println(data);
    }


    @Test
    public void testHistoryCongestionService() {
        // 历史拥堵预警
        HistoryCongestionPostBody congestionPostBody = new HistoryCongestionPostBody();
        List<String> adCodes = new ArrayList<>();
        adCodes.add(GlobalConstant.QZ_AD_CODE);
        congestionPostBody.setAdCodes(adCodes);
        congestionPostBody.setStartTime(DateUtils.parseDate("2024-08-15", "yyyy-MM-dd").getTime() / 1000);
        congestionPostBody.setEndTime(DateUtils.parseDate("2024-08-27", "yyyy-MM-dd").getTime() / 1000);
        List<HistoryCongestionPostBody.HistoryCongestionQuery> queryList = new ArrayList<>();
        HistoryCongestionPostBody.HistoryCongestionQuery congestionQuery = new HistoryCongestionPostBody.HistoryCongestionQuery();
        congestionQuery.setDuration(4);
        congestionQuery.setRoadType(0);
        congestionQuery.setDistance(200);
        congestionQuery.setCongestRate("0");
        congestionQuery.setCongestType(0);
//        queryList.add(congestionQuery);

        HistoryCongestionPostBody.HistoryCongestionQuery congestionQuery2 = new HistoryCongestionPostBody.HistoryCongestionQuery();
        congestionQuery2.setDuration(4);
        congestionQuery2.setRoadType(1);
        congestionQuery2.setDistance(200);
        congestionQuery2.setCongestRate("0");
        congestionQuery2.setCongestType(1);
//        queryList.add(congestionQuery2);
        congestionPostBody.setQueryList(queryList);

        HistoryCongestionRequest congestionRequest = new HistoryCongestionRequest();
        congestionRequest.setPostBody(congestionPostBody);
        CongestionUrlParams urlParams = new CongestionUrlParams();
        //urlParams.setClientKey(congestionRequest.apikey());
        //urlParams.setTimestamp(System.currentTimeMillis() / 1000);
        congestionRequest.setUrlParams(urlParams);
        CongestionData data = (CongestionData) commonDataService.fetchData(congestionRequest);
        System.out.println(data);
    }

}