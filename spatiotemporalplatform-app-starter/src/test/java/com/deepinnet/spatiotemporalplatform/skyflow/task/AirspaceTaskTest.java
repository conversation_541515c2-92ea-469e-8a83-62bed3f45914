package com.deepinnet.spatiotemporalplatform.skyflow.task;

import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.spatiotemporalplatform.dal.dataobject.BaseStationDO;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.BaseStationRepository;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

/**
 * AirspaceTask 集成测试类
 * 
 * <AUTHOR>
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class AirspaceTaskTest {


    @Resource
    private AirspaceTask airspaceTask;

    private Long testBatchNo;

    @After
    public void tearDown() {
        // 清理测试数据
       // cleanTestData();
    }

    /**
     * AirspaceTask
     * 这个测试会调用真实的外部接口和数据库
     */
    @Test
    public void testSyncBaseStationData() {
        try {
            airspaceTask.syncAirspaces();

        } catch (Exception e) {
            LogUtil.error("[AirspaceTask] 测试任务执行异常", e);
        }
    }

} 