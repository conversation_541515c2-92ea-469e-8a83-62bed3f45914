package com.deepinnet.spatiotemporalplatform.skyflow.task;

import com.deepinnet.spatiotemporalplatform.dal.dataobject.LandingPointDO;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.LandingPointRepository;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 起降点数据同步任务测试
 *
 * <AUTHOR>
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class LandingPointDataSyncTaskTest {

    @Resource
    private LandingPointDataSyncTask landingPointDataSyncTask;

    @Resource
    private LandingPointRepository landingPointRepository;

    @Test
    public void testSyncLandingPointData() {
        System.out.println("测试起降点数据同步任务");
        
        landingPointDataSyncTask.syncLandingPointData();
        
        System.out.println("起降点数据同步任务测试完成");
    }

    @Test
    public void testSaveLandingPointData() {
        System.out.println("测试保存起降点数据");

        // 创建测试数据
        LandingPointDO landingPointDO = new LandingPointDO();
        landingPointDO.setExternalId("test-landing-point-001");
        landingPointDO.setCode("LP001");
        landingPointDO.setType("临时起降点");
        landingPointDO.setName("测试起降点");
        landingPointDO.setAlt(100);
        landingPointDO.setHeight(50);
        landingPointDO.setRadius(200);
        landingPointDO.setPoint("120.123,30.456");
        landingPointDO.setAddress("浙江省杭州市测试区域");
        landingPointDO.setPointOrName("测试起降点");
        landingPointDO.setTeamId("team001");
        landingPointDO.setCrtType("manual");
        landingPointDO.setCrtUserId("user001");
        landingPointDO.setCrtTime(System.currentTimeMillis());
        landingPointDO.setCrtUserName("测试用户");
        landingPointDO.setRemark("测试用起降点数据");
        landingPointDO.setIsDisabled("0");
        landingPointDO.setDepartId("dept001");
        landingPointDO.setTenantId("tenant001");
        landingPointDO.setOwner("测试所有人");
        landingPointDO.setOperator("测试运营人");
        landingPointDO.setPhoneNumber("13800138000");
        landingPointDO.setBusiType("emergency");
        landingPointDO.setAirType("helipad");
        landingPointDO.setStreet("测试街道");
        landingPointDO.setAccuracy("high");
        landingPointDO.setGmtCreated(LocalDateTime.now());
        landingPointDO.setGmtModified(LocalDateTime.now());

        // 保存数据
        boolean saved = landingPointRepository.save(landingPointDO);
        System.out.println("保存结果: " + saved);
        System.out.println("保存的数据ID: " + landingPointDO.getId());

        // 查询验证
        if (saved && landingPointDO.getId() != null) {
            LandingPointDO savedData = landingPointRepository.getById(landingPointDO.getId());
            System.out.println("查询到的数据: " + savedData);
            
            // 清理测试数据
            landingPointRepository.removeById(landingPointDO.getId());
            System.out.println("测试数据已清理");
        }

        System.out.println("保存起降点数据测试完成");
    }

    @Test
    public void testQueryLandingPointByExternalId() {
        System.out.println("测试根据外部ID查询起降点数据");

        // 先保存一条测试数据
        LandingPointDO testData = new LandingPointDO();
        testData.setExternalId("test-external-001");
        testData.setName("测试查询起降点");
        testData.setCode("TQ001");
        testData.setType("测试类型");
        testData.setGmtCreated(LocalDateTime.now());
        testData.setGmtModified(LocalDateTime.now());
        
        landingPointRepository.save(testData);
        System.out.println("测试数据已保存，ID: " + testData.getId());

        // 根据外部ID查询
        LandingPointDO queryResult = landingPointRepository.getByExternalId("test-external-001");
        System.out.println("查询结果: " + queryResult);

        // 清理测试数据
        if (testData.getId() != null) {
            landingPointRepository.removeById(testData.getId());
            System.out.println("测试数据已清理");
        }

        System.out.println("根据外部ID查询起降点数据测试完成");
    }
} 