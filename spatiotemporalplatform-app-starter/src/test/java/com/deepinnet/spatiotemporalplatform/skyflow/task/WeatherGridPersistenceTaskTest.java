package com.deepinnet.spatiotemporalplatform.skyflow.task;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * C<PERSON> zengjuerui
 * Date 2025-04-21
 **/

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@ActiveProfiles("local")
public class WeatherGridPersistenceTaskTest {

    @Resource
    private WeatherGridPersistenceTask task;

    @Test
    public void testTask() {
        task.persistence();
    }
}


