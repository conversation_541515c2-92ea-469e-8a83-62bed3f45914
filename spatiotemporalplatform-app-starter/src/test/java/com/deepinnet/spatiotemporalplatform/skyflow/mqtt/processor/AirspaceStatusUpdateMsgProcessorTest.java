package com.deepinnet.spatiotemporalplatform.skyflow.mqtt.processor;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * <PERSON><PERSON> zengjuerui
 * Date 2025-07-15
 **/

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@ActiveProfiles("local-inner")
public class AirspaceStatusUpdateMsgProcessorTest {

    @Resource
    private AirspaceStatusUpdateMsgProcessor airspaceStatusUpdateMsgProcessor;


    @Test
    public void testHandle() {

        String body = "\"[{\\\"asid\\\":\\\"空域2(CC42)\\\",\\\"status\\\":2,\\\"boundary\\\":\\\"POLYGON ((114.143333 22.620833, 114.158333 22.626667, 114.170833 22.608056, 114.147222 22.597222, 114.143333 22.620833))\\\",\\\"top\\\":350,\\\"bot\\\":0,\\\"tms\\\":1745914811.0}]\"";

        airspaceStatusUpdateMsgProcessor.handle("test", body);
    }
}
