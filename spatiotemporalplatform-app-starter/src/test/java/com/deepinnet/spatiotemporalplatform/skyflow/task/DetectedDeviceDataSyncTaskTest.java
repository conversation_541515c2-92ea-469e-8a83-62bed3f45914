package com.deepinnet.spatiotemporalplatform.skyflow.task;

import com.deepinnet.spatiotemporalplatform.dal.dataobject.DetectedDeviceDO;
import com.deepinnet.spatiotemporalplatform.skyflow.repository.DetectedDeviceRepository;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.*;

/**
 * DetectedDeviceDataSyncTask 集成测试类
 * 
 * <AUTHOR>
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class DetectedDeviceDataSyncTaskTest {

    @Resource
    private DetectedDeviceRepository detectedDeviceRepository;

    @Resource
    private DetectedDeviceDataSyncTask detectedDeviceDataSyncTask;

    private Long testBatchNo;

    @Before
    public void setUp() {
        // 生成测试批次号
        testBatchNo = System.currentTimeMillis();
        
        // 清理测试数据
        cleanTestData();
        
        // 准备测试数据
        prepareTestData();
    }

    @After
    public void tearDown() {
        // 清理测试数据
        // cleanTestData();
    }

    /**
     * 测试侦测定位设备数据同步任务
     * 这个测试会调用真实的外部接口和数据库
     */
    @Test
    public void testSyncDetectedDeviceData() {
        try {
            // 记录同步前的数据量
            long countBefore = detectedDeviceRepository.count();
            System.out.println("同步前侦测定位设备数据量: " + countBefore);

            // 执行同步任务
            detectedDeviceDataSyncTask.syncDetectedDeviceData();

            // 记录同步后的数据量
            long countAfter = detectedDeviceRepository.count();
            System.out.println("同步后侦测定位设备数据量: " + countAfter);

            // 验证数据是否有变化（如果外部接口有数据的话）
            // 注意：这里不强制要求数据量增加，因为外部接口可能没有数据
            assertTrue("同步后数据量应该大于等于同步前", countAfter >= countBefore);

        } catch (Exception e) {
            // 如果外部接口不可用，这是正常的，记录日志即可
            System.out.println("同步任务执行异常（可能是外部接口不可用）: " + e.getMessage());
            // 不让测试失败，因为外部接口的可用性不在我们控制范围内
        }
    }

    /**
     * 测试侦测定位设备数据的新增逻辑
     */
    @Test
    public void testDetectedDeviceInsert() {
        // 创建测试数据
        DetectedDeviceDO detectedDevice = createTestDetectedDeviceDO("test_external_id_001");
        
        // 确保数据不存在
        DetectedDeviceDO existing = detectedDeviceRepository.getByExternalId(detectedDevice.getExternalId());
        assertNull("测试数据不应该已存在", existing);

        // 保存数据
        boolean saveResult = detectedDeviceRepository.save(detectedDevice);
        assertTrue("保存应该成功", saveResult);

        // 验证数据已保存
        DetectedDeviceDO saved = detectedDeviceRepository.getByExternalId(detectedDevice.getExternalId());
        assertNotNull("保存的数据应该能查询到", saved);
        assertEquals("外部ID应该匹配", detectedDevice.getExternalId(), saved.getExternalId());
        assertEquals("设备名称应该匹配", detectedDevice.getDeviceName(), saved.getDeviceName());
    }

    /**
     * 测试侦测定位设备数据的更新逻辑
     */
    @Test
    public void testDetectedDeviceUpdate() {
        String externalId = "test_external_id_002";
        
        // 先插入一条数据
        DetectedDeviceDO originalData = createTestDetectedDeviceDO(externalId);
        originalData.setDeviceName("原始设备名称");
        detectedDeviceRepository.save(originalData);

        // 查询插入的数据
        DetectedDeviceDO existing = detectedDeviceRepository.getByExternalId(externalId);
        assertNotNull("原始数据应该存在", existing);

        // 更新数据
        existing.setDeviceName("更新后的设备名称");
        existing.setManufacturer("更新后的厂家");
        existing.setGmtModified(new Date());
        
        boolean updateResult = detectedDeviceRepository.updateById(existing);
        assertTrue("更新应该成功", updateResult);

        // 验证更新结果
        DetectedDeviceDO updated = detectedDeviceRepository.getByExternalId(externalId);
        assertNotNull("更新后的数据应该存在", updated);
        assertEquals("设备名称应该已更新", "更新后的设备名称", updated.getDeviceName());
        assertEquals("厂家应该已更新", "更新后的厂家", updated.getManufacturer());
    }

    /**
     * 测试根据externalId查询功能
     */
    @Test
    public void testGetByExternalId() {
        String externalId = "test_external_id_003";
        
        // 先插入一条数据
        DetectedDeviceDO testData = createTestDetectedDeviceDO(externalId);
        detectedDeviceRepository.save(testData);

        // 测试查询存在的数据
        DetectedDeviceDO found = detectedDeviceRepository.getByExternalId(externalId);
        assertNotNull("应该能查询到数据", found);
        assertEquals("外部ID应该匹配", externalId, found.getExternalId());

        // 测试查询不存在的数据
        DetectedDeviceDO notFound = detectedDeviceRepository.getByExternalId("non_existing_id");
        assertNull("不存在的数据应该返回null", notFound);

        // 测试查询空值
        DetectedDeviceDO nullResult = detectedDeviceRepository.getByExternalId(null);
        assertNull("空值查询应该返回null", nullResult);

        // 测试查询空字符串
        DetectedDeviceDO emptyResult = detectedDeviceRepository.getByExternalId("");
        assertNull("空字符串查询应该返回null", emptyResult);
    }

    /**
     * 测试批量操作
     */
    @Test
    public void testBatchOperations() {
        // 创建多条测试数据
        DetectedDeviceDO data1 = createTestDetectedDeviceDO("batch_test_001");
        DetectedDeviceDO data2 = createTestDetectedDeviceDO("batch_test_002");
        DetectedDeviceDO data3 = createTestDetectedDeviceDO("batch_test_003");

        List<DetectedDeviceDO> batchData = List.of(data1, data2, data3);

        // 测试批量保存
        boolean batchSaveResult = detectedDeviceRepository.saveBatch(batchData);
        assertTrue("批量保存应该成功", batchSaveResult);

        // 验证数据已保存
        for (DetectedDeviceDO data : batchData) {
            DetectedDeviceDO saved = detectedDeviceRepository.getByExternalId(data.getExternalId());
            assertNotNull("批量保存的数据应该能查询到: " + data.getExternalId(), saved);
        }

        // 重新查询数据用于批量更新
        List<DetectedDeviceDO> updateData = batchData.stream()
                .map(data -> detectedDeviceRepository.getByExternalId(data.getExternalId()))
                .peek(data -> {
                    data.setDeviceName("批量更新_" + data.getDeviceName());
                    data.setGmtModified(new Date());
                })
                .collect(java.util.stream.Collectors.toList());

        // 测试批量更新
        boolean batchUpdateResult = detectedDeviceRepository.updateBatchById(updateData);
        assertTrue("批量更新应该成功", batchUpdateResult);

        // 验证更新结果
        for (DetectedDeviceDO originalData : batchData) {
            DetectedDeviceDO updated = detectedDeviceRepository.getByExternalId(originalData.getExternalId());
            assertNotNull("更新后的数据应该存在", updated);
            assertTrue("设备名称应该包含批量更新前缀", 
                    updated.getDeviceName().startsWith("批量更新_"));
        }
    }

    /**
     * 测试数据库连接和基本查询
     */
    @Test
    public void testDatabaseConnection() {
        // 测试基本的count查询
        long count = detectedDeviceRepository.count();
        assertTrue("数据库连接正常，count查询应该返回非负数", count >= 0);

        // 测试基本的list查询
        List<DetectedDeviceDO> allData = detectedDeviceRepository.list();
        assertNotNull("list查询应该返回非null结果", allData);
        assertEquals("count和list结果应该一致", count, allData.size());
    }

    /**
     * 测试设备特有字段
     */
    @Test
    public void testDeviceSpecificFields() {
        String externalId = "test_external_id_004";
        
        // 创建包含设备特有字段的测试数据
        DetectedDeviceDO testData = createTestDetectedDeviceDO(externalId);
        testData.setSerialNo("SN123456789");
        testData.setStationType("雷达");
        testData.setTechParams("技术参数详情");
        testData.setInstallCount(5);
        testData.setLon(118.87);
        testData.setLat(28.97);
        testData.setReceiverGain("30dB");
        testData.setTransPower("100W");
        testData.setBandWidth("20MHz");
        
        // 保存数据
        detectedDeviceRepository.save(testData);

        // 验证特有字段
        DetectedDeviceDO saved = detectedDeviceRepository.getByExternalId(externalId);
        assertNotNull("保存的数据应该存在", saved);
        assertEquals("序列号应该匹配", "SN123456789", saved.getSerialNo());
        assertEquals("基站类型应该匹配", "雷达", saved.getStationType());
        assertEquals("技术参数应该匹配", "技术参数详情", saved.getTechParams());
        assertEquals("安装数量应该匹配", Integer.valueOf(5), saved.getInstallCount());
        assertEquals("经度应该匹配", Double.valueOf(118.87), saved.getLon());
        assertEquals("纬度应该匹配", Double.valueOf(28.97), saved.getLat());
        assertEquals("接收增益应该匹配", "30dB", saved.getReceiverGain());
        assertEquals("发射功率应该匹配", "100W", saved.getTransPower());
        assertEquals("带宽应该匹配", "20MHz", saved.getBandWidth());
    }

    /**
     * 准备测试数据
     */
    private void prepareTestData() {
        // 可以在这里准备一些基础测试数据
        System.out.println("准备侦测定位设备测试数据，批次号: " + testBatchNo);
    }

    /**
     * 清理测试数据
     */
    private void cleanTestData() {
        try {
            // 清理测试相关的数据
            detectedDeviceRepository.remove(
                    detectedDeviceRepository.lambdaQuery()
                            .like(DetectedDeviceDO::getExternalId, "test_external_id")
                            .or()
                            .like(DetectedDeviceDO::getExternalId, "batch_test")
                            .getWrapper()
            );
            System.out.println("清理侦测定位设备测试数据完成");
        } catch (Exception e) {
            System.out.println("清理侦测定位设备测试数据时发生异常: " + e.getMessage());
        }
    }

    /**
     * 创建测试用的DetectedDeviceDO对象
     */
    private DetectedDeviceDO createTestDetectedDeviceDO(String externalId) {
        DetectedDeviceDO detectedDevice = new DetectedDeviceDO();
        detectedDevice.setExternalId(externalId);
        detectedDevice.setManufacturer("测试厂家");
        detectedDevice.setDeviceName("测试设备_" + externalId);
        detectedDevice.setBrandModel("测试型号_V1.0");
        detectedDevice.setSerialNo("SN_" + externalId);
        detectedDevice.setStationType("测试基站类型");
        detectedDevice.setTechParams("测试技术参数");
        detectedDevice.setDataStandard("GB/T标准");
        detectedDevice.setEquipUsage("侦测定位");
        detectedDevice.setInstallEnvir("室外");
        detectedDevice.setInstallAdress("浙江省衢州市柯城区测试地址");
        detectedDevice.setAerialHeight("10米");
        detectedDevice.setOperatingFrequency("2.4GHz");
        detectedDevice.setInstallDate("2024-01-01");
        detectedDevice.setInstallCount(1);
        detectedDevice.setInstallSituation("正常运行");
        detectedDevice.setPropCompany("测试物业公司");
        detectedDevice.setPropLinkUser("张三");
        detectedDevice.setLinkPhone("13800138000");
        detectedDevice.setDeviceManager("李四");
        detectedDevice.setDevicePhone("13900139000");
        detectedDevice.setManageRequire("定期维护");
        detectedDevice.setRemark("测试设备备注");
        detectedDevice.setTenantId("test_tenant");
        detectedDevice.setReceiverGain("25dB");
        detectedDevice.setTransPower("50W");
        detectedDevice.setPulseWaveform("矩形波");
        detectedDevice.setPolarizationMode("垂直极化");
        detectedDevice.setFigure("3dB");
        detectedDevice.setBandWidth("10MHz");
        detectedDevice.setLon(118.87);
        detectedDevice.setLat(28.97);
        detectedDevice.setGcoverRage("5km");
        detectedDevice.setVcoverRage("1km");
        detectedDevice.setBatchNo(testBatchNo);
        detectedDevice.setGmtCreated(new Date());
        detectedDevice.setGmtModified(new Date());
        return detectedDevice;
    }
} 