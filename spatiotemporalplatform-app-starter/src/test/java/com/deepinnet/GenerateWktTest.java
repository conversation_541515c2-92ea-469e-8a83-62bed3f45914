package com.deepinnet;

import com.bedatadriven.jackson.datatype.jts.JtsModule;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import org.locationtech.jts.geom.*;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wong
 * @create 2025/6/5 10:14
 * @Description
 */
public class GenerateWktTest {

    public static void main(String[] args) throws Exception{
        ObjectMapper mapper = new ObjectMapper()
                .registerModule(new JtsModule())
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .configure(DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES, false);

        Data nowData = mapper.readValue(new File("/Users/<USER>/Downloads/data.geo.json"), new TypeReference<Data>() {
        });

        List<Geometry> now_geometries = nowData.getFeatures()
                .stream()
                .map(Feature::getGeometry)
                .filter(g -> g.getGeometryType().equals(Polygon.TYPENAME_POLYGON))
                .collect(Collectors.toList());

        Map<String, List<Feature>> nowMap = nowData.getFeatures().stream()
                .filter(feature -> feature.getProperties().getM_nType().equals("airspace"))
                .collect(Collectors.groupingBy(feature -> feature.getProperties().getM_szName()));

        System.out.println(now_geometries.size());

        System.out.println(nowMap.keySet());


        now_geometries.forEach(f -> System.out.println(f.toText()));
    }


    @lombok.Data
    static class Data {
        private List<Feature> features;
    }

    @lombok.Data
    static class Feature {
        private String type;

        private Properties properties;

        private Geometry geometry;
    }

    @lombok.Data
    static class Properties {
        private String m_szCode;

        private String m_szName;

        private String m_nType;

        private String first_alt;

        private String end_alt;

        private Double m_fElevation;
    }
}

