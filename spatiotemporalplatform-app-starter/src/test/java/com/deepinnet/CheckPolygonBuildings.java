package com.deepinnet;

import com.deepinnet.spatiotemporalplatform.model.util.WktUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.SneakyThrows;
import org.locationtech.jts.geom.*;

import java.io.File;
import java.io.IOException;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CheckPolygonBuildings {
    public static int total = 0;
    public static void main(String[] args) throws Exception {
        // 读取 monomer 文件夹下的所有 JSON 文件
        Path folderPath = Paths.get("/Users/<USER>/Downloads/monomer");
        DirectoryStream<Path> stream = Files.newDirectoryStream(folderPath, "*.json");

        List<HashMap> list = new ArrayList<>();
        for (Path filePath : stream) {
            exec(filePath, filePath.getFileName().toString(),list);
        }
        ObjectMapper mapper = new ObjectMapper();

        mapper.writeValue(new File("/Users/<USER>/Downloads/test/poi_name.json"), list);

    }

    @SneakyThrows
    private static void exec(Path filePath, String outFileName, List<HashMap> list) throws IOException {
        ObjectMapper mapper = new ObjectMapper();

        ArrayNode data = (ArrayNode) mapper.readTree(filePath.toFile());

        // 建筑坐标数据
        Map<String, Coordinate> buildings = new HashMap<>();
        buildings.put("龙岗区政府大楼", new Coordinate(114.246945, 22.721277));
        buildings.put("龙城广场", new Coordinate(114.249208, 22.718505));
        buildings.put("龙岗区文化馆", new Coordinate(114.250167, 22.720132));
        buildings.put("深圳大运中心", new Coordinate(114.216289, 22.695967));
        buildings.put("龙岗区体育中心体育馆", new Coordinate(114.204893, 22.682668));
        buildings.put("龙岗天安数码城", new Coordinate(114.216975, 22.72086));
        buildings.put("华为坂田基地", new Coordinate(114.060204, 22.650555));
        buildings.put("龙岗万科广场", new Coordinate(114.243821, 22.714413));
        buildings.put("布吉中心广场写字楼", new Coordinate(114.117488, 22.599982));
        buildings.put("甘坑市场", new Coordinate(114.105332, 22.65472));
        buildings.put("大芬油画村", new Coordinate(114.136248, 22.609589));
        buildings.put("深圳北理莫斯科大学主楼", new Coordinate(114.203975, 22.679945));
        buildings.put("深圳信息职业技术学院致远楼", new Coordinate(114.215448, 22.682089));
        buildings.put("龙岗区人民医院", new Coordinate(114.227944, 22.726491));
        buildings.put("九州家园东区", new Coordinate(114.263599, 22.72861));
        buildings.put("深圳国际低碳城", new Coordinate(114.292176, 22.778594));
        buildings.put("龙岗区图书馆", new Coordinate(114.250545, 22.719443));
        buildings.put("东江潮红色文化博物馆", new Coordinate(114.234132, 22.719084));
        buildings.put("深圳·红立方", new Coordinate(114.248514, 22.717158));
        buildings.put("大亚湾核电基地文化中心", new Coordinate(114.535122, 22.593754));
        buildings.put("龙岗区妇女儿童活动中心", new Coordinate(114.234046, 22.718784));
        buildings.put("大华兴寺", new Coordinate(114.287388, 22.625248));
        buildings.put("深圳龙岗COCOPark", new Coordinate(114.228435, 22.690977));
        buildings.put("横岗第一市场", new Coordinate(114.210363, 22.645739));
        buildings.put("金基吉祥广场写字楼", new Coordinate(114.237653, 22.71855));
        buildings.put("龙岗区中心医院医技楼", new Coordinate(114.282873, 22.735028));
        buildings.put("龙岗区实验学校", new Coordinate(114.230287, 22.724802));
        buildings.put("北京中医药大学深圳医院", new Coordinate(114.215546, 22.708213));
        buildings.put("龙岗区妇幼保健院", new Coordinate(114.234828, 22.728069));
        buildings.put("龙岗区科技馆", new Coordinate(114.247745, 22.717784));
        //buildings.put("22", new Coordinate(114.2133853,22.7196358));



        GeometryFactory geometryFactory = new GeometryFactory();

        HashMap jsonObject = new HashMap();

        List<HashMap> list1 = new ArrayList<>();
        for (JsonNode item : data) {
            if (item.has("contours")) {
                total++;
                ArrayNode contours = (ArrayNode) item.get("contours");
                Point[] coordinates = new Point[contours.size()];

                for (int i = 0; i < contours.size(); i++) {
                    ArrayNode point = (ArrayNode) contours.get(i);
                    coordinates[i] = WktUtil.toPoint(point.get(0).asDouble(), point.get(1).asDouble());
                }
                Polygon polygon = WktUtil.pointListToPolygon(Arrays.asList(coordinates));

                int count = 0;
                String name = "";
                Point point=null;
                String id = null;
                Double minHeight = null;
                Double maxHeight = null;
                Point valuePoint = null;
                for (Map.Entry<String, Coordinate> entry : buildings.entrySet()) {
                    if (count >1) {
                        break;
                    }
                    point = WktUtil.toPoint(entry.getValue().getX(), entry.getValue().getY());

                    if (polygon.contains(point) || polygon.covers(point)) {
                        name = entry.getKey();
                        JsonNode id1 = item.get("id");
                        id = id1.asText();
                        valuePoint =  WktUtil.toPoint(entry.getValue().getX(), entry.getValue().getY());
                        minHeight =item.get("min_height").asDouble();
                        maxHeight = item.get("max_height").asDouble();
                        count++;
                    }
                }
                if (count ==1) {
                    ((ObjectNode) item).put("name", name);
                    HashMap value = new HashMap();
                    value.put("id", id);

                    value.put("name", name);
                    value.put("point",WktUtil.toWkt(valuePoint) );
                    value.put("polygon",WktUtil.toWkt(polygon));
                    value.put("center", WktUtil.toWkt(WktUtil.toPoint(polygon.getCentroid().getCoordinate().getX(), polygon.getCentroid().getCoordinate().getY())));
                    value.put("minHeight",minHeight);
                    value.put("maxHeight",maxHeight);
                    //list1.add(value);
                    list.add(value);

                }
            }

        }
        //jsonObject.put(filePath.getFileName(), list1);

        //list.add(jsonObject);


        // 保存修改后的 JSON
       // mapper.writeValue(new File("/Users/<USER>/Downloads/test/"+outFileName+".json"), data);
    }
}