package com.deepinnet.spatiotemporalbusiness.service.impl;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.smartcity.service.client.CustomAreaClient;
import com.deepinnet.spatiotemporalplatform.smartcity.service.convert.AreaConvert;
import com.deepinnet.spatiotemporalplatform.model.area.AreaDTO;
import com.deepinnet.spatiotemporalplatform.model.area.AreaCondition;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.AreaDomainService;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.CircleLayerDomainService;
import com.deepinnet.spatiotemporalplatform.SpatiotemporalplatformApplication;
import com.deepinnet.spatiotemporalplatform.dal.daoservice.AreaService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = {SpatiotemporalplatformApplication.class})
@ActiveProfiles("local")
@Transactional
public class AreaDomainServiceImplIntegrationTest {

    @Resource
    private AreaDomainService areaDomainService;

    @Resource
    private AreaConvert areaConverter;

    @Resource
    private AreaService areaService;

    @Resource
    private CircleLayerDomainService circleLayerDomainService;

    @Resource
    private CustomAreaClient customAreaClient;

    private AreaDTO areaDTO;

    @BeforeEach
    public void setUp() {
        // 初始化测试数据
        areaDTO = new AreaDTO();
        areaDTO.setAreaName("Test Area");
        areaDTO.setGeo("POLYGON ((0 0, 1 0, 1 1, 0 1, 0 0))");
        areaDTO.setGeoBig("POLYGON ((0 0, 2 0, 2 2, 0 2, 0 0))");
    }

    @Test
    @Transactional
    @Rollback
    public void createArea_ValidDTO_CreatesRecord() {
        // 操作
        AreaDTO createdDTO = areaDomainService.createArea(areaDTO);

        // 验证
        assertNotNull(createdDTO.getAreaCode());
        assertEquals(areaDTO.getAreaName(), createdDTO.getAreaName());
        assertEquals(areaDTO.getGeo(), createdDTO.getGeo());
        assertEquals(areaDTO.getGeoBig(), createdDTO.getGeoBig());
    }

    @Test
    @Transactional
    @Rollback
    public void deleteArea_ExistingAreaCode_DeletesRecord() {
        // 准备
        AreaDTO createdDTO = areaDomainService.createArea(areaDTO);

        // 操作
        boolean result = areaDomainService.deleteArea(createdDTO.getAreaCode());

        // 验证
        assertTrue(result);
        AreaDTO retrievedDTO = areaDomainService.getAreaByCode(createdDTO.getAreaCode());
        assertNull(retrievedDTO);
    }

    @Test
    @Transactional
    @Rollback
    public void deleteArea_NonExistingAreaCode_ReturnsFalse() {
        // 操作
        boolean result = areaDomainService.deleteArea("nonExistingAreaCode");

        // 验证
        assertFalse(result);
    }

    @Test
    @Transactional
    @Rollback
    public void getAreaByCode_ExistingAreaCode_ReturnsDTO() {
        // 准备
        AreaDTO createdDTO = areaDomainService.createArea(areaDTO);

        // 操作
        AreaDTO retrievedDTO = areaDomainService.getAreaByCode(createdDTO.getAreaCode());

        // 验证
        assertNotNull(retrievedDTO);
        assertEquals(createdDTO.getAreaCode(), retrievedDTO.getAreaCode());
        assertEquals(createdDTO.getAreaName(), retrievedDTO.getAreaName());
        assertEquals(createdDTO.getGeo(), retrievedDTO.getGeo());
        assertEquals(createdDTO.getGeoBig(), retrievedDTO.getGeoBig());
    }

    @Test
    @Transactional
    @Rollback
    public void getAreaByCode_NonExistingAreaCode_ReturnsNull() {
        // 操作
        AreaDTO retrievedDTO = areaDomainService.getAreaByCode("nonExistingAreaCode");

        // 验证
        assertNull(retrievedDTO);
    }

    @Test
    @Transactional
    @Rollback
    public void pageArea_ValidCondition_ReturnsPage() {
        // 准备
        areaDomainService.createArea(areaDTO);

        // 操作
        AreaCondition condition = new AreaCondition();
        condition.setPageNum(1);
        condition.setPageSize(10);
        condition.setAreaName("Test Area");

        CommonPage<AreaDTO> page = areaDomainService.pageArea(condition);

        // 验证
        assertEquals(1, page.getPageNum());
        assertEquals(10, page.getPageSize());
        assertEquals(1, page.getTotalPage());
        assertEquals(1, page.getTotal());
        assertEquals(1, page.getList().size());
    }

    @Test
    @Transactional
    @Rollback
    public void pageArea_NoMatchingRecords_ReturnsEmptyPage() {
        // 操作
        AreaCondition condition = new AreaCondition();
        condition.setPageNum(1);
        condition.setPageSize(10);
        condition.setAreaName("nonExistingAreaName");

        CommonPage<AreaDTO> page = areaDomainService.pageArea(condition);

        // 验证
        assertEquals(1, page.getPageNum());
        assertEquals(10, page.getPageSize());
        assertEquals(1, page.getTotalPage());
        assertEquals(1, page.getTotal());
        assertEquals(1, page.getList().size());
    }

    @Test
    @Transactional
    @Rollback
    public void searchAreasByName_ExistingName_ReturnsList() {
        // 准备
        areaDomainService.createArea(areaDTO);

        // 操作
        List<AreaDTO> result = areaDomainService.searchAreasByName("Test Area");

        // 验证
        assertEquals(1, result.size());
        assertEquals(areaDTO.getAreaName(), result.get(0).getAreaName());
    }

    @Test
    @Transactional
    @Rollback
    public void searchAreasByName_NonExistingName_ReturnsEmptyList() {
        // 操作
        List<AreaDTO> result = areaDomainService.searchAreasByName("nonExistingAreaName");

        // 验证
        assertTrue(result.isEmpty());
    }

    @Test
    @Transactional
    @Rollback
    public void getAreaByName_ExistingName_ReturnsDTO() {
        // 准备
        areaDomainService.createArea(areaDTO);

        // 操作
        AreaDTO result = areaDomainService.getAreaByName("Test Area");

        // 验证
        assertNotNull(result);
        assertEquals(areaDTO.getAreaName(), result.getAreaName());
    }

    @Test
    @Transactional
    @Rollback
    public void getAreaByName_NonExistingName_ReturnsNull() {
        // 操作
        AreaDTO result = areaDomainService.getAreaByName("nonExistingAreaName");

        // 验证
        assertNull(result);
    }

    @Test
    @Transactional
    @Rollback
    public void getAreaByNameList_ExistingNames_ReturnsList() {
        // 准备
        areaDomainService.createArea(areaDTO);

        // 操作
        List<AreaDTO> result = areaDomainService.getAreaByNameList(Arrays.asList("Test Area"));

        // 验证
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(areaDTO.getAreaName(), result.get(0).getAreaName());
    }

    @Test
    @Transactional
    @Rollback
    public void getAreaByNameList_NonExistingNames_ReturnsNull() {
        // 操作
        List<AreaDTO> result = areaDomainService.getAreaByNameList(Arrays.asList("nonExistingAreaName"));

        // 验证
        assertNull(result);
    }

    @Test
    @Transactional
    @Rollback
    public void updateArea_ValidDTO_UpdatesRecord() {
        // 准备
        AreaDTO createdDTO = areaDomainService.createArea(areaDTO);
        createdDTO.setAreaName("Updated Test Area");

        // 操作
        AreaDTO updatedDTO = areaDomainService.updateArea(createdDTO);

        // 验证
        assertNotNull(updatedDTO);
        assertEquals("Updated Test Area", updatedDTO.getAreaName());
        assertEquals(createdDTO.getAreaCode(), updatedDTO.getAreaCode());
    }

    @Test
    @Transactional
    @Rollback
    public void updateArea_InvalidDTO_ThrowsException() {
        // 准备
        AreaDTO invalidDTO = new AreaDTO();
        invalidDTO.setAreaCode("nonExistingAreaCode");
        invalidDTO.setGeo("INVALID WKT");

        // 操作和验证
        Exception exception = assertThrows(RuntimeException.class, () -> {
            areaDomainService.updateArea(invalidDTO);
        });

        // 验证异常信息
        assertTrue(exception.getMessage().contains("geo数据不合法"));
    }
}
