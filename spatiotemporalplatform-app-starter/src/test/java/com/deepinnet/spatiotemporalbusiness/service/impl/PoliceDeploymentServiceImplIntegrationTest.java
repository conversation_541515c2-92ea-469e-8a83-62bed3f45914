package com.deepinnet.spatiotemporalbusiness.service.impl;

import com.deepinnet.spatiotemporalplatform.smartcity.service.convert.CommonBaseServiceConvert;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.PoliceDeploymentService;
import com.deepinnet.spatiotemporalplatform.SpatiotemporalplatformApplication;
import com.deepinnet.spatiotemporalplatform.model.police.PoliceDeployment;
import com.deepinnet.spatiotemporalplatform.model.police.PoliceDeploymentDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.deepinnet.spatiotemporalplatform.common.enums.ObjectType.CONTINGENCY_PLAN;
import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = {SpatiotemporalplatformApplication.class})
@ActiveProfiles("local")
public class PoliceDeploymentServiceImplIntegrationTest {

    @Resource
    private PoliceDeploymentService policeDeploymentService;

    @Resource
    private CommonBaseServiceConvert commonBaseServiceConvert;

    private PoliceDeploymentDTO deploymentDTO;
    private PoliceDeployment deployment;

    @BeforeEach
    public void setUp() {
        // 初始化测试数据
        deploymentDTO = new PoliceDeploymentDTO();
        deploymentDTO.setAreaCode("areaCode");
        deploymentDTO.setObjectType(CONTINGENCY_PLAN.getCode());
        deploymentDTO.setObjectCode("objectCode");
        deploymentDTO.setOfficerName("officerName");
        deploymentDTO.setOfficerId("officerId");
        deploymentDTO.setPoliceType("policeType");
        deploymentDTO.setStartTime(System.currentTimeMillis());
        deploymentDTO.setEndTime(System.currentTimeMillis());
        deploymentDTO.setCount(1);
        deploymentDTO.setRemark("remark");
        deploymentDTO.setOrgName("orgName");


        deployment = commonBaseServiceConvert.toPoliceDeployment(deploymentDTO);
    }

    @Test
    @Transactional
    @Rollback
    public void listByObjCode_ExistingObjCode_ReturnsList() {
        // 准备
        policeDeploymentService.create(deploymentDTO);

        // 操作
        List<PoliceDeployment> result = policeDeploymentService.listByObjCode(deploymentDTO.getObjectCode());

        // 验证
        assertEquals(1, result.size());
    }

    @Test
    @Transactional
    @Rollback
    public void listByObjCode_NonExistingObjCode_ReturnsEmptyList() {
        // 操作
        List<PoliceDeployment> result = policeDeploymentService.listByObjCode("nonExistingObjCode");

        // 验证
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    @Transactional
    @Rollback
    public void listByObjCodeList_ExistingObjCodes_ReturnsList() {
        // 准备
        policeDeploymentService.create(deploymentDTO);

        // 操作
        List<PoliceDeployment> result = policeDeploymentService.listByObjCodeList(Collections.singletonList(deploymentDTO.getObjectCode()));

        // 验证
        assertEquals(1, result.size());
    }


    @Test
    @Transactional
    @Rollback
    public void deleteByObjCode_ExistingObjCode_DeletesRecord() {
        // 准备
        policeDeploymentService.create(deploymentDTO);

        // 操作
        policeDeploymentService.deleteByObjCode(deploymentDTO.getObjectCode());

        // 验证
        List<PoliceDeployment> result = policeDeploymentService.listByObjCode(deploymentDTO.getObjectCode());
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    @Transactional
    @Rollback
    public void deleteByObjCode_NonExistingObjCode_NoDeletion() {
        // 操作
        policeDeploymentService.deleteByObjCode("nonExistingObjCode");

        // 验证
        List<PoliceDeployment> result = policeDeploymentService.listByObjCode("nonExistingObjCode");
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    @Transactional
    @Rollback
    public void batchSave_NonEmptyList_SavesRecords() {
        // 准备
        List<PoliceDeploymentDTO> deploymentDTOList = new ArrayList<>();
        deploymentDTOList.add(deploymentDTO);

        // 操作
        policeDeploymentService.batchSave(deploymentDTOList);

        // 验证
        List<PoliceDeployment> result = policeDeploymentService.listByObjCode(deploymentDTO.getObjectCode());
        assertEquals(1, result.size());
    }

    @Test
    @Transactional
    @Rollback
    public void batchSave_EmptyList_NoChange() {
        // 操作
        policeDeploymentService.batchSave(Collections.emptyList());

        // 验证
        List<PoliceDeployment> result = policeDeploymentService.listByObjCode(deploymentDTO.getObjectCode());
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    @Transactional
    @Rollback
    public void assignToObject_NonEmptyList_AssignsRecords() {
        // 准备
        policeDeploymentService.create(deploymentDTO);
        List<PoliceDeployment> deploymentList = new ArrayList<>();
        deploymentList.add(deployment);

        // 操作
        policeDeploymentService.assignToObject(deploymentList, deploymentDTO.getObjectCode());

        // 验证
        List<PoliceDeployment> result = policeDeploymentService.listByObjCode(deploymentDTO.getObjectCode());
        assertEquals(1, result.size());
    }

    @Test
    @Transactional
    @Rollback
    public void assignToObject_EmptyList_DeletesRecords() {
        // 准备
        policeDeploymentService.create(deploymentDTO);

        // 操作
        policeDeploymentService.assignToObject(Collections.emptyList(), deploymentDTO.getObjectCode());

        // 验证
        List<PoliceDeployment> result = policeDeploymentService.listByObjCode(deploymentDTO.getObjectCode());
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    @Transactional
    @Rollback
    public void create_ValidDTO_CreatesRecord() {
        // 操作
        PoliceDeploymentDTO createdDTO = policeDeploymentService.create(deploymentDTO);

        // 验证
        assertNotNull(createdDTO.getId());
        assertEquals(deploymentDTO.getAreaCode(), createdDTO.getAreaCode());
        assertEquals(deploymentDTO.getObjectType(), createdDTO.getObjectType());
        assertEquals(deploymentDTO.getObjectCode(), createdDTO.getObjectCode());
    }

    @Test
    @Transactional
    @Rollback
    public void list_ValidParameters_ReturnsList() {
        // 准备
        policeDeploymentService.create(deploymentDTO);

        // 操作
        List<PoliceDeployment> result = policeDeploymentService.list(deploymentDTO.getAreaCode(), deploymentDTO.getObjectType(), deploymentDTO.getObjectCode());

        // 验证
        assertEquals(1, result.size());
    }

    @Test
    @Transactional
    @Rollback
    public void list_NoMatchingRecords_ReturnsEmptyList() {
        // 操作
        List<PoliceDeployment> result = policeDeploymentService.list("nonExistingAreaCode", "nonExistingObjectType", "nonExistingObjectCode");

        // 验证
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    @Transactional
    @Rollback
    public void listByAreaCodeAndPlanCodeList_ValidParameters_ReturnsList() {
        // 准备
        policeDeploymentService.create(deploymentDTO);
        List<String> planCodeList = Collections.singletonList(deploymentDTO.getObjectCode());

        // 操作
        List<PoliceDeployment> result = policeDeploymentService.listByAreaCodeAndPlanCodeList(deploymentDTO.getAreaCode(), planCodeList);

        // 验证
        assertEquals(1, result.size());
    }

    @Test
    @Transactional
    @Rollback
    public void listByAreaCodeAndPlanCodeList_NoMatchingRecords_ReturnsEmptyList() {
        // 操作
        List<PoliceDeployment> result = policeDeploymentService.listByAreaCodeAndPlanCodeList("nonExistingAreaCode", Collections.singletonList("nonExistingPlanCode"));

        // 验证
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    @Transactional
    @Rollback
    public void delete_ExistingId_DeletesRecord() {
        // 准备
        PoliceDeploymentDTO createdDTO = policeDeploymentService.create(deploymentDTO);

        // 操作
        boolean result = policeDeploymentService.delete(createdDTO.getId());

        // 验证
        assertTrue(result);
        List<PoliceDeployment> resultList = policeDeploymentService.listByObjCode(deploymentDTO.getObjectCode());
        assertEquals(Collections.emptyList(), resultList);
    }

    @Test
    @Transactional
    @Rollback
    public void delete_NonExistingId_ReturnsFalse() {
        // 操作
        boolean result = policeDeploymentService.delete(999999);

        // 验证
        assertFalse(result);
    }

    @Test
    @Transactional
    @Rollback
    public void deleteByIds_NonEmptyList_DeletesRecords() {
        // 准备
        PoliceDeploymentDTO createdDTO = policeDeploymentService.create(deploymentDTO);
        List<Integer> ids = Collections.singletonList(createdDTO.getId());

        // 操作
        policeDeploymentService.deleteByIds(ids);

        // 验证
        List<PoliceDeployment> resultList = policeDeploymentService.listByObjCode(deploymentDTO.getObjectCode());
        assertEquals(Collections.emptyList(), resultList);
    }

    @Test
    @Transactional
    @Rollback
    public void deleteByIds_EmptyList_NoChange() {
        // 操作
        policeDeploymentService.deleteByIds(Collections.emptyList());

        // 验证
        List<PoliceDeployment> resultList = policeDeploymentService.listByObjCode(deploymentDTO.getObjectCode());
        assertEquals(Collections.emptyList(), resultList);
    }

    @Test
    @Transactional
    @Rollback
    public void deleteByObjCodeAndType_ExistingPlanCodeAndType_DeletesRecords() {
        // 准备
        policeDeploymentService.create(deploymentDTO);

        // 操作
        policeDeploymentService.deleteByObjCodeAndType(deploymentDTO.getObjectCode(), deploymentDTO.getObjectType());

        // 验证
        List<PoliceDeployment> resultList = policeDeploymentService.listByObjCode(deploymentDTO.getObjectCode());
        assertEquals(Collections.emptyList(), resultList);
    }

    @Test
    @Transactional
    @Rollback
    public void deleteByObjCodeAndType_NonExistingPlanCodeAndType_NoDeletion() {
        // 操作
        policeDeploymentService.deleteByObjCodeAndType("nonExistingPlanCode", "nonExistingType");

        // 验证
        List<PoliceDeployment> resultList = policeDeploymentService.listByObjCode(deploymentDTO.getObjectCode());
        assertEquals(Collections.emptyList(), resultList);
    }

    @Test
    @Transactional
    @Rollback
    public void listRealTimeByAreaCodeAndObjectCode_EmptyList_ReturnsEmptyList() {
        // 操作
        List<PoliceDeployment> result = policeDeploymentService.listRealTimeByAreaCodeAndObjectCode(deploymentDTO.getAreaCode(), deploymentDTO.getObjectType(), deploymentDTO.getObjectCode());

        // 验证
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    @Transactional
    @Rollback
    public void listRealTimeByAreaCodeAndObjectCode_NonEmptyList_ReturnsList() {
        // 准备
        policeDeploymentService.create(deploymentDTO);

        // 操作
        List<PoliceDeployment> result = policeDeploymentService.listRealTimeByAreaCodeAndObjectCode(deploymentDTO.getAreaCode(), deploymentDTO.getObjectType(), deploymentDTO.getObjectCode());

        // 验证
        assertEquals(1, result.size());
    }
}
