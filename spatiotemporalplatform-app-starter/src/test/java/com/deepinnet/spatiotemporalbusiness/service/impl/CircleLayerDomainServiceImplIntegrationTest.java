package com.deepinnet.spatiotemporalbusiness.service.impl;

import com.deepinnet.spatiotemporalplatform.model.area.CircleLayerBatchDTO;
import com.deepinnet.spatiotemporalplatform.model.area.CircleLayerDTO;
import com.deepinnet.spatiotemporalplatform.smartcity.service.service.CircleLayerDomainService;
import com.deepinnet.spatiotemporalplatform.SpatiotemporalplatformApplication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest(classes = {SpatiotemporalplatformApplication.class})
@ActiveProfiles("local")
@Transactional
class CircleLayerDomainServiceImplIntegrationTest {

    @Resource
    private CircleLayerDomainService circleLayerDomainService;

    @BeforeEach
    void setUp() {
        // 可以在这里进行一些初始化操作，例如清理数据库等
    }

    @Test
    void testCreateCircleLayer() {
        // Arrange
        CircleLayerDTO circleLayerDTO = new CircleLayerDTO();
        circleLayerDTO.setAreaCode("testAreaCode");
        circleLayerDTO.setLayerName("Test Circle Layer");

        // Act
        CircleLayerDTO createdCircleLayer = circleLayerDomainService.createCircleLayer(circleLayerDTO);

        // Assert
        assertNotNull(createdCircleLayer);
        assertNotNull(createdCircleLayer.getId());
        assertEquals("Test Circle Layer", createdCircleLayer.getLayerName());
    }

    @Test
    void testDeleteCircleLayer() {
        // Arrange
        CircleLayerDTO circleLayerDTO = new CircleLayerDTO();
        circleLayerDTO.setAreaCode("testAreaCode");
        circleLayerDTO.setLayerName("Test Circle Layer");
        CircleLayerDTO createdCircleLayer = circleLayerDomainService.createCircleLayer(circleLayerDTO);
        assertNotNull(createdCircleLayer);

        // Act
        boolean result = circleLayerDomainService.deleteCircleLayer(createdCircleLayer.getLayerId());

        // Assert
        assertTrue(result);
        assertNull(circleLayerDomainService.getCircleLayerByLayerId(createdCircleLayer.getLayerId()));
    }

    @Test
    void testGetCircleLayerByLayerId() {
        // Arrange
        CircleLayerDTO circleLayerDTO = new CircleLayerDTO();
        circleLayerDTO.setAreaCode("testAreaCode");
        circleLayerDTO.setLayerName("Test Circle Layer");
        CircleLayerDTO createdCircleLayer = circleLayerDomainService.createCircleLayer(circleLayerDTO);
        assertNotNull(createdCircleLayer);

        // Act
        CircleLayerDTO retrievedCircleLayer = circleLayerDomainService.getCircleLayerByLayerId(createdCircleLayer.getLayerId());

        // Assert
        assertNotNull(retrievedCircleLayer);
        assertEquals(createdCircleLayer.getId(), retrievedCircleLayer.getId());
    }

    @Test
    void testGetAllCircleLayers() {
        // Arrange
        CircleLayerDTO circleLayerDTO1 = new CircleLayerDTO();
        circleLayerDTO1.setAreaCode("testAreaCode1");
        circleLayerDTO1.setLayerName("Test Circle Layer 1");
        circleLayerDomainService.createCircleLayer(circleLayerDTO1);

        CircleLayerDTO circleLayerDTO2 = new CircleLayerDTO();
        circleLayerDTO2.setAreaCode("testAreaCode2");
        circleLayerDTO2.setLayerName("Test Circle Layer 2");
        circleLayerDomainService.createCircleLayer(circleLayerDTO2);

        // Act
        List<CircleLayerDTO> allCircleLayers = circleLayerDomainService.getAllCircleLayers(1, 10);

        // Assert
        assertNotNull(allCircleLayers);
        assertEquals(2, allCircleLayers.size());
    }

    @Test
    void testListByAreaCodeList() {
        // Arrange
        CircleLayerDTO circleLayerDTO1 = new CircleLayerDTO();
        circleLayerDTO1.setAreaCode("testAreaCode1");
        circleLayerDTO1.setLayerName("Test Circle Layer 1");
        circleLayerDomainService.createCircleLayer(circleLayerDTO1);

        CircleLayerDTO circleLayerDTO2 = new CircleLayerDTO();
        circleLayerDTO2.setAreaCode("testAreaCode2");
        circleLayerDTO2.setLayerName("Test Circle Layer 2");
        circleLayerDomainService.createCircleLayer(circleLayerDTO2);

        List<String> areaCodeList = List.of("testAreaCode1", "testAreaCode2");

        // Act
        List<CircleLayerDTO> circleLayers = circleLayerDomainService.listByAreaeCodeList(areaCodeList);

        // Assert
        assertNotNull(circleLayers);
        assertEquals(2, circleLayers.size());
    }

    @Test
    void testBatchCreateOrUpdateCircleLayer() {
        // Arrange
        CircleLayerBatchDTO batchDTO = new CircleLayerBatchDTO();
        batchDTO.setAreaCode("testAreaCode");

        List<CircleLayerDTO> circleLayers = new ArrayList<>();
        CircleLayerDTO layer1 = new CircleLayerDTO();
        layer1.setLayerName("Test Circle Layer 1");
        circleLayers.add(layer1);

        CircleLayerDTO layer2 = new CircleLayerDTO();
        layer2.setLayerName("Test Circle Layer 2");
        circleLayers.add(layer2);

        batchDTO.setCircleLayers(circleLayers);

        // Act
        List<CircleLayerDTO> createdCircleLayers = circleLayerDomainService.batchCreateOrUpdateCircleLayer(batchDTO);

        // Assert
        assertNotNull(createdCircleLayers);
        assertEquals(2, createdCircleLayers.size());
    }

    @Test
    void testUpdateCircleLayer() {
        // Arrange
        CircleLayerDTO circleLayerDTO = new CircleLayerDTO();
        circleLayerDTO.setAreaCode("testAreaCode");
        circleLayerDTO.setLayerName("Test Circle Layer");
        CircleLayerDTO createdCircleLayer = circleLayerDomainService.createCircleLayer(circleLayerDTO);
        assertNotNull(createdCircleLayer);

        // Act
        createdCircleLayer.setLayerName("Updated Test Circle Layer");
        CircleLayerDTO updatedCircleLayer = circleLayerDomainService.updateCircleLayer(createdCircleLayer);

        // Assert
        assertNotNull(updatedCircleLayer);
        assertEquals("Updated Test Circle Layer", updatedCircleLayer.getLayerName());
    }
}