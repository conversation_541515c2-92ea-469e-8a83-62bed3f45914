package com.deepinnet;

import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;

public class JasyptDecryptor {

    public static String decrypt(String encryptedText, String password) {
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();

        config.setPassword(password);
        config.setAlgorithm("PBEWithMD5AndDES");
        config.setKeyObtentionIterations("1000");
        config.setPoolSize("1");
        config.setProviderName("SunJCE");

        // 这里必须和加密时的配置一致
        config.setIvGeneratorClassName("org.jasypt.iv.NoIvGenerator");
        config.setStringOutputType("base64");

        encryptor.setConfig(config);
        return encryptor.decrypt(encryptedText);
    }

    public static void main(String[] args) {
        String encryptedText = "miyi4KUBRWgcx4NhYmMqOq3lFb20I9o+"; // 替换为加密后的文本
        String password = "shendu188";  // 确保和加密时使用的密码一致

        try {
            String decryptedText = decrypt(encryptedText, password);
            System.out.println("解密结果：" + decryptedText);
        } catch (Exception e) {
            System.err.println("解密失败：" + e.getMessage());
        }
    }
}