#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
登录接口监控脚本 (Python版本)
功能：
1. 每10秒调用一次登录接口
2. 记录详细的错误信息和堆栈
3. 提供实时统计和分析
4. 支持邮件/钉钉通知（可选）
"""

import requests
import time
import json
import logging
import traceback
import signal
import sys
from datetime import datetime
from typing import Dict, Any, Optional
import threading
from collections import defaultdict

class LoginAPIMonitor:
    def __init__(self):
        self.api_url = "http://192.168.3.200:8056/api/user/login"
        self.interval = 10  # 秒
        self.success_count = 0
        self.error_count = 0
        self.error_types = defaultdict(int)
        self.start_time = datetime.now()
        self.running = True
        
        # 设置日志
        self.setup_logging()
        
        # 请求配置
        self.headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json',
            'Origin': 'http://192.168.3.200:8056',
            'Referer': 'http://192.168.3.200:8056/login',
            'Tenant-Id': 'sz_unifly',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
        }
        
        self.payload = {
            "account": "***********",
            "password": "6b86b273ff34fce19d6b804eff5a3f5747ada4eaa22f1d49c01e52ddb7875b4b",
            "scene": "matching_platform"
        }
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def setup_logging(self):
        """设置日志配置"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建logger
        self.logger = logging.getLogger('LoginAPIMonitor')
        self.logger.setLevel(logging.INFO)
        
        # 创建文件处理器
        file_handler = logging.FileHandler(f'login_api_monitor_{timestamp}.log', encoding='utf-8')
        error_handler = logging.FileHandler(f'login_api_errors_{timestamp}.log', encoding='utf-8')
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        
        # 设置日志格式
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        error_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 设置错误处理器只记录ERROR级别
        error_handler.setLevel(logging.ERROR)
        
        # 添加处理器
        self.logger.addHandler(file_handler)
        self.logger.addHandler(error_handler)
        self.logger.addHandler(console_handler)
    
    def send_login_request(self) -> Dict[str, Any]:
        """发送登录请求"""
        try:
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json=self.payload,
                timeout=30,
                verify=False  # 对应curl的--insecure
            )
            
            result = {
                'success': False,
                'http_code': response.status_code,
                'response_time': response.elapsed.total_seconds(),
                'response_body': None,
                'error_type': None,
                'error_message': None,
                'stack_trace': None
            }
            
            # 记录响应时间
            if response.elapsed.total_seconds() > 5:
                self.logger.warning(f"响应时间较长: {response.elapsed.total_seconds():.2f}秒")
            
            # 检查HTTP状态码
            if response.status_code != 200:
                result['error_type'] = 'HTTP_ERROR'
                result['error_message'] = f"HTTP状态码异常: {response.status_code}"
                result['response_body'] = response.text
                return result
            
            # 解析响应内容
            try:
                response_data = response.json()
                result['response_body'] = response_data
                
                # 检查业务逻辑错误
                if not response_data.get('success', True):
                    result['error_type'] = 'BUSINESS_ERROR'
                    result['error_message'] = response_data.get('message', '业务逻辑错误')
                    return result
                
                # 检查错误代码
                if 'code' in response_data and response_data['code'] != 0:
                    result['error_type'] = 'BUSINESS_ERROR'
                    result['error_message'] = f"错误代码: {response_data['code']}, 消息: {response_data.get('message', '')}"
                    return result
                
                # 成功
                result['success'] = True
                return result
                
            except json.JSONDecodeError as e:
                result['error_type'] = 'JSON_PARSE_ERROR'
                result['error_message'] = f"JSON解析失败: {str(e)}"
                result['response_body'] = response.text
                result['stack_trace'] = traceback.format_exc()
                return result
                
        except requests.exceptions.Timeout:
            result = {
                'success': False,
                'error_type': 'TIMEOUT_ERROR',
                'error_message': '请求超时',
                'stack_trace': traceback.format_exc()
            }
            return result
            
        except requests.exceptions.ConnectionError as e:
            result = {
                'success': False,
                'error_type': 'CONNECTION_ERROR',
                'error_message': f'连接错误: {str(e)}',
                'stack_trace': traceback.format_exc()
            }
            return result
            
        except Exception as e:
            result = {
                'success': False,
                'error_type': 'UNKNOWN_ERROR',
                'error_message': f'未知错误: {str(e)}',
                'stack_trace': traceback.format_exc()
            }
            return result
    
    def log_result(self, request_num: int, result: Dict[str, Any]):
        """记录请求结果"""
        if result['success']:
            self.success_count += 1
            response_time = result.get('response_time', 0)
            self.logger.info(f"第{request_num}次请求成功 (响应时间: {response_time:.2f}s)")
        else:
            self.error_count += 1
            error_type = result.get('error_type', 'UNKNOWN')
            self.error_types[error_type] += 1
            
            error_msg = f"第{request_num}次请求失败 - {error_type}: {result.get('error_message', '')}"
            self.logger.error(error_msg)
            
            # 记录详细错误信息
            if result.get('response_body'):
                self.logger.error(f"响应内容: {json.dumps(result['response_body'], ensure_ascii=False, indent=2)}")
            
            if result.get('stack_trace'):
                self.logger.error(f"堆栈信息:\n{result['stack_trace']}")
    
    def show_stats(self):
        """显示统计信息"""
        total = self.success_count + self.error_count
        success_rate = (self.success_count / total * 100) if total > 0 else 0
        runtime = datetime.now() - self.start_time
        
        print("\n" + "="*50)
        print("统计信息")
        print("="*50)
        print(f"运行时间: {runtime}")
        print(f"总请求数: {total}")
        print(f"成功次数: {self.success_count}")
        print(f"失败次数: {self.error_count}")
        print(f"成功率: {success_rate:.2f}%")
        
        if self.error_types:
            print("\n错误类型分布:")
            for error_type, count in self.error_types.items():
                percentage = (count / self.error_count * 100) if self.error_count > 0 else 0
                print(f"  {error_type}: {count} ({percentage:.1f}%)")
        
        print("="*50)
    
    def signal_handler(self, signum, frame):
        """信号处理函数"""
        print(f"\n收到信号 {signum}，正在停止监控...")
        self.running = False
    
    def run(self):
        """运行监控"""
        print("=== 登录接口监控脚本 (Python版) ===")
        print(f"目标URL: {self.api_url}")
        print(f"监控间隔: {self.interval}秒")
        print("按 Ctrl+C 停止监控\n")
        
        self.logger.info("开始监控登录接口")
        
        request_count = 0
        consecutive_errors = 0
        
        try:
            while self.running:
                request_count += 1
                
                print(f"\n--- 第 {request_count} 次请求 ---")
                
                # 发送请求
                result = self.send_login_request()
                
                # 记录结果
                self.log_result(request_count, result)
                
                # 更新连续错误计数
                if result['success']:
                    consecutive_errors = 0
                else:
                    consecutive_errors += 1
                
                # 显示当前统计
                total = self.success_count + self.error_count
                success_rate = (self.success_count / total * 100) if total > 0 else 0
                print(f"成功: {self.success_count} | 失败: {self.error_count} | 成功率: {success_rate:.1f}%")
                
                # 连续失败警告
                if consecutive_errors >= 5:
                    warning_msg = f"连续失败 {consecutive_errors} 次，请检查服务状态！"
                    print(f"⚠️  {warning_msg}")
                    self.logger.warning(warning_msg)
                
                # 等待下次请求
                if self.running:
                    print(f"等待 {self.interval} 秒...")
                    time.sleep(self.interval)
                    
        except KeyboardInterrupt:
            print("\n用户中断监控")
        finally:
            self.show_stats()
            self.logger.info("监控结束")

def main():
    monitor = LoginAPIMonitor()
    monitor.run()

if __name__ == "__main__":
    main()
