package com.deepinnet.infra.web.annotation;


import com.deepinnet.infra.web.enums.*;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @version 2022/12/28
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
@Documented
public @interface UserOperate {

    /**
     * 接口操作名，如操作名称在OperationNameEnum中不存在，请先添加后继续操作
     *
     * @return 接口操作名枚举类
     */
    OperationNameEnum operationName();

    /**
     * 操作类型(SELECT, UPDATE, INSERT, DELETE)
     *
     * @return 接口操作类型枚举类
     */
    OperationTypeEnum operationType();

}
