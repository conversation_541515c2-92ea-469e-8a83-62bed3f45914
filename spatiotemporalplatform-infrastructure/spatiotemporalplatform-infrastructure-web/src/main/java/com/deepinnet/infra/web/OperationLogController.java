package com.deepinnet.infra.web;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.service.log.OperationLogService;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR> wong
 * @create 2024/8/8 16:11
 * @Description
 */
@RestController
@Api(tags = "操作日志服务")
@Validated
@RequestMapping("/domain/operation/log")
public class OperationLogController {

    @Resource
    private OperationLogService operationLogService;



    @PostMapping("/page")
    @ApiOperation("分页查询操作日志")
    public Result<CommonPage<OperationLogDTO>> pageQueryOperationLog(@Valid @RequestBody OperationLogQueryDTO queryDTO) {
        return Result.success(operationLogService.pageQueryOperationLog(queryDTO));
    }
}
