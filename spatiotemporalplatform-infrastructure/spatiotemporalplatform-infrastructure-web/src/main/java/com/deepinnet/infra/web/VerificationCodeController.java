package com.deepinnet.infra.web;

import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.service.VerificationCodeService;
import io.swagger.annotations.*;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 验证码控制器
 *
 * <AUTHOR> wong
 * @create 2025/04/18
 */
@RestController
@RequestMapping("/domain/sms/code")
@Api(tags = "验证码接口")
public class VerificationCodeController {

    @Resource
    private VerificationCodeService verificationCodeService;

    /**
     * 发送验证码
     *
     * @param requestDTO 请求参数
     * @return 发送结果
     */
    @ApiOperation("发送验证码")
    @PostMapping("/send")
    public Result<SendSmsResultDTO> sendVerificationCode(@Valid @RequestBody VerifyCodeRequestDTO requestDTO) {
        LogUtil.info("接收到发送验证码请求，手机号：[{}]", requestDTO.getPhone());
        return verificationCodeService.sendVerificationCode(requestDTO.getPhone(), requestDTO.getBizType(), requestDTO.getScene());
    }

    /**
     * 验证验证码
     *
     * @param requestDTO 请求参数
     * @return 验证结果
     */
    @ApiOperation("验证验证码")
    @PostMapping("/verify")
    public Result<Boolean> verifyCode(@RequestBody VerifyCodeRequestDTO requestDTO) {
        LogUtil.info("接收到验证码校验请求，手机号：[{}]，验证码：[{}]", requestDTO.getPhone(), requestDTO.getCode());
        return verificationCodeService.verifyCode(requestDTO.getPhone(), requestDTO.getCode(), requestDTO.getBizType(), requestDTO.getScene());
    }
} 