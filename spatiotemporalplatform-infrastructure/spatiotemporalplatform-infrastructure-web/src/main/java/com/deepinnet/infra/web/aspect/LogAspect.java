package com.deepinnet.infra.web.aspect;

/**
 * <AUTHOR>
 * @version 2022/12/28
 */
//@Aspect
//@Component
public class LogAspect {

//    @Resource
//    private UserOperationRepository userOperationRepository;
//
//    @Pointcut("@annotation(com.deepinnet.infra.web.annotation.UserOperate)")
//    public void logPointCut() {
//    }
//
//    @Around("logPointCut()")
//    public Object aroundLog(ProceedingJoinPoint joinPoint) throws Throwable {
//        UserOperationDO userOperationDO = new UserOperationDO();
//        String allArgsJson = null;
//        boolean isSuccess = true;
//        Exception exception = null;
//        Object result = null;
//
//        try {
//            result = joinPoint.proceed();
//        } catch (Exception e) {
//            isSuccess = false;
//            exception = e;
//        }
//
//        try {
//            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
//            String ip = ServletUtil.getClientIP(request);
//            String accountNo = StpUtil.getLoginIdAsString();
//            String userName = (String) StpUtil.getExtra("name");
//            String userNo = (String) StpUtil.getExtra("userNo");
//
//            UserOperate userOperate = getAnnotationLog(joinPoint);
//            String operationType = userOperate.operationType().getType();
//            String operationName = userOperate.operationName().getName();
//
//            Object[] args = joinPoint.getArgs();
//            if (ObjectUtil.isNotEmpty(args)) {
//                Object arg = args[0];
//                if (arg instanceof MultipartFile) {
//                    MultipartFile multipartFile = (MultipartFile) arg;
//                    allArgsJson = JSONUtil.toJsonStr(multipartFile.getOriginalFilename());
//                } else {
//                    allArgsJson = JSONUtil.toJsonStr(Arrays.asList(joinPoint.getArgs()));
//                }
//            }
//
//            userOperationDO.setUserNo(userNo);
//            userOperationDO.setAccountNo(accountNo);
//            userOperationDO.setUserName(userName);
//            userOperationDO.setIp(ip);
//            userOperationDO.setType(operationType);
//            userOperationDO.setOperationName(operationName);
//            userOperationDO.setArgs(allArgsJson);
//            userOperationDO.setSuccess(isSuccess);
//            if (!isSuccess) {
//                userOperationDO.setTraceId(TraceIdUtil.getTraceId());
//            }
//            userOperationRepository.save(userOperationDO);
//        } catch (Exception e) {
//            LogUtil.error("保存用户操作记录失败，堆栈为：{}", e);
//        }
//
//        if (exception != null) {
//            throw exception;
//        }
//
//        return result;
//    }
//
//
//    /**
//     * 是否存在注解，如果存在就获取
//     */
//    private static UserOperate getAnnotationLog(JoinPoint joinPoint) throws Exception {
//        Signature signature = joinPoint.getSignature();
//        MethodSignature methodSignature = (MethodSignature) signature;
//        Method method = methodSignature.getMethod();
//        if (method != null) {
//            return method.getAnnotation(UserOperate.class);
//        }
//        return null;
//    }

}
