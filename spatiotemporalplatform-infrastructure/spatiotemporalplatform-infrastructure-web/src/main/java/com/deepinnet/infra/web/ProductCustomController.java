package com.deepinnet.infra.web;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.api.vo.CustomerInfoVO;
import com.deepinnet.infra.service.ProductCustomerService;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 客户单位信息控制器
 *
 * <AUTHOR> wong
 * @create 2025/04/22
 */
@RestController
@RequestMapping("/user/customer")
@Api(tags = "客户单位信息接口")
public class ProductCustomController {

    @Resource
    private ProductCustomerService productCustomerService;

    /**
     * 保存客户单位信息
     *
     * @param customerInfoDTO 客户单位信息
     * @return 处理结果
     */
    @ApiOperation("保存客户单位信息")
    @PostMapping("/save")
    public Result<String> saveCustomerInfo(@Valid @RequestBody CustomerInfoDTO customerInfoDTO) {
        return productCustomerService.saveCustomerInfo(customerInfoDTO);
    }

    /**
     * 获取客户单位详情
     *
     * @return 客户单位详情
     */
    @ApiOperation("获取客户单位详情")
    @PostMapping("/detail")
    public Result<CustomerDetailDTO> getCustomerDetail(@RequestBody CustomerQueryDetailDTO queryDetailDTO) {
        return Result.success(productCustomerService.getCustomerDetail(queryDetailDTO));
    }


    /**
     * 分页查询客户列表
     * 支持按照以下条件查询：
     * 1. 登录手机号码（phone）
     * 2. 单位名称（organizationName）
     * 3. 联系人姓名（contactName）
     * 4. 联系人电话（contactPhone）
     *
     * @param queryDTO 查询条件
     * @return 客户列表分页数据
     */
    @ApiOperation(value = "分页查询客户列表", notes = "支持按登录手机号、单位名称、联系人姓名、联系人电话进行筛选")
    @PostMapping("/page")
    public Result<CommonPage<CustomerInfoVO>> pageQueryCustomers(@RequestBody @Validated CustomerQueryDTO queryDTO) {
        return productCustomerService.pageQueryCustomers(queryDTO);
    }
}