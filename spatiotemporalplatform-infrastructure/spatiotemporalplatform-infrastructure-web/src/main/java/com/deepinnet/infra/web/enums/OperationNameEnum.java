package com.deepinnet.infra.web.enums;

/**
 * <AUTHOR>
 * @version 2022/12/28
 */
public enum OperationNameEnum {

    SAVE_ACCOUNT("保存账号"),

    DELETE_ACCOUNT("删除账号"),

    SAVE_OR_UPDATE_ROLE("保存或者更新角色"),

    DELETE_ROLE("删除角色"),

    LOGIN("登录"),

    CHANGE_PASSWORD("修改密码"),

    FORGET_PASSWORD("忘记密码"),

    LOGOUT("注销"),

    ;

    private String name;

    OperationNameEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
