package com.deepinnet.infra.web;

import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.service.DepartmentService;
import com.deepinnet.infra.service.enums.PermissionSystemEnum;
import com.deepinnet.infra.service.error.BizErrorCode;
import com.deepinnet.infra.service.util.DataPermissionProcessor;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2024/11/26 15:04
 * @Description
 */
@RestController
@RequestMapping("/domain/admin/department")
@Api(tags = "部门服务")
public class DepartmentController {

    @Resource
    private DepartmentService departmentService;

    @Resource
    private DataPermissionProcessor dataPermissionProcessor;

    @PostMapping("/save")
    @ApiOperation("保存部门")
    public Result<Long> saveDepartment(@RequestBody DepartmentSaveDTO saveDTO) {
        if (saveDTO == null || StringUtils.isBlank(saveDTO.getName())) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        if (saveDTO.getName().length() > 20) {
            throw new BizException(BizErrorCode.LENGTH_EXCEEDS_LIMIT.getCode(), "组织名称不能超过20个字");
        }

        return Result.success(departmentService.saveDepartment(saveDTO));
    }

    @PostMapping("/add/member")
    @ApiOperation("添加或者更新部门成员")
    public Result<Boolean> addMember(@RequestBody DepartmentAddMemberDTO addMemberDTO) {
        if (addMemberDTO == null || StringUtils.isBlank(addMemberDTO.getName())) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        if (addMemberDTO.getName().length() > 20) {
            throw new BizException(BizErrorCode.LENGTH_EXCEEDS_LIMIT.getCode(), "组织成员名称不能超过20字");
        }

        return Result.success(departmentService.addMember(addMemberDTO));
    }

    @PostMapping("/update/user/member")
    @ApiOperation("添加或者更新部门成员")
    public Result<Boolean> updateUserMemberInfo(@Valid @RequestBody UpdateUserMemberInfoDTO updateDTO) {
        if (updateDTO == null || StringUtils.isBlank(updateDTO.getPhone())) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        return Result.success(departmentService.updateUserMemberInfo(updateDTO));
    }

    @PostMapping("/update")
    @ApiOperation("更新部门")
    public Result<Boolean> updateDepartment(@RequestBody DepartmentUpdateDTO updateDTO) {
        if (updateDTO == null || StringUtils.isBlank(updateDTO.getName())) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        if (updateDTO.getName().length() > 20) {
            throw new BizException(BizErrorCode.LENGTH_EXCEEDS_LIMIT.getCode(), "组织名称不能超过20个字");
        }

        return Result.success(departmentService.updateDepartment(updateDTO));
    }

    @GetMapping("/delete")
    @ApiOperation("删除部门")
    public Result<Boolean> deleteDepartment(@RequestParam("id") Long id) {
        if (id == null) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        return Result.success(departmentService.deleteDepartment(id));
    }

    @GetMapping("/delete/member")
    @ApiOperation("删除部门成员")
    public Result<Boolean> deleteMember(@RequestParam("id") Long id) {
        if (id == null) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        return Result.success(departmentService.removeMember(id));
    }

    @PostMapping("/tree")
    @ApiOperation("获取部门树")
    public Result<List<DepartmentDTO>> getDepartmentByTree(@RequestBody QueryDepartmentTreeDTO queryDTO) {
        PermissionSystemEnum permissionSystemEnum = dataPermissionProcessor.routeToPermissionSystem();
        if (permissionSystemEnum == PermissionSystemEnum.NO_DATA_PERMISSION) {
            return Result.success(departmentService.getDepartmentByTree(queryDTO));
        }

        return Result.success(departmentService.getDepartmentByTreeWithDataPermission(queryDTO));
    }

    @PostMapping("/subTree")
    @ApiOperation("获取子部门")
    public Result<List<DepartmentDTO>> getSubDepartments(@RequestBody QuerySubDepartmentDTO queryDTO) {
        return Result.success(departmentService.getSubDepartments(queryDTO));
    }

    @GetMapping("/tree/ids")
    @ApiOperation("根据id获取部门")
    public Result<List<DepartmentTreeDTO>> listSimpleDepartmentsByIds(@RequestParam("ids") List<Long> ids) {
        return Result.success(departmentService.listSimpleDepartmentsByIds(ids));
    }

    @PostMapping("/root")
    @ApiOperation("获取用户根节点的部门信息")
    public Result<List<UserRootDepartmentDTO>> getUserRootDepartments(@RequestBody @Valid UserRootDepartmentQueryDTO queryDTO) {
        return Result.success(departmentService.getUserRootDepartments(queryDTO));
    }

    @PostMapping("/members")
    @ApiOperation("根据部门ID查询成员列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "departmentId", value = "部门ID", required = true, dataTypeClass = Long.class),
            @ApiImplicitParam(name = "memberStatus", value = "成员状态：ALL-全部成员，AVAILABLE-未被占用的成员，OCCUPIED-已被占用的成员", dataTypeClass = String.class)
    })
    public Result<List<DepartmentMemberDTO>> listDepartmentMembers(@RequestBody @Valid DepartmentMemberQueryDTO queryDTO) {
        if (queryDTO == null || queryDTO.getDepartmentId() == null) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }
        return Result.success(departmentService.listDepartmentMembers(queryDTO));
    }

    @PostMapping("/bind/users")
    @ApiOperation("获取当前部门以及子部门的所有关联的用户")
    public Result<List<SimpleUserInfoDTO>> getDepartmentBindUsers(@RequestBody @Valid QueryDepartmentBindUserDTO queryDTO) {
        return Result.success(departmentService.getDepartmentBindUsers(queryDTO));
    }

    @PostMapping("/whole/info")
    @ApiOperation("获取当前用户所属组织整棵树的信息")
    public Result<WholeDepartmentDTO> getWholeDepartmentInfo(@RequestBody @Valid WholeDepartmentQueryDTO queryDTO) {
        return Result.success(departmentService.getWholeDepartmentInfo(queryDTO));
    }

}
