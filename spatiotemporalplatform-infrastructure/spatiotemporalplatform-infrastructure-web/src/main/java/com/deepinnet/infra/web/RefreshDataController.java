package com.deepinnet.infra.web;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.infra.api.enums.*;
import com.deepinnet.infra.dal.dataobject.*;
import com.deepinnet.infra.dal.repository.*;
import com.deepinnet.infra.service.context.TenantContext;
import com.google.common.collect.*;
import io.swagger.annotations.Api;
import lombok.Data;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wong
 * @create 2025/7/12 16:08
 * @Description 刷数据脚本Controller
 */
@RestController
@RequestMapping("/stpf/refresh")
@Api(tags = "刷数据脚本")
public class RefreshDataController {

    @Resource
    private DepartmentRepository departmentRepository;

    @Resource
    private UserDepartmentRepository userDepartmentRepository;

    @Resource
    private DepartmentMemberRepository departmentMemberRepository;

    @Resource
    private AccountRepository accountRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private RoleRepository roleRepository;

    @Resource
    private RolePermissionRepository rolePermissionRepository;

    @Resource
    private PermissionRepository permissionRepository;

    @Resource
    private UserMemberRepository userMemberRepository;

    @Resource
    private TransactionTemplate transactionTemplate;

    @PostMapping("/user")
    public FlushDataResult refreshOldUserCenterData(String userNo) {
        LogUtil.info("开始执行用户中心数据刷新脚本");

        TenantContext.disableTenantLine();

        List<AccountDO> accountDOs = accountRepository.list(Wrappers.lambdaQuery(AccountDO.class)
                .eq(StrUtil.isNotBlank(userNo), AccountDO::getUserNo, userNo));

        List<String> accountUserNos = accountDOs.stream().map(AccountDO::getUserNo)
                .collect(Collectors.toList());

        List<UserDO> userDOs = userRepository.list(Wrappers.lambdaQuery(UserDO.class)
                .in(CollUtil.isNotEmpty(accountUserNos), UserDO::getUserNo, accountUserNos));

        List<UserDO> finalUserDOs = Lists.newArrayList();
        getNeedProcessUsers(userDOs, finalUserDOs);
        if (CollUtil.isEmpty(finalUserDOs)) {
            LogUtil.info("没有需要处理的用户");
            FlushDataResult flushDataResult = new FlushDataResult();
            flushDataResult.setDesc("没有需要处理的用户");
            return flushDataResult;
        }

        // 查询所有的部门信息
        List<DepartmentDO> departmentList = departmentRepository.list(Wrappers.lambdaQuery(DepartmentDO.class));
        if (CollUtil.isEmpty(departmentList)) {
            FlushDataResult flushDataResult = new FlushDataResult();
            flushDataResult.setDesc("当前系统部门数据为空");
            LogUtil.info("当前系统部门数据为空，脚本执行结束.");
            return null;
        }

        // 处理用户和部门关联关系，多对多改为一对多，保留最底层部门
        List<String> userNos = finalUserDOs.stream()
                .map(UserDO::getUserNo)
                .collect(Collectors.toList());
//        List<UserDepartmentDO> userDepartmentDOs = userDepartmentRepository.list(Wrappers.lambdaQuery(UserDepartmentDO.class)
//                .in(UserDepartmentDO::getUserNo, userNos));
//
//        Map<String, List<DepartmentDO>> departmentSceneMap = departmentList.stream()
//                .collect(Collectors.groupingBy(DepartmentDO::getScene));
//
//        if (CollUtil.isEmpty(userDepartmentDOs)) {
//            userDepartmentDOs = new ArrayList<>();
//        }
//
//        Map<String, List<UserDepartmentDO>> userDepartmentMap = userDepartmentDOs.stream()
//                .collect(Collectors.groupingBy(UserDepartmentDO::getUserNo));
//
//        // 用户和部门关联关系，没有关联部门的用户自动分配到根部门
//        List<UserDepartmentDO> newUserDepartmentDOs = new ArrayList<>();
//        List<Long> needDeleteUserDepartmentIds = new ArrayList<>();
//
//        // 用户和部门关联关系的处理，没有绑定部门的自动绑定根部门，有多个部门的自动选择最底层的部门
//        for (UserDO userDO : userDOs) {
//            createNewUserDepartmentRelationOrDeleteRedundantDepartmentRelations(userDO, userDepartmentMap, newUserDepartmentDOs, departmentSceneMap, departmentList, needDeleteUserDepartmentIds);
//        }
//
//        if (CollUtil.isNotEmpty(newUserDepartmentDOs)) {
//            userDepartmentRepository.saveBatch(newUserDepartmentDOs);
//        }
//
//        if (CollUtil.isNotEmpty(needDeleteUserDepartmentIds)) {
//            userDepartmentRepository.removeBatchByIds(needDeleteUserDepartmentIds);
//        }
//
//        // 成员创建，找用户关联的部门下面有没有同名的成员，如果有则跳过，如果没有则创建
//        List<DepartmentMemberDO> departmentMemberDOs = processDepartmentMembers(userNos, userDOs);
//        if (CollUtil.isEmpty(departmentMemberDOs)) {
//            departmentMemberRepository.saveBatch(departmentMemberDOs);
//        }

        // 用户和成员关联关系
        FlushDataResult flushDataResult = buildNewUserMembers(userNos, finalUserDOs);

        transactionTemplate.executeWithoutResult(action -> {
            if (flushDataResult != null && CollUtil.isNotEmpty(flushDataResult.getUserMemberDOs())) {
                userMemberRepository.saveBatch(flushDataResult.getUserMemberDOs());
            }

            if (flushDataResult != null && CollUtil.isNotEmpty(flushDataResult.getDepartmentMemberDOs())) {
                departmentMemberRepository.updateBatchById(flushDataResult.getDepartmentMemberDOs());
            }
        });

        return flushDataResult;
    }

    @PostMapping("/role")
    public Map<String, Integer> refreshRolePermission() {
        LogUtil.info("开始刷取角色权限");
        TenantContext.disableTenantLine();
        Map<String, Integer> map = Maps.newHashMap();

        List<RoleDO> roleList = roleRepository.list(Wrappers.lambdaQuery(RoleDO.class)
                .eq(RoleDO::getTenantId, "sz_unifly")
                .eq(RoleDO::getScene, "customer_dashboard"));
        if (CollUtil.isEmpty(roleList)) {
            map.put("一网统飞客户工作台没有角色", 0);
            return map;
        }

        map.put("总共的角色数量", roleList.size());

        List<PermissionDO> permissionList = permissionRepository.list(Wrappers.lambdaQuery(PermissionDO.class)
                .eq(PermissionDO::getTenantId, "sz_unifly")
                .eq(PermissionDO::getScene, "customer_dashboard"));
        map.put("总共的权限数", permissionList.size());

        List<RolePermissionDO> rolePermissionList = Lists.newArrayList();
        for (RoleDO roleDO : roleList) {
            for (PermissionDO permission : permissionList) {
                RolePermissionDO rolePermissionDO = new RolePermissionDO();
                rolePermissionDO.setRoleCode(roleDO.getRoleCode());
                rolePermissionDO.setPermissionCode(permission.getCode());
                rolePermissionDO.setTenantId(roleDO.getTenantId());
                rolePermissionDO.setGmtCreated(new Date());
                rolePermissionDO.setGmtModified(new Date());
                rolePermissionDO.setIsDeleted(false);
                rolePermissionList.add(rolePermissionDO);
            }
        }

        map.put("刷取的rolePermission数量", rolePermissionList.size());

        List<String> roleCodes = roleList.stream()
                .map(RoleDO::getRoleCode)
                .distinct()
                .collect(Collectors.toList());
        List<RolePermissionDO> existRolePermissionList = rolePermissionRepository.list(Wrappers.lambdaQuery(RolePermissionDO.class)
                .in(RolePermissionDO::getRoleCode, roleCodes)
                .eq(RolePermissionDO::getTenantId, "sz_unifly"));
        map.put("要删除的数量", existRolePermissionList.size());

        transactionTemplate.executeWithoutResult(action -> {
            rolePermissionRepository.removeBatchByIds(existRolePermissionList);
            rolePermissionRepository.saveBatch(rolePermissionList);
        });

        return map;
    }

    private FlushDataResult buildNewUserMembers(List<String> userNos, List<UserDO> userDOs) {
        List<UserDO> finalUserDOs = Lists.newArrayList();
        getNeedProcessUsers(userDOs, finalUserDOs);
        if (CollUtil.isEmpty(finalUserDOs)) {
            LogUtil.info("没有需要处理的用户");
            FlushDataResult flushDataResult = new FlushDataResult();
            flushDataResult.setDesc("没有需要处理的用户");
            return flushDataResult;
        }

        AtomicInteger ignoreCount = new AtomicInteger();
        List<FlushDataDTO> flushDataDTOs = new ArrayList<>();
        List<String> multiDepartmentUserNos = new ArrayList<>();
        List<String> superAdminUserNos = new ArrayList<>();
        List<String> notRelatedMemberUserNos = new ArrayList<>();

        // 查询用户成员关联关系
        List<UserMemberDO> userMemberDOs = userMemberRepository.list(Wrappers.lambdaQuery(UserMemberDO.class)
                .in(UserMemberDO::getUserNo, userNos));

        if (CollUtil.isEmpty(userMemberDOs)) {
            userMemberDOs = new ArrayList<>();
        }

        Map<String, UserMemberDO> userMemberMap = userMemberDOs.stream()
                .collect(Collectors.toMap(UserMemberDO::getUserNo, Function.identity()));

        // 用户部门关联关系
        List<UserDepartmentDO> latestUserDepartmentDOs = userDepartmentRepository.list(Wrappers.lambdaQuery(UserDepartmentDO.class)
                .in(UserDepartmentDO::getUserNo, userNos));
        if (CollUtil.isEmpty(latestUserDepartmentDOs)) {
            latestUserDepartmentDOs = new ArrayList<>();
        }

        List<Long> departmentIds = latestUserDepartmentDOs.stream()
                .map(UserDepartmentDO::getDepartmentId)
                .distinct()
                .collect(Collectors.toList());

        // 针对每个用户可能关联多个部门的情况，移除掉这些数据，只保留每个用户只关联一个部门的情况
        // 先按照userNo进行分组，筛选出只关联一个部门的用户
        Map<String, List<UserDepartmentDO>> userNoGroupMap = latestUserDepartmentDOs.stream()
                .collect(Collectors.groupingBy(UserDepartmentDO::getUserNo));

        // 只保留每个用户只关联一个部门的数据
        List<UserDepartmentDO> filteredUserDepartmentDOs = userNoGroupMap.values().stream()
                .filter(list -> {
                    if (list.size() == 1) {
                        return true;
                    }

                    FlushDataDTO flushDataDTO = new FlushDataDTO();
                    flushDataDTO.setUserNo(list.get(0).getUserNo());
                    flushDataDTO.setDepartmentId(JSONUtil.toJsonStr(list.stream().map(UserDepartmentDO::getDepartmentId).collect(Collectors.toList())));
                    flushDataDTO.setDesc("用户关联多个部门");
                    flushDataDTOs.add(flushDataDTO);
                    multiDepartmentUserNos.add(list.get(0).getUserNo());
                    return false;
                })
                .map(list -> list.get(0))
                .collect(Collectors.toList());

        latestUserDepartmentDOs = filteredUserDepartmentDOs;
        if (CollUtil.isEmpty(latestUserDepartmentDOs)) {
            return null;
        }

        List<String> finalUserNos = filteredUserDepartmentDOs.stream()
                .map(UserDepartmentDO::getUserNo)
                .collect(Collectors.toList());
        LogUtil.info("所有只关联一个部门的用户：{}", JSONUtil.toJsonStr(finalUserNos));

        Map<String, Long> latestUserDepartmentMap = latestUserDepartmentDOs.stream()
                .collect(Collectors.toMap(UserDepartmentDO::getUserNo, UserDepartmentDO::getDepartmentId));

        // 部门成员关联关系
        List<DepartmentMemberDO> allDepartmentMembers = departmentMemberRepository.list(Wrappers.lambdaQuery(DepartmentMemberDO.class)
                .in(DepartmentMemberDO::getDepartmentId, departmentIds));
        if (CollUtil.isEmpty(allDepartmentMembers)) {
            allDepartmentMembers = new ArrayList<>();
        }

        Map<Long, List<DepartmentMemberDO>> memberMap = allDepartmentMembers.stream()
                .collect(Collectors.groupingBy(DepartmentMemberDO::getDepartmentId));

        List<UserMemberDO> newUserMemberDOs = new ArrayList<>();
        List<DepartmentMemberDO> needUpdateMemberList = new ArrayList<>();

        userDOs.forEach(userDO -> {
            if (userDO.getIsSuperAdmin()) {
                superAdminUserNos.add(userDO.getUserNo());
                return;
            }

            UserMemberDO userMemberDO = userMemberMap.get(userDO.getUserNo());
            if (userMemberDO == null) {
                // 找到这个用户对应的成员
                Long departmentId = latestUserDepartmentMap.get(userDO.getUserNo());
                List<DepartmentMemberDO> members = memberMap.get(departmentId);
                if (CollUtil.isEmpty(members)) {
                    FlushDataDTO flushDataDTO = new FlushDataDTO();
                    flushDataDTO.setUserNo(userDO.getUserNo());
                    flushDataDTO.setDepartmentId(departmentId + "");
                    flushDataDTO.setDesc("用户部门下没有成员");
                    flushDataDTOs.add(flushDataDTO);

                    notRelatedMemberUserNos.add(userDO.getUserNo());

                    LogUtil.info("当前用户{}，没有对应的成员，用户名：{},departmentId={}", userDO.getUserNo(), userDO.getUserName(), departmentId);
                    return;
                }

                DepartmentMemberDO currentUserMember = members.stream()
                        .filter(m -> StrUtil.equals(m.getName(), userDO.getUserName()))
                        .collect(Collectors.toList())
                        .stream()
                        .findFirst()
                        .orElse(null);

                if (currentUserMember == null) {
                    FlushDataDTO flushDataDTO = new FlushDataDTO();
                    flushDataDTO.setUserNo(userDO.getUserNo());
                    flushDataDTO.setDepartmentId(departmentId + "");
                    flushDataDTO.setDesc("用户部门下没有同名成员");
                    flushDataDTOs.add(flushDataDTO);

                    notRelatedMemberUserNos.add(userDO.getUserNo());
                    LogUtil.info("当前用户{}，没有对应的成员，用户名：{},departmentId={}", userDO.getUserNo(), userDO.getUserName(), departmentId);
                    return;
                    //throw new BizException(BizErrorCode.UNKNOWN_EXCEPTION.getCode(), "成员不存在");
                } else {
                    if (!StrUtil.equals(currentUserMember.getOccupyStatus(), MemberOccupyStatusEnum.AVAILABLE.getCode())) {
                        FlushDataDTO flushDataDTO = new FlushDataDTO();
                        flushDataDTO.setUserNo(userDO.getUserNo());
                        flushDataDTO.setDepartmentId(departmentId + "");
                        flushDataDTO.setDesc("用户部门下同名成员已被占用");
                        flushDataDTOs.add(flushDataDTO);

                        notRelatedMemberUserNos.add(userDO.getUserNo());
                        LogUtil.info("当前用户{}，部门下同名成员已被占用，用户名：{},departmentId={}", userDO.getUserNo(), userDO.getUserName(), departmentId);
                    } else {
                        currentUserMember.setOccupyStatus(MemberOccupyStatusEnum.OCCUPIED.getCode());
                        currentUserMember.setTenantId(userDO.getTenantId());
                        needUpdateMemberList.add(currentUserMember);
                        newUserMemberDOs.add(buildUserMember(userDO, currentUserMember));
                    }
                }

            } else {
                ignoreCount.getAndIncrement();
            }
        });


        return buildDataResult(userDOs, newUserMemberDOs, needUpdateMemberList, multiDepartmentUserNos, superAdminUserNos, notRelatedMemberUserNos, flushDataDTOs, ignoreCount.get());
    }

    private void getNeedProcessUsers(List<UserDO> userDOs, List<UserDO> finalUserDOs) {
        for (UserDO userDO : userDOs) {
            String userType = userDO.getUserType();
            String tenantId = userDO.getTenantId();

            // 开放平台
            boolean isOpenProdUser = StrUtil.equals(userType, UserTypeEnum.SPATIOTEMPORAL_OPENPROD.getCode());
            // 一网统飞服务商/撮合服务商
            boolean isSupplier = StrUtil.equals(userType, UserTypeEnum.SUPPLIER.getCode());
            // 撮合的客户
            boolean isCustomer = StrUtil.equals(userType, UserTypeEnum.CUSTOMER.getCode()) && (StrUtil.equals(tenantId, "deepinnet") || StrUtil.equals(tenantId, "sz_lg"));

            if (!isOpenProdUser && !isSupplier && !isCustomer) {
                finalUserDOs.add(userDO);
            }
        }
    }

    private FlushDataResult buildDataResult(List<UserDO> userDOs, List<UserMemberDO> newUserMemberDOs, List<DepartmentMemberDO> needUpdateMemberList,
                                            List<String> multiDepartmentUserNos, List<String> superAdminUserNos,
                                            List<String> notRelatedMemberUserNos, List<FlushDataDTO> flushDataDTOs, Integer ignoreCount) {
        FlushDataResult flushDataResult = new FlushDataResult();
        flushDataResult.setUserMemberDOs(newUserMemberDOs);
        flushDataResult.setDepartmentMemberDOs(needUpdateMemberList);
        flushDataResult.setTotalCount(userDOs.size());
        flushDataResult.setNeedSaveCount(newUserMemberDOs.size());
        flushDataResult.setMultiDepartmentUserNos(multiDepartmentUserNos);
        flushDataResult.setMultiDepartmentCount(multiDepartmentUserNos.size());
        flushDataResult.setSuperAdminUserNos(superAdminUserNos);
        flushDataResult.setSuperAdminUserCount(superAdminUserNos.size());
        flushDataResult.setIgnoreCount(ignoreCount);
        flushDataResult.setNotRelatedMemberUserNos(notRelatedMemberUserNos);
        flushDataResult.setNotRelatedMemberCount(notRelatedMemberUserNos.size());
        flushDataResult.setFlushDataDTOs(flushDataDTOs);
        return flushDataResult;
    }

    private UserMemberDO buildUserMember(UserDO userDO, DepartmentMemberDO currentUserMember) {
        UserMemberDO newUserMember = new UserMemberDO();
        newUserMember.setUserNo(userDO.getUserNo());
        newUserMember.setMemberId(currentUserMember.getId());
        newUserMember.setGmtCreated(new Date());
        newUserMember.setGmtModified(new Date());
        newUserMember.setTenantId(userDO.getTenantId());
        return newUserMember;
    }

    private List<DepartmentMemberDO> processDepartmentMembers(List<String> userNos, List<UserDO> userDOs) {
        // 成员创建，找用户关联的部门下面有没有同名的成员，如果有则跳过，如果没有则创建
        List<UserDepartmentDO> latestUserDepartmentDOs = userDepartmentRepository.list(Wrappers.lambdaQuery(UserDepartmentDO.class)
                .in(UserDepartmentDO::getUserNo, userNos));
        if (CollUtil.isEmpty(latestUserDepartmentDOs)) {
            latestUserDepartmentDOs = new ArrayList<>();
        }

        // 到这部门和用户一定是一对一的关系
        Map<String, Long> latestUserDepartmentMap = latestUserDepartmentDOs.stream()
                .collect(Collectors.toMap(UserDepartmentDO::getUserNo, UserDepartmentDO::getDepartmentId));

        List<Long> departmentIds = latestUserDepartmentDOs.stream()
                .map(UserDepartmentDO::getDepartmentId)
                .distinct()
                .collect(Collectors.toList());

        List<DepartmentMemberDO> allDepartmentMembers = departmentMemberRepository.list(Wrappers.lambdaQuery(DepartmentMemberDO.class)
                .in(DepartmentMemberDO::getDepartmentId, departmentIds));
        if (CollUtil.isEmpty(allDepartmentMembers)) {
            allDepartmentMembers = new ArrayList<>();
        }

        Map<Long, List<DepartmentMemberDO>> departmentMemberMap = allDepartmentMembers.stream()
                .collect(Collectors.groupingBy(DepartmentMemberDO::getDepartmentId));

        List<DepartmentMemberDO> needInsertDepartmentMembers = new ArrayList<>();
        for (UserDO userDO : userDOs) {
            String scene = getScene(userDO.getUserType());

            Long relatedDepartmentId = latestUserDepartmentMap.get(userDO.getUserNo());
            List<DepartmentMemberDO> departmentMemberDOs = departmentMemberMap.get(relatedDepartmentId);
            List<DepartmentMemberDO> currentUserRelatedMembers = departmentMemberDOs.stream()
                    .filter(e -> StrUtil.equals(e.getName(), userDO.getUserName()))
                    .collect(Collectors.toList());

            // 没有对应成员则创建，如果有则不处理
            if (CollUtil.isEmpty(currentUserRelatedMembers)) {
                needInsertDepartmentMembers.add(buildMember(userDO, relatedDepartmentId, scene));
            }
        }

        return needInsertDepartmentMembers;
    }

    private DepartmentMemberDO buildMember(UserDO userDO, Long relatedDepartmentId, String scene) {
        DepartmentMemberDO memberDO = new DepartmentMemberDO();
        memberDO.setDepartmentId(relatedDepartmentId);
        memberDO.setPhone(userDO.getPhone());
        memberDO.setEmail(null);
        memberDO.setPosition(null);
        memberDO.setName(userDO.getUserName());
        memberDO.setType(MemberTypeEnums.NORMAL_PERSON.getCode());
        memberDO.setOccupyStatus(MemberOccupyStatusEnum.OCCUPIED.getCode());
        memberDO.setTenantId(userDO.getTenantId());
        memberDO.setGmtCreated(new Date());
        memberDO.setGmtModified(new Date());
        memberDO.setScene(scene);
        return memberDO;
    }

    private void createNewUserDepartmentRelationOrDeleteRedundantDepartmentRelations(UserDO userDO, Map<String, List<UserDepartmentDO>> userDepartmentMap, List<UserDepartmentDO> newUserDepartmentDOs, Map<String, List<DepartmentDO>> departmentSceneMap, List<DepartmentDO> departmentList, List<Long> needDeleteUserDepartmentIds) {
        List<UserDepartmentDO> currentUserRelatedDepartments = userDepartmentMap.get(userDO.getUserNo());
        // 没有关联部门的用户自动分配到根部门
        if (CollUtil.isEmpty(currentUserRelatedDepartments)) {
            newUserDepartmentDOs.add(buildUserDepartmentRelation(userDO, departmentSceneMap));
        } else {
            // 关联了多个部门，只保留组织层级最大的（即最底层的）那个部门
            if (currentUserRelatedDepartments.size() > 1) {
                buildOrRemoveUserDepartmentRelation(currentUserRelatedDepartments, departmentList, needDeleteUserDepartmentIds);
            }
        }
    }

    private void buildOrRemoveUserDepartmentRelation(List<UserDepartmentDO> currentUserRelatedDepartments, List<DepartmentDO> departmentList, List<Long> needDeleteUserDepartmentIds) {
        List<Long> currentDepartmentIds = currentUserRelatedDepartments.stream()
                .map(UserDepartmentDO::getId)
                .collect(Collectors.toList());

        List<DepartmentDO> currentDepartments = departmentList.stream()
                .filter(e -> currentDepartmentIds.contains(e.getId()))
                .collect(Collectors.toList());

        DepartmentDO maxLevelDepartment = currentDepartments.stream()
                .max(Comparator.comparing(DepartmentDO::getLevel))
                .get();

        // 可能有多个同级的数据，这里随机取一个
        // 先过滤出所有与最大层级相同的部门
        List<DepartmentDO> sameLevelDepartments = currentDepartments.stream()
                .filter(e -> Objects.equals(e.getLevel(), maxLevelDepartment.getLevel()))
                .collect(Collectors.toList());
        // 随机选取一个部门
        DepartmentDO selectedDepartment = sameLevelDepartments.get(0);
        // 保留选中的部门，其他的都需要删除
        currentDepartments.forEach(e -> {
            if (!Objects.equals(e.getId(), selectedDepartment.getId())) {
                needDeleteUserDepartmentIds.add(e.getId());
            }
        });
    }

    private UserDepartmentDO buildUserDepartmentRelation(UserDO userDO, Map<String, List<DepartmentDO>> departmentMap) {
        String scene = getScene(userDO.getUserType());
        List<DepartmentDO> departmentDOs = departmentMap.get(scene);
        DepartmentDO rootDepartment = departmentDOs.stream().filter(e -> e.getParentId() == -1)
                .collect(Collectors.toList())
                .stream()
                .findFirst()
                .get();

        UserDepartmentDO userDepartmentDO = new UserDepartmentDO();
        userDepartmentDO.setUserNo(userDO.getUserNo());
        userDepartmentDO.setDepartmentId(rootDepartment.getId());
        userDepartmentDO.setTenantId(userDO.getTenantId());
        userDepartmentDO.setGmtCreated(new Date());
        userDepartmentDO.setGmtModified(new Date());
        return userDepartmentDO;
    }

    /**
     * 根据账号类型获取适合的场景
     */
    private String getScene(String userType) {
        if (StrUtil.isBlank(userType)) {
            return SceneEnum.TRAFFIC_POLICE.getCode();
        }

        if (StrUtil.equals(UserTypeEnum.SPATIOTEMPORAL_OPENPROD.getCode(), userType)) {
            return SceneEnum.SPATIOTEMPORAL_OPENPROD.getCode();
        }

        if (StrUtil.equals(UserTypeEnum.OPEN_OPERATION_PLATFORM_OPERATION.getCode(), userType)) {
            return SceneEnum.OPEN_OPERATION_PLATFORM.getCode();
        }

        if (StrUtil.equals(UserTypeEnum.CUSTOMER.getCode(), userType)) {
            return SceneEnum.CUSTOMER_DASHBOARD.getCode();
        }

        if (StrUtil.equals(UserTypeEnum.OPERATION.getCode(), userType)) {
            return SceneEnum.OPERATION_CENTER.getCode();
        }

        return null;
    }

    @Data
    class FlushDataResult {
        List<UserMemberDO> userMemberDOs;
        List<DepartmentMemberDO> departmentMemberDOs;
        int totalCount;
        int needSaveCount;
        int multiDepartmentCount;
        int ignoreCount;
        int superAdminUserCount;
        int notRelatedMemberCount;
        List<String> multiDepartmentUserNos = new ArrayList<>();
        List<String> superAdminUserNos = new ArrayList<>();
        List<String> notRelatedMemberUserNos = new ArrayList<>();
        List<FlushDataDTO> flushDataDTOs;
        String desc;
    }

    @Data
    class FlushDataDTO {
        private String userNo;
        private String userName;
        private String departmentId;
        private String desc;
    }
}
