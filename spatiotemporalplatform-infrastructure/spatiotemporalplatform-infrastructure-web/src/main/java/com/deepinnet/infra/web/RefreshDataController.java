package com.deepinnet.infra.web;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.infra.api.enums.*;
import com.deepinnet.infra.dal.dataobject.*;
import com.deepinnet.infra.dal.repository.*;
import com.deepinnet.infra.service.context.TenantContext;
import io.swagger.annotations.Api;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wong
 * @create 2025/7/12 16:08
 * @Description 刷数据脚本Controller
 */
@RestController
@RequestMapping("/stpf/refresh")
@Api(tags = "刷数据脚本")
public class RefreshDataController {

    @Resource
    private DepartmentRepository departmentRepository;

    @Resource
    private UserDepartmentRepository userDepartmentRepository;

    @Resource
    private DepartmentMemberRepository departmentMemberRepository;

    @Resource
    private AccountRepository accountRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private UserMemberRepository userMemberRepository;

    @Resource
    private TransactionTemplate transactionTemplate;

    @PostMapping("/user")
    public Boolean refreshOldUserCenterData() {
        LogUtil.info("开始执行用户中心数据刷新脚本");

        TenantContext.disableTenantLine();

        List<UserDO> userDOs = userRepository.list(Wrappers.lambdaQuery(UserDO.class));
        if (CollUtil.isEmpty(userDOs)) {
            LogUtil.info("当前系统用户数据为空，脚本执行结束.");
            return true;
        }

        // 查询所有的部门信息
        List<DepartmentDO> departmentList = departmentRepository.list(Wrappers.lambdaQuery(DepartmentDO.class));
        if (CollUtil.isEmpty(departmentList)) {
            LogUtil.info("当前系统部门数据为空，脚本执行结束.");
            return true;
        }

        // 处理用户和部门关联关系，多对多改为一对多，保留最底层部门
        List<String> userNos = userDOs.stream()
                .map(UserDO::getUserNo)
                .collect(Collectors.toList());
//        List<UserDepartmentDO> userDepartmentDOs = userDepartmentRepository.list(Wrappers.lambdaQuery(UserDepartmentDO.class)
//                .in(UserDepartmentDO::getUserNo, userNos));
//
//        Map<String, List<DepartmentDO>> departmentSceneMap = departmentList.stream()
//                .collect(Collectors.groupingBy(DepartmentDO::getScene));
//
//        if (CollUtil.isEmpty(userDepartmentDOs)) {
//            userDepartmentDOs = new ArrayList<>();
//        }
//
//        Map<String, List<UserDepartmentDO>> userDepartmentMap = userDepartmentDOs.stream()
//                .collect(Collectors.groupingBy(UserDepartmentDO::getUserNo));
//
//        // 用户和部门关联关系，没有关联部门的用户自动分配到根部门
//        List<UserDepartmentDO> newUserDepartmentDOs = new ArrayList<>();
//        List<Long> needDeleteUserDepartmentIds = new ArrayList<>();
//
//        // 用户和部门关联关系的处理，没有绑定部门的自动绑定根部门，有多个部门的自动选择最底层的部门
//        for (UserDO userDO : userDOs) {
//            createNewUserDepartmentRelationOrDeleteRedundantDepartmentRelations(userDO, userDepartmentMap, newUserDepartmentDOs, departmentSceneMap, departmentList, needDeleteUserDepartmentIds);
//        }
//
//        if (CollUtil.isNotEmpty(newUserDepartmentDOs)) {
//            userDepartmentRepository.saveBatch(newUserDepartmentDOs);
//        }
//
//        if (CollUtil.isNotEmpty(needDeleteUserDepartmentIds)) {
//            userDepartmentRepository.removeBatchByIds(needDeleteUserDepartmentIds);
//        }
//
//        // 成员创建，找用户关联的部门下面有没有同名的成员，如果有则跳过，如果没有则创建
//        List<DepartmentMemberDO> departmentMemberDOs = processDepartmentMembers(userNos, userDOs);
//        if (CollUtil.isEmpty(departmentMemberDOs)) {
//            departmentMemberRepository.saveBatch(departmentMemberDOs);
//        }

        // 用户和成员关联关系
        Pair<List<UserMemberDO>, List<DepartmentMemberDO>> pair = buildNewUserMembers(userNos, userDOs);

        if (pair != null && CollUtil.isNotEmpty(pair.getKey())) {
            userMemberRepository.saveBatch(pair.getKey());
        }

        if (pair != null && CollUtil.isNotEmpty(pair.getValue())) {
            departmentMemberRepository.updateBatchById(pair.getValue());
        }


        return true;
    }

    private Pair<List<UserMemberDO>, List<DepartmentMemberDO>> buildNewUserMembers(List<String> userNos, List<UserDO> userDOs) {
        // 查询用户成员关联关系
        List<UserMemberDO> userMemberDOs = userMemberRepository.list(Wrappers.lambdaQuery(UserMemberDO.class)
                .in(UserMemberDO::getUserNo, userNos));

        if (CollUtil.isEmpty(userMemberDOs)) {
            userMemberDOs = new ArrayList<>();
        }

        Map<String, UserMemberDO> userMemberMap = userMemberDOs.stream()
                .collect(Collectors.toMap(UserMemberDO::getUserNo, Function.identity()));

        // 用户部门关联关系
        List<UserDepartmentDO> latestUserDepartmentDOs = userDepartmentRepository.list(Wrappers.lambdaQuery(UserDepartmentDO.class)
                .in(UserDepartmentDO::getUserNo, userNos));
        if (CollUtil.isEmpty(latestUserDepartmentDOs)) {
            latestUserDepartmentDOs = new ArrayList<>();
        }

        List<Long> departmentIds = latestUserDepartmentDOs.stream()
                .map(UserDepartmentDO::getDepartmentId)
                .distinct()
                .collect(Collectors.toList());

        // 针对每个用户可能关联多个部门的情况，移除掉这些数据，只保留每个用户只关联一个部门的情况
        // 先按照userNo进行分组，筛选出只关联一个部门的用户
        Map<String, List<UserDepartmentDO>> userNoGroupMap = latestUserDepartmentDOs.stream()
                .collect(Collectors.groupingBy(UserDepartmentDO::getUserNo));
        // 只保留每个用户只关联一个部门的数据
        List<UserDepartmentDO> filteredUserDepartmentDOs = userNoGroupMap.values().stream()
                .filter(list -> list.size() == 1)
                .map(list -> list.get(0))
                .collect(Collectors.toList());
        latestUserDepartmentDOs = filteredUserDepartmentDOs;
        if (CollUtil.isEmpty(latestUserDepartmentDOs)) {
            return null;
        }

        Map<String, Long> latestUserDepartmentMap = latestUserDepartmentDOs.stream()
                .collect(Collectors.toMap(UserDepartmentDO::getUserNo, UserDepartmentDO::getDepartmentId));

        // 部门成员关联关系
        List<DepartmentMemberDO> allDepartmentMembers = departmentMemberRepository.list(Wrappers.lambdaQuery(DepartmentMemberDO.class)
                .in(DepartmentMemberDO::getDepartmentId, departmentIds));
        if (CollUtil.isEmpty(allDepartmentMembers)) {
            allDepartmentMembers = new ArrayList<>();
        }
        Map<Long, List<DepartmentMemberDO>> memberMap = allDepartmentMembers.stream()
                .collect(Collectors.groupingBy(DepartmentMemberDO::getDepartmentId));

        List<UserMemberDO> newUserMemberDOs = new ArrayList<>();
        List<DepartmentMemberDO> needUpdateMemberList = new ArrayList<>();

        userDOs.forEach(userDO -> {
            if (userDO.getIsSuperAdmin()) {
                return;
            }
            UserMemberDO userMemberDO = userMemberMap.get(userDO.getUserNo());
            if (userMemberDO == null) {
                // 找到这个用户对应的成员
                Long departmentId = latestUserDepartmentMap.get(userDO.getUserNo());
                List<DepartmentMemberDO> members = memberMap.get(departmentId);
                if (CollUtil.isEmpty(members)) {
                    return;
                }
                DepartmentMemberDO currentUserMember = members.stream()
                        .filter(m -> StrUtil.equals(m.getName(), userDO.getUserName()))
                        .collect(Collectors.toList())
                        .stream()
                        .findFirst()
                        .orElse(null);

                if (currentUserMember == null) {
                    return;
                    //throw new BizException(BizErrorCode.UNKNOWN_EXCEPTION.getCode(), "成员不存在");
                }

                currentUserMember.setOccupyStatus(MemberOccupyStatusEnum.OCCUPIED.getCode());
                currentUserMember.setTenantId(userDO.getTenantId());
                needUpdateMemberList.add(currentUserMember);
                newUserMemberDOs.add(buildUserMember(userDO, currentUserMember));
            }
        });
        return Pair.of(newUserMemberDOs, needUpdateMemberList);
    }

    private UserMemberDO buildUserMember(UserDO userDO, DepartmentMemberDO currentUserMember) {
        UserMemberDO newUserMember = new UserMemberDO();
        newUserMember.setUserNo(userDO.getUserNo());
        newUserMember.setMemberId(currentUserMember.getId());
        newUserMember.setGmtCreated(new Date());
        newUserMember.setGmtModified(new Date());
        newUserMember.setTenantId(userDO.getTenantId());
        return newUserMember;
    }

    private List<DepartmentMemberDO> processDepartmentMembers(List<String> userNos, List<UserDO> userDOs) {
        // 成员创建，找用户关联的部门下面有没有同名的成员，如果有则跳过，如果没有则创建
        List<UserDepartmentDO> latestUserDepartmentDOs = userDepartmentRepository.list(Wrappers.lambdaQuery(UserDepartmentDO.class)
                .in(UserDepartmentDO::getUserNo, userNos));
        if (CollUtil.isEmpty(latestUserDepartmentDOs)) {
            latestUserDepartmentDOs = new ArrayList<>();
        }

        // 到这部门和用户一定是一对一的关系
        Map<String, Long> latestUserDepartmentMap = latestUserDepartmentDOs.stream()
                .collect(Collectors.toMap(UserDepartmentDO::getUserNo, UserDepartmentDO::getDepartmentId));

        List<Long> departmentIds = latestUserDepartmentDOs.stream()
                .map(UserDepartmentDO::getDepartmentId)
                .distinct()
                .collect(Collectors.toList());

        List<DepartmentMemberDO> allDepartmentMembers = departmentMemberRepository.list(Wrappers.lambdaQuery(DepartmentMemberDO.class)
                .in(DepartmentMemberDO::getDepartmentId, departmentIds));
        if (CollUtil.isEmpty(allDepartmentMembers)) {
            allDepartmentMembers = new ArrayList<>();
        }

        Map<Long, List<DepartmentMemberDO>> departmentMemberMap = allDepartmentMembers.stream()
                .collect(Collectors.groupingBy(DepartmentMemberDO::getDepartmentId));

        List<DepartmentMemberDO> needInsertDepartmentMembers = new ArrayList<>();
        for (UserDO userDO : userDOs) {
            String scene = getScene(userDO.getUserType());

            Long relatedDepartmentId = latestUserDepartmentMap.get(userDO.getUserNo());
            List<DepartmentMemberDO> departmentMemberDOs = departmentMemberMap.get(relatedDepartmentId);
            List<DepartmentMemberDO> currentUserRelatedMembers = departmentMemberDOs.stream()
                    .filter(e -> StrUtil.equals(e.getName(), userDO.getUserName()))
                    .collect(Collectors.toList());

            // 没有对应成员则创建，如果有则不处理
            if (CollUtil.isEmpty(currentUserRelatedMembers)) {
                needInsertDepartmentMembers.add(buildMember(userDO, relatedDepartmentId, scene));
            }
        }

        return needInsertDepartmentMembers;
    }

    private DepartmentMemberDO buildMember(UserDO userDO, Long relatedDepartmentId, String scene) {
        DepartmentMemberDO memberDO = new DepartmentMemberDO();
        memberDO.setDepartmentId(relatedDepartmentId);
        memberDO.setPhone(userDO.getPhone());
        memberDO.setEmail(null);
        memberDO.setPosition(null);
        memberDO.setName(userDO.getUserName());
        memberDO.setType(MemberTypeEnums.NORMAL_PERSON.getCode());
        memberDO.setOccupyStatus(MemberOccupyStatusEnum.OCCUPIED.getCode());
        memberDO.setTenantId(userDO.getTenantId());
        memberDO.setGmtCreated(new Date());
        memberDO.setGmtModified(new Date());
        memberDO.setScene(scene);
        return memberDO;
    }

    private void createNewUserDepartmentRelationOrDeleteRedundantDepartmentRelations(UserDO userDO, Map<String, List<UserDepartmentDO>> userDepartmentMap, List<UserDepartmentDO> newUserDepartmentDOs, Map<String, List<DepartmentDO>> departmentSceneMap, List<DepartmentDO> departmentList, List<Long> needDeleteUserDepartmentIds) {
        List<UserDepartmentDO> currentUserRelatedDepartments = userDepartmentMap.get(userDO.getUserNo());
        // 没有关联部门的用户自动分配到根部门
        if (CollUtil.isEmpty(currentUserRelatedDepartments)) {
            newUserDepartmentDOs.add(buildUserDepartmentRelation(userDO, departmentSceneMap));
        } else {
            // 关联了多个部门，只保留组织层级最大的（即最底层的）那个部门
            if (currentUserRelatedDepartments.size() > 1) {
                buildOrRemoveUserDepartmentRelation(currentUserRelatedDepartments, departmentList, needDeleteUserDepartmentIds);
            }
        }
    }

    private void buildOrRemoveUserDepartmentRelation(List<UserDepartmentDO> currentUserRelatedDepartments, List<DepartmentDO> departmentList, List<Long> needDeleteUserDepartmentIds) {
        List<Long> currentDepartmentIds = currentUserRelatedDepartments.stream()
                .map(UserDepartmentDO::getId)
                .collect(Collectors.toList());

        List<DepartmentDO> currentDepartments = departmentList.stream()
                .filter(e -> currentDepartmentIds.contains(e.getId()))
                .collect(Collectors.toList());

        DepartmentDO maxLevelDepartment = currentDepartments.stream()
                .max(Comparator.comparing(DepartmentDO::getLevel))
                .get();

        // 可能有多个同级的数据，这里随机取一个
        // 先过滤出所有与最大层级相同的部门
        List<DepartmentDO> sameLevelDepartments = currentDepartments.stream()
                .filter(e -> Objects.equals(e.getLevel(), maxLevelDepartment.getLevel()))
                .collect(Collectors.toList());
        // 随机选取一个部门
        DepartmentDO selectedDepartment = sameLevelDepartments.get(0);
        // 保留选中的部门，其他的都需要删除
        currentDepartments.forEach(e -> {
            if (!Objects.equals(e.getId(), selectedDepartment.getId())) {
                needDeleteUserDepartmentIds.add(e.getId());
            }
        });
    }

    private UserDepartmentDO buildUserDepartmentRelation(UserDO userDO, Map<String, List<DepartmentDO>> departmentMap) {
        String scene = getScene(userDO.getUserType());
        List<DepartmentDO> departmentDOs = departmentMap.get(scene);
        DepartmentDO rootDepartment = departmentDOs.stream().filter(e -> e.getParentId() == -1)
                .collect(Collectors.toList())
                .stream()
                .findFirst()
                .get();

        UserDepartmentDO userDepartmentDO = new UserDepartmentDO();
        userDepartmentDO.setUserNo(userDO.getUserNo());
        userDepartmentDO.setDepartmentId(rootDepartment.getId());
        userDepartmentDO.setTenantId(userDO.getTenantId());
        userDepartmentDO.setGmtCreated(new Date());
        userDepartmentDO.setGmtModified(new Date());
        return userDepartmentDO;
    }

    /**
     * 根据账号类型获取适合的场景
     */
    private String getScene(String userType) {
        if (StrUtil.isBlank(userType)) {
            return SceneEnum.TRAFFIC_POLICE.getCode();
        }

        if (StrUtil.equals(UserTypeEnum.SPATIOTEMPORAL_OPENPROD.getCode(), userType)) {
            return SceneEnum.SPATIOTEMPORAL_OPENPROD.getCode();
        }

        if (StrUtil.equals(UserTypeEnum.OPEN_OPERATION_PLATFORM_OPERATION.getCode(), userType)) {
            return SceneEnum.OPEN_OPERATION_PLATFORM.getCode();
        }

        if (StrUtil.equals(UserTypeEnum.CUSTOMER.getCode(), userType)) {
            return SceneEnum.CUSTOMER_DASHBOARD.getCode();
        }

        if (StrUtil.equals(UserTypeEnum.OPERATION.getCode(), userType)) {
            return SceneEnum.OPERATION_CENTER.getCode();
        }

        return null;
    }
}
