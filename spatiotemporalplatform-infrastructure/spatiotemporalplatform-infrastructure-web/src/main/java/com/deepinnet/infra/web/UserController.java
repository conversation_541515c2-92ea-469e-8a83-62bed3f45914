package com.deepinnet.infra.web;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.service.UserService;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2024/8/8 16:11
 * @Description
 */
@RestController
@Api(tags = "用户服务")
@Validated
@RequestMapping("/domain/user")
public class UserController {

    @Resource
    private UserService userService;

    @PostMapping("/login")
    @ApiOperation("登录")
    public Result<UserLoginSuccessDTO> login(@Valid @RequestBody UserLoginDTO loginDTO) {
        return Result.success(userService.login(loginDTO));
    }

    @PostMapping("/login/code")
    @ApiOperation("登录")
    public Result<UserLoginSuccessDTO> loginByCode(@Valid @RequestBody UserLoginDTO loginDTO) {
        return Result.success(userService.loginByCode(loginDTO));
    }

    /**
     * 用户注册
     *
     * @param registerDTO 注册信息
     * @return 注册结果
     */
    @ApiOperation("用户注册")
    @PostMapping("/register")
    public Result<Boolean> register(@Valid @RequestBody UserRegisterDTO registerDTO) {
        return Result.success(userService.register(registerDTO));
    }

    /**
     * 用户信息是否填写
     */
    @ApiOperation("用户信息是否填写")
    @PostMapping("/checkInfo")
    public Result<Boolean> checkUserInfoStatus() {
        return Result.success(userService.checkUserInfoStatus());
    }

    /**
     * 用户信息是否填写
     */
    @ApiOperation("批量查询用户审核状态")
    @PostMapping("/approval/status")
    public Result<List<UserApprovalStatusDTO>> getUserApprovalStatus(@Valid @RequestBody QueryUserApprovalStatusDTO queryDTO) {
        return Result.success(userService.getUserApprovalStatus(queryDTO));
    }

    /**
     * 获取用户绑定的部门
     */
    @ApiOperation("获取用户绑定的部门")
    @PostMapping("/department")
    public Result<List<SimpleDepartmentDTO>> getUserDepartments() {
        return Result.success(userService.getUserDepartments());
    }

    /**
     *
     */
    @ApiOperation("查询用户关联的部门以及查询用户部门的fullPath")
    @PostMapping("/department/fullPath")
    public Result<List<SimpleDepartmentDTO>> getUserRelatedDepartment(@Valid @RequestBody QueryUserRelatedDepartmentDTO queryDTO) {
        return Result.success(userService.getUserRelatedDepartment(queryDTO));
    }

    /**
     * 获取用户详情
     */
    @ApiOperation("获取用户详情")
    @PostMapping("/detail/list")
    public Result<List<UserDetailDTO>> getUserDetailList(@RequestBody UserQueryDTO queryDTO) {
        return Result.success(userService.getUserDetailList(queryDTO));
    }

    /**
     * 获取用户详情
     */
    @ApiOperation("获取用户详情没有租户id")
    @PostMapping("/detail/list/tenantId")
    public Result<List<UserDetailDTO>> getUserDetailListWithoutTenantId(@RequestBody UserQueryDTO queryDTO) {
        return Result.success(userService.getUserDetailListWithoutTenantId(queryDTO));
    }

    /**
     * 获取当前用户可见的数据内容
     */
    @ApiOperation("获取当前用户可见的数据内容")
    @PostMapping("/data/access")
    public Result<DataAccessDTO> getAvailableQueryData() {
        return Result.success(userService.getAvailableQueryData());
    }


    @PostMapping("/info")
    @ApiOperation("获取用户信息")
    public Result<UserInfoDTO> getAdminInfo() {
        return Result.success(userService.getUserInfo());
    }

    @PostMapping("/password")
    @ApiOperation("修改密码")
    public Result<Boolean> changePassword(@Valid @RequestBody UserPasswordChangeDTO passwordChangeDTO) {
        return Result.success(userService.changePassword(passwordChangeDTO));
    }

    @PostMapping("/password/forget")
    @ApiOperation("忘记密码")
    public Result<Boolean> forgetPassword(@RequestBody UserPasswordForgetDTO passwordForgetDTO) {
        return Result.success(userService.forgetPassword(passwordForgetDTO));
    }

    @PostMapping("/logout")
    @ApiOperation("注销")
    public Result<Boolean> logout() {
        return Result.success(userService.logout());
    }

    /**
     * 根据成员id获取用户信息
     *
     * @param memberIds
     * @return
     */
    @GetMapping("/query/memberId")
    public Result<List<UserMemberDTO>> getUserInfoByMemberId(@RequestParam("memberIds") List<Long> memberIds) {
        return Result.success(userService.getUserInfoByMemberId(memberIds));
    }

    /**
     * 根据用户编号列表获取用户
     *
     * @param userNos
     * @return
     */
    @GetMapping("/query/userNos")
    public Result<List<UserMemberDTO>> getUserInfoByUserNo(@RequestParam("userNos") List<String> userNos) {
        return Result.success(userService.getUserInfoByUserNo(userNos));
    }

    /**
     * 根据用户编号列表获取用户简略信息
     *
     * @param userNos
     * @return
     */
    @GetMapping("/querySimple/userNos")
    public Result<List<UserMemberDTO>> getUserInfoSimpleByUserNos(@RequestParam("userNos") List<String> userNos) {
        return Result.success(userService.getUserInfoSimpleByUserNos(userNos));
    }

    @GetMapping("/account/initialized")
    @ApiOperation("检查当前用户密码状态（是否已完成初始化）")
    public Result<Boolean> checkUserInitializedStatus() {
        return Result.success(userService.checkUserInitializedStatus());
    }

    /**
     * 开给家驹定时任务的接口，查询系统所有的用户，不带租户id
     *
     * @r>eturn
     */
    @PostMapping("/list")
    @ApiOperation("查询系统中用户")
    public Result<List<SimpleUserInfoDTO>> getSimpleUserInfoWithoutTenantId(@RequestBody QueryUserInfoDTO queryDTO) {
        return Result.success(userService.getSimpleUserInfoWithoutTenantId(queryDTO));
    }

    /**
     * 获取当前用户可见的组织树内容
     */
    @ApiOperation("获取当前用户可见的数据内容")
    @PostMapping("/data/accessTree")
    public Result<List<DataScopeTreeNodeDTO>> getAvailableDataScopeTree() {
        return Result.success(userService.getAvailableDataScopeTree());
    }
}
