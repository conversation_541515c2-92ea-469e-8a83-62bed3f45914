package com.deepinnet.infra.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.infra.dal.dataobject.UserContactInfoDO;
import com.deepinnet.infra.dal.mapper.UserContactInfoMapper;
import com.deepinnet.infra.dal.repository.UserContactInfoRepository;
import org.springframework.stereotype.Repository;

/**
 * 服务商联系人Repository实现类
 *
 * <AUTHOR> wong
 * @create 2025/04/21
 */
@Repository
public class UserContactInfoRepositoryImpl extends ServiceImpl<UserContactInfoMapper, UserContactInfoDO>
        implements UserContactInfoRepository {
}




