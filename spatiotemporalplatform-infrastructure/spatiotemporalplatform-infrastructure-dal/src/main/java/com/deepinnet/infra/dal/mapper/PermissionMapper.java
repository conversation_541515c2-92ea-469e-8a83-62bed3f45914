package com.deepinnet.infra.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.infra.api.dto.PermissionDTO;
import com.deepinnet.infra.dal.dataobject.PermissionDO;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024-11-25 17:32:42
 */
public interface PermissionMapper extends BaseMapper<PermissionDO> {

    List<PermissionDTO> getPermissionTree(String scene);
}




