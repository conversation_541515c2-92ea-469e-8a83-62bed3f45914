package com.deepinnet.infra.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.infra.dal.dataobject.FileAttachmentDO;
import com.deepinnet.infra.dal.mapper.FileAttachmentMapper;
import com.deepinnet.infra.dal.repository.FileAttachmentRepository;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【file_attachment】的数据库操作Service实现
* @createDate 2025-04-17 17:20:54
*/
@Service
public class FileAttachmentRepositoryImpl extends ServiceImpl<FileAttachmentMapper, FileAttachmentDO>
    implements FileAttachmentRepository {

}




