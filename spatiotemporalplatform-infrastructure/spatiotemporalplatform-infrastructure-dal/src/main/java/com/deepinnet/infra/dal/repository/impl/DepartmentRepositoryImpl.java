package com.deepinnet.infra.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.infra.dal.mapper.DepartmentMapper;
import com.deepinnet.infra.dal.dataobject.DepartmentDO;
import com.deepinnet.infra.dal.repository.DepartmentRepository;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【department(组织表)】的数据库操作Service实现
* @createDate 2024-11-22 16:31:26
*/
@Service
public class DepartmentRepositoryImpl extends ServiceImpl<DepartmentMapper, DepartmentDO>
    implements DepartmentRepository {

}




