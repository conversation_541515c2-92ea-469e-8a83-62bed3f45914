package com.deepinnet.infra.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.infra.dal.dataobject.ProductServiceSupplierDO;
import com.deepinnet.infra.dal.mapper.ProductServiceSupplierMapper;
import com.deepinnet.infra.dal.repository.ProductServiceSupplierRepository;
import org.springframework.stereotype.Repository;

/**
 * 服务商信息Repository实现类
 *
 * <AUTHOR> wong
 * @create 2025/04/21
 */
@Repository
public class ProductServiceSupplierRepositoryImpl extends ServiceImpl<ProductServiceSupplierMapper, ProductServiceSupplierDO>
        implements ProductServiceSupplierRepository {
}




