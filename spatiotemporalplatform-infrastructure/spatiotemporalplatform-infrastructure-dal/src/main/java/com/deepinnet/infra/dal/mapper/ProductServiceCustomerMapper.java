package com.deepinnet.infra.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.infra.dal.dataobject.ProductServiceCustomerDO;
import com.deepinnet.infra.api.dto.CustomerQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【product_service_customer(客户需求方)】的数据库操作Mapper
* @createDate 2025-04-17 10:34:48
* @Entity generator.domain.ProductServiceCustomer
*/
public interface ProductServiceCustomerMapper extends BaseMapper<ProductServiceCustomerDO> {

    /**
     * 分页查询客户列表（联表查询联系人信息）
     *
     * @param queryDTO 查询条件
     * @return 客户列表
     */
    List<ProductServiceCustomerDO> pageQueryCustomersWithContact(@Param("query") CustomerQueryDTO queryDTO);
}




