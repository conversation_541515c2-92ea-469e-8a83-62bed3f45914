package com.deepinnet.infra.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.infra.dal.dataobject.ProductServiceSupplierDO;
import com.deepinnet.infra.api.dto.SupplierQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【product_service_supplier(产品服务商表，存储服务商信息)】的数据库操作Mapper
* @createDate 2025-04-17 10:34:48
* @Entity generator.domain.ProductServiceSupplier
*/
public interface ProductServiceSupplierMapper extends BaseMapper<ProductServiceSupplierDO> {

    /**
     * 分页查询服务商列表（联表查询联系人信息）
     *
     * @param queryDTO 查询条件
     * @return 服务商列表
     */
    List<ProductServiceSupplierDO> pageQuerySuppliersWithContact(@Param("query") SupplierQueryDTO queryDTO);
}




