package com.deepinnet.infra.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.infra.dal.mapper.UserOperationMapper;
import com.deepinnet.infra.dal.dataobject.UserOperationDO;
import com.deepinnet.infra.dal.repository.UserOperationRepository;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【user_operation】的数据库操作Service实现
* @createDate 2024-12-03 10:55:20
*/
@Service
public class UserOperationRepositoryImpl extends ServiceImpl<UserOperationMapper, UserOperationDO>
    implements UserOperationRepository {

}




