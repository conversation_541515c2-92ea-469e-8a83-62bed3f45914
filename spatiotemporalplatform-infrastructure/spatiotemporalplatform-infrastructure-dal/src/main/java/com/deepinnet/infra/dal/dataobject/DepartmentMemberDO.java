package com.deepinnet.infra.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 组织成员表
 *
 * @TableName department_member
 */
@TableName(value = "department_member")
@Data
public class DepartmentMemberDO implements Serializable {
    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 部门id
     */
    private Long departmentId;

    /**
     * 手机号
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String phone;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 职务
     */
    private String position;

    /**
     * 成员名称
     */
    private String name;

    /**
     * 类型
     */
    private String type;

    /**
     * 占用状态：available-未占用，occupied-已占用
     */
    private String occupyStatus;

    /**
     * 成员身份信息，JSON格式存储
     * 用于标识成员的身份类型，如飞手等
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String identityType;

    private String tenantId;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     *
     */
    private String bizData;

    /**
     *
     */
    private Date gmtCreated;

    /**
     *
     */
    private Date gmtModified;

    private String scene;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}