package com.deepinnet.infra.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 审核记录
 *
 * @TableName approval
 */
@TableName(value = "approval")
@Data
public class ApprovalDO {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 申请ID
     */
    private String approvalId;

    /**
     * 审核类型：1-客户信息认证，2-服务商信息认证
     */
    private Integer approvalType;

    /**
     * 单位/企业名称
     */
    private String organizationName;

    /**
     * 统一社会信用代码
     */
    private String socialCreditCode;

    /**
     * 审核状态：0-待审核，1-审核通过，2-审核驳回
     */
    private Integer status;

    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    /**
     * 审核时间
     */
    private LocalDateTime approvalTime;

    /**
     * 审核人
     */
    private String approver;

    /**
     * 审核意见
     */
    private String approvalComment;

    /**
     * 业务关联ID，客户ID或服务商ID
     */
    private String bizId;

    /**
     * 租户
     */
    private String tenantId;

    /**
     * 逻辑删除标识
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 修改时间
     */
    private Date gmtModified;
} 