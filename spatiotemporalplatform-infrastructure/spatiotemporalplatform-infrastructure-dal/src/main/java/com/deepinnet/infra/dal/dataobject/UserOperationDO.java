package com.deepinnet.infra.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName user_operation
 */
@TableName(value ="user_operation")
@Data
public class UserOperationDO implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    private String userNo;

    private String accountNo;


    private String userName;

    private String ip;

    /**
     * 操作类型，如登录=login
     */
    private String type;

    private String operationName;

    private String args;

    private Boolean success;

    private String traceId;

    private String tenantId;

    /**
     * 
     */
    private Date gmtCreated;

    /**
     * 
     */
    private Date gmtModified;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}