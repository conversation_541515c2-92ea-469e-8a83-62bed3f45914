package com.deepinnet.infra.dal.repository.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.infra.dal.mapper.UserRoleMapper;
import com.deepinnet.infra.dal.dataobject.UserRoleDO;
import com.deepinnet.infra.dal.repository.UserRoleRepository;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【account_role(账号角色表)】的数据库操作Service实现
 * @createDate 2024-08-08 17:11:24
 */
@Service
public class UserRoleRepositoryImpl extends ServiceImpl<UserRoleMapper, UserRoleDO>
        implements UserRoleRepository {

}




