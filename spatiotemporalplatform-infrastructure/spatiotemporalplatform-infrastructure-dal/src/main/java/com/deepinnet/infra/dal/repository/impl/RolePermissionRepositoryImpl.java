package com.deepinnet.infra.dal.repository.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.infra.dal.mapper.RolePermissionMapper;
import com.deepinnet.infra.dal.dataobject.RolePermissionDO;
import com.deepinnet.infra.dal.repository.RolePermissionRepository;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【role_permission(角色权限表)】的数据库操作Service实现
 * @createDate 2024-08-08 17:12:26
 */
@Service
public class RolePermissionRepositoryImpl extends ServiceImpl<RolePermissionMapper, RolePermissionDO>
        implements RolePermissionRepository {

}




