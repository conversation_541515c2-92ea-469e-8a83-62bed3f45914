package com.deepinnet.infra.dal.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.infra.dal.dataobject.AccountDO;
import com.deepinnet.infra.dal.mapper.AccountMapper;
import com.deepinnet.infra.dal.repository.AccountRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024-08-08 16:08:12
 */
@Repository
public class AccountRepositoryImpl extends ServiceImpl<AccountMapper, AccountDO>
        implements AccountRepository {

    @Override
    public AccountDO getAccount(String accountNo, String account) {
        LambdaQueryWrapper<AccountDO> wrapper = Wrappers.lambdaQuery(AccountDO.class)
                .eq(accountNo != null, AccountDO::getAccountNo, accountNo)
                .eq(StringUtils.isNotBlank(account), AccountDO::getAccount, account);
        return super.getOne(wrapper);
    }

    @Override
    public AccountDO getAccountByUserType(String account, List<String> userTypes) {
        LambdaQueryWrapper<AccountDO> wrapper = Wrappers.lambdaQuery(AccountDO.class)
                .eq(AccountDO::getAccount, account)
                .in(AccountDO::getUserType, userTypes);
        return super.getOne(wrapper);
    }

    public AccountDO getAccountByUserTypeWithNull(String account) {
        LambdaQueryWrapper<AccountDO> wrapper = Wrappers.lambdaQuery(AccountDO.class)
                .eq(AccountDO::getAccount, account)
                .isNull(AccountDO::getUserType);
        return super.getOne(wrapper);
    }
}




