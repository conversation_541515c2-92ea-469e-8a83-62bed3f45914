package com.deepinnet.infra.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.infra.dal.dataobject.UserResetTokenDO;
import com.deepinnet.infra.dal.mapper.UserResetTokenMapper;
import com.deepinnet.infra.dal.repository.UserResetTokenRepository;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【user_reset_token】的数据库操作Service实现
* @createDate 2025-04-22 11:00:18
*/
@Service
public class UserResetTokenRepositoryImpl extends ServiceImpl<UserResetTokenMapper, UserResetTokenDO>
    implements UserResetTokenRepository {

}




