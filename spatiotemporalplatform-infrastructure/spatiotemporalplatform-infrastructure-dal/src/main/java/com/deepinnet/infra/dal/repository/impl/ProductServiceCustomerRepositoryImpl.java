package com.deepinnet.infra.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.infra.dal.dataobject.ProductServiceCustomerDO;
import com.deepinnet.infra.dal.mapper.ProductServiceCustomerMapper;
import com.deepinnet.infra.dal.repository.ProductServiceCustomerRepository;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【product_service_customer(客户需求方)】的数据库操作Service实现
* @createDate 2025-04-17 10:34:48
*/
@Service
public class ProductServiceCustomerRepositoryImpl extends ServiceImpl<ProductServiceCustomerMapper, ProductServiceCustomerDO>
    implements ProductServiceCustomerRepository {

}




