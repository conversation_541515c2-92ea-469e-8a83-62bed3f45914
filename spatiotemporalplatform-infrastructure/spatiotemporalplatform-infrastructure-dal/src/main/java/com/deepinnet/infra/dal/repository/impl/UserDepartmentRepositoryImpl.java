package com.deepinnet.infra.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.infra.dal.mapper.UserDepartmentMapper;
import com.deepinnet.infra.dal.dataobject.UserDepartmentDO;
import com.deepinnet.infra.dal.repository.UserDepartmentRepository;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【user_department】的数据库操作Service实现
* @createDate 2024-12-02 16:41:24
*/
@Service
public class UserDepartmentRepositoryImpl extends ServiceImpl<UserDepartmentMapper, UserDepartmentDO>
    implements UserDepartmentRepository {

}




