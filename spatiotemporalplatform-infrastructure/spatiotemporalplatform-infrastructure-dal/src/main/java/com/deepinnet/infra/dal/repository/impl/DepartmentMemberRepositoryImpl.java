package com.deepinnet.infra.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.infra.dal.mapper.DepartmentMemberMapper;
import com.deepinnet.infra.dal.dataobject.DepartmentMemberDO;
import com.deepinnet.infra.dal.repository.DepartmentMemberRepository;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【department_member(组织成员表)】的数据库操作Service实现
* @createDate 2024-11-22 16:31:26
*/
@Service
public class DepartmentMemberRepositoryImpl extends ServiceImpl<DepartmentMemberMapper, DepartmentMemberDO>
    implements DepartmentMemberRepository {

}




