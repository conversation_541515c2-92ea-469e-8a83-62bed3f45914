package com.deepinnet.infra.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.infra.api.dto.DepartmentTreeDTO;
import com.deepinnet.infra.dal.dataobject.DepartmentDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【department(组织表)】的数据库操作Mapper
 * @createDate 2024-11-22 16:31:26
 * @Entity generator.domain.Department
 */
public interface DepartmentMapper extends BaseMapper<DepartmentDO> {

    List<DepartmentTreeDTO> getDepartmentTreeByParentId(@Param("parentId") Long parentId, @Param("scene") String scene);

    List<DepartmentTreeDTO> getDepartmentTreeByParentIdWithMemberStatus(@Param("parentId") Long parentId, @Param("scene") String scene, @Param("memberStatus") String memberStatus);

    List<Long> getDepartmentFullPathFromRoot(@Param("targetId") Long targetId);

    List<DepartmentTreeDTO> getSubTreeWithParentIdList(@Param("parentIds") List<Long> parentIds);

    List<DepartmentTreeDTO> getSubDepartmentsAndMembers(@Param("departmentIds") List<Long> departmentIds, @Param("depth") Integer depth);
    
    /**
     * 根据父部门ID查询所有子部门ID（包括自身）
     * @param parentId 父部门ID
     * @param scene 场景
     * @return 所有子部门ID列表
     */
    List<Long> getAllSubDepartmentIds(@Param("parentId") Long parentId, @Param("scene") String scene);
}




