package com.deepinnet.infra.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @TableName account_token
 */
@TableName(value = "account_token")
@Data
public class AccountTokenDO implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * key
     */
    private String key;

    /**
     * value
     */
    private String value;

    /**
     * 登录时间
     */
    private LocalDateTime loginTime;

    /**
     * 过期时间 单位：秒
     */
    private Long expireTime;


    /**
     *
     */
    private LocalDateTime gmtCreated;

    /**
     *
     */
    private LocalDateTime gmtModified;

    /**
     *
     */
    private Boolean isDeleted;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}