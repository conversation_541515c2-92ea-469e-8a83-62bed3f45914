package com.deepinnet.infra.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.infra.dal.dataobject.SupplierFlightServiceDO;
import com.deepinnet.infra.dal.mapper.SupplierFlightServiceMapper;
import com.deepinnet.infra.dal.repository.SupplierFlightServiceRepository;
import org.springframework.stereotype.Repository;

/**
 * 服务商飞行服务Repository实现类
 *
 * <AUTHOR> wong
 * @create 2025/04/21
 */
@Repository
public class SupplierFlightServiceRepositoryImpl extends ServiceImpl<SupplierFlightServiceMapper, SupplierFlightServiceDO>
        implements SupplierFlightServiceRepository {
}




