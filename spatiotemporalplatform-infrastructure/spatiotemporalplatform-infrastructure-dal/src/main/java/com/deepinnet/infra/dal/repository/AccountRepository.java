package com.deepinnet.infra.dal.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.infra.dal.dataobject.AccountDO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AccountRepository extends IService<AccountDO> {

    AccountDO getAccount(String accountNo, String account);

    AccountDO getAccountByUserType(String account, List<String> userType);

    AccountDO getAccountByUserTypeWithNull(String account);
}
