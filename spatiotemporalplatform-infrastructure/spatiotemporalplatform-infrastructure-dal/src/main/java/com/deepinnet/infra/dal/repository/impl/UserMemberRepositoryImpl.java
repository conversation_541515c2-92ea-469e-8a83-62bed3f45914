package com.deepinnet.infra.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.infra.dal.dataobject.UserMemberDO;
import com.deepinnet.infra.dal.mapper.UserMemberMapper;
import com.deepinnet.infra.dal.repository.UserMemberRepository;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【user_member】的数据库操作Service实现
* @createDate 2025-07-07 15:24:48
*/
@Service
public class UserMemberRepositoryImpl extends ServiceImpl<UserMemberMapper, UserMemberDO>
    implements UserMemberRepository {

}




