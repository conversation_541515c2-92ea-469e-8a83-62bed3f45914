package com.deepinnet.infra.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.infra.dal.mapper.UserAccountMapper;
import com.deepinnet.infra.dal.dataobject.UserAccountDO;
import com.deepinnet.infra.dal.repository.UserAccountRepository;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @createDate 2024-11-28 10:24:02
 */
@Service
public class UserAccountRepositoryImpl extends ServiceImpl<UserAccountMapper, UserAccountDO>
        implements UserAccountRepository {

}




