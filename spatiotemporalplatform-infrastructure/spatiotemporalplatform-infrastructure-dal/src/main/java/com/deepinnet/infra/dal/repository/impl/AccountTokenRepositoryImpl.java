package com.deepinnet.infra.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.infra.dal.mapper.AccountTokenMapper;
import com.deepinnet.infra.dal.dataobject.AccountTokenDO;
import com.deepinnet.infra.dal.repository.AccountTokenRepository;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @createDate 2024-11-27 16:16:29
 */
@Service
public class AccountTokenRepositoryImpl extends ServiceImpl<AccountTokenMapper, AccountTokenDO>
        implements AccountTokenRepository {

}




