package com.deepinnet.infra.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 产品服务商表，存储服务商信息
 *
 * @TableName product_service_supplier
 */
@TableName(value = "product_service_supplier")
@Data
public class ProductServiceSupplierDO {
    /**
     * 自增主键，唯一标识服务商
     */
    @TableId
    private Long id;

    /**
     * 服务商公司名称
     */
    private String companyName;

    /**
     * 服务商用户编号
     */
    private String userNo;

    /**
     * 地址
     */
    private String address;

    /**
     * 区域code
     */
    private String regionCode;

    /**
     * 区域名称
     */
    private String regionName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 统一社会信用代码
     */
    private String creditCode;

    private String tenantId;

    /**
     * 是否同意合作协议
     */
    private Boolean cooperationAgreement;

    /**
     * 注册时间
     */
    private LocalDateTime registrationTime;

    /**
     * 审核状态：-1-初始状态（未认证），0-审核中，1-已认证，2-不通过
     */
    private Integer approvalStatus;

    private Boolean isDeleted;

    private Date gmtCreated;

    private Date gmtModified;
}