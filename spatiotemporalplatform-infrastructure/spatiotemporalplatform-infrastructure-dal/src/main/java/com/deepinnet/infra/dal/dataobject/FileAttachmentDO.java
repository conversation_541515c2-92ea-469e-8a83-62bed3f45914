package com.deepinnet.infra.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 
 * @TableName file_attachment
 */
@TableName(value ="file_attachment")
@Data
public class FileAttachmentDO {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    private String userNo;

    /**
     * 
     */
    private String filePath;

    /**
     * 
     */
    private String bizType;

    /**
     * 
     */
    private String bizNo;

    private String tenantId;

    private String originalFilename;

    private String fileUrl;
}