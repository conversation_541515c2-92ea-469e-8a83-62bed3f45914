package com.deepinnet.infra.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 角色表
 *
 * @TableName role
 */
@TableName(value = "role")
@Data
public class RoleDO implements Serializable {
    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 角色名称
     */
    private String roleCode;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色描述
     */
    private String roleDesc;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 是否可见
     */
    private Boolean visible;

    /**
     * 场景
     */
    private String scene;

    /**
     * 数据权限作用域
     * 1-个人数据 2-组织数据（本级及下属） 3-组织数据（全局） 4-全局数据
     */
    private Integer dataScope;

    private String tenantId;


    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}