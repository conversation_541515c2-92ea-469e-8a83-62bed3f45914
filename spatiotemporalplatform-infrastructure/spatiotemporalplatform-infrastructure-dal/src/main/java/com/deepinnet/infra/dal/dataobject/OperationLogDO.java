package com.deepinnet.infra.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 操作日志数据对象
 * 
 * <AUTHOR> wong
 * @create 2025/7/31
 */
@Data
@TableName("operation_log")
public class OperationLogDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 操作人用户编号
     */
    private String userNo;

    /**
     * 操作人姓名
     */
    private String userName;

    /**
     * 操作模块
     */
    private String operationModule;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 操作详情
     */
    private String operationDetail;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 请求URI
     */
    private String requestUri;

    /**
     * 请求方法
     */
    private String requestMethod;

    /**
     * 请求参数（JSON格式）
     */
    private String requestParams;

    /**
     * 操作结果：SUCCESS-成功，FAILURE-失败
     */
    private String operationResult;

    /**
     * 错误信息（操作失败时记录）
     */
    private String errorMessage;

    /**
     * 执行耗时（毫秒）
     */
    private Long executionTime;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 场景标识
     */
    private String scene;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
