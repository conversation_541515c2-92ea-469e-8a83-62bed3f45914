package com.deepinnet.infra.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 客户需求方
 *
 * @TableName product_service_customer
 */
@TableName(value = "product_service_customer")
@Data
public class ProductServiceCustomerDO {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 单位名称
     */
    private String organizationName;

    /**
     * 单位地址
     */
    private String address;

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 单位身份证明
     */
    private String identifiedPictureList;

    /**
     * 租户
     */
    private String tenantId;

    private String phone;

    /**
     * 注册时间
     */
    private LocalDateTime registrationTime;

    /**
     *
     */
    private Boolean isDeleted;

    /**
     *
     */
    private Date gmtCreated;

    /**
     *
     */
    private Date gmtModified;

    /**
     * 区域code
     */
    private String regionCode;

    /**
     * 区域名称
     */
    private String regionName;

    /**
     * 是否同意合作协议
     */
    private Boolean cooperationAgreement;
    
    /**
     * 身份类型：1-工商企业，2-政府/事业单位
     */
    private Integer identityType;
    
    /**
     * 统一社会信用代码
     */
    private String socialCreditCode;
    
    /**
     * 审核状态：-1-初始状态（未认证），0-审核中，1-已认证，2-不通过
     */
    private Integer approvalStatus;
}