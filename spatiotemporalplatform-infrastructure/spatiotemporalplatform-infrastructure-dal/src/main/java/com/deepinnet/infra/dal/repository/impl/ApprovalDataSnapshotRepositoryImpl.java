package com.deepinnet.infra.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.infra.dal.dataobject.ApprovalDataSnapshotDO;
import com.deepinnet.infra.dal.mapper.ApprovalDataSnapshotMapper;
import com.deepinnet.infra.dal.repository.ApprovalDataSnapshotRepository;
import org.springframework.stereotype.Repository;

/**
 * 审核数据快照Repository实现类
 *
 * <AUTHOR> wong
 * @create 2025/04/25
 */
@Repository
public class ApprovalDataSnapshotRepositoryImpl extends ServiceImpl<ApprovalDataSnapshotMapper, ApprovalDataSnapshotDO>
        implements ApprovalDataSnapshotRepository {
} 