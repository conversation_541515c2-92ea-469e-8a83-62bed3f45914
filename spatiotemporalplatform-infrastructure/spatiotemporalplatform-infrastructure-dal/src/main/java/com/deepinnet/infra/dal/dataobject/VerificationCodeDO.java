package com.deepinnet.infra.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * @TableName verification_code
 */
@TableName(value = "verification_code")
@Data
public class VerificationCodeDO {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     * 验证码内容
     */
    private String code;

    /**
     * 业务类型
     */
    private String bizType;

    private String scene;

    private String status;

    /**
     * 过期时间
     */
    private Date expireTime;

    /**
     * 手机号
     */
    private String phone;

    private String tenantId;

}