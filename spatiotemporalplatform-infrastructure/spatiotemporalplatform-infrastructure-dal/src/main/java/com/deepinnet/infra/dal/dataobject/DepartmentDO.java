package com.deepinnet.infra.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 组织表
 *
 * @TableName department
 */
@TableName(value = "department")
@Data
public class DepartmentDO implements Serializable {
    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 部门名称
     */
    private String name;

    /**
     * 部门层级
     */
    private Integer level;

    /**
     * 父级的id
     */
    private Long parentId;

    /**
     * 完整路径
     */
    private String fullPath;

    private String tenantId;


    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     *
     */
    private String bizData;

    /**
     *
     */
    private Date gmtCreated;

    /**
     *
     */
    private Date gmtModified;

    private String ext;

    private String scene;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}