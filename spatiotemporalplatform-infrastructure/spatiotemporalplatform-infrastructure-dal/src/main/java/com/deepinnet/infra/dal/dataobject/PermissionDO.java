package com.deepinnet.infra.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> wong
 * @create 2024/11/25 16:58
 * @Description
 */
@Data
@TableName(value ="permission")
public class PermissionDO {

    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long parentId;

    private String code;

    private String name;

    private String tenantId;

    private String scene;


    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 是否删除
     */
    private Boolean isDeleted;
}
