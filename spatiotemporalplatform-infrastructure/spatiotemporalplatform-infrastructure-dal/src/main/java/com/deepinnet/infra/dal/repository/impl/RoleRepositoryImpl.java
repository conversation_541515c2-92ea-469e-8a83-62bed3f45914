package com.deepinnet.infra.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.infra.dal.mapper.RoleMapper;
import com.deepinnet.infra.dal.dataobject.RoleDO;
import com.deepinnet.infra.dal.repository.RoleRepository;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【role(角色表)】的数据库操作Service实现
 * @createDate 2024-08-08 17:12:21
 */
@Service
public class RoleRepositoryImpl extends ServiceImpl<RoleMapper, RoleDO>
        implements RoleRepository {

}




