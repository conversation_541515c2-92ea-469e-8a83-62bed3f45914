package com.deepinnet.infra.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.infra.dal.mapper.PermissionMapper;
import com.deepinnet.infra.dal.dataobject.PermissionDO;
import com.deepinnet.infra.dal.repository.PermissionRepository;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【permission(权限表)】的数据库操作Service实现
* @createDate 2024-11-25 17:32:42
*/
@Service
public class PermissionRepositoryImpl extends ServiceImpl<PermissionMapper, PermissionDO>
    implements PermissionRepository {

}




