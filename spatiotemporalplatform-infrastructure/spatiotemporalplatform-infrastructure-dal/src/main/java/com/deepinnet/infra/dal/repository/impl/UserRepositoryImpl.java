package com.deepinnet.infra.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.infra.dal.mapper.UserMapper;
import com.deepinnet.infra.dal.dataobject.UserDO;
import com.deepinnet.infra.dal.repository.UserRepository;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @createDate 2024-11-28 10:24:02
 */
@Service
public class UserRepositoryImpl extends ServiceImpl<UserMapper, UserDO>
        implements UserRepository {

}




