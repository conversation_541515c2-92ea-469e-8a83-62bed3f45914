<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.infra.dal.mapper.AccountMapper">

    <resultMap id="BaseResultMap" type="com.deepinnet.infra.dal.dataobject.AccountDO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="accountNo" column="account_no" jdbcType="VARCHAR"/>
        <result property="account" column="account" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
        <result property="isDeleted" column="is_deleted" jdbcType="BOOLEAN"/>
        <result property="bizData" column="biz_data" jdbcType="VARCHAR"/>
        <result property="gmtCreated" column="gmt_created" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="creatorId" column="creator_id" jdbcType="VARCHAR"/>
        <result property="visible" column="visible" jdbcType="BOOLEAN"/>
        <result property="userNo" column="user_no" jdbcType="VARCHAR"/>
        <result property="userType" column="user_type" jdbcType="VARCHAR"/>
        <result property="isSuperAdmin" column="is_super_admin" jdbcType="BOOLEAN"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,account_no,account,
        phone,name,password,
        is_deleted,biz_data,gmt_created,
        gmt_modified,creator_id,visible,
        user_no,user_type,is_super_admin
    </sql>

    <select id="searchAccount" resultType="com.deepinnet.infra.dal.dataobject.AccountDO">
        SELECT DISTINCT a.*
        FROM account a
        LEFT JOIN user_department ud
        ON a.user_no = ud.user_no
        AND ud.is_deleted = false
        WHERE a.is_deleted = false
        AND a.visible = true
        <if test="queryCondition.userType != null">
            AND a.user_type = #{queryCondition.userType}
        </if>
        <if test="queryCondition.userType == null">
            AND a.user_type IS NULL
        </if>
        <if test="queryCondition.username != null and queryCondition.username.trim() != ''">
            AND a.name LIKE CONCAT('%', #{queryCondition.username}, '%')
        </if>
        <if test="queryCondition.phone != null and queryCondition.phone.trim() != ''">
            AND a.phone LIKE CONCAT('%', #{queryCondition.phone}, '%')
        </if>
        <if test="queryCondition.departmentIds != null and queryCondition.departmentIds.size > 0">
            -- 当传入 departmentIds 时，只查询关联的账号
            AND ud.department_id IN
            <foreach item="id" collection="queryCondition.departmentIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        ORDER BY a.gmt_created DESC
    </select>

    <select id="getSameNameAccountInDepartments" resultType="com.deepinnet.infra.dal.dataobject.AccountDO">
        SELECT a.*
        FROM account a
        LEFT JOIN user_department ud
        ON a.user_no = ud.user_no
        AND ud.is_deleted = false
        WHERE a.is_deleted = false
        <if test="queryCondition.departmentIds != null and queryCondition.departmentIds.size > 0">
            -- 当传入 departmentIds 时，只查询关联的账号
            AND ud.department_id IN
            <foreach item="id" collection="queryCondition.departmentIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        AND a.name = #{queryCondition.username}
    </select>
</mapper>
