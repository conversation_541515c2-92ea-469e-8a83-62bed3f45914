<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.infra.dal.mapper.DepartmentMapper">

    <resultMap id="BaseResultMap" type="com.deepinnet.infra.dal.dataobject.DepartmentDO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="level" column="level" jdbcType="INTEGER"/>
        <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
        <result property="isDeleted" column="is_deleted" jdbcType="BOOLEAN"/>
        <result property="bizData" column="biz_data" jdbcType="VARCHAR"/>
        <result property="gmtCreated" column="gmt_created" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="scene" column="scene" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,name,level,
        parent_id,is_deleted,biz_data,
        gmt_created,gmt_modified
    </sql>

    <select id="getDepartmentTreeByParentId"
            resultType="com.deepinnet.infra.api.dto.DepartmentTreeDTO">
        WITH RECURSIVE department_tree AS (
            -- 基础查询：判断 parentId 是否为 -1，-1 查询整棵树，否则从指定节点开始
            SELECT id,
                   name,
                   level,
                   parent_id,
                   scene
            FROM department
            WHERE ((parent_id = -1 AND #{parentId} = -1) -- 如果传入 parentId = -1，则从根节点开始
               OR (id = #{parentId} AND #{parentId} != -1)) -- 否则从指定节点开始
                AND is_deleted = false
                <if test="scene != null and scene != ''">
                AND scene = #{scene} AND scene IS NOT NULL
                </if>

            UNION ALL

            -- 子节点递归，过滤未删除数据
            SELECT d.id,
                   d.name,
                   d.level,
                   d.parent_id,
                   d.scene
            FROM department d
                     INNER JOIN department_tree dt ON d.parent_id = dt.id
            WHERE d.is_deleted = false
              <if test="scene != null and scene != ''">
              AND d.scene = #{scene} AND d.scene IS NOT NULL
              </if>
              AND dt.level &lt; 15 -- 限制递归深度，防止死循环
              -- 如果父节点有scene，子节点必须也有相同的scene
              <if test="scene != null and scene != ''">
              AND (dt.scene = #{scene})
              </if>
        )
        SELECT dt.id        AS departmentId,
               dt.name      AS departmentName,
               dt.level,
               dt.parent_id AS parentId,
               dt.scene,
               dm.id        AS memberId,
               dm.name      AS memberName,
               dm.type      AS memberType,
               dm.phone     AS memberPhone,
               dm.email     AS memberEmail,
               dm.position  AS memberPosition,
               dm.occupy_status AS occupyStatus
        FROM department_tree dt
                 LEFT JOIN department_member dm ON dt.id = dm.department_id
            AND dm.is_deleted = false -- 确保成员表中仅包含未删除数据
            <if test="scene != null and scene != ''">
            AND dm.scene = #{scene} AND dm.scene IS NOT NULL
            </if>
    </select>

    <select id="getSubTreeWithParentIdList"
            resultType="com.deepinnet.infra.api.dto.DepartmentTreeDTO">
        WITH RECURSIVE department_tree AS (
        -- 根节点条件：支持多个 parentId，并过滤未删除的部门
        SELECT id,
        name,
        level,
        parent_id,
        1 AS depth -- 初始深度为1
        FROM department
        WHERE (
        (parent_id = -1 AND -1 IN
        <foreach collection="parentIds" item="parentId" open="(" separator="," close=")">
            #{parentId}
        </foreach>) -- parentId = -1 时查询所有根节点
        OR
        (id IN
        <foreach collection="parentIds" item="parentId" open="(" separator="," close=")">
            #{parentId}
        </foreach>
        AND -1 NOT IN
        <foreach collection="parentIds" item="parentId" open="(" separator="," close=")">
            #{parentId}
        </foreach>) -- 否则查询指定的 parentId 节点
        )
        AND is_deleted = false

        UNION ALL

        -- 子节点递归，确保子节点也满足 is_deleted = false，并限制递归深度
        SELECT d.id,
        d.name,
        d.level,
        d.parent_id,
        dt.depth + 1 AS depth -- 深度递增
        FROM department d
        INNER JOIN department_tree dt ON d.parent_id = dt.id
        WHERE d.is_deleted = false
        AND dt.depth + 1 &lt; 15 -- 限制递归深度
        )
        SELECT dt.id AS departmentId,
        dt.name AS departmentName,
        dt.level,
        dt.parent_id AS parentId,
        dm.name AS memberName,
        dm.type AS memberType,
        dm.phone AS memberPhone,
        dm.email AS memberEmail,
        dm.position AS memberPosition
        FROM department_tree dt
        LEFT JOIN department_member dm ON dt.id = dm.department_id
        AND dm.is_deleted = false -- 确保成员表中仅包含未删除数据
    </select>

    <select id="getDepartmentFullPathFromRoot" resultType="java.lang.Long">
        WITH RECURSIVE department_path AS (
            -- 基础查询：从根节点 (parent_id = -1) 开始
            SELECT id, parent_id, id::text AS path  -- 初始路径直接为当前节点的 id
            FROM department
            WHERE parent_id = -1

            UNION ALL

            -- 子节点递归：从根节点向下逐层构建完整路径
            SELECT d.id, d.parent_id, dp.path || ',' || d.id -- 拼接上新的 id，用逗号分隔
            FROM department d
                     INNER JOIN department_path dp ON d.parent_id = dp.id)
        -- 过滤目标节点路径，并将路径拆分为单独的 ID 并转换为 BIGINT
        SELECT UNNEST(string_to_array(path, ',')) ::BIGINT AS single_path
        FROM department_path
        WHERE id = #{targetId};
    </select>

    <select id="getSubDepartmentsAndMembers"
            resultType="com.deepinnet.infra.api.dto.DepartmentTreeDTO">
        WITH RECURSIVE department_tree AS (
        -- 根节点：查询传入的部门 ID
        SELECT id,
        name,
        level,
        parent_id,
        full_path,
        is_deleted,
        0 AS current_depth <!-- 初始深度为 0 -->
        FROM department
        WHERE id IN
        <foreach item="id" collection="departmentIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND is_deleted = false <!-- 过滤删除的根部门 -->

        UNION ALL

        -- 子节点递归：查询子部门
        SELECT d.id,
        d.name,
        d.level,
        d.parent_id,
        d.full_path,
        d.is_deleted,
        dt.current_depth + 1 AS current_depth <!-- 深度递增 -->
        FROM department d
        INNER JOIN department_tree dt ON d.parent_id = dt.id
        WHERE d.is_deleted = false <!-- 过滤已删除的子部门 -->
        AND (
        #{depth} = -1 OR #{depth} > 0 AND dt.current_depth &lt; #{depth} <!-- 当 depth = -1 时查询所有层级，否则限制层级 -->
        )
        )
        SELECT
        dt.id AS departmentId,
        dt.name AS departmentName,
        dt.level AS departmentLevel,
        dt.parent_id AS parentId,
        dm.id AS memberId,
        dm.name AS memberName,
        dm.phone AS memberPhone,
        dm.email AS memberEmail,
        dm.position AS memberPosition,
        dm.type AS memberType,
        dm.occupy_status AS occupyStatus
        FROM department_tree dt
        LEFT JOIN department_member dm ON dt.id = dm.department_id
        AND dm.is_deleted = false <!-- 确保成员未被删除 -->
        WHERE dt.is_deleted = false <!-- 确保递归结果中的部门未被删除 -->
    </select>

    <select id="getDepartmentTreeByParentIdWithMemberStatus"
            resultType="com.deepinnet.infra.api.dto.DepartmentTreeDTO">
        WITH RECURSIVE department_tree AS (
            -- 基础查询：判断 parentId 是否为 -1，-1 查询整棵树，否则从指定节点开始
            SELECT id,
                   name,
                   level,
                   parent_id,
                   scene
            FROM department
            WHERE ((parent_id = -1 AND #{parentId} = -1) -- 如果传入 parentId = -1，则从根节点开始
               OR (id = #{parentId} AND #{parentId} != -1)) -- 否则从指定节点开始
                AND is_deleted = false
                <if test="scene != null and scene != ''">
                AND scene = #{scene} AND scene IS NOT NULL
                </if>

            UNION ALL

            -- 子节点递归，过滤未删除数据
            SELECT d.id,
                   d.name,
                   d.level,
                   d.parent_id,
                   d.scene
            FROM department d
                     INNER JOIN department_tree dt ON d.parent_id = dt.id
            WHERE d.is_deleted = false
              <if test="scene != null and scene != ''">
              AND d.scene = #{scene} AND d.scene IS NOT NULL
              </if>
              AND dt.level &lt; 15 -- 限制递归深度，防止死循环
              -- 如果父节点有scene，子节点必须也有相同的scene
              <if test="scene != null and scene != ''">
              AND (dt.scene = #{scene})
              </if>
        )
        SELECT dt.id        AS departmentId,
               dt.name      AS departmentName,
               dt.level,
               dt.parent_id AS parentId,
               dt.scene,
               dm.id        AS memberId,
               dm.name      AS memberName,
               dm.type      AS memberType,
               dm.phone     AS memberPhone,
               dm.email     AS memberEmail,
               dm.position  AS memberPosition,
               dm.occupy_status AS occupyStatus
        FROM department_tree dt
                 LEFT JOIN department_member dm ON dt.id = dm.department_id
            AND dm.is_deleted = false
            <if test="scene != null and scene != ''">
            AND dm.scene = #{scene} AND dm.scene IS NOT NULL
            </if>
            <if test="memberStatus != null and memberStatus != '' and memberStatus != 'all'">
            AND dm.occupy_status = #{memberStatus}
            </if>
    </select>

    <select id="getAllSubDepartmentIds" resultType="java.lang.Long">
        WITH RECURSIVE department_tree AS (
            -- 根节点：包含指定的父部门ID
            SELECT id
            FROM department
            WHERE id = #{parentId}
                AND is_deleted = false
                <if test="scene != null and scene != ''">
                    AND scene = #{scene}
                </if>

            UNION ALL

            -- 递归查询子部门
            SELECT d.id
            FROM department d
            INNER JOIN department_tree dt ON d.parent_id = dt.id
            WHERE d.is_deleted = false
                <if test="scene != null and scene != ''">
                    AND d.scene = #{scene}
                </if>
        )
        SELECT id
        FROM department_tree
        ORDER BY id
    </select>

</mapper>
