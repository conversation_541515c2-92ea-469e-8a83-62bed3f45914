<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.infra.dal.mapper.PermissionMapper">

    <resultMap id="BaseResultMap" type="com.deepinnet.infra.dal.dataobject.PermissionDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
            <result property="code" column="code" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="isDeleted" column="is_deleted" jdbcType="BOOLEAN"/>
            <result property="gmtCreated" column="gmt_created" jdbcType="TIMESTAMP"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="scene" column="scene" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,parent_id,code,
        name,is_deleted,biz_data,
        gmt_created,gmt_modified
    </sql>
    <select id="getPermissionTree"
            resultType="com.deepinnet.infra.api.dto.PermissionDTO">
        WITH RECURSIVE permission_tree AS (
            -- 根节点
            SELECT id AS id,
            parent_id AS parentId,
            code,
            name,
            scene,
            1 AS levels
            FROM permission
            WHERE parent_id = -1
            AND is_deleted = false
            <if test="scene != null and scene != ''">
                AND scene = #{scene}
            </if>

            UNION ALL

            -- 子节点递归
            SELECT p.id AS id,
                p.parent_id AS parentId,
                p.code,
                p.name,
                p.scene,
            pt.levels + 1 AS levels
            FROM permission p
            INNER JOIN permission_tree pt
            ON p.parent_id = pt.id
            WHERE p.is_deleted = false
            <if test="scene != null and scene != ''">
                AND p.scene = #{scene}
            </if>
            AND pt.levels &lt; 5
        )
        SELECT *
        FROM permission_tree
    </select>
</mapper>
