<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.infra.dal.mapper.DepartmentMemberMapper">

    <resultMap id="BaseResultMap" type="com.deepinnet.infra.dal.dataobject.DepartmentMemberDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="departmentId" column="department_id" jdbcType="BIGINT"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
            <result property="position" column="position" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="occupyStatus" column="occupy_status" jdbcType="VARCHAR"/>
            <result property="identityType" column="identity_type" jdbcType="VARCHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="isDeleted" column="is_deleted" jdbcType="BOOLEAN"/>
            <result property="bizData" column="biz_data" jdbcType="VARCHAR"/>
            <result property="gmtCreated" column="gmt_created" jdbcType="TIMESTAMP"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="scene" column="scene" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,department_id,phone,email,position,
        name,type,occupy_status,identity_type,tenant_id,
        is_deleted,biz_data,gmt_created,gmt_modified,scene
    </sql>
</mapper>
