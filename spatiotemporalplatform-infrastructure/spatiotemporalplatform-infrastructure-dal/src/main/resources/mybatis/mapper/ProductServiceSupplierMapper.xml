<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.infra.dal.mapper.ProductServiceSupplierMapper">

    <resultMap id="BaseResultMap" type="com.deepinnet.infra.dal.dataobject.ProductServiceSupplierDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="companyName" column="company_name" jdbcType="VARCHAR"/>
            <result property="userNo" column="user_no" jdbcType="VARCHAR"/>
            <result property="address" column="address" jdbcType="VARCHAR"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="creditCode" column="credit_code" jdbcType="VARCHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="gmtCreated" column="gmt_created" jdbcType="TIMESTAMP"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="is_deleted" jdbcType="BOOLEAN"/>
            <result property="cooperationAgreement" column="cooperation_agreement" jdbcType="BOOLEAN"/>
            <result property="registrationTime" column="registration_time" jdbcType="TIMESTAMP"/>
            <result property="approvalStatus" column="approval_status" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,company_name,user_no,
        address,phone,credit_code,
        tenant_id,gmt_created,gmt_modified,
        is_deleted,cooperation_agreement,registration_time,approval_status
    </sql>
    
    <!-- 分页查询服务商列表（联表查询联系人信息） -->
    <select id="pageQuerySuppliersWithContact" resultMap="BaseResultMap">
        SELECT DISTINCT s.*
        FROM product_service_supplier s
        LEFT JOIN user_contact_info u ON s.user_no = u.user_no
        <where>
            AND s.is_deleted = false
            <if test="query.phone != null and query.phone != ''">
                AND s.phone LIKE CONCAT('%', #{query.phone}, '%')
            </if>
            <if test="query.companyName != null and query.companyName != ''">
                AND s.company_name LIKE CONCAT('%', #{query.companyName}, '%')
            </if>
            <if test="query.contactName != null and query.contactName != ''">
                AND u.name LIKE CONCAT('%', #{query.contactName}, '%')
            </if>
            <if test="query.contactPhone != null and query.contactPhone != ''">
                AND u.phone LIKE CONCAT('%', #{query.contactPhone}, '%')
            </if>
            <if test="query.approvalStatus != null">
                AND s.approval_status = #{query.approvalStatus}
            </if>
        </where>
        ORDER BY s.registration_time DESC
    </select>
</mapper>
