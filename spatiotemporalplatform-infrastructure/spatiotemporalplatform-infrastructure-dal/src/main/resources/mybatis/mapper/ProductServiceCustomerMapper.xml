<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.infra.dal.mapper.ProductServiceCustomerMapper">

    <resultMap id="BaseResultMap" type="com.deepinnet.infra.dal.dataobject.ProductServiceCustomerDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="organizationName" column="organization_name" jdbcType="VARCHAR"/>
            <result property="address" column="address" jdbcType="VARCHAR"/>
            <result property="userNo" column="user_no" jdbcType="VARCHAR"/>
            <result property="identifiedPictureList" column="identified_picture_list" jdbcType="VARCHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="isDeleted" column="is_deleted" jdbcType="BOOLEAN"/>
            <result property="gmtCreated" column="gmt_created" jdbcType="TIMESTAMP"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="regionCode" column="region_code" jdbcType="VARCHAR"/>
            <result property="regionName" column="region_name" jdbcType="VARCHAR"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="cooperationAgreement" column="cooperation_agreement" jdbcType="BOOLEAN"/>
            <result property="registrationTime" column="registration_time" jdbcType="TIMESTAMP"/>
            <result property="identityType" column="identity_type" jdbcType="INTEGER"/>
            <result property="socialCreditCode" column="social_credit_code" jdbcType="VARCHAR"/>
            <result property="approvalStatus" column="approval_status" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,organization_name,address,
        user_no,identified_picture_list,tenant_id,
        is_deleted,gmt_created,gmt_modified,
        region_code,region_name,phone,
        cooperation_agreement,registration_time,identity_type,social_credit_code,approval_status
    </sql>
    
    <!-- 分页查询客户列表（联表查询联系人信息） -->
    <select id="pageQueryCustomersWithContact" resultMap="BaseResultMap">
        SELECT DISTINCT
        <include refid="Base_Column_List"/>
        FROM product_service_customer psc
        LEFT JOIN user_contact_info uci ON psc.user_no = uci.user_no
        <where>
            psc.is_deleted = false
            <if test="query.phone != null and query.phone != ''">
                AND psc.phone LIKE CONCAT('%', #{query.phone}, '%')
            </if>
            <if test="query.organizationName != null and query.organizationName != ''">
                AND psc.organization_name LIKE CONCAT('%', #{query.organizationName}, '%')
            </if>
            <if test="query.contactName != null and query.contactName != ''">
                AND uci.contact_name LIKE CONCAT('%', #{query.contactName}, '%')
            </if>
            <if test="query.contactPhone != null and query.contactPhone != ''">
                AND uci.contact_phone LIKE CONCAT('%', #{query.contactPhone}, '%')
            </if>
            <if test="query.approvalStatus != null and query.approvalStatus != ''">
                AND psc.approval_status = #{query.approvalStatus}
            </if>
        </where>
        ORDER BY psc.registration_time DESC
    </select>
</mapper>
