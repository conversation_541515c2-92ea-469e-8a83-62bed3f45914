<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.infra.dal.mapper.UserContactInfoMapper">

    <resultMap id="BaseResultMap" type="com.deepinnet.infra.dal.dataobject.UserContactInfoDO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="userNo" column="user_no" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="position" column="position" jdbcType="VARCHAR"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,user_no,name,
        phone,position,tenant_id
    </sql>
</mapper>
