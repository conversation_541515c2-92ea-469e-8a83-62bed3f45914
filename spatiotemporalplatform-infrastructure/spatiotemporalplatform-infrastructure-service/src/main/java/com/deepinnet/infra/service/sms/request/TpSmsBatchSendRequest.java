package com.deepinnet.infra.service.sms.request;

import com.deepinnet.infra.api.template.SmsBaseTemplate;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR> wong
 * @create 2024/6/4 11:00
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TpSmsBatchSendRequest implements Serializable {

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 模板
     */
    private SmsBaseTemplate smsTemplate;

    /**
     * 发送的参数
     */
    private String sendParam;
}
