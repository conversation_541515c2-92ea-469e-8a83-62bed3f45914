package com.deepinnet.infra.service.convert;

import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.dal.dataobject.RoleDO;
import org.mapstruct.*;

import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2024/11/25 17:38
 * @Description
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface RoleConvert {
    RoleFlatDTO toDTO(RoleDO roleDO);

    List<RoleFlatDTO> toDTOList(List<RoleDO> roleDOs);

    @Mappings({
            @Mapping(target = "createTime", source = "gmtCreated")
    })
    RoleDTO toRoleDTO(RoleDO roleDO);

    List<RoleDTO> toRoleDTOList(List<RoleDO> roleDOs);
}
