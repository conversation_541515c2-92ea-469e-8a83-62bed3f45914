package com.deepinnet.infra.service.util;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.infra.dal.dataobject.*;
import com.deepinnet.infra.dal.repository.*;
import com.deepinnet.infra.service.error.BizErrorCode;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2025/7/25 17:49
 * @Description
 */
@Component
public class DataPermissionUtil {

    @Resource
    private UserDepartmentRepository userDepartmentRepository;

    @Resource
    private DepartmentRepository departmentRepository;

    public DepartmentDO getRootDepartment(String userNo) {
        Long rootId = getRootDepartmentId(userNo);
        return departmentRepository.getById(rootId);
    }

    public Long getRootDepartmentId(String userNo) {
        UserDepartmentDO userDepartment = userDepartmentRepository.getOne(Wrappers.lambdaQuery(UserDepartmentDO.class)
                .eq(UserDepartmentDO::getUserNo, userNo));
        if (userDepartment == null) {
            throw new BizException(BizErrorCode.USER_DEPARTMENT_NOT_FOUND.getCode(), BizErrorCode.USER_DEPARTMENT_NOT_FOUND.getDesc());
        }

        DepartmentDO departmentDO = departmentRepository.getById(userDepartment.getDepartmentId());
        if (departmentDO == null) {
            throw new BizException(BizErrorCode.DEPARTMENT_NOT_FOUND.getCode(), BizErrorCode.DEPARTMENT_NOT_FOUND.getDesc());
        }

        String fullPath = departmentDO.getFullPath();
        List<Long> departmentIds = JSONUtil.toList(fullPath, Long.class);
        departmentIds.remove(-1L);

        return departmentIds.get(0);
    }
}
