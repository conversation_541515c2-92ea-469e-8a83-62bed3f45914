package com.deepinnet.infra.service.impl;

import cn.dev33.satoken.secure.SaSecureUtil;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.infra.api.constants.DataScopeConstants;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.api.enums.*;
import com.deepinnet.infra.dal.condition.AccountQueryCondition;
import com.deepinnet.infra.dal.dataobject.*;
import com.deepinnet.infra.dal.mapper.AccountMapper;
import com.deepinnet.infra.dal.repository.*;
import com.deepinnet.infra.service.*;
import com.deepinnet.infra.service.annotation.OperationLog;
import com.deepinnet.infra.service.context.OperationLogContext;
import com.deepinnet.infra.service.convert.RoleConvert;
import com.deepinnet.infra.service.enums.PermissionSystemEnum;
import com.deepinnet.infra.service.error.BizErrorCode;
import com.deepinnet.infra.service.util.*;
import com.github.pagehelper.*;
import com.google.common.collect.*;
import org.apache.commons.collections4.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wong
 * @create 2024/11/27 12:39
 * @Description
 */
@Service
public class AdminServiceImpl implements AdminService {

    @Resource
    private AccountRepository accountRepository;

    @Resource
    private UserRoleRepository userRoleRepository;

    @Resource
    private RoleRepository roleRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private AccountMapper accountMapper;

    @Resource
    private UserAccountRepository userAccountRepository;

    @Resource
    private UserDepartmentRepository userDepartmentRepository;

    @Resource
    private DepartmentMemberRepository departmentMemberRepository;

    @Resource
    private UserMemberRepository userMemberRepository;

    @Resource
    private ProductServiceCustomerRepository productServiceCustomerRepository;

    @Resource
    private DepartmentRepository departmentRepository;

    @Resource
    private RoleConvert roleConvert;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private ProductServiceCustomerRepository customerRepository;

    @Resource
    private UserService userService;

    @Resource
    private DataPermissionProcessor dataPermissionProcessor;

    private static final Long ROOT_PARENT_ID = -1L;

    @Value("${salt}")
    private String salt;

    @Override
    public CommonPage<UserInfoDTO> pageQueryUserAccount(UserAccountQueryDTO queryDTO) {
        PermissionSystemEnum permissionSystemEnum = dataPermissionProcessor.routeToPermissionSystem();
        List<Long> departmentIds;

        // 查询所有数据，不根据部门id筛选
        if (permissionSystemEnum == PermissionSystemEnum.NO_DATA_PERMISSION) {
            departmentIds = null;
        } else {
            DataAccessDTO availableQueryData = dataPermissionProcessor.getAvailableQueryData();
            if (availableQueryData == null || CollUtil.isEmpty(availableQueryData.getSupportQueryUserNos())) {
                return CommonPage.buildEmptyPage();
            }

            // 超管
            if (!availableQueryData.getSupportQueryUserNos().contains(DataScopeConstants.SUPER_ADMIN)) {
                departmentIds = availableQueryData.getSupportQueryDepartmentIds();
            } else {
                departmentIds = null;
            }
        }

        queryDTO.setDepartmentIds(departmentIds);
        return doPageQueryUserAccount(queryDTO);
    }

    @Override
    @OperationLog(
        module = "成员管理",
        type = "#{#saveDTO.accountNo == null ? '新增' : '编辑'}",
        detail = "#{#saveDTO.accountNo == null ? '新增了成员【' + #saveDTO.name + '】' : '编辑了成员【' + #saveDTO.name + '】'}",
        recordParams = true,
        excludeParams = {"password"}
    )
    public Boolean saveOrUpdateAccount(UserAccountSaveDTO saveDTO) {
        // 数据权限校验
        // 用户和部门的关系是一一对应的
        dataPermissionProcessor.checkDataPermissionByDepartmentId(saveDTO.getDepartmentIdList().get(0));

        // 保存
        if (StringUtils.isBlank(saveDTO.getAccountNo())) {
            saveAccount(saveDTO);
            // 设置上下文信息，用于日志记录
            OperationLogContext.setContextData("memberName", saveDTO.getName());
        } else {
            updateAccount(saveDTO);
            OperationLogContext.setContextData("memberName", saveDTO.getName());
        }

        return true;
    }

    @Override
    public AccountDTO getAccountDetail(String accountNo) {
        AccountDO account = accountRepository.getAccount(accountNo, null);
        if (account == null) {
            throw new BizException(BizErrorCode.ACCOUNT_NOT_FOUND.getCode(), BizErrorCode.ACCOUNT_NOT_FOUND.getDesc());
        }

        List<UserRoleDO> accountRoleList = userRoleRepository.list(Wrappers.lambdaQuery(UserRoleDO.class)
                .eq(UserRoleDO::getUserNo, account.getUserNo()));
        List<String> roleCodeList = accountRoleList.stream().map(UserRoleDO::getRoleCode)
                .collect(Collectors.toList());
        List<RoleDO> roleDOList = roleRepository.list(Wrappers.lambdaQuery(RoleDO.class)
                .in(RoleDO::getRoleCode, roleCodeList));
        List<RoleDTO> roleDTOList = roleConvert.toRoleDTOList(roleDOList);
        List<UserDepartmentDO> userDepartments = userDepartmentRepository.list(Wrappers.lambdaQuery(UserDepartmentDO.class)
                .eq(UserDepartmentDO::getUserNo, account.getUserNo()));

        // 获取用户关联的成员信息
        DepartmentMemberDO relatedMember = getUserRelatedMember(account.getUserNo());

        return buildAccountDTO(account, roleDTOList, userDepartments, relatedMember);
    }

    @Override
    @OperationLog(
        module = "成员管理",
        type = "删除",
        detail = "删除了成员【#{#account.name}】"
    )
    public Boolean deleteAccount(String accountNo) {
        AccountDO account = accountRepository.getAccount(accountNo, null);
        if (account == null) {
            throw new BizException(BizErrorCode.ACCOUNT_NOT_FOUND.getCode(), BizErrorCode.ACCOUNT_NOT_FOUND.getDesc());
        }

        String currentAccountNo = StpUtil.getLoginIdAsString();
        if (StrUtil.equals(currentAccountNo, accountNo)) {
            throw new BizException(BizErrorCode.DELETE_SELF_ACCOUNT_ERROR.getCode(), BizErrorCode.DELETE_SELF_ACCOUNT_ERROR.getDesc());
        }

        // 设置上下文信息供日志使用
        OperationLogContext.setContextData("account", account);

        // 数据权限校验
        dataPermissionProcessor.checkDataPermissionByUserNo(account.getUserNo());

        // 获取当前绑定的成员信息，用于释放占用状态
        DepartmentMemberDO relatedMember = getUserRelatedMember(account.getUserNo());

        List<UserRoleDO> existUserRoleDOS = userRoleRepository.list(Wrappers.lambdaQuery(UserRoleDO.class)
                .eq(UserRoleDO::getUserNo, account.getUserNo()));

        transactionTemplate.executeWithoutResult(action -> {
            // 删除用户，目前用户和账户是一对一可以直接删除
            userRepository.remove(Wrappers.lambdaQuery(UserDO.class)
                    .eq(UserDO::getUserNo, account.getUserNo()));

            userAccountRepository.remove(Wrappers.lambdaQuery(UserAccountDO.class)
                    .eq(UserAccountDO::getAccountNo, accountNo));

            userRoleRepository.removeBatchByIds(existUserRoleDOS);

            accountRepository.remove(Wrappers.lambdaQuery(AccountDO.class)
                    .eq(AccountDO::getAccountNo, accountNo));

            // 用户部门关联关系也需要删除
            userDepartmentRepository.remove(Wrappers.lambdaQuery(UserDepartmentDO.class)
                    .eq(UserDepartmentDO::getUserNo, account.getUserNo()));

            // 删除用户与成员的关联关系，但不删除成员本身
            userMemberRepository.remove(Wrappers.lambdaQuery(UserMemberDO.class)
                    .eq(UserMemberDO::getUserNo, account.getUserNo()));

            // 释放成员的占用状态
            if (relatedMember != null) {
                relatedMember.setOccupyStatus(MemberOccupyStatusEnum.AVAILABLE.getCode());
                departmentMemberRepository.updateById(relatedMember);
                LogUtil.info("释放成员占用状态，memberId={}, memberName={}", relatedMember.getId(), relatedMember.getName());
            }

            // 删除客户需求方，一网统飞里需要用到
            if (StrUtil.equals(account.getUserType(), UserTypeEnum.CUSTOMER.getCode())) {
                customerRepository.remove(Wrappers.lambdaQuery(ProductServiceCustomerDO.class)
                        .eq(ProductServiceCustomerDO::getUserNo, account.getUserNo()));
            }

            LogUtil.info("删除用户账户完成，删除账号但保留成员，accountNo={}, userNo={}, 成员占用状态已释放",
                    accountNo, account.getUserNo());
        });

        StpUtil.logout(account.getAccountNo());

        return true;
    }

    private CommonPage<UserInfoDTO> doPageQueryUserAccount(UserAccountQueryDTO queryDTO) {
        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        AccountQueryCondition queryCondition = new AccountQueryCondition();
        BeanUtils.copyProperties(queryDTO, queryCondition);
        List<AccountDO> accountList = accountMapper.searchAccount(queryCondition);
        if (CollectionUtils.isEmpty(accountList)) {
            return CommonPage.buildEmptyPage();
        }

        List<String> creatorIds = accountList.stream()
                .map(AccountDO::getCreatorId)
                .distinct()
                .collect(Collectors.toList());
        List<AccountDO> creatorAccountList = accountRepository.list(Wrappers.lambdaQuery(AccountDO.class)
                .in(AccountDO::getAccountNo, creatorIds));
        Map<String, AccountDO> creatorMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(creatorAccountList)) {
            creatorMap = creatorAccountList.stream()
                    .collect(Collectors.toMap(AccountDO::getAccountNo, Function.identity()));
        }

        // 获取用户关联的成员信息
        List<String> userNos = accountList.stream()
                .map(AccountDO::getUserNo)
                .distinct()
                .collect(Collectors.toList());
        Map<String, DepartmentMemberDO> userMemberMap = getUserMemberMap(userNos);

        // 构建部门名称
        Map<String, List<List<String>>> departmentNamePathMap = null;
        Map<String, List<List<SimpleDepartmentDTO>>> departmentMap = null;
        Map<String, List<String>> departmentIdPathMap = buildDepartmentIdPath(accountList);

        if (MapUtils.isNotEmpty(departmentIdPathMap)) {
            Pair<Map<String, List<List<String>>>, Map<String, List<List<SimpleDepartmentDTO>>>> pair = buildDepartmentNamePath(departmentIdPathMap);
            departmentNamePathMap = pair.getKey();
            departmentMap = pair.getValue();
        }

        PageInfo<AccountDO> pageInfo = PageInfo.of(accountList);
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPages(page.getPages());

        List<UserInfoDTO> userInfoDTOs = buildUserInfoDTOs(accountList, creatorMap, departmentIdPathMap, departmentNamePathMap, departmentMap, userMemberMap);
        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), pageInfo.getPages(), pageInfo.getTotal(), userInfoDTOs);
    }

    private Pair<Map<String, List<List<String>>>, Map<String, List<List<SimpleDepartmentDTO>>>> buildDepartmentNamePath(Map<String, List<String>> departmentPathMap) {
        // 获取部门关联的所有的id
        List<Long> departmentIds = Lists.newArrayList();
        departmentPathMap.forEach((userNo, idPathList) -> idPathList.forEach(id -> departmentIds.addAll(JSONUtil.toList(id, Long.class))));

        List<Long> distinctDepartmentIds = departmentIds.stream()
                .distinct()
                .collect(Collectors.toList());
        List<DepartmentDO> allDepartmentList = departmentRepository.listByIds(distinctDepartmentIds);
        Map<Long, DepartmentDO> departmentMap = allDepartmentList.stream()
                .collect(Collectors.toMap(DepartmentDO::getId, Function.identity()));

        Map<String, List<List<String>>> departmentNamePathMap = Maps.newHashMap();
        Map<String, List<List<SimpleDepartmentDTO>>> simDepartmentMap = Maps.newHashMap();

        departmentPathMap.forEach((userNo, idPathList) -> {
            List<List<String>> currentUserPathNameList = Lists.newArrayList();
            List<List<SimpleDepartmentDTO>> currentUserDepartmentList = Lists.newArrayList();

            for (String mergedIdPath : idPathList) {
                List<String> currentDepartmentNames = Lists.newArrayList();
                List<SimpleDepartmentDTO> simpleDepartmentDTOs = Lists.newArrayList();
                List<Long> splitIds = JSONUtil.toList(mergedIdPath, Long.class);
                for (Long splitId : splitIds) {
                    if (splitId == -1) {
                        continue;
                    }
                    String name = departmentMap.get(splitId).getName();
                    currentDepartmentNames.add(name);

                    SimpleDepartmentDTO simpleDepartmentDTO = new SimpleDepartmentDTO();
                    simpleDepartmentDTO.setId(splitId);
                    simpleDepartmentDTO.setName(name);
                    simpleDepartmentDTOs.add(simpleDepartmentDTO);
                }
                currentUserPathNameList.add(currentDepartmentNames);
                currentUserDepartmentList.add(simpleDepartmentDTOs);
            }

            departmentNamePathMap.put(userNo, currentUserPathNameList);
            simDepartmentMap.put(userNo, currentUserDepartmentList);
        });

        return Pair.of(departmentNamePathMap, simDepartmentMap);
    }

    private Map<String, List<String>> buildDepartmentIdPath(List<AccountDO> accountList) {
        List<String> userNos = accountList.stream()
                .map(AccountDO::getUserNo)
                .distinct()
                .collect(Collectors.toList());
        List<UserDepartmentDO> userDepartmentDOs = userDepartmentRepository.list(Wrappers.lambdaQuery(UserDepartmentDO.class)
                .in(UserDepartmentDO::getUserNo, userNos));

        if (CollectionUtils.isEmpty(userDepartmentDOs)) {
            return null;
        }

        List<Long> departmentIds = userDepartmentDOs.stream()
                .map(UserDepartmentDO::getDepartmentId)
                .distinct()
                .collect(Collectors.toList());
        List<DepartmentDO> userDirectRelatedDepartments = departmentRepository.listByIds(departmentIds);

        Map<String, List<Long>> userDepartmentMap = userDepartmentDOs.stream()
                .collect(Collectors.groupingBy(
                        UserDepartmentDO::getUserNo,
                        Collectors.mapping(
                                UserDepartmentDO::getDepartmentId,
                                Collectors.toList()
                        )
                ));

        Map<Long, DepartmentDO> departmentMap = userDirectRelatedDepartments.stream()
                .collect(Collectors.toMap(DepartmentDO::getId, Function.identity()));
        Map<String, List<String>> departmentPathIdMap = Maps.newHashMap();

        userDepartmentMap.forEach((userNo, ids) -> {
            List<DepartmentDO> currentUserDepartments = Lists.newArrayList();
            ids.forEach(id -> {
                DepartmentDO departmentDO = departmentMap.get(id);
                if (departmentDO != null) {
                    currentUserDepartments.add(departmentDO);
                }
            });

            List<String> fullPath = currentUserDepartments.stream()
                    .map(DepartmentDO::getFullPath)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(fullPath)) {
                List<String> sortedPaths = sortPathsByParentAndLastNode(fullPath);
                departmentPathIdMap.put(userNo, sortedPaths);
            }
        });

        return departmentPathIdMap;
    }

    public List<String> sortPathsByParentAndLastNode(List<String> paths) {
        return paths.stream()
                // 将路径解析为 List<Integer>
                .map(path -> Arrays.stream(path.replaceAll("[\\[\\]]", "").split(","))
                        .map(Integer::parseInt)
                        .collect(Collectors.toList()))
                // 按父级路径（倒数第二个节点）分组，然后按最后一个节点排序
                .sorted((list1, list2) -> {
                    // 获取倒数第二个节点作为父节点
                    Integer parent1 = list1.size() > 1 ? list1.get(list1.size() - 2) : Integer.MIN_VALUE;
                    Integer parent2 = list2.size() > 1 ? list2.get(list2.size() - 2) : Integer.MIN_VALUE;

                    // 比较父节点
                    int parentComparison = parent1.compareTo(parent2);
                    if (parentComparison != 0) {
                        return parentComparison;
                    }

                    // 如果父节点相同，按最后一个节点排序
                    Integer last1 = list1.get(list1.size() - 1);
                    Integer last2 = list2.get(list2.size() - 1);
                    return last1.compareTo(last2);
                })
                // 转换回原始的字符串格式（加上方括号）
                .map(path -> path.stream()
                        .map(String::valueOf)
                        .collect(Collectors.joining(",", "[", "]")))
                .collect(Collectors.toList());
    }

    private void updateAccount(UserAccountSaveDTO saveDTO) {
        AccountDO account = accountRepository.getAccount(saveDTO.getAccountNo(), null);
        if (account == null) {
            throw new BizException(BizErrorCode.ACCOUNT_NOT_FOUND.getCode(), BizErrorCode.ACCOUNT_NOT_FOUND.getDesc());
        }

        DepartmentMemberDO newMember = departmentMemberRepository.getById(saveDTO.getBindMemberId());
        if (newMember == null) {
            throw new BizException(BizErrorCode.BIND_MEMBER_NOT_FOUND.getCode(), BizErrorCode.BIND_MEMBER_NOT_FOUND.getDesc());
        }

        // 检查成员是否已经被其他用户绑定（排除当前用户）
        validateMemberBinding(saveDTO.getBindMemberId(), account.getUserNo());

        // 获取当前绑定的成员信息，用于释放占用状态
        DepartmentMemberDO oldMember = getUserRelatedMember(account.getUserNo());

        List<UserRoleDO> existUserRoleDOS = userRoleRepository.list(Wrappers.lambdaQuery(UserRoleDO.class)
                .eq(UserRoleDO::getUserNo, account.getUserNo()));
        List<UserDepartmentDO> existUserDepartmentDOs = userDepartmentRepository.list(Wrappers.lambdaQuery(UserDepartmentDO.class)
                .eq(UserDepartmentDO::getUserNo, account.getUserNo()));
        List<UserDepartmentDO> newUserDepartmentDOs = buildUserDepartmentList(account.getUserNo(), saveDTO.getDepartmentIdList());

        AccountDO needUpdateAccount = updateAccount(saveDTO, account);
        List<UserRoleDO> userRoleDOS = buildUserRoleRelation(saveDTO.getRoleCodeList(), account.getUserNo());

        // 构建用户和成员绑定关系
        UserMemberDO userMemberDO = buildUserMember(saveDTO, needUpdateAccount.getUserNo());

        transactionTemplate.executeWithoutResult(action -> {
            // 更新用户名称
            userRepository.update(Wrappers.lambdaUpdate(UserDO.class)
                    .eq(UserDO::getUserNo, needUpdateAccount.getUserNo())
                    .set(UserDO::getUserName, newMember.getName()));
            userDepartmentRepository.removeBatchByIds(existUserDepartmentDOs);
            accountRepository.updateById(needUpdateAccount);
            userRoleRepository.removeBatchByIds(existUserRoleDOS);
            userRoleRepository.saveBatch(userRoleDOS);
            userDepartmentRepository.saveBatch(newUserDepartmentDOs);

            // 删除旧的关联关系
            userMemberRepository.remove(Wrappers.lambdaQuery(UserMemberDO.class)
                    .eq(UserMemberDO::getUserNo, account.getUserNo()));
            userMemberRepository.save(userMemberDO);

            // 更新成员占用状态
            if (oldMember != null && !oldMember.getId().equals(newMember.getId())) {
                // 如果更换了绑定成员，释放旧成员的占用状态
                oldMember.setOccupyStatus(MemberOccupyStatusEnum.AVAILABLE.getCode());
                departmentMemberRepository.updateById(oldMember);
                LogUtil.info("释放旧成员占用状态，oldMemberId={}, oldMemberName={}", oldMember.getId(), oldMember.getName());
            }

            // 设置新成员为已占用状态
            newMember.setOccupyStatus(MemberOccupyStatusEnum.OCCUPIED.getCode());
            departmentMemberRepository.updateById(newMember);

            LogUtil.info("更新用户账户完成，accountNo={}, userNo={}, oldMemberId={}, newMemberId={}, 成员占用状态已更新",
                    saveDTO.getAccountNo(), account.getUserNo(),
                    oldMember != null ? oldMember.getId() : null, saveDTO.getBindMemberId());
        });
    }

    private void saveAccount(UserAccountSaveDTO saveDTO) {
        Assert.hasText(saveDTO.getPassword(), "密码不能为空");
        Assert.hasText(saveDTO.getPhone(), "手机号不能为空");

        List<AccountDO> existAccounts = accountRepository.list(Wrappers.lambdaQuery(AccountDO.class)
                .eq(AccountDO::getPhone, saveDTO.getPhone()));

        DepartmentMemberDO member = departmentMemberRepository.getById(saveDTO.getBindMemberId());
        if (member == null) {
            throw new BizException(BizErrorCode.BIND_MEMBER_NOT_FOUND.getCode(), BizErrorCode.BIND_MEMBER_NOT_FOUND.getDesc());
        }

        // 检查成员是否已经被其他用户绑定
        validateMemberBinding(saveDTO.getBindMemberId(), null);

        // 校验手机号重复
        checkDuplicatePhone(saveDTO, existAccounts);

        String userNo = BizNoGenerateUtil.generateUserNo();
        String accountNo = BizNoGenerateUtil.generateAccountNo();

        AccountDO accountDO = buildAccount(saveDTO, userNo, accountNo, StpUtil.getLoginIdAsString(), member.getName());
        UserDO user = buildUser(saveDTO, userNo, member.getName());
        UserAccountDO userAccountDO = buildUserAccount(userNo, accountNo);
        List<UserDepartmentDO> userDepartmentDOs = buildUserDepartmentList(userNo, saveDTO.getDepartmentIdList());

        // 需求方，需要保存customer表
        ProductServiceCustomerDO customerDO = buildCustomer(saveDTO, userNo);

        // 构建用户和成员绑定关系
        UserMemberDO userMemberDO = buildUserMember(saveDTO, userNo);

        List<UserRoleDO> userRoleDOS = buildUserRoleRelation(saveDTO.getRoleCodeList(), accountDO.getUserNo());
        transactionTemplate.executeWithoutResult(action -> {
            userRepository.save(user);
            userAccountRepository.save(userAccountDO);
            accountRepository.save(accountDO);
            userRoleRepository.saveBatch(userRoleDOS);
            userDepartmentRepository.saveBatch(userDepartmentDOs);
            userMemberRepository.save(userMemberDO);

            // 更新成员占用状态为已占用
            member.setOccupyStatus(MemberOccupyStatusEnum.OCCUPIED.getCode());
            departmentMemberRepository.updateById(member);

            if (StrUtil.equals(saveDTO.getUserType(), UserTypeEnum.CUSTOMER.getCode())) {
                productServiceCustomerRepository.save(customerDO);
            }

            LogUtil.info("创建用户账户完成，accountNo={}, userNo={}, bindMemberId={}, 成员状态已更新为已占用",
                    accountNo, userNo, saveDTO.getBindMemberId());
        });
    }

    private UserMemberDO buildUserMember(UserAccountSaveDTO saveDTO, String userNo) {
        UserMemberDO userMemberDO = new UserMemberDO();
        userMemberDO.setUserNo(userNo);
        userMemberDO.setMemberId(saveDTO.getBindMemberId());
        userMemberDO.setGmtCreated(new Date());
        userMemberDO.setGmtModified(new Date());
        return userMemberDO;
    }

    private ProductServiceCustomerDO buildCustomer(UserAccountSaveDTO saveDTO, String userNo) {
        ProductServiceCustomerDO customerDO = null;
        if (StrUtil.equals(saveDTO.getUserType(), UserTypeEnum.CUSTOMER.getCode())) {
            customerDO = new ProductServiceCustomerDO();
            customerDO.setUserNo(userNo);
            customerDO.setPhone(saveDTO.getPhone());
            // 从当前登录用户的租户信息中获取租户ID
            if (StpUtil.getExtra("tenantId") != null) {
                customerDO.setTenantId(StpUtil.getExtra("tenantId").toString());
            }
            customerDO.setApprovalStatus(-1); // 设置初始状态为 -1（未认证）
            customerDO.setGmtCreated(new Date());
            customerDO.setGmtModified(new Date());
            customerDO.setRegistrationTime(LocalDateTime.now());
        }
        return customerDO;
    }

    private void checkDuplicatePhone(UserAccountSaveDTO saveDTO, List<AccountDO> existAccounts) {
        boolean existUser;
        if (CollectionUtils.isNotEmpty(existAccounts)) {
            if (StrUtil.isBlank(saveDTO.getUserType())) {
                existUser = existAccounts.stream()
                        .anyMatch(a -> StrUtil.isBlank(a.getUserType()));
            } else {
                existUser = existAccounts.stream()
                        .anyMatch(a -> StrUtil.equals(saveDTO.getUserType(), a.getUserType()));
            }

            if (existUser) {
                throw new BizException(BizErrorCode.DUPLICATE_PHONE_NUMBER.getCode(), BizErrorCode.DUPLICATE_PHONE_NUMBER.getDesc());
            }
        }
    }

    private List<UserDepartmentDO> buildUserDepartmentList(String userNo, List<Long> departmentIdList) {
        // 查询所有部门信息
        List<DepartmentDO> departments = departmentRepository.listByIds(departmentIdList);
        if (CollectionUtils.isEmpty(departments)) {
            throw new BizException(BizErrorCode.ORGANIZATION_NOT_EXIST.getCode(), "部门已经被删除，请刷新页面后重新选择");
        }

        departments.forEach(department -> {
            if (!departmentIdList.contains(department.getId())) {
                throw new BizException(BizErrorCode.ORGANIZATION_NOT_EXIST.getCode(), "部门已经被删除，请刷新页面后重新选择");
            }
        });

        // 构建 UserDepartmentDO 对象列表
        List<UserDepartmentDO> userDepartmentDOS = Lists.newArrayList();
        departments.forEach(department -> {
            UserDepartmentDO userDepartmentDO = new UserDepartmentDO();
            userDepartmentDO.setUserNo(userNo);
            userDepartmentDO.setDepartmentId(department.getId());
            userDepartmentDOS.add(userDepartmentDO);
        });

        return userDepartmentDOS;
    }

    private UserDO buildUser(UserAccountSaveDTO saveDTO, String userNo, String name) {
        UserDO userDO = new UserDO();
        userDO.setUserNo(userNo);
        userDO.setUserName(name);
        userDO.setPhone(saveDTO.getPhone());
        userDO.setRegistrationTime(LocalDateTime.now());
        userDO.setUserType(saveDTO.getUserType());
        return userDO;
    }


    private List<UserInfoDTO> buildUserInfoDTOs(List<AccountDO> accountList, Map<String, AccountDO> creatorMap, Map<String, List<String>> departmentIdPathMap,
                                                Map<String, List<List<String>>> departmentNamePathMap, Map<String, List<List<SimpleDepartmentDTO>>> departmentMap, Map<String, DepartmentMemberDO> userMemberMap) {
        List<UserInfoDTO> userInfoDTOs = Lists.newArrayList();
        accountList.forEach(account -> {
            UserInfoDTO userInfoDTO = new UserInfoDTO();
            userInfoDTO.setAccountNo(account.getAccountNo());
            userInfoDTO.setAccount(account.getAccount());
            userInfoDTO.setUserNo(account.getUserNo());
            userInfoDTO.setName(account.getName());
            userInfoDTO.setPhone(account.getPhone());
            userInfoDTO.setCreateTime(account.getGmtCreated());
            // 设置账号状态
            userInfoDTO.setStatus(account.getStatus());

            if (MapUtils.isNotEmpty(departmentIdPathMap)) {
                userInfoDTO.setDepartmentIdPath(departmentIdPathMap.get(account.getUserNo()));
                userInfoDTO.setDepartmentNamePath(departmentNamePathMap.get(account.getUserNo()));
                userInfoDTO.setDepartments(departmentMap.get(account.getUserNo()));
            }

            if (MapUtils.isNotEmpty(creatorMap)) {
                AccountDO creator = creatorMap.get(account.getCreatorId());
                if (creator != null) {
                    userInfoDTO.setCreatorName(creator.getName());
                }
                userInfoDTO.setCreatorId(account.getCreatorId());
            }

            DepartmentMemberDO member = userMemberMap.get(account.getUserNo());
            if (member != null) {
                UserMemberBindDTO userMemberBindDTO = buildUserMemberBindDTO(member);
                userInfoDTO.setUserMemberBindDTO(userMemberBindDTO);
            }

            userInfoDTOs.add(userInfoDTO);
        });

        return userInfoDTOs;
    }

    private UserMemberBindDTO buildUserMemberBindDTO(DepartmentMemberDO member) {
        UserMemberBindDTO userMemberBindDTO = new UserMemberBindDTO();
        userMemberBindDTO.setName(member.getName());
        userMemberBindDTO.setMemberId(member.getId());
        return userMemberBindDTO;
    }

    private AccountDO buildAccount(UserAccountSaveDTO saveDTO, String userNo, String accountNo, String creatorId, String name) {
        AccountDO accountDO = new AccountDO();
        accountDO.setUserNo(userNo);
        accountDO.setAccountNo(accountNo);
        accountDO.setAccount(saveDTO.getPhone());
        accountDO.setPhone(saveDTO.getPhone());
        accountDO.setName(name);
        accountDO.setCreatorId(creatorId);
        accountDO.setRegistrationTime(LocalDateTime.now());
        accountDO.setUserType(saveDTO.getUserType());
        String encrypt = SaSecureUtil.aesEncrypt(salt, saveDTO.getPassword());
        accountDO.setPassword(encrypt);
        return accountDO;
    }

    private AccountDO updateAccount(UserAccountSaveDTO saveDTO, AccountDO accountDO) {
        accountDO.setName(null);
        return accountDO;
    }

    private List<UserRoleDO> buildUserRoleRelation(List<String> roleCodeList, String userNo) {
        List<UserRoleDO> userRoleDOS = Lists.newArrayList();
        roleCodeList.forEach(roleCode -> {
            UserRoleDO userRoleDO = new UserRoleDO();
            userRoleDO.setUserNo(userNo);
            userRoleDO.setRoleCode(roleCode);
            userRoleDOS.add(userRoleDO);
        });

        return userRoleDOS;
    }

    private AccountDTO buildAccountDTO(AccountDO account, List<RoleDTO> roleDTOList, List<UserDepartmentDO> userDepartmentDOs, DepartmentMemberDO relatedMember) {
        AccountDTO accountDTO = new AccountDTO();
        accountDTO.setId(account.getId());
        accountDTO.setAccountNo(account.getAccountNo());
        accountDTO.setAccount(account.getAccount());
        accountDTO.setPhone(account.getPhone());
        accountDTO.setName(account.getName());
        accountDTO.setRoleList(roleDTOList);

        // 设置关联的成员信息
        if (relatedMember != null) {
            UserMemberBindDTO userMemberBindDTO = buildUserMemberBindDTO(relatedMember);
            accountDTO.setUserMemberBindDTO(userMemberBindDTO);
            LogUtil.info("用户{}关联的成员信息：memberId={}, memberName={}", account.getUserNo(), relatedMember.getId(), relatedMember.getName());
        }

        if (CollectionUtils.isNotEmpty(userDepartmentDOs)) {
            List<Long> departmentIds = userDepartmentDOs.stream()
                    .map(UserDepartmentDO::getDepartmentId)
                    .distinct()
                    .collect(Collectors.toList());
            accountDTO.setDepartmentIds(departmentIds);

            // 设置账号关联的部门信息
            List<DepartmentDO> departmentDOs = departmentRepository.listByIds(departmentIds);
            Map<Long, DepartmentDO> departmentMap = departmentDOs.stream()
                    .collect(Collectors.toMap(DepartmentDO::getId, Function.identity()));
            List<DepartmentFlatDTO> departmentFlatDTOs = new ArrayList<>();
            for (UserDepartmentDO userDepartmentDO : userDepartmentDOs) {
                DepartmentDO departmentDO = departmentMap.get(userDepartmentDO.getDepartmentId());
                if (departmentDO != null) {
                    DepartmentFlatDTO departmentFlatDTO = new DepartmentFlatDTO();
                    departmentFlatDTO.setId(departmentDO.getId());
                    departmentFlatDTO.setName(departmentDO.getName());
                    departmentFlatDTO.setLevel(departmentDO.getLevel());
                    departmentFlatDTO.setParentId(departmentDO.getParentId());
                    String fullPath = departmentDO.getFullPath();
                    List<Long> fullPathList = JSONUtil.toList(fullPath, Long.class);
                    fullPathList.remove(ROOT_PARENT_ID);
                    departmentFlatDTO.setFullPath(JSONUtil.toJsonStr(fullPathList));
                    departmentFlatDTOs.add(departmentFlatDTO);
                }
            }
            accountDTO.setDepartmentFlatDTOs(departmentFlatDTOs);
        }
        return accountDTO;
    }

    private UserAccountDO buildUserAccount(String userNo, String accountNo) {
        UserAccountDO userAccountDO = new UserAccountDO();
        userAccountDO.setUserNo(userNo);
        userAccountDO.setAccountNo(accountNo);
        return userAccountDO;
    }

    private Map<String, DepartmentMemberDO> getUserMemberMap(List<String> userNos) {
        if (CollectionUtils.isEmpty(userNos)) {
            return Collections.emptyMap();
        }

        // 先获取用户与成员的关联关系
        List<UserMemberDO> userMemberList = userMemberRepository.list(Wrappers.lambdaQuery(UserMemberDO.class)
                .in(UserMemberDO::getUserNo, userNos));

        if (CollectionUtils.isEmpty(userMemberList)) {
            return Collections.emptyMap();
        }

        // 获取所有关联的成员ID
        List<Long> memberIds = userMemberList.stream()
                .map(UserMemberDO::getMemberId)
                .distinct()
                .collect(Collectors.toList());

        // 获取成员详细信息
        List<DepartmentMemberDO> memberList = departmentMemberRepository.listByIds(memberIds);
        Map<Long, DepartmentMemberDO> memberMap = memberList.stream()
                .collect(Collectors.toMap(DepartmentMemberDO::getId, Function.identity()));

        // 构建用户编号到成员信息的映射
        Map<String, DepartmentMemberDO> userMemberMap = new HashMap<>();
        for (UserMemberDO userMember : userMemberList) {
            DepartmentMemberDO member = memberMap.get(userMember.getMemberId());
            if (member != null) {
                userMemberMap.put(userMember.getUserNo(), member);
            }
        }

        LogUtil.info("获取用户成员关联信息完成，用户数量={}, 关联成员数量={}", userNos.size(), userMemberMap.size());
        return userMemberMap;
    }

    /**
     * 验证成员绑定关系
     *
     * @param memberId      成员ID
     * @param excludeUserNo 排除的用户编号（用于更新时排除当前用户）
     */
    private void validateMemberBinding(Long memberId, String excludeUserNo) {
        // 检查成员是否存在
        DepartmentMemberDO member = departmentMemberRepository.getById(memberId);
        if (member == null) {
            throw new BizException(BizErrorCode.BIND_MEMBER_NOT_FOUND.getCode(), "要绑定的成员不存在");
        }

        // 检查成员占用状态
        if (MemberOccupyStatusEnum.OCCUPIED.getCode().equals(member.getOccupyStatus())) {
            // 如果是更新操作，需要检查是否是当前用户绑定的成员
            if (StringUtils.isNotBlank(excludeUserNo)) {
                List<UserMemberDO> currentUserBindings = userMemberRepository.list(Wrappers.lambdaQuery(UserMemberDO.class)
                        .eq(UserMemberDO::getMemberId, memberId)
                        .eq(UserMemberDO::getUserNo, excludeUserNo));

                if (CollectionUtils.isEmpty(currentUserBindings)) {
                    // 成员被其他用户占用
                    LogUtil.error("成员已被其他用户占用，memberId={}, memberName={}, occupyStatus={}",
                            memberId, member.getName(), member.getOccupyStatus());
                    throw new BizException(BizErrorCode.DUPLICATE_MEMBER_NAME.getCode(), "该成员已被其他用户绑定，请选择其他成员");
                }
            } else {
                // 新建操作时，成员已被占用
                LogUtil.error("成员已被占用，无法绑定，memberId={}, memberName={}, occupyStatus={}",
                        memberId, member.getName(), member.getOccupyStatus());
                throw new BizException(BizErrorCode.DUPLICATE_MEMBER_NAME.getCode(), "该成员已被其他用户绑定，请选择其他成员");
            }
        }

        // 双重校验：检查用户成员关联表中的绑定关系
        List<UserMemberDO> existingBindings = userMemberRepository.list(Wrappers.lambdaQuery(UserMemberDO.class)
                .eq(UserMemberDO::getMemberId, memberId));

        if (CollectionUtils.isNotEmpty(existingBindings)) {
            // 如果是更新操作，排除当前用户
            if (StringUtils.isNotBlank(excludeUserNo)) {
                existingBindings = existingBindings.stream()
                        .filter(binding -> !excludeUserNo.equals(binding.getUserNo()))
                        .collect(Collectors.toList());
            }

            if (CollectionUtils.isNotEmpty(existingBindings)) {
                LogUtil.error("成员已被其他用户绑定（关联表校验），memberId={}, 已绑定用户={}", memberId,
                        existingBindings.stream().map(UserMemberDO::getUserNo).collect(Collectors.toList()));
                throw new BizException(BizErrorCode.DUPLICATE_MEMBER_NAME.getCode(), "该成员已被其他用户绑定，请选择其他成员");
            }
        }

        LogUtil.info("成员绑定校验通过，memberId={}, memberName={}, occupyStatus={}",
                memberId, member.getName(), member.getOccupyStatus());
    }

    private DepartmentMemberDO getUserRelatedMember(String userNo) {
        List<UserMemberDO> userMemberList = userMemberRepository.list(Wrappers.lambdaQuery(UserMemberDO.class)
                .eq(UserMemberDO::getUserNo, userNo));
        if (CollectionUtils.isNotEmpty(userMemberList)) {
            UserMemberDO userMember = userMemberList.get(0); // 假设一个用户只有一个成员关联
            return departmentMemberRepository.getById(userMember.getMemberId());
        }
        return null;
    }
}
