package com.deepinnet.infra.service.convert;

import com.deepinnet.infra.api.dto.OperationLogDTO;
import com.deepinnet.infra.dal.dataobject.OperationLogDO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 操作日志转换器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface OperationLogConvert {

    /**
     * DO转DTO
     */
    OperationLogDTO convertToDTO(OperationLogDO operationLogDO);

    /**
     * DO列表转DTO列表
     */
    List<OperationLogDTO> convertToDTO(List<OperationLogDO> operationLogDOList);

    /**
     * DTO转DO
     */
    OperationLogDO convertToDO(OperationLogDTO operationLogDTO);
}
