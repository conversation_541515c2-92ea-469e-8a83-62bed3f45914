package com.deepinnet.infra.service.sms.client;


import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.infra.service.config.GuoYangSmsConfig;
import com.deepinnet.infra.service.constants.*;
import com.deepinnet.infra.service.sms.request.TpSmsBatchSendRequest;
import com.deepinnet.infra.api.template.SmsBaseTemplate;
import com.deepinnet.infra.service.util.SmsSendUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR> wong
 * @create 2023/11/9 14:21
 * @Description 发送短信client
 */
@Component(value = "guoYangYunSmsClient")
public class GuoYangYunSmsClient extends AbstractSmsClient {

    protected SmsRequestResult doSend(String mobile, SmsBaseTemplate smsTemplate, String param) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(SmsConstants.SMS_SIGN_ID, GuoYangSmsConfig.SIGN_ID);
        paramMap.put(SmsConstants.APP_KEY, GuoYangSmsConfig.APP_KEY);
        paramMap.put(SmsConstants.APP_SECRET, GuoYangSmsConfig.APP_SECRET);
        paramMap.put(SmsConstants.MOBILE, mobile);
        paramMap.put(SmsConstants.PARAM, param);
        paramMap.put(SmsConstants.TEMPLATE_ID, smsTemplate.getTemplateId());

        try {
            String responseJson = HttpUtil.post(GuoYangSmsConfig.HOST + GuoYangSmsConfig.SEND_BY_TEMPLATE_PATH, paramMap);
            GuoYangResult response = JSONUtil.toBean(responseJson, GuoYangResult.class);
            return SmsRequestResult.requestDone(StringUtils.equals(GuoYangResponseCode.SUCCESS, response.getCode()), response.getMsg());
        } catch (Exception e) {
            LogUtil.error("国阳云短信请求失败，" + e.getMessage(), e);
            return SmsRequestResult.requestDone(false, e.getMessage());
        }
    }

    @Override
    protected SmsRequestResult doBatchSend(List<String> mobileList, String templateUniqNo, String param) {
        return null;
    }

    @Override
    protected SmsRequestResult doBatchSend(List<TpSmsBatchSendRequest> batchSendRequests) {
        return null;
    }

    protected String generateParamJSON(Map<String, String> paramMap) {
        return SmsSendUtil.convertMap2Json(paramMap);
    }

}
