package com.deepinnet.infra.service.util;

import cn.hutool.core.util.StrUtil;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR> wong
 * @create 2025/4/27 17:52
 * @Description
 */
public class FileUtil {

    public static String getFileExtension(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return null;
        }

        String originalFilename = file.getOriginalFilename();
        if (StrUtil.isBlank(originalFilename)) {
            return null;
        }
        int index = originalFilename.lastIndexOf('.');
        // 没有后缀
        if (index == -1) {
            return null;
        }
        return originalFilename.substring(index);
    }
}
