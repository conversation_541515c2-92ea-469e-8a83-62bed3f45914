package com.deepinnet.infra.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.*;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.api.enums.*;
import com.deepinnet.infra.api.vo.SupplierInfoVO;
import com.deepinnet.infra.dal.dataobject.*;
import com.deepinnet.infra.dal.mapper.ProductServiceSupplierMapper;
import com.deepinnet.infra.dal.repository.*;
import com.deepinnet.infra.service.ProductSupplierService;
import com.deepinnet.infra.service.ApprovalService;
import com.deepinnet.infra.service.convert.*;
import com.deepinnet.infra.service.error.BizErrorCode;
import com.deepinnet.tenant.TenantIdUtil;
import com.github.pagehelper.*;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务商信息服务实现类
 *
 * <AUTHOR> wong
 * @create 2025/04/21
 */
@Service
public class ProductSupplierServiceImpl implements ProductSupplierService {

    @Resource
    private ProductServiceSupplierRepository productServiceSupplierRepository;

    @Resource
    private UserContactInfoRepository supplierContactInfoRepository;

    @Resource
    private SupplierFlightServiceRepository supplierFlightServiceRepository;

    @Resource
    private FileAttachmentRepository fileAttachmentRepository;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private ProductServiceSupplierConvert productServiceSupplierConvert;

    @Resource
    private UserContactInfoRepository userContactInfoRepository;

    @Resource
    private ProductServiceSupplierMapper productServiceSupplierMapper;

    @Resource
    private UserContactConvert userContactConvert;

    @Resource
    private ApprovalService approvalService;

    @Resource
    private ApprovalRepository approvalRepository;

    @Override
    public String saveSupplierInfo(SupplierInfoDTO supplierInfoDTO) {
        // 参数校验
        if (supplierInfoDTO == null) {
            LogUtil.error("保存服务商信息失败，参数为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "服务商信息不能为空");
        }

        // 创建服务商基本信息
        String userNo = (String) StpUtil.getExtra("userNo");

        // 检查用户编号是否已存在
        if (StringUtils.isBlank(userNo)) {
            LogUtil.error("保存服务商信息失败，用户编号为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "用户编号不能为空");
        }

        LambdaQueryWrapper<ProductServiceSupplierDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductServiceSupplierDO::getUserNo, userNo);
        ProductServiceSupplierDO existSupplier = productServiceSupplierRepository.getOne(queryWrapper);
        if (existSupplier == null) {
            LogUtil.error("保存服务商信息失败，用户编号不存在：{}", userNo);
            throw new BizException(BizErrorCode.USER_ALREADY_EXISTS.getCode(), "服务商账号不存在");
        }

        // 检查信息是否发生变更
        // boolean isInfoChanged = isSupplierInfoChanged(existSupplier, supplierInfoDTO);

        // 给服务供应商填充属性
        assembleSupplier(supplierInfoDTO, existSupplier, userNo);

        try {
            // 使用事务保存数据
            return transactionTemplate.execute(status -> {
                // 保存服务商基本信息
                productServiceSupplierRepository.updateById(existSupplier);

                // 保存联系人信息
                saveContactInfos(supplierInfoDTO.getContactInfoList(), userNo);

                // 保存服务类型信息
                saveFlightServices(supplierInfoDTO.getServiceCodeList(), userNo);

                // 保存附件信息
                List<String> bizTypes = new ArrayList<>();
                List<FileAttachmentDO> fileAttachmentDOList = buildFileAttachmentList(supplierInfoDTO, userNo, bizTypes);

                if (CollUtil.isNotEmpty(fileAttachmentDOList)) {
                    // 删除原有的附件
                    LambdaQueryWrapper<FileAttachmentDO> wrappers = Wrappers.lambdaQuery(FileAttachmentDO.class)
                            .eq(FileAttachmentDO::getUserNo, userNo)
                            .in(FileAttachmentDO::getBizType, bizTypes);
                    fileAttachmentRepository.remove(wrappers);

                    // 保存附件
                    fileAttachmentRepository.saveBatch(fileAttachmentDOList);
                }

                // 如果信息发生变更，创建新的审核记录
                // if (isInfoChanged && supplierInfoDTO.getNeedAudit()) {
                if (supplierInfoDTO.getNeedAudit()) {
                    String approvalId = approvalService.createSupplierApproval(
                            userNo,
                            supplierInfoDTO.getCompanyName(),
                            supplierInfoDTO.getSocialCreditCode()
                    );
                    LogUtil.info("服务商信息变更，已创建审核记录，申请ID：{}", approvalId);
                }

                LogUtil.info("服务商信息保存成功，用户编号：{}", userNo);
                return userNo;
            });
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            LogUtil.error("保存服务商信息异常，用户编号：{}，异常信息：{}", userNo, e.getMessage(), e);
            throw new BizException(BizErrorCode.UNKNOWN_EXCEPTION.getCode(), "保存服务商信息失败");
        }
    }

    private List<FileAttachmentDO> buildFileAttachmentList(SupplierInfoDTO supplierInfoDTO, String userNo, List<String> bizTypes) {
        List<FileAttachmentDO> fileAttachmentDOList = new ArrayList<>();

        if (CollUtil.isNotEmpty(supplierInfoDTO.getQualificationPicPathList())) {
            bizTypes.add(AttachmentBizTypeEnum.LOW_ALTITUDE_FLIGHT_LICENSE.getCode());
            for (String path : supplierInfoDTO.getQualificationPicPathList()) {
                FileAttachmentDO fileAttachmentDO = new FileAttachmentDO();
                fileAttachmentDO.setUserNo(userNo);
                fileAttachmentDO.setFilePath(path);
                fileAttachmentDO.setBizType(AttachmentBizTypeEnum.LOW_ALTITUDE_FLIGHT_LICENSE.getCode());
                fileAttachmentDO.setBizNo(userNo);
                fileAttachmentDOList.add(fileAttachmentDO);
            }
        }

        if (CollUtil.isNotEmpty(supplierInfoDTO.getBusinessLicensePicPathList())) {
            bizTypes.add(AttachmentBizTypeEnum.BUSINESS_LICENSE.getCode());
            for (String path : supplierInfoDTO.getBusinessLicensePicPathList()) {
                FileAttachmentDO fileAttachmentDO = new FileAttachmentDO();
                fileAttachmentDO.setUserNo(userNo);
                fileAttachmentDO.setFilePath(path);
                fileAttachmentDO.setBizType(AttachmentBizTypeEnum.BUSINESS_LICENSE.getCode());
                fileAttachmentDO.setBizNo(userNo);
                fileAttachmentDOList.add(fileAttachmentDO);
            }
        }

        return fileAttachmentDOList;
    }

    private void assembleSupplier(SupplierInfoDTO supplierInfoDTO, ProductServiceSupplierDO existSupplier, String userNo) {
        existSupplier.setCompanyName(supplierInfoDTO.getCompanyName());
        existSupplier.setUserNo(userNo);
        existSupplier.setAddress(supplierInfoDTO.getCompanyAddress());
        existSupplier.setCreditCode(supplierInfoDTO.getSocialCreditCode());
        existSupplier.setTenantId(TenantIdUtil.getTenantId());
        existSupplier.setRegionCode(supplierInfoDTO.getRegionCode());
        existSupplier.setRegionName(supplierInfoDTO.getRegionName());
        existSupplier.setCooperationAgreement(true);

        // 设置审核状态
        if (supplierInfoDTO.getNeedAudit() != null && supplierInfoDTO.getNeedAudit()) {
            existSupplier.setApprovalStatus(0); // 0-审核中
        } else {
            // 如果不需要审核或首次保存，设置为初始状态
            if (existSupplier.getApprovalStatus() == null) {
                existSupplier.setApprovalStatus(-1); // -1-初始状态（未认证）
            }
        }

        if (supplierInfoDTO.getNeedAudit() != null && !supplierInfoDTO.getNeedAudit()) {
            existSupplier.setApprovalStatus(ApprovalStatusEnum.APPROVED.getCode());
        }

        existSupplier.setGmtModified(new Date());
    }

    @Override
    public Boolean updateSupplierInfo(SupplierInfoDTO supplierInfoDTO) {
        LogUtil.info("开始更新服务商信息：{}", supplierInfoDTO);

        // 参数校验
        if (supplierInfoDTO == null) {
            LogUtil.error("更新服务商信息失败，参数为空或用户编号为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "服务商信息或用户编号不能为空");
        }

        String userNo = (String) StpUtil.getExtra("userNo");

        // 检查服务商是否存在
        LambdaQueryWrapper<ProductServiceSupplierDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductServiceSupplierDO::getUserNo, userNo);
        ProductServiceSupplierDO existSupplier = productServiceSupplierRepository.getOne(queryWrapper);
        if (existSupplier == null) {
            LogUtil.error("更新服务商信息失败，服务商不存在，用户编号：{}", userNo);
            throw new BizException(BizErrorCode.USER_ALREADY_EXISTS.getCode(), "服务商不存在");
        }

        // 检查信息是否发生变更
        boolean isInfoChanged = isSupplierInfoChanged(existSupplier, supplierInfoDTO);

        try {
            // 使用事务更新数据
            return transactionTemplate.execute(status -> {
                // 更新基本信息
                BeanUtils.copyProperties(supplierInfoDTO, existSupplier);
                existSupplier.setGmtModified(new Date());
                existSupplier.setCooperationAgreement(true);

                // 如果信息发生变更，创建新的审核记录并设置审核状态
                if (isInfoChanged) {
                    // 设置审核状态为审核中
                    existSupplier.setApprovalStatus(0); // 0-审核中

                    String approvalId = approvalService.createSupplierApproval(
                            userNo,
                            supplierInfoDTO.getCompanyName(),
                            supplierInfoDTO.getSocialCreditCode()
                    );
                    LogUtil.info("服务商信息变更，已创建审核记录，申请ID：{}", approvalId);
                }

                productServiceSupplierRepository.updateById(existSupplier);

                // 删除原有联系人信息
                LambdaQueryWrapper<UserContactInfoDO> contactQueryWrapper = new LambdaQueryWrapper<>();
                contactQueryWrapper.eq(UserContactInfoDO::getUserNo, userNo);
                supplierContactInfoRepository.remove(contactQueryWrapper);

                // 保存新的联系人信息
                saveContactInfos(supplierInfoDTO.getContactInfoList(), userNo);

                // 删除原有服务类型信息
                LambdaQueryWrapper<SupplierFlightServiceDO> serviceQueryWrapper = new LambdaQueryWrapper<>();
                serviceQueryWrapper.eq(SupplierFlightServiceDO::getUserNo, userNo);
                supplierFlightServiceRepository.remove(serviceQueryWrapper);

                // 保存新的服务类型信息
                saveFlightServices(supplierInfoDTO.getServiceCodeList(), userNo);

                LogUtil.info("服务商信息更新成功，用户编号：{}", userNo);
                return true;
            });
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            LogUtil.error("更新服务商信息异常，用户编号：{}，异常信息：{}", userNo, e.getMessage(), e);
            throw new BizException(BizErrorCode.UNKNOWN_EXCEPTION.getCode(), "更新服务商信息失败");
        }
    }

    @Override
    public SupplierDetailDTO getSupplierDetail(SupplierQueryDetailDTO queryDetailDTO) {
        String userNo = (String) StpUtil.getExtra("userNo");
        if (queryDetailDTO != null && StrUtil.isNotBlank(queryDetailDTO.getUserNo())) {
            userNo = queryDetailDTO.getUserNo();
        }

        if (StringUtils.isBlank(userNo)) {
            LogUtil.error("获取服务商详情失败，用户编号为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "用户编号不能为空");
        }

        // 查询服务商基本信息
        LambdaQueryWrapper<ProductServiceSupplierDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductServiceSupplierDO::getUserNo, userNo);
        ProductServiceSupplierDO supplierDO = productServiceSupplierRepository.getOne(queryWrapper);
        if (supplierDO == null) {
            LogUtil.error("获取服务商详情失败，服务商不存在，用户编号：{}", userNo);
            throw new BizException(BizErrorCode.USER_ALREADY_EXISTS.getCode(), "服务商不存在");
        }

        // 构建详情DTO
        SupplierDetailDTO detailDTO = productServiceSupplierConvert.convertToSupplierDetailDTO(supplierDO);

        // 设置审核状态（直接从服务商表字段获取）
        detailDTO.setApprovalStatus(supplierDO.getApprovalStatus());
        detailDTO.setApprovalStatusDesc(getApprovalStatusDescription(supplierDO.getApprovalStatus()));

        // 查询联系人信息
        List<SupplierContactInfoDTO> contactInfoDTOs = getContactInfos(userNo);
        detailDTO.setContactInfoList(contactInfoDTOs);

        // 查询服务类型信息
        List<SupplierServiceDTO> serviceDTOs = getFlightServices(userNo);
        detailDTO.setServiceList(serviceDTOs);

        // 查询附件信息
        List<FileAttachmentDO> fileAttachments = fileAttachmentRepository.list(Wrappers.lambdaQuery(FileAttachmentDO.class)
                .eq(FileAttachmentDO::getUserNo, userNo)
                .in(FileAttachmentDO::getBizType, Lists.newArrayList(AttachmentBizTypeEnum.BUSINESS_LICENSE.getCode(), AttachmentBizTypeEnum.LOW_ALTITUDE_FLIGHT_LICENSE.getCode())));
        if (CollUtil.isNotEmpty(fileAttachments)) {
            List<String> businessLicenseList = fileAttachments.stream()
                    .filter(e -> StrUtil.equals(e.getBizType(), AttachmentBizTypeEnum.BUSINESS_LICENSE.getCode()))
                    .map(FileAttachmentDO::getFilePath)
                    .collect(Collectors.toList());

            List<String> flightLicenseList = fileAttachments.stream()
                    .filter(e -> StrUtil.equals(e.getBizType(), AttachmentBizTypeEnum.LOW_ALTITUDE_FLIGHT_LICENSE.getCode()))
                    .map(FileAttachmentDO::getFilePath)
                    .collect(Collectors.toList());

            detailDTO.setQualificationPicPaths(flightLicenseList);
            detailDTO.setBusinessLicensePicPath(businessLicenseList);
        }

        return detailDTO;
    }

    /**
     * 保存联系人信息
     *
     * @param contactInfoList 联系人信息列表
     * @param userNo          用户编号
     */
    private void saveContactInfos(List<SupplierContactInfoDTO> contactInfoList, String userNo) {
        if (CollectionUtils.isEmpty(contactInfoList)) {
            LogUtil.error("保存联系人信息失败，联系人列表为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "联系人信息不能为空");
        }

        // 把现有的都删除掉
        supplierContactInfoRepository.remove(
                Wrappers.lambdaQuery(UserContactInfoDO.class)
                        .eq(UserContactInfoDO::getUserNo, userNo)
        );

        // 新增新的联系人信息
        List<UserContactInfoDO> contactInfoDOs = Lists.newArrayList();
        for (SupplierContactInfoDTO contactInfoDTO : contactInfoList) {
            UserContactInfoDO contactInfoDO = new UserContactInfoDO();
            BeanUtils.copyProperties(contactInfoDTO, contactInfoDO);
            contactInfoDO.setUserNo(userNo);
            contactInfoDO.setTenantId(TenantIdUtil.getTenantId());
            contactInfoDOs.add(contactInfoDO);
        }

        supplierContactInfoRepository.saveBatch(contactInfoDOs);
    }

    /**
     * 保存飞行服务信息
     *
     * @param serviceCodeList 服务编码列表
     * @param userNo          用户编号
     */
    private void saveFlightServices(List<String> serviceCodeList, String userNo) {
        // 先把以前的数据删除
        supplierFlightServiceRepository.remove(Wrappers.lambdaQuery(SupplierFlightServiceDO.class)
                .eq(SupplierFlightServiceDO::getUserNo, userNo));

        if (CollectionUtils.isEmpty(serviceCodeList)) {
            LogUtil.error("保存飞行服务信息失败，服务编码列表为空");
            return;
        }

        // 然后新增新的数据
        List<SupplierFlightServiceDO> serviceDOs = new ArrayList<>();
        for (String serviceCode : serviceCodeList) {
            FlightServiceTypeEnum serviceType = FlightServiceTypeEnum.getByCode(serviceCode);
            if (serviceType == null) {
                LogUtil.error("保存飞行服务信息失败，无效的服务编码：{}", serviceCode);
                throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "无效的服务类型编码");
            }

            SupplierFlightServiceDO serviceDO = new SupplierFlightServiceDO();
            serviceDO.setUserNo(userNo);
            serviceDO.setServiceCode(serviceCode);
            serviceDO.setServiceName(serviceType.getDesc());
            serviceDO.setTenantId(TenantIdUtil.getTenantId());
            serviceDOs.add(serviceDO);
        }

        supplierFlightServiceRepository.saveBatch(serviceDOs);
    }

    /**
     * 获取联系人信息列表
     *
     * @param userNo 用户编号
     * @return 联系人信息列表
     */
    private List<SupplierContactInfoDTO> getContactInfos(String userNo) {
        LambdaQueryWrapper<UserContactInfoDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserContactInfoDO::getUserNo, userNo);
        List<UserContactInfoDO> contactInfoDOList = supplierContactInfoRepository.list(queryWrapper);

        List<SupplierContactInfoDTO> resultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(contactInfoDOList)) {
            resultList = contactInfoDOList.stream().map(item -> {
                SupplierContactInfoDTO dto = new SupplierContactInfoDTO();
                BeanUtils.copyProperties(item, dto);
                return dto;
            }).collect(Collectors.toList());
        }

        return resultList;
    }

    /**
     * 获取飞行服务信息列表
     *
     * @param userNo 用户编号
     * @return 服务信息列表
     */
    private List<SupplierServiceDTO> getFlightServices(String userNo) {
        LambdaQueryWrapper<SupplierFlightServiceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierFlightServiceDO::getUserNo, userNo);
        List<SupplierFlightServiceDO> serviceDOList = supplierFlightServiceRepository.list(queryWrapper);

        List<SupplierServiceDTO> resultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(serviceDOList)) {
            resultList = serviceDOList.stream().map(item -> {
                SupplierServiceDTO dto = new SupplierServiceDTO();
                BeanUtils.copyProperties(item, dto);
                return dto;
            }).collect(Collectors.toList());
        }

        return resultList;
    }

    /**
     * 分页查询服务商列表
     *
     * @param queryDTO 查询参数
     * @return 服务商列表分页数据
     */
    @Override
    public Result<CommonPage<SupplierInfoVO>> pageQuerySuppliers(SupplierQueryDTO queryDTO) {
        if (queryDTO == null || queryDTO.getPageNum() == null || queryDTO.getPageSize() == null) {
            LogUtil.error("分页查询服务商列表参数错误");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        try {
            // 使用PageHelper进行分页查询
            Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

            // 判断是否需要联表查询（有联系人姓名或联系人电话条件时）
            List<ProductServiceSupplierDO> supplierList;
            if (StringUtils.isNotBlank(queryDTO.getContactName()) || StringUtils.isNotBlank(queryDTO.getContactPhone())) {
                // 使用联表查询
                supplierList = productServiceSupplierMapper.pageQuerySuppliersWithContact(queryDTO);
            } else {
                // 不需要联表查询时使用普通查询
                LambdaQueryWrapper<ProductServiceSupplierDO> queryWrapper = buildQueryWrapper(queryDTO);
                supplierList = productServiceSupplierRepository.list(queryWrapper);
            }

            // 如果查询结果为空，返回空分页结果
            if (CollectionUtils.isEmpty(supplierList)) {
                return Result.success(CommonPage.buildEmptyPage());
            }

            // 获取所有服务商的用户编号
            List<String> userNoList = supplierList.stream()
                    .map(ProductServiceSupplierDO::getUserNo)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            // 查询所有服务商对应的联系人信息
            Map<String, List<UserContactInfoDO>> contactInfoMap = Collections.emptyMap();
            if (!CollectionUtils.isEmpty(userNoList)) {
                List<UserContactInfoDO> contactInfoList = userContactInfoRepository.list(
                        Wrappers.lambdaQuery(UserContactInfoDO.class)
                                .in(UserContactInfoDO::getUserNo, userNoList)
                );

                // 按userNo分组
                contactInfoMap = contactInfoList.stream()
                        .collect(Collectors.groupingBy(UserContactInfoDO::getUserNo));
            }

            // 转换为VO对象
            List<SupplierInfoVO> supplierVOList = convertToVOList(supplierList, contactInfoMap);

            // 构建分页结果
            PageInfo<SupplierInfoVO> pageInfo = new PageInfo<>(supplierVOList);
            pageInfo.setPageNum(page.getPageNum());
            pageInfo.setPageSize(page.getPageSize());
            pageInfo.setTotal(page.getTotal());
            pageInfo.setPages(page.getPages());

            // 返回分页结果
            CommonPage<SupplierInfoVO> result = CommonPage.buildPage(
                    queryDTO.getPageNum(),
                    queryDTO.getPageSize(),
                    pageInfo.getPages(),
                    pageInfo.getTotal(),
                    supplierVOList
            );

            LogUtil.info("分页查询服务商列表成功，共查询到{}条数据", result.getTotal());
            return Result.success(result);
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            LogUtil.error("分页查询服务商列表异常", e);
            throw new BizException(BizErrorCode.UNKNOWN_EXCEPTION.getCode(), "查询服务商列表失败");
        }
    }

    /**
     * 构建查询条件
     *
     * @param queryDTO 查询参数
     * @return 查询条件
     */
    private LambdaQueryWrapper<ProductServiceSupplierDO> buildQueryWrapper(SupplierQueryDTO queryDTO) {
        LambdaQueryWrapper<ProductServiceSupplierDO> queryWrapper = Wrappers.lambdaQuery(ProductServiceSupplierDO.class);

        // 按登录手机号查询
        if (StringUtils.isNotBlank(queryDTO.getPhone())) {
            queryWrapper.like(ProductServiceSupplierDO::getPhone, queryDTO.getPhone());
        }

        // 按企业名称查询
        if (StringUtils.isNotBlank(queryDTO.getCompanyName())) {
            queryWrapper.like(ProductServiceSupplierDO::getCompanyName, queryDTO.getCompanyName());
        }

        // 按审核状态查询
        if (queryDTO.getApprovalStatus() != null) {
            queryWrapper.eq(ProductServiceSupplierDO::getApprovalStatus, queryDTO.getApprovalStatus());
        }

        // 按创建时间倒序排序
        queryWrapper.orderByDesc(ProductServiceSupplierDO::getRegistrationTime);
        queryWrapper.eq(ProductServiceSupplierDO::getIsDeleted, false);

        return queryWrapper;
    }

    /**
     * 将DO对象转换为VO对象
     *
     * @param supplierList   服务商列表
     * @param contactInfoMap 联系人信息映射
     * @return VO对象列表
     */
    private List<SupplierInfoVO> convertToVOList(List<ProductServiceSupplierDO> supplierList,
                                                 Map<String, List<UserContactInfoDO>> contactInfoMap) {
        List<SupplierInfoVO> resultList = new ArrayList<>(supplierList.size());

        for (ProductServiceSupplierDO supplier : supplierList) {
            SupplierInfoVO vo = new SupplierInfoVO();

            // 设置服务商基本信息
            vo.setSupplierId(supplier.getUserNo());
            vo.setPhone(supplier.getPhone());
            vo.setCompanyName(supplier.getCompanyName());
            vo.setRegisterTime(LocalDateTimeUtil.toEpochMilli(supplier.getRegistrationTime()));

            // 设置审核状态（直接从服务商表字段获取）
            vo.setApprovalStatus(supplier.getApprovalStatus());
            vo.setApprovalStatusDesc(getApprovalStatusDescription(supplier.getApprovalStatus()));

            // 设置联系人信息
            if (contactInfoMap.containsKey(supplier.getUserNo()) && !contactInfoMap.get(supplier.getUserNo()).isEmpty()) {
                List<UserContactInfoDO> contactInfos = contactInfoMap.get(supplier.getUserNo());
                List<ContactInfoDTO> contactInfoDTOs = userContactConvert.convertToUserContactDTOs(contactInfos);
                vo.setContactInfoDTOList(contactInfoDTOs);
            }

            resultList.add(vo);
        }

        return resultList;
    }

    /**
     * 检查服务商信息是否发生变更
     *
     * @param existSupplier   现有服务商信息
     * @param supplierInfoDTO 新的服务商信息
     * @return 是否发生变更
     */
    private boolean isSupplierInfoChanged(ProductServiceSupplierDO existSupplier, SupplierInfoDTO supplierInfoDTO) {
        // 检查基本信息是否变更
        boolean basicInfoChanged = isSupplierBasicInfoChanged(existSupplier, supplierInfoDTO);

        // 检查联系人信息是否变更
        boolean contactInfoChanged = isContactInfoChanged(existSupplier.getUserNo(), supplierInfoDTO.getContactInfoList());

        // 检查服务类型是否变更
        boolean serviceTypeChanged = isServiceTypeChanged(existSupplier.getUserNo(), supplierInfoDTO.getServiceCodeList());

        // 检查附件是否变更
        boolean attachmentChanged = isSupplierAttachmentChanged(existSupplier.getUserNo(), supplierInfoDTO);

        boolean changed = basicInfoChanged || contactInfoChanged || serviceTypeChanged || attachmentChanged;

        if (changed) {
            LogUtil.info("服务商信息变更详情 - 用户：{}，基本信息变更：{}，联系人变更：{}，服务类型变更：{}，附件变更：{}",
                    existSupplier.getUserNo(), basicInfoChanged, contactInfoChanged, serviceTypeChanged, attachmentChanged);
        }

        return changed;
    }

    /**
     * 检查服务商基本信息是否发生变更
     *
     * @param existSupplier   现有服务商信息
     * @param supplierInfoDTO 新的服务商信息
     * @return 是否发生变更
     */
    private boolean isSupplierBasicInfoChanged(ProductServiceSupplierDO existSupplier, SupplierInfoDTO supplierInfoDTO) {
        return !Objects.equals(existSupplier.getCompanyName(), supplierInfoDTO.getCompanyName()) ||
                !Objects.equals(existSupplier.getAddress(), supplierInfoDTO.getCompanyAddress()) ||
                !Objects.equals(existSupplier.getCreditCode(), supplierInfoDTO.getSocialCreditCode()) ||
                !Objects.equals(existSupplier.getRegionCode(), supplierInfoDTO.getRegionCode()) ||
                !Objects.equals(existSupplier.getRegionName(), supplierInfoDTO.getRegionName());
    }

    /**
     * 检查联系人信息是否发生变更
     *
     * @param userNo          用户编号
     * @param newContactInfos 新的联系人信息列表
     * @return 是否发生变更
     */
    private boolean isContactInfoChanged(String userNo, List<SupplierContactInfoDTO> newContactInfos) {
        // 查询现有联系人信息
        LambdaQueryWrapper<UserContactInfoDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserContactInfoDO::getUserNo, userNo);
        List<UserContactInfoDO> existingContacts = userContactInfoRepository.list(queryWrapper);

        // 处理新的联系人信息
        List<SupplierContactInfoDTO> processedNewContacts = new ArrayList<>();
        if (!CollectionUtils.isEmpty(newContactInfos)) {
            processedNewContacts = newContactInfos.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }

        // 比较数量
        if (existingContacts.size() != processedNewContacts.size()) {
            return true;
        }

        // 比较内容
        for (SupplierContactInfoDTO newContact : processedNewContacts) {
            boolean found = existingContacts.stream()
                    .anyMatch(existing ->
                            Objects.equals(existing.getName(), newContact.getName()) &&
                                    Objects.equals(existing.getPhone(), newContact.getPhone()) &&
                                    Objects.equals(existing.getPosition(), newContact.getPosition()));
            if (!found) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查服务类型是否发生变更
     *
     * @param userNo          用户编号
     * @param newServiceCodes 新的服务编码列表
     * @return 是否发生变更
     */
    private boolean isServiceTypeChanged(String userNo, List<String> newServiceCodes) {
        // 查询现有服务类型
        LambdaQueryWrapper<SupplierFlightServiceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierFlightServiceDO::getUserNo, userNo);
        List<SupplierFlightServiceDO> existingServices = supplierFlightServiceRepository.list(queryWrapper);

        // 提取现有服务编码
        Set<String> existingCodes = existingServices.stream()
                .map(SupplierFlightServiceDO::getServiceCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 处理新的服务编码
        Set<String> newCodes = new HashSet<>();
        if (!CollectionUtils.isEmpty(newServiceCodes)) {
            newCodes = newServiceCodes.stream()
                    .filter(Objects::nonNull)
                    .filter(code -> !code.trim().isEmpty())
                    .collect(Collectors.toSet());
        }

        // 比较两个编码集合是否相同
        return !existingCodes.equals(newCodes);
    }

    /**
     * 检查附件信息是否发生变更
     *
     * @param userNo          用户编号
     * @param supplierInfoDTO 新的服务商信息
     * @return 是否发生变更
     */
    private boolean isSupplierAttachmentChanged(String userNo, SupplierInfoDTO supplierInfoDTO) {
        // 查询现有附件
        LambdaQueryWrapper<FileAttachmentDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileAttachmentDO::getUserNo, userNo)
                .in(FileAttachmentDO::getBizType,
                        Lists.newArrayList(AttachmentBizTypeEnum.BUSINESS_LICENSE.getCode(),
                                AttachmentBizTypeEnum.LOW_ALTITUDE_FLIGHT_LICENSE.getCode()));
        List<FileAttachmentDO> existingAttachments = fileAttachmentRepository.list(queryWrapper);

        // 分类现有附件
        Set<String> existingBusinessLicense = existingAttachments.stream()
                .filter(e -> AttachmentBizTypeEnum.BUSINESS_LICENSE.getCode().equals(e.getBizType()))
                .map(FileAttachmentDO::getFilePath)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Set<String> existingQualificationPics = existingAttachments.stream()
                .filter(e -> AttachmentBizTypeEnum.LOW_ALTITUDE_FLIGHT_LICENSE.getCode().equals(e.getBizType()))
                .map(FileAttachmentDO::getFilePath)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 处理新的附件路径
        Set<String> newBusinessLicense = new HashSet<>();
        if (!CollectionUtils.isEmpty(supplierInfoDTO.getBusinessLicensePicPathList())) {
            newBusinessLicense = supplierInfoDTO.getBusinessLicensePicPathList().stream()
                    .filter(Objects::nonNull)
                    .filter(path -> !path.trim().isEmpty())
                    .collect(Collectors.toSet());
        }

        Set<String> newQualificationPics = new HashSet<>();
        if (!CollectionUtils.isEmpty(supplierInfoDTO.getQualificationPicPathList())) {
            newQualificationPics = supplierInfoDTO.getQualificationPicPathList().stream()
                    .filter(Objects::nonNull)
                    .filter(path -> !path.trim().isEmpty())
                    .collect(Collectors.toSet());
        }

        // 比较附件路径是否相同
        boolean businessLicenseChanged = !existingBusinessLicense.equals(newBusinessLicense);
        boolean qualificationPicsChanged = !existingQualificationPics.equals(newQualificationPics);

        return businessLicenseChanged || qualificationPicsChanged;
    }

    /**
     * 获取审核状态描述
     *
     * @param status 审核状态
     * @return 状态描述
     */
    private String getApprovalStatusDescription(Integer status) {
        if (status == null) {
            return "未知状态";
        }

        switch (status) {
            case -1:
                return "未认证";
            case 0:
                return "审核中";
            case 1:
                return "已认证";
            case 2:
                return "不通过，请重新认证";
            default:
                return "未知状态";
        }
    }
}