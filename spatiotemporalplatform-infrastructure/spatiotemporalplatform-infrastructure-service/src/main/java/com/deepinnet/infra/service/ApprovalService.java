package com.deepinnet.infra.service;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.infra.api.dto.ApprovalDetailDTO;
import com.deepinnet.infra.api.dto.ApprovalProcessDTO;
import com.deepinnet.infra.api.dto.ApprovalQueryDTO;
import com.deepinnet.infra.api.vo.ApprovalVO;

/**
 * 审核服务接口
 *
 * <AUTHOR> wong
 * @create 2025/04/25
 */
public interface ApprovalService {

    /**
     * 分页查询审核记录
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    CommonPage<ApprovalVO> pageQuery(ApprovalQueryDTO queryDTO);

    /**
     * 获取审核记录详情
     *
     * @param approvalId 申请ID
     * @return 审核记录详情
     */
    ApprovalDetailDTO getDetail(String approvalId);

    /**
     * 提交审核处理
     *
     * @param processDTO 审核处理参数
     * @return 处理结果
     */
    Boolean process(ApprovalProcessDTO processDTO);

    /**
     * 创建客户信息认证审核记录
     *
     * @param bizId            业务ID，即客户ID
     * @param organizationName 单位名称
     * @param socialCreditCode 统一社会信用代码
     * @return 申请ID
     */
    String createCustomerApproval(String bizId, String organizationName, String socialCreditCode);

    /**
     * 创建服务商信息认证审核记录
     *
     * @param bizId            业务ID，即服务商ID
     * @param organizationName 企业名称
     * @param socialCreditCode 统一社会信用代码
     * @return 申请ID
     */
    String createSupplierApproval(String bizId, String organizationName, String socialCreditCode);
} 