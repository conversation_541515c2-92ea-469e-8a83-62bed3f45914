package com.deepinnet.infra.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.SendSmsResultDTO;
import com.deepinnet.infra.api.enums.*;
import com.deepinnet.infra.api.template.VerificationCodeTemplate;
import com.deepinnet.infra.dal.dataobject.*;
import com.deepinnet.infra.dal.repository.*;
import com.deepinnet.infra.service.VerificationCodeService;
import com.deepinnet.infra.service.enums.ResetTokenStatusEnum;
import com.deepinnet.infra.service.error.BizErrorCode;
import com.deepinnet.infra.service.sms.client.SmsClient;
import com.deepinnet.infra.service.util.BizNoGenerateUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;

/**
 * 验证码服务实现类
 *
 * <AUTHOR> wong
 * @create 2025/04/18
 */
@Service
public class VerificationCodeServiceImpl implements VerificationCodeService {

    /**
     * 验证码有效期（分钟）
     */
    private static final int EXPIRE_MINUTES = 5;

    @Resource
    private SmsClient smsClient;

    @Resource
    private VerificationCodeRepository verificationCodeRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private UserResetTokenRepository userResetTokenRepository;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Override
    public Result<SendSmsResultDTO> sendVerificationCode(String phone, String bizType, String scene) {
        if (StringUtils.isBlank(phone)) {
            LogUtil.error("发送验证码失败，手机号为空");
            return Result.fail(BizErrorCode.ILLEGAL_PARAMS.getCode(), "手机号不能为空");
        }

        SendSmsResultDTO sendSmsResultDTO = new SendSmsResultDTO();

        // 生成6位随机数字验证码
        String code = RandomStringUtils.randomNumeric(6);
        LogUtil.info("为手机号[{}]生成验证码：{}", phone, code);

        // 设置过期时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, EXPIRE_MINUTES);
        Date expireTime = calendar.getTime();

        transactionTemplate.executeWithoutResult(action -> {
            if (!StrUtil.equals(bizType, SmsTypeEnum.REGISTER.getCode())) {
                List<String> userTypes = getUserTypeByScene(scene);
                UserDO user = userRepository.getOne(Wrappers.lambdaQuery(UserDO.class)
                        .eq(UserDO::getPhone, phone)
                        .in(UserDO::getUserType, userTypes));

                if (user == null) {
                    throw new BizException(BizErrorCode.USER_NOT_EXIST.getCode(), BizErrorCode.USER_NOT_EXIST.getDesc());
                }

                if (StrUtil.equals(bizType, SmsTypeEnum.FORGET_PASSWORD.getCode())) {
                    // 生成resetToken
                    UserResetTokenDO userResetTokenDO = buildResetToken(phone, user);
                    userResetTokenRepository.save(userResetTokenDO);
                    sendSmsResultDTO.setResetToken(userResetTokenDO.getResetToken());
                }
            }

            // 存储验证码
            saveVerificationCode(phone, bizType, scene, code, expireTime);

            // 发送短信
            sendSms(phone, code);
        });

        sendSmsResultDTO.setSendSuccess(true);
        LogUtil.info("验证码发送成功，手机号：[{}]", phone);
        return Result.success(sendSmsResultDTO);

    }

    private List<String> getUserTypeByScene(String scene) {
        if (StrUtil.equals(scene, SceneEnum.MATCHING_PLATFORM.getCode())) {
            return Lists.newArrayList(UserTypeEnum.CUSTOMER.getCode(), UserTypeEnum.SUPPLIER.getCode());
        }

        if (StrUtil.equals(scene, SceneEnum.OPERATION_CENTER.getCode())) {
            return Lists.newArrayList(UserTypeEnum.OPERATION.getCode());
        }

        return null;
    }

    private UserResetTokenDO buildResetToken(String phone, UserDO user) {
        String resetToken = BizNoGenerateUtil.generateResetToken();
        UserResetTokenDO userResetTokenDO = new UserResetTokenDO();
        userResetTokenDO.setUserNo(user.getUserNo());
        userResetTokenDO.setResetToken(resetToken);
        userResetTokenDO.setPhone(phone);
        userResetTokenDO.setStatus(ResetTokenStatusEnum.CREATED.getCode());
        userResetTokenDO.setGmtCreated(new Date());
        userResetTokenDO.setGmtModified(new Date());
        return userResetTokenDO;
    }

    private void sendSms(String phone, String code) {
        VerificationCodeTemplate template = new VerificationCodeTemplate();
        template.setCode(code);
        template.setMinute(String.valueOf(EXPIRE_MINUTES));
        smsClient.sendByTemplate(phone, template);
    }

    private void saveVerificationCode(String phone, String bizType, String scene, String code, Date expireTime) {
        VerificationCodeDO verificationCodeDO = new VerificationCodeDO();
        verificationCodeDO.setPhone(phone);
        verificationCodeDO.setCode(code);
        verificationCodeDO.setBizType(bizType);
        verificationCodeDO.setExpireTime(expireTime);
        verificationCodeDO.setScene(scene);
        verificationCodeDO.setStatus(VerificationCodeStatusEnum.SENT.getCode());
        verificationCodeRepository.save(verificationCodeDO);
    }

    @Override
    public Result<Boolean> verifyCode(String phone, String code, String bizType, String scene) {
        if (StringUtils.isBlank(phone) || StringUtils.isBlank(code)) {
            LogUtil.error("验证码校验失败，手机号或验证码为空");
            return Result.fail(BizErrorCode.UNKNOWN_EXCEPTION.getCode(), "手机号或验证码不能为空");
        }

        try {
            List<VerificationCodeDO> currentCodeList = verificationCodeRepository.lambdaQuery()
                    .eq(VerificationCodeDO::getPhone, phone)
                    .eq(VerificationCodeDO::getCode, code)
                    .eq(VerificationCodeDO::getBizType, bizType)
                    .eq(VerificationCodeDO::getScene, scene)
                    .orderByDesc(VerificationCodeDO::getId)
                    .last(" LIMIT 1")
                    .list();
            if (CollUtil.isEmpty(currentCodeList)) {
                throw new BizException(BizErrorCode.VERIFICATION_CODE_NOT_FOUND.getCode(), BizErrorCode.VERIFICATION_CODE_NOT_FOUND.getDesc());
            }

            VerificationCodeDO currentCode = currentCodeList.get(0);

            if (StrUtil.equals(currentCode.getStatus(), VerificationCodeStatusEnum.VERIFIED.getCode())) {
                throw new BizException(BizErrorCode.VERIFICATION_CODE_IS_USED.getCode(), BizErrorCode.VERIFICATION_CODE_IS_USED.getDesc());
            }

            List<VerificationCodeDO> verificationCodes = verificationCodeRepository.lambdaQuery()
                    .eq(VerificationCodeDO::getPhone, phone)
                    .eq(VerificationCodeDO::getBizType, bizType)
                    .eq(VerificationCodeDO::getScene, scene)
                    .orderByDesc(VerificationCodeDO::getId)
                    .last(" LIMIT 1")
                    .list();
            if (CollUtil.isEmpty(verificationCodes)) {
                throw new BizException(BizErrorCode.VERIFICATION_CODE_NOT_FOUND.getCode(), BizErrorCode.VERIFICATION_CODE_NOT_FOUND.getDesc());
            }

            VerificationCodeDO latestCode = verificationCodes.get(0);
            if (!StrUtil.equals(latestCode.getCode(), code)) {
                LogUtil.error("验证码校验失败，当前验证码不是最新的：code={},phone={}，最新的验证码={}", code, phone, latestCode.getCode());
                return Result.fail(BizErrorCode.NEED_LATEST_CODE.getCode(), BizErrorCode.NEED_LATEST_CODE.getDesc());
            }

            Date now = new Date();

            // 验证码过期
            if (now.after(latestCode.getExpireTime())) {
                LogUtil.error("验证码校验失败，手机号[{}]的验证码已过期", phone);
                return Result.fail(BizErrorCode.VERIFICATION_CODE_EXPIRED.getCode(), "验证码已过期，请重新获取");
            }

            // 验证码不匹配
            if (!code.equals(latestCode.getCode())) {
                LogUtil.error("验证码校验失败，手机号[{}]的验证码不匹配", phone);
                return Result.fail(BizErrorCode.VERIFICATION_CODE_INCORRECT.getCode(), "验证码不正确");
            }

            LogUtil.info("验证码校验成功，手机号：[{}]", phone);
            latestCode.setStatus(VerificationCodeStatusEnum.VERIFIED.getCode());
            verificationCodeRepository.updateById(latestCode);
            return Result.success(true);
        } catch (Exception e) {
            LogUtil.error("验证码校验异常，手机号：[{}]，异常信息：{}", phone, e.getMessage(), e);
            if (e instanceof BizException) {
                throw e;
            }

            return Result.fail(BizErrorCode.VERIFY_CODE_ERROR.getCode(), BizErrorCode.VERIFY_CODE_ERROR.getDesc());
        }
    }
} 