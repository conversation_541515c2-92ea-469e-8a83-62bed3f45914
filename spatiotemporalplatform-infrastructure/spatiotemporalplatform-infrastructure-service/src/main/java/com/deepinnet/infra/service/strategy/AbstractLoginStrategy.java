package com.deepinnet.infra.service.strategy;

import cn.dev33.satoken.secure.SaSecureUtil;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.api.enums.*;
import com.deepinnet.infra.dal.dataobject.*;
import com.deepinnet.infra.dal.repository.UserDepartmentRepository;
import com.deepinnet.infra.service.VerificationCodeService;
import com.deepinnet.infra.service.error.BizErrorCode;
import com.deepinnet.infra.service.log.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 抽象登录策略
 */
public abstract class AbstractLoginStrategy implements LoginStrategy {

    @Resource
    protected UserDepartmentRepository userDepartmentRepository;

    @Resource
    protected VerificationCodeService verificationCodeService;

    @Value("${salt}")
    protected String salt;

    protected final Long timeout = 60 * 60 * 24 * 365L;

    @Override
    public void validateLogin(UserLoginDTO loginDTO, AccountDO account) {
        // 检查账号是否被冻结
        if (account.getStatus() != null && account.getStatus().equals(AccountStatusEnum.FROZEN.getCode())) {
            LogUtil.error("无法登录，账户已被冻结，账号为：{}", account.getAccount());
            throw new BizException(BizErrorCode.ACCOUNT_FROZEN.getCode(), BizErrorCode.ACCOUNT_FROZEN.getDesc());
        }

        // 密码登录
        if (StrUtil.isNotBlank(loginDTO.getPassword())) {
            validatePassword(loginDTO, account);
        } else if (StrUtil.isNotBlank(loginDTO.getVerifyCode())) {
            validateVerifyCode(loginDTO);
        } else {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "密码或验证码不能为空");
        }
    }

    /**
     * 验证密码
     */
    protected void validatePassword(UserLoginDTO loginDTO, AccountDO account) {
        String encrypt = SaSecureUtil.aesEncrypt(salt, loginDTO.getPassword());
        if (!StringUtils.equals(encrypt, account.getPassword())) {
            BizErrorCode errorCode = getBizErrorCode(loginDTO, BizErrorCode.ACCOUNT_OR_PASSWORD_ERROR, BizErrorCode.PASSWORD_ERROR);
            throw new BizException(errorCode.getCode(), errorCode.getDesc());
        }
    }

    /**
     * 验证验证码
     */
    protected void validateVerifyCode(UserLoginDTO loginDTO) {
        Result<Boolean> res = verificationCodeService.verifyCode(
                loginDTO.getAccount(),
                loginDTO.getVerifyCode(),
                SmsTypeEnum.LOGIN.getCode(),
                loginDTO.getScene());

        if (!res.isSuccess()) {
            throw new BizException(res.getErrorCode(), res.getErrorDesc());
        }
    }

    /**
     * 获取错误码
     */
    protected BizErrorCode getBizErrorCode(UserLoginDTO loginDTO, BizErrorCode trafficErrorCode, BizErrorCode skyFlowErrorCode) {
        if (StrUtil.isBlank(loginDTO.getScene()) || "traffic_police".equals(loginDTO.getScene())) {
            return trafficErrorCode;
        } else {
            return skyFlowErrorCode;
        }
    }

    /**
     * 获取部门ID列表
     */
    protected List<Long> getDepartmentIds(String userNo) {
        List<UserDepartmentDO> userDepartmentDOs = userDepartmentRepository.list(
                Wrappers.lambdaQuery(UserDepartmentDO.class)
                        .eq(UserDepartmentDO::getUserNo, userNo));

        return userDepartmentDOs.stream()
                .map(UserDepartmentDO::getDepartmentId)
                .collect(Collectors.toList());
    }

    /**
     * 构建登录成功响应
     */
    protected UserLoginSuccessDTO buildLoginSuccessDTO(AccountDO account, List<Long> departmentIds) {
        UserLoginSuccessDTO loginSuccessDTO = new UserLoginSuccessDTO();
        loginSuccessDTO.setToken(StpUtil.getTokenValue());
        loginSuccessDTO.setName(account.getName());
        loginSuccessDTO.setAccountNo(account.getAccountNo());
        loginSuccessDTO.setAccount(account.getAccount());
        loginSuccessDTO.setUserNo(account.getUserNo());
        loginSuccessDTO.setDepartmentIds(departmentIds);
        loginSuccessDTO.setUserType(account.getUserType());
        loginSuccessDTO.setIsSuperAdmin(account.getIsSuperAdmin());
        loginSuccessDTO.setStatus(account.getStatus() != null ? account.getStatus() : AccountStatusEnum.NORMAL.getCode());
        return loginSuccessDTO;
    }
} 