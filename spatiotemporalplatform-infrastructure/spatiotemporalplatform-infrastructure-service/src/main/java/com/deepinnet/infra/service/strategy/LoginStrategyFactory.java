package com.deepinnet.infra.service.strategy;

import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 登录策略工厂
 */
@Component
public class LoginStrategyFactory {
    
    @Resource
    private List<LoginStrategy> loginStrategies;
    
    /**
     * 根据场景获取对应的登录策略
     * 
     * @param scene 场景
     * @return 登录策略
     */
    public LoginStrategy getStrategy(String scene) {
        for (LoginStrategy strategy : loginStrategies) {
            if (strategy.supports(scene)) {
                return strategy;
            }
        }
        
        // 如果没有找到合适的策略，使用默认策略（应该至少有一个默认策略）
        return loginStrategies.stream()
                .filter(strategy -> strategy instanceof TrafficPoliceLoginStrategy)
                .findFirst()
                .orElseThrow(() -> new RuntimeException("没有找到合适的登录策略"));
    }
} 