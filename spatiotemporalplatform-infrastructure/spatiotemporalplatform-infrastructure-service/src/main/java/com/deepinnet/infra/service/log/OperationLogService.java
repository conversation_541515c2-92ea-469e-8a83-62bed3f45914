package com.deepinnet.infra.service.log;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.service.log.context.OperationLogContext;

/**
 * 操作日志服务接口
 *
 * <AUTHOR>
 */
public interface OperationLogService {

    /**
     * 记录操作日志
     *
     * @param context 操作日志上下文
     * @param requestUri 请求URI
     * @param requestMethod 请求方法
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     */
    void recordOperationLog(OperationLogContext context, String requestUri, String requestMethod,
                            String ipAddress, String userAgent);

    /**
     * 分页查询操作日志
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    CommonPage<OperationLogDTO> pageQueryOperationLog(OperationLogQueryDTO queryDTO);

    /**
     * 根据ID获取操作日志详情
     *
     * @param id 日志ID
     * @return 操作日志详情
     */
    OperationLogDTO getOperationLogById(Long id);

    /**
     * 渲染操作日志模板
     *
     * @param context 操作日志上下文
     * @return 渲染后的操作详情
     */
    String renderTemplate(OperationLogContext context);
}
