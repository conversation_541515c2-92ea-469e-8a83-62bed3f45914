package com.deepinnet.infra.service.impl;

import cn.dev33.satoken.secure.SaSecureUtil;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.constants.DataScopeConstants;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.api.enums.*;
import com.deepinnet.infra.dal.dataobject.*;
import com.deepinnet.infra.dal.mapper.DepartmentMapper;
import com.deepinnet.infra.dal.repository.*;
import com.deepinnet.infra.service.*;
import com.deepinnet.infra.service.context.TenantContext;
import com.deepinnet.infra.service.convert.*;
import com.deepinnet.infra.service.enums.*;
import com.deepinnet.infra.service.error.BizErrorCode;
import com.deepinnet.infra.service.strategy.*;
import com.deepinnet.infra.service.util.*;
import com.deepinnet.tenant.TenantIdUtil;
import com.google.common.collect.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wong
 * @create 2024/8/8 16:10
 * @Description
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @Resource
    private AccountRepository accountRepository;

    @Resource
    private RolePermissionRepository rolePermissionRepository;

    @Resource
    private UserRoleRepository userRoleRepository;

    @Resource
    private UserAccountRepository userAccountRepository;

    @Resource
    private RoleRepository roleRepository;

    @Resource
    private PermissionRepository permissionRepository;

    @Resource
    private RoleConvert roleConvert;

    @Resource
    private PermissionConvert permissionConvert;

    @Resource
    private DepartmentMemberRepository departmentMemberRepository;

    @Resource
    private UserDepartmentRepository userDepartmentRepository;

    @Resource
    private VerificationCodeService verificationCodeService;

    @Resource
    private DepartmentService departmentService;

    @Resource
    private DepartmentRepository departmentRepository;

    @Resource
    private DepartmentMapper departmentMapper;

    @Resource
    private UserRepository userRepository;

    @Resource
    private ProductServiceCustomerRepository customerRepository;

    @Resource
    private ProductServiceSupplierRepository supplierRepository;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private UserResetTokenRepository userResetTokenRepository;

    @Resource
    private UserContactInfoRepository userContactInfoRepository;

    @Resource
    private UserContactConvert userContactConvert;

    @Resource
    private UserDepartmentConvert userDepartmentConvert;

    @Resource
    private LoginStrategyFactory loginStrategyFactory;

    @Resource
    private UserMemberRepository userMemberRepository;

    @Resource
    private DataPermissionProcessor dataPermissionProcessor;

    private static final Long ROOT_PARENT_ID = -1L;

    @Value("${salt}")
    private String salt;

    @Override
    public UserLoginSuccessDTO login(UserLoginDTO loginDTO) {
        LogUtil.info("用户登录入参：{}", JSONUtil.toJsonStr(loginDTO));

        // 获取登录策略
        LoginStrategy loginStrategy = loginStrategyFactory.getStrategy(loginDTO.getScene());

        // 获取账号信息
        AccountDO account = loginStrategy.getAccount(loginDTO);
        if (account == null) {
            LogUtil.error("无法登录，账户不存在，入参为：{}", JSONUtil.toJsonStr(loginDTO));
            BizErrorCode errorCode = getBizErrorCode(loginDTO, BizErrorCode.ACCOUNT_OR_PASSWORD_ERROR, BizErrorCode.PHONE_NOT_FOUND);
            throw new BizException(errorCode.getCode(), errorCode.getDesc());
        }

        // 验证登录信息
        loginStrategy.validateLogin(loginDTO, account);

        // 处理登录
        return loginStrategy.processLogin(loginDTO, account);
    }

    private BizErrorCode getBizErrorCode(UserLoginDTO loginDTO, BizErrorCode trafficErrorCode, BizErrorCode skyFlowErrorCode) {
        BizErrorCode errorCode;
        if (StrUtil.isBlank(loginDTO.getScene()) || StrUtil.equals(loginDTO.getScene(), SceneEnum.TRAFFIC_POLICE.getCode())) {
            errorCode = trafficErrorCode;
        } else {
            errorCode = skyFlowErrorCode;
        }

        return errorCode;
    }

    @Override
    public UserLoginSuccessDTO loginByCode(UserLoginDTO loginDTO) {
        LogUtil.info("验证码登录入参：{}", JSONUtil.toJsonStr(loginDTO));

        // 使用与普通登录相同的策略处理
        return login(loginDTO);
    }

    @Override
    public UserInfoDTO getUserInfo() {
        String accountNo = StpUtil.getLoginIdAsString();
        AccountDO accountDO = accountRepository.getAccount(accountNo, null);
        if (accountDO == null) {
            throw new BizException(BizErrorCode.ACCOUNT_NOT_LOGIN.getCode(), BizErrorCode.ACCOUNT_NOT_LOGIN.getDesc());
        }

        // 用户绑定部门成员
        UserMemberDO userMember = userMemberRepository.getOne(Wrappers.lambdaQuery(UserMemberDO.class)
                .eq(UserMemberDO::getUserNo, accountDO.getUserNo()));

        List<UserRoleDO> userRoleDOS = userRoleRepository.list(Wrappers.lambdaQuery(UserRoleDO.class)
                .eq(UserRoleDO::getUserNo, accountDO.getUserNo()));
        if (CollectionUtils.isEmpty(userRoleDOS)) {
            return buildUserInfoDTO(accountDO, null, null, null, userMember, null, null);
        }

        // 检查是否是超级管理员
        if (Boolean.TRUE.equals(accountDO.getIsSuperAdmin())) {
            LogUtil.info("用户{}是超级管理员，返回所有权限", accountDO.getUserNo());
            String scene = (String) StpUtil.getExtra("scene");

            // 超级管理员返回所有权限
            List<PermissionDO> allPermissions = permissionRepository.list(Wrappers.lambdaQuery(PermissionDO.class)
                    .eq(StrUtil.isNotBlank(scene), PermissionDO::getScene, scene));

            return buildUserInfoDTO(accountDO, null, allPermissions, null, null, null, null);
        }

        // 查询角色和权限
        List<String> roleCodes = userRoleDOS.stream().map(UserRoleDO::getRoleCode)
                .collect(Collectors.toList());
        List<RoleDO> roleList = roleRepository.list(Wrappers.lambdaQuery(RoleDO.class)
                .in(RoleDO::getRoleCode, roleCodes));

        UserDepartmentDO userDepartmentDO = userDepartmentRepository.getOne(Wrappers.lambdaQuery(UserDepartmentDO.class)
                .eq(UserDepartmentDO::getUserNo, accountDO.getUserNo()));
        if (userDepartmentDO == null) {
            throw new BizException(BizErrorCode.ACCOUNT_NOT_BIND_DEPARTMENT.getCode(), BizErrorCode.ACCOUNT_NOT_BIND_DEPARTMENT.getDesc());
        }

        DepartmentDO departmentDO = departmentRepository.getById(userDepartmentDO.getDepartmentId());

        List<RolePermissionDO> rolePermissionDOs = rolePermissionRepository.list(Wrappers.lambdaQuery(RolePermissionDO.class)
                .in(RolePermissionDO::getRoleCode, roleCodes));
        List<String> permissionCode = rolePermissionDOs.stream().map(RolePermissionDO::getPermissionCode)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(permissionCode)) {
            return buildUserInfoDTO(accountDO, roleList, null, userDepartmentDO, userMember, null, departmentDO.getName());
        }

        DepartmentFlatDTO departmentFlatDTO = getDepartmentFlatDTO(departmentDO);

        List<PermissionDO> permissionList = permissionRepository.list(Wrappers.lambdaQuery(PermissionDO.class)
                .in(PermissionDO::getCode, permissionCode));

        return buildUserInfoDTO(accountDO, roleList, permissionList, userDepartmentDO, userMember, departmentFlatDTO, departmentDO.getName());
    }

    @Override
    public Boolean changePassword(UserPasswordChangeDTO passwordChangeDTO) {
        String accountNo = StpUtil.getLoginIdAsString();
        AccountDO account = accountRepository.getAccount(accountNo, null);
        if (account == null) {
            throw new BizException(BizErrorCode.ACCOUNT_NOT_FOUND.getCode(), BizErrorCode.ACCOUNT_NOT_FOUND.getDesc());
        }

        if (StrUtil.isBlank(passwordChangeDTO.getScene())
                || StrUtil.equals(passwordChangeDTO.getScene(), SceneEnum.TRAFFIC_POLICE.getCode())
                || StrUtil.equals(passwordChangeDTO.getScene(), SceneEnum.SPATIOTEMPORAL_OPENPROD.getCode())
                || StrUtil.equals(passwordChangeDTO.getScene(), SceneEnum.OPEN_OPERATION_PLATFORM.getCode())) {
            String encrypt = SaSecureUtil.aesEncrypt(salt, passwordChangeDTO.getOldPassword());
            if (!StringUtils.equals(encrypt, account.getPassword())) {
                throw new BizException(BizErrorCode.INVALID_PASSWORD.getCode(), BizErrorCode.INVALID_PASSWORD.getDesc());
            }
        }

        String newPassword = SaSecureUtil.aesEncrypt(salt, passwordChangeDTO.getNewPassword());
        account.setPassword(newPassword);
        // 设置用户已完成初始化
        account.setIsInitialized(true);
        accountRepository.updateById(account);

        StpUtil.logout();

        return true;
    }

    @Override
    public Boolean forgetPassword(UserPasswordForgetDTO passwordChangeDTO) {
        if (passwordChangeDTO == null || StrUtil.isBlank(passwordChangeDTO.getResetToken()) || StrUtil.isBlank(passwordChangeDTO.getNewPassword())) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        UserResetTokenDO userResetToken = userResetTokenRepository.getOne(Wrappers.lambdaQuery(UserResetTokenDO.class)
                .eq(UserResetTokenDO::getResetToken, passwordChangeDTO.getResetToken()));
        if (userResetToken == null) {
            throw new BizException(BizErrorCode.RESET_TOKEN_NOT_FOUND.getCode(), BizErrorCode.RESET_TOKEN_NOT_FOUND.getDesc());
        }

        if (StrUtil.equals(userResetToken.getStatus(), ResetTokenStatusEnum.VERIFIED.getCode())) {
            throw new BizException(BizErrorCode.RESET_TOKEN_INVALID.getCode(), BizErrorCode.RESET_TOKEN_INVALID.getDesc());
        }

        String userNo = userResetToken.getUserNo();
        String newPassword = SaSecureUtil.aesEncrypt(salt, passwordChangeDTO.getNewPassword());
        AccountDO accountDO = accountRepository.getOne(Wrappers.lambdaQuery(AccountDO.class)
                .eq(AccountDO::getUserNo, userNo));

        transactionTemplate.executeWithoutResult(action -> {
            accountDO.setPassword(newPassword);
            accountRepository.updateById(accountDO);

            userResetToken.setStatus(ResetTokenStatusEnum.VERIFIED.getCode());
            userResetTokenRepository.updateById(userResetToken);
        });

        return true;
    }

    @Override
    public List<SimpleDepartmentDTO> getUserDepartments() {
        String userNo = (String) StpUtil.getExtra("userNo");
        List<UserDepartmentDO> userDepartments = userDepartmentRepository.list(Wrappers.lambdaQuery(UserDepartmentDO.class)
                .eq(UserDepartmentDO::getUserNo, userNo));
        if (CollUtil.isNotEmpty(userDepartments)) {
            List<Long> departmentIds = userDepartments.stream()
                    .map(UserDepartmentDO::getDepartmentId)
                    .collect(Collectors.toList());
            List<DepartmentDO> departmentDOs = departmentRepository.listByIds(departmentIds);
            List<SimpleDepartmentDTO> simpleDepartmentDTOS = new ArrayList<>();
            for (DepartmentDO departmentDO : departmentDOs) {
                SimpleDepartmentDTO simpleDepartmentDTO = new SimpleDepartmentDTO();
                simpleDepartmentDTO.setId(departmentDO.getId());
                simpleDepartmentDTO.setName(departmentDO.getName());
                simpleDepartmentDTOS.add(simpleDepartmentDTO);
            }

            return simpleDepartmentDTOS;
        }

        return null;
    }

    @Override
    public List<SimpleDepartmentDTO> getUserRelatedDepartment(QueryUserRelatedDepartmentDTO queryDTO) {
        List<String> userNos = queryDTO.getUserNos();
        List<UserDO> users = userRepository.list(Wrappers.lambdaQuery(UserDO.class)
                .in(UserDO::getUserNo, userNos));
        List<UserDepartmentDO> userDepartmentDOs = userDepartmentRepository.list(Wrappers.lambdaQuery(UserDepartmentDO.class)
                .in(UserDepartmentDO::getUserNo, userNos));
        if (CollUtil.isEmpty(userDepartmentDOs)) {
            throw new BizException(BizErrorCode.USER_DEPARTMENT_NOT_FOUND.getCode(), BizErrorCode.USER_DEPARTMENT_NOT_FOUND.getDesc());
        }

        List<SimpleDepartmentDTO> simpleDepartmentDTOs = Lists.newArrayList();
        List<Long> departmentIds = userDepartmentDOs.stream()
                .map(UserDepartmentDO::getDepartmentId)
                .distinct()
                .collect(Collectors.toList());

        Map<String, UserDepartmentDO> userDepartmentMap = userDepartmentDOs.stream()
                .collect(Collectors.toMap(UserDepartmentDO::getUserNo, Function.identity()));

        List<DepartmentDO> departmentDOs = departmentRepository.listByIds(departmentIds);
        if (CollUtil.isEmpty(departmentDOs)) {
            throw new BizException(BizErrorCode.DEPARTMENT_NOT_FOUND.getCode(), BizErrorCode.DEPARTMENT_NOT_FOUND.getDesc());
        }

        List<UserMemberDO> userMemberDOs = userMemberRepository.list(Wrappers.lambdaQuery(UserMemberDO.class)
                .in(UserMemberDO::getUserNo, userNos));
        if (CollUtil.isEmpty(userMemberDOs)) {
            throw new BizException(BizErrorCode.USER_MEMBER_NOT_EXIST.getCode(), BizErrorCode.USER_MEMBER_NOT_EXIST.getDesc());
        }

        Map<String, UserMemberDO> userMemberMap = userMemberDOs.stream()
                .collect(Collectors.toMap(UserMemberDO::getUserNo, Function.identity()));

        // 部门成员map
        Map<Long, DepartmentMemberDO> memberMap = getDepartmentMemberMap(userMemberDOs);

        Map<Long, DepartmentDO> departmentMap = departmentDOs.stream()
                .collect(Collectors.toMap(DepartmentDO::getId, Function.identity()));

        users.forEach(u -> {
            UserDepartmentDO userDepartmentDO = userDepartmentMap.get(u.getUserNo());
            if (userDepartmentDO == null) {
                LogUtil.error("数据错误，用户未关联部门信息，userNo={}", u.getUserNo());
                throw new BizException(BizErrorCode.USER_DEPARTMENT_NOT_FOUND.getCode(), BizErrorCode.USER_DEPARTMENT_NOT_FOUND.getDesc());
            }

            DepartmentDO departmentDO = departmentMap.get(userDepartmentDO.getDepartmentId());
            if (departmentDO == null) {
                LogUtil.error("数据错误，部门信息不存在，userNo={},departmentId={}", u.getUserNo(), userDepartmentDO.getDepartmentId());
                throw new BizException(BizErrorCode.DEPARTMENT_NOT_FOUND.getCode(), BizErrorCode.DEPARTMENT_NOT_FOUND.getDesc());
            }

            UserMemberDO userMemberDO = userMemberMap.get(u.getUserNo());
            if (userMemberDO == null) {
                throw new BizException(BizErrorCode.USER_MEMBER_NOT_EXIST.getCode(), BizErrorCode.USER_MEMBER_NOT_EXIST.getDesc());
            }

            Long memberId = userMemberDO.getMemberId();
            DepartmentMemberDO departmentMemberDO = memberMap.get(memberId);
            if (departmentMemberDO == null) {
                throw new BizException(BizErrorCode.DEPARTMENT_MEMBER_NOT_FOUND.getCode(), BizErrorCode.DEPARTMENT_MEMBER_NOT_FOUND.getDesc());
            }

            simpleDepartmentDTOs.add(buildSimpleDepartmentDTO(u, userDepartmentDO, departmentDO, departmentMemberDO));
        });

        return simpleDepartmentDTOs;
    }

    private Map<Long, DepartmentMemberDO> getDepartmentMemberMap(List<UserMemberDO> userMemberDOS) {
        List<Long> memberIds = userMemberDOS.stream()
                .map(UserMemberDO::getMemberId)
                .collect(Collectors.toList());
        List<DepartmentMemberDO> members = departmentMemberRepository.listByIds(memberIds);
        if (CollUtil.isEmpty(members)) {
            throw new BizException(BizErrorCode.DEPARTMENT_MEMBER_NOT_FOUND.getCode(), BizErrorCode.DEPARTMENT_MEMBER_NOT_FOUND.getDesc());
        }

        return members.stream()
                .collect(Collectors.toMap(DepartmentMemberDO::getId, Function.identity()));
    }

    private SimpleDepartmentDTO buildSimpleDepartmentDTO(UserDO u, UserDepartmentDO userDepartmentDO, DepartmentDO departmentDO, DepartmentMemberDO departmentMemberDO) {
        SimpleDepartmentDTO simpleDepartmentDTO = new SimpleDepartmentDTO();
        simpleDepartmentDTO.setId(userDepartmentDO.getDepartmentId());
        simpleDepartmentDTO.setName(departmentDO.getName());
        simpleDepartmentDTO.setUserNo(u.getUserNo());
        simpleDepartmentDTO.setUserName(u.getUserName());
        simpleDepartmentDTO.setMemberPhone(departmentMemberDO.getPhone());
        List<Long> fullPath = JSONUtil.toList(departmentDO.getFullPath(), Long.class);
        if (fullPath.contains(ROOT_PARENT_ID)) {
            fullPath.remove(ROOT_PARENT_ID);
        }
        simpleDepartmentDTO.setFullPath(fullPath);

        return simpleDepartmentDTO;
    }

    @Override
    public Boolean checkUserInfoStatus() {
        String userNo = (String) StpUtil.getExtra("userNo");
        UserDO user = userRepository.getOne(Wrappers.lambdaQuery(UserDO.class).eq(UserDO::getUserNo, userNo));
        if (user == null) {
            throw new BizException(BizErrorCode.USER_NOT_EXIST.getCode(), BizErrorCode.USER_NOT_EXIST.getDesc());
        }

        if (StrUtil.equals(user.getUserType(), UserTypeEnum.CUSTOMER.getCode())) {
            ProductServiceCustomerDO customerDO = customerRepository.getOne(Wrappers.lambdaQuery(ProductServiceCustomerDO.class)
                    .eq(ProductServiceCustomerDO::getUserNo, userNo));
            if (StrUtil.isBlank(customerDO.getOrganizationName())) {
                return false;
            }

            // 查询最新的审核记录，判断是否审核通过
            return Objects.equals(customerDO.getApprovalStatus(), ApprovalStatusEnum.APPROVED.getCode());
        }

        if (StrUtil.equals(user.getUserType(), UserTypeEnum.SUPPLIER.getCode())) {
            ProductServiceSupplierDO supplierDO = supplierRepository.getOne(Wrappers.lambdaQuery(ProductServiceSupplierDO.class)
                    .eq(ProductServiceSupplierDO::getUserNo, userNo));
            if (StrUtil.isBlank(supplierDO.getCompanyName())) {
                return false;
            }

            // 查询最新的审核记录，判断是否审核通过
            return Objects.equals(supplierDO.getApprovalStatus(), ApprovalStatusEnum.APPROVED.getCode());
        }

        return true;
    }

    @Override
    public List<UserApprovalStatusDTO> getUserApprovalStatus(QueryUserApprovalStatusDTO queryDTO) {
        List<UserDO> userDOs = userRepository.list(Wrappers.lambdaQuery(UserDO.class)
                .in(UserDO::getUserNo, queryDTO.getUserNos()));
        if (CollectionUtils.isEmpty(userDOs)) {
            LogUtil.error("查询的用户不存在，入参为:{}，租户id为:{}", JSONUtil.toJsonStr(queryDTO), TenantIdUtil.getTenantId());
            return null;
        }

        // 按用户类型分组
        Map<String, List<UserDO>> userTypeMap = userDOs.stream()
                .collect(Collectors.groupingBy(UserDO::getUserType));

        // 批量查询客户信息
        Map<String, Integer> customerApprovalMap = buildCustomerApprovalMap(userTypeMap);

        // 批量查询服务商信息
        Map<String, Integer> supplierApprovalMap = buildSupplierApprovalMap(userTypeMap);

        // 构建返回结果
        return buildUserApprovalStatus(userDOs, customerApprovalMap, supplierApprovalMap);
    }

    private Map<String, Integer> buildSupplierApprovalMap(Map<String, List<UserDO>> userTypeMap) {
        Map<String, Integer> supplierApprovalMap = new HashMap<>();
        List<UserDO> supplierUsers = userTypeMap.get(UserTypeEnum.SUPPLIER.getCode());
        if (CollectionUtils.isNotEmpty(supplierUsers)) {
            List<String> supplierUserNos = supplierUsers.stream()
                    .map(UserDO::getUserNo)
                    .collect(Collectors.toList());

            List<ProductServiceSupplierDO> supplierDOs = supplierRepository.list(
                    Wrappers.lambdaQuery(ProductServiceSupplierDO.class)
                            .in(ProductServiceSupplierDO::getUserNo, supplierUserNos)
            );

            supplierApprovalMap = supplierDOs.stream()
                    .collect(Collectors.toMap(
                            ProductServiceSupplierDO::getUserNo,
                            ProductServiceSupplierDO::getApprovalStatus,
                            (existing, replacement) -> existing
                    ));
        }
        return supplierApprovalMap;
    }

    private Map<String, Integer> buildCustomerApprovalMap(Map<String, List<UserDO>> userTypeMap) {
        Map<String, Integer> customerApprovalMap = new HashMap<>();
        List<UserDO> customerUsers = userTypeMap.get(UserTypeEnum.CUSTOMER.getCode());
        if (CollectionUtils.isNotEmpty(customerUsers)) {
            List<String> customerUserNos = customerUsers.stream()
                    .map(UserDO::getUserNo)
                    .collect(Collectors.toList());

            List<ProductServiceCustomerDO> customerDOs = customerRepository.list(
                    Wrappers.lambdaQuery(ProductServiceCustomerDO.class)
                            .in(ProductServiceCustomerDO::getUserNo, customerUserNos)
            );

            customerApprovalMap = customerDOs.stream()
                    .collect(Collectors.toMap(
                            ProductServiceCustomerDO::getUserNo,
                            ProductServiceCustomerDO::getApprovalStatus,
                            (existing, replacement) -> existing
                    ));
        }
        return customerApprovalMap;
    }

    private List<UserApprovalStatusDTO> buildUserApprovalStatus(List<UserDO> userDOs, Map<String, Integer> customerApprovalMap, Map<String, Integer> supplierApprovalMap) {
        List<UserApprovalStatusDTO> result = new ArrayList<>();

        for (UserDO userDO : userDOs) {
            UserApprovalStatusDTO statusDTO = new UserApprovalStatusDTO();
            statusDTO.setUserNo(userDO.getUserNo());

            // 根据用户类型获取审核状态
            Integer approvalStatus = null;
            if (StrUtil.equals(userDO.getUserType(), UserTypeEnum.CUSTOMER.getCode())) {
                approvalStatus = customerApprovalMap.get(userDO.getUserNo());
            } else if (StrUtil.equals(userDO.getUserType(), UserTypeEnum.SUPPLIER.getCode())) {
                approvalStatus = supplierApprovalMap.get(userDO.getUserNo());
            }

            statusDTO.setApprovalStatus(approvalStatus);
            result.add(statusDTO);
        }
        return result;
    }

    @Override
    public List<UserDetailDTO> getUserDetailList(UserQueryDTO queryDTO) {
        if (queryDTO == null || CollUtil.isEmpty(queryDTO.getUserNos()) && StrUtil.isBlank(queryDTO.getUserName())) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        List<UserDO> userDOs;
        if (CollUtil.isNotEmpty(queryDTO.getUserNos())) {
            userDOs = userRepository.list(Wrappers.lambdaQuery(UserDO.class)
                    .in(UserDO::getUserNo, queryDTO.getUserNos()));
        } else if (StrUtil.isNotBlank(queryDTO.getUserName())) {
            userDOs = userRepository.list(Wrappers.lambdaQuery(UserDO.class)
                    .like(UserDO::getUserName, queryDTO.getUserName())
                    .eq(StrUtil.isNotBlank(queryDTO.getUserType()), UserDO::getUserType, queryDTO.getUserType()));
        } else {
            return null;
        }

        if (CollUtil.isEmpty(userDOs)) {
            LogUtil.info("查询出来的用户数据为空，入参为：{}", JSONUtil.toJsonStr(queryDTO));
            return null;
        }

        Map<String, ProductServiceCustomerDO> customerMap = new HashMap<>();
        Map<String, ProductServiceSupplierDO> supplierMap = new HashMap<>();
        Map<String, List<ContactInfoDTO>> userContactMap = new HashMap<>();
        List<String> userNos = new ArrayList<>();

        List<UserDO> customerList = userDOs.stream()
                .filter(u -> StrUtil.equals(u.getUserType(), UserTypeEnum.CUSTOMER.getCode()))
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(customerList)) {
            List<String> customerNos = customerList.stream().map(UserDO::getUserNo)
                    .distinct()
                    .collect(Collectors.toList());
            List<ProductServiceCustomerDO> customers = customerRepository.list(Wrappers.lambdaQuery(ProductServiceCustomerDO.class)
                    .in(ProductServiceCustomerDO::getUserNo, customerNos));
            customerMap = customers.stream()
                    .collect(Collectors.toMap(ProductServiceCustomerDO::getUserNo, Function.identity()));
            userNos = customerNos;
        }

        List<UserDO> supplierList = userDOs.stream()
                .filter(u -> StrUtil.equals(u.getUserType(), UserTypeEnum.SUPPLIER.getCode()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(supplierList)) {
            List<String> supplierNos = supplierList.stream().map(UserDO::getUserNo)
                    .distinct()
                    .collect(Collectors.toList());
            List<ProductServiceSupplierDO> suppliers = supplierRepository.list(Wrappers.lambdaQuery(ProductServiceSupplierDO.class)
                    .in(ProductServiceSupplierDO::getUserNo, supplierNos));
            supplierMap = suppliers.stream()
                    .collect(Collectors.toMap(ProductServiceSupplierDO::getUserNo, Function.identity()));
            userNos = supplierNos;
        }

        if (CollUtil.isEmpty(userNos)) {
            List<UserDetailDTO> userDetailDTOs = Lists.newArrayList();
            userDOs.forEach(u -> userDetailDTOs.add(buildUserDetailDTO(u)));
            return userDetailDTOs;
        }

        List<UserContactInfoDO> userContactInfos = userContactInfoRepository.list(Wrappers.lambdaQuery(UserContactInfoDO.class)
                .in(UserContactInfoDO::getUserNo, userNos));

        if (CollUtil.isNotEmpty(userContactInfos)) {
            List<ContactInfoDTO> contactInfoDTOs = userContactConvert.convertToUserContactDTOs(userContactInfos);
            userContactMap = contactInfoDTOs.stream()
                    .collect(Collectors.groupingBy(ContactInfoDTO::getUserNo));
        }

        return buildUserDetailDTO(userDOs, customerMap, supplierMap, userContactMap);
    }

    @Override
    public List<UserDetailDTO> getUserDetailListWithoutTenantId(UserQueryDTO queryDTO) {
        try {
            TenantContext.disableTenantLine();
            return getUserDetailList(queryDTO);
        } finally {
            TenantContext.clear();
        }

    }

    @Override
    public List<SimpleUserInfoDTO> getSimpleUserInfoWithoutTenantId(QueryUserInfoDTO queryDTO) {
        TenantContext.disableTenantLine();
        List<SimpleUserInfoDTO> userInfoDTOs = Lists.newArrayList();
        List<UserDO> userDOs = userRepository.list(Wrappers.lambdaQuery(UserDO.class)
                .eq(UserDO::getUserType, queryDTO.getUserType()));
        if (CollUtil.isEmpty(userDOs)) {
            return null;
        }

        userDOs.forEach(u -> {
            SimpleUserInfoDTO simpleUserInfoDTO = new SimpleUserInfoDTO();
            simpleUserInfoDTO.setUserNo(u.getUserNo());
            simpleUserInfoDTO.setName(u.getUserName());
            simpleUserInfoDTO.setPhone(u.getPhone());
            simpleUserInfoDTO.setTenantId(u.getTenantId());
            userInfoDTOs.add(simpleUserInfoDTO);
        });

        return userInfoDTOs;
    }

    private UserDetailDTO buildUserDetailDTO(UserDO u) {
        UserDetailDTO userDetailDTO = new UserDetailDTO();
        userDetailDTO.setUserNo(u.getUserNo());
        userDetailDTO.setUserName(u.getUserName());
        userDetailDTO.setPhone(u.getPhone());
        userDetailDTO.setTenantId(u.getTenantId());
        userDetailDTO.setUserType(u.getUserType());
        userDetailDTO.setRegistrationTime(LocalDateTimeUtil.toEpochMilli(u.getRegistrationTime()));

        // 查询用户账号状态
        List<UserAccountDO> userAccountDOs = userAccountRepository.list(Wrappers.lambdaQuery(UserAccountDO.class)
                .eq(UserAccountDO::getUserNo, u.getUserNo()));
        if (CollUtil.isNotEmpty(userAccountDOs)) {
            String accountNo = userAccountDOs.get(0).getAccountNo();
            AccountDO accountDO = accountRepository.getAccount(accountNo, null);
            if (accountDO != null && accountDO.getStatus() != null) {
                userDetailDTO.setStatus(accountDO.getStatus());
            } else {
                // 默认正常状态
                userDetailDTO.setStatus(AccountStatusEnum.NORMAL.getCode());
            }
        }

        return userDetailDTO;
    }

    @Override
    public DataAccessDTO getAvailableQueryData() {
        // 老的逻辑，不需要校验数据权限
        PermissionSystemEnum permissionSystemEnum = dataPermissionProcessor.routeToPermissionSystem();
        if (permissionSystemEnum == PermissionSystemEnum.NO_DATA_PERMISSION) {
            return processWithDefaultPermissionSystem();
        }

        // 需要做数据权限校验
        return processWithDataAccessPermissionSystem();
    }

    private DataAccessDTO processWithDataAccessPermissionSystem() {
        DataAccessDTO dataAccessDTO = new DataAccessDTO();

        String userNo = (String) StpUtil.getExtra("userNo");
        Boolean isSuperAdminTag = false;
        Object isSuperAdmin = StpUtil.getExtra("isSuperAdmin");
        if (ObjectUtil.isNotNull(isSuperAdmin)) {
            isSuperAdminTag = (Boolean) StpUtil.getExtra("isSuperAdmin");
        }

        // 超级管理员特殊处理，能查看所有的数据
        if (isSuperAdminTag || DataScopeConstants.SUPER_ADMIN_NOS.contains(userNo)) {
            dataAccessDTO.setSupportQueryUserNos(Lists.newArrayList(DataScopeConstants.SUPER_ADMIN));
            return dataAccessDTO;
        }

        // 查询用户的所有角色
        List<UserRoleDO> userRoles = userRoleRepository.list(Wrappers.lambdaQuery(UserRoleDO.class)
                .eq(UserRoleDO::getUserNo, userNo));

        if (CollectionUtils.isEmpty(userRoles)) {
            // 用户没有角色，只能查看个人数据
            dataAccessDTO.setSupportQueryUserNos(Lists.newArrayList(userNo));
            return dataAccessDTO;
        }

        // 查询角色信息，计算最高数据权限作用域（功能权限取并集，数据权限作用域取最高）
        List<String> roleCodes = userRoles.stream()
                .map(UserRoleDO::getRoleCode)
                .collect(Collectors.toList());

        List<RoleDO> roles = roleRepository.list(Wrappers.lambdaQuery(RoleDO.class)
                .in(RoleDO::getRoleCode, roleCodes));

        if (CollectionUtils.isEmpty(roles)) {
            // 角色不存在，只能查看个人数据
            dataAccessDTO.setSupportQueryUserNos(Lists.newArrayList(userNo));
            return dataAccessDTO;
        }

        // 计算最高数据权限作用域（取最大值）
        Integer maxDataScope = roles.stream()
                .filter(role -> role.getDataScope() != null)
                .mapToInt(RoleDO::getDataScope)
                .max()
                .orElse(1); // 默认为个人数据权限

        // 根据数据权限作用域返回可查询的数据范围
        return buildDataAccessByScope(maxDataScope, userNo, dataAccessDTO);
    }

    private DataAccessDTO processWithDefaultPermissionSystem() {
        DataAccessDTO dataAccessDTO = new DataAccessDTO();

        String userNo = (String) StpUtil.getExtra("userNo");
        boolean isSuperAdmin = (boolean) StpUtil.getExtra("isSuperAdmin");

        // 超级管理员特殊处理，能查看所有的数据
        if (isSuperAdmin || DataScopeConstants.SUPER_ADMIN_NOS.contains(userNo)) {
            dataAccessDTO.setSupportQueryUserNos(Lists.newArrayList(DataScopeConstants.SUPER_ADMIN));
            return dataAccessDTO;
        }

        QuerySubDepartmentDTO queryDTO = new QuerySubDepartmentDTO();
        queryDTO.setUserNo(userNo);
        queryDTO.setDepth(-1);
        List<DepartmentDTO> userDepartmentList = departmentService.getSubDepartments(queryDTO);
        if (CollectionUtils.isEmpty(userDepartmentList)) {
            dataAccessDTO.setSupportQueryUserNos(Lists.newArrayList(userNo));
            return dataAccessDTO;
        }

        List<DataScopeTreeNodeDTO> treeNodes = userDepartmentList.stream()
                .map(this::convertToTreeNode)
                .collect(Collectors.toList());

        treeNodes = filterAndPruneRelatedMember(treeNodes, userNo);
        if (CollectionUtils.isEmpty(treeNodes)) {
            dataAccessDTO.setSupportQueryUserNos(Lists.newArrayList(userNo));
            return dataAccessDTO;
        }

        treeNodes.forEach(node -> collectAllInfo(node, dataAccessDTO, userNo));

        List<String> supportQueryUserNos = dataAccessDTO.getSupportQueryUserNos();
        if (CollUtil.isEmpty(supportQueryUserNos) || !supportQueryUserNos.contains(userNo)) {
            supportQueryUserNos.add(userNo);
        }

        return dataAccessDTO;
    }

    /**
     * 根据数据权限作用域构建数据访问权限
     */
    private DataAccessDTO buildDataAccessByScope(Integer dataScope, String userNo, DataAccessDTO dataAccessDTO) {
        switch (dataScope) {
            case 1:
                // 个人数据：只能查看自己的数据
                dataAccessDTO.setSupportQueryUserNos(Lists.newArrayList(userNo));
                dataAccessDTO.setDataScope(1);
                break;
            case 2:
                // 组织数据（本级及下属）：查看本级部门及下属部门的数据
                buildDepartmentAndChildrenDataWithUniflyStrategy(userNo, dataAccessDTO);
                dataAccessDTO.setDataScope(2);
                break;
            case 3:
                // 组织数据（本组织全局）：查看本部门的所有数据
                buildCurrentDepartmentAllData(userNo, dataAccessDTO);
                dataAccessDTO.setDataScope(3);
                break;
            case 4:
                // 全局数据：查看所有数据
                dataAccessDTO.setSupportQueryUserNos(Lists.newArrayList(DataScopeConstants.SUPER_ADMIN));
                dataAccessDTO.setDataScope(4);
                break;
            default:
                // 默认个人数据权限
                dataAccessDTO.setSupportQueryUserNos(Lists.newArrayList(userNo));
                dataAccessDTO.setDataScope(1);
                break;
        }

        return dataAccessDTO;
    }

    /**
     * 构建本级及下属部门数据访问权限
     */
    private void buildDepartmentAndChildrenDataWithUniflyStrategy(String userNo, DataAccessDTO dataAccessDTO) {
        QuerySubDepartmentDTO queryDTO = new QuerySubDepartmentDTO();
        queryDTO.setUserNo(userNo);
        queryDTO.setDepth(-1);
        List<DepartmentDTO> userDepartmentList = departmentService.getSubDepartments(queryDTO);

        if (CollectionUtils.isEmpty(userDepartmentList)) {
            dataAccessDTO.setSupportQueryUserNos(Lists.newArrayList(userNo));
            return;
        }

        // 收集所有部门ID
        Set<Long> allDepartmentIds = new HashSet<>();
        // 收集所有关联的用户编号
        Set<String> allRelatedUserNos = new HashSet<>();

        // 递归收集所有部门及其成员的关联用户编号
        collectDepartmentAndMemberUserNos(userDepartmentList, allDepartmentIds, allRelatedUserNos);

        // 确保当前用户在可查询列表中
        allRelatedUserNos.add(userNo);

        dataAccessDTO.setSupportQueryUserNos(Lists.newArrayList(allRelatedUserNos));
        dataAccessDTO.setSupportQueryDepartmentIds(Lists.newArrayList(allDepartmentIds));
    }

    /**
     * 递归收集部门及其成员的关联用户编号
     */
    private void collectDepartmentAndMemberUserNos(List<DepartmentDTO> departments, Set<Long> allDepartmentIds, Set<String> allRelatedUserNos) {
        if (CollectionUtils.isEmpty(departments)) {
            return;
        }

        for (DepartmentDTO department : departments) {
            // 收集部门ID
            allDepartmentIds.add(department.getId());

            // 收集部门成员的关联用户编号
            if (CollectionUtils.isNotEmpty(department.getMembers())) {
                for (DepartmentMemberDTO member : department.getMembers()) {
                    if (StrUtil.isNotBlank(member.getRelatedUserNo())) {
                        allRelatedUserNos.add(member.getRelatedUserNo());
                    }
                }
            }

            // 递归处理子部门
            if (CollectionUtils.isNotEmpty(department.getChildren())) {
                collectDepartmentAndMemberUserNos(department.getChildren(), allDepartmentIds, allRelatedUserNos);
            }
        }
    }

    /**
     * 构建所有部门数据访问权限
     */
    private void buildCurrentDepartmentAllData(String userNo, DataAccessDTO dataAccessDTO) {
        String scene = (String) StpUtil.getExtra("scene");

        List<UserDepartmentDO> userDepartmentDOs = userDepartmentRepository.list(Wrappers.lambdaQuery(UserDepartmentDO.class)
                .eq(UserDepartmentDO::getUserNo, userNo));

        if (CollectionUtils.isEmpty(userDepartmentDOs)) {
            dataAccessDTO.setSupportQueryUserNos(Lists.newArrayList(userNo));
            return;
        }

        // 根据部门的full_path路径找到当前部门的根节点的id
        List<Long> departmentIds = userDepartmentDOs.stream()
                .map(UserDepartmentDO::getDepartmentId)
                .collect(Collectors.toList());

        List<DepartmentDO> departmentDOs = departmentRepository.listByIds(departmentIds);
        List<Long> rootDepartmentIds = Lists.newArrayList();

        departmentDOs.forEach(department -> {
            List<Long> parentDepartmentList = JSONUtil.toList(department.getFullPath(), Long.class);
            Long rootId = parentDepartmentList.get(0);
            if (rootId == -1) {
                rootId = parentDepartmentList.get(1);
            }

            rootDepartmentIds.add(rootId);
        });

        // 根据根节点id，获取这棵子树上所有部门id
        Set<Long> allSubDepartmentIds = Sets.newHashSet();
        rootDepartmentIds.forEach(rootId -> {
            List<Long> currentSubDepartmentIds = departmentMapper.getAllSubDepartmentIds(rootId, scene);
            allSubDepartmentIds.addAll(currentSubDepartmentIds);
        });

        List<UserDepartmentDO> allUserDepartments = userDepartmentRepository.list(Wrappers.lambdaQuery(UserDepartmentDO.class)
                .in(UserDepartmentDO::getDepartmentId, allSubDepartmentIds));

        if (CollectionUtils.isNotEmpty(allUserDepartments)) {
            List<String> allUserNos = allUserDepartments.stream()
                    .map(UserDepartmentDO::getUserNo)
                    .distinct()
                    .collect(Collectors.toList());

            dataAccessDTO.setSupportQueryUserNos(allUserNos);
            dataAccessDTO.setSupportQueryDepartmentIds(Lists.newArrayList(allSubDepartmentIds));
        } else {
            dataAccessDTO.setSupportQueryUserNos(Lists.newArrayList(userNo));
        }
    }

    private List<UserDetailDTO> buildUserDetailDTO(List<UserDO> userDOs, Map<String, ProductServiceCustomerDO> customerMap, Map<String, ProductServiceSupplierDO> supplierMap,
                                                   Map<String, List<ContactInfoDTO>> userContactMap) {
        List<UserDetailDTO> detailDTOs = new ArrayList<>();
        for (UserDO userDO : userDOs) {
            UserDetailDTO userDetailDTO = buildUserDetailDTO(userDO);

            List<ContactInfoDTO> contactInfoDTOs = userContactMap.get(userDO.getUserNo());
            if (StrUtil.equals(userDO.getUserType(), UserTypeEnum.CUSTOMER.getCode())) {
                ProductServiceCustomerDO productServiceCustomerDO = customerMap.get(userDO.getUserNo());
                CustomerDetailDTO detailDTO = new CustomerDetailDTO();
                BeanUtils.copyProperties(productServiceCustomerDO, detailDTO);
                detailDTO.setContactInfoDTOs(contactInfoDTOs);
                userDetailDTO.setCustomerDetailDTO(detailDTO);
                userDetailDTO.setShowName(detailDTO.getOrganizationName());
            }

            if (StrUtil.equals(userDO.getUserType(), UserTypeEnum.SUPPLIER.getCode())) {
                ProductServiceSupplierDO supplierDO = supplierMap.get(userDO.getUserNo());
                SupplierDetailDTO supplierDetailDTO = new SupplierDetailDTO();
                BeanUtils.copyProperties(supplierDO, supplierDetailDTO);
                List<SupplierContactInfoDTO> supplierContactInfoDTOs = BeanUtil.copyToList(contactInfoDTOs, SupplierContactInfoDTO.class);
                supplierDetailDTO.setContactInfoList(supplierContactInfoDTOs);
                userDetailDTO.setSupplierDetailDTO(supplierDetailDTO);
                userDetailDTO.setShowName(supplierDO.getCompanyName());
            }

            detailDTOs.add(userDetailDTO);
        }
        return detailDTOs;
    }

    @Override
    public Boolean logout() {
        StpUtil.logout();
        return true;
    }

    @Override
    public List<UserMemberDTO> getUserInfoByMemberId(List<Long> memberIds) {
        if (CollectionUtils.isEmpty(memberIds)) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        List<DepartmentMemberDO> departmentMembers = departmentMemberRepository.listByIds(memberIds);
        if (CollectionUtils.isEmpty(departmentMembers)) {
            return null;
        }

        List<Long> departmentIds = departmentMembers.stream()
                .map(DepartmentMemberDO::getDepartmentId)
                .distinct()
                .collect(Collectors.toList());
        List<UserDepartmentDTO> userDepartments = buildUserDepartmentDTO(departmentIds);
        if (userDepartments == null) {
            return null;
        }

        return buildUserMemberDTOs(departmentMembers, userDepartments);
    }

    @Override
    public List<UserMemberDTO> getUserInfoByUserNo(List<String> userNos) {
        if (CollectionUtils.isEmpty(userNos)) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        List<DepartmentMemberDO> departmentMembers = departmentMemberRepository.list(Wrappers.lambdaQuery(DepartmentMemberDO.class));
        if (CollectionUtils.isEmpty(departmentMembers)) {
            return null;
        }

        List<Long> departmentIds = departmentMembers.stream()
                .map(DepartmentMemberDO::getDepartmentId)
                .distinct()
                .collect(Collectors.toList());
        List<UserDepartmentDTO> userDepartments = buildUserDepartmentDTO(departmentIds);
        if (userDepartments == null) {
            return null;
        }

        return buildUserMemberDTOs(departmentMembers, userDepartments);
    }

    @Override
    public Boolean register(UserRegisterDTO registerDTO) {
        // 参数校验
        if (registerDTO == null || StringUtils.isBlank(registerDTO.getPhone())
                || StringUtils.isBlank(registerDTO.getPassword())
                || StringUtils.isBlank(registerDTO.getCode())
                || registerDTO.getUserType() == null) {
            LogUtil.error("用户注册失败，参数不完整");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "注册信息不完整");
        }

        // 检查用户是否已存在
        LambdaQueryWrapper<UserDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserDO::getPhone, registerDTO.getPhone());
        queryWrapper.eq(StringUtils.isNotBlank(TenantIdUtil.getTenantId()), UserDO::getTenantId, TenantIdUtil.getTenantId());
        List<UserDO> existUsers = userRepository.list(queryWrapper);
        if (CollUtil.isNotEmpty(existUsers)) {
            List<String> userTypes = existUsers.stream()
                    .map(UserDO::getUserType)
                    .collect(Collectors.toList());

            // 服务商
            if (StrUtil.equals(registerDTO.getUserType(), UserTypeEnum.SUPPLIER.getCode())) {
                if (userTypes.contains(UserTypeEnum.SUPPLIER.getCode())) {
                    throw new BizException(BizErrorCode.USER_ALREADY_EXISTS.getCode(), BizErrorCode.USER_ALREADY_EXISTS.getDesc());
                }

                if (userTypes.contains(UserTypeEnum.CUSTOMER.getCode())) {
                    throw new BizException(BizErrorCode.USER_ALREADY_EXISTS.getCode(), "您已拥有客户身份，不可再注册服务商身份！");
                }
            } else if (StrUtil.equals(registerDTO.getUserType(), UserTypeEnum.CUSTOMER.getCode())) {
                if (userTypes.contains(UserTypeEnum.CUSTOMER.getCode())) {
                    throw new BizException(BizErrorCode.USER_ALREADY_EXISTS.getCode(), BizErrorCode.USER_ALREADY_EXISTS.getDesc());
                }

                if (userTypes.contains(UserTypeEnum.SUPPLIER.getCode())) {
                    throw new BizException(BizErrorCode.USER_ALREADY_EXISTS.getCode(), "您已拥有客户身份，不可再注册服务商身份！");
                }
            } else if (StrUtil.equals(registerDTO.getUserType(), UserTypeEnum.SPATIOTEMPORAL_OPENPROD.getCode())) {
                if (userTypes.contains(UserTypeEnum.SPATIOTEMPORAL_OPENPROD.getCode())) {
                    throw new BizException(BizErrorCode.USER_ALREADY_EXISTS.getCode(), BizErrorCode.USER_ALREADY_EXISTS.getDesc());
                }
            }
        }

        // 校验验证码
        String scene;
        if (StrUtil.equals(registerDTO.getUserType(), UserTypeEnum.OPERATION.getCode())) {
            scene = SceneEnum.OPERATION_CENTER.getCode();
        } else if (StrUtil.equals(registerDTO.getUserType(), UserTypeEnum.SPATIOTEMPORAL_OPENPROD.getCode())) {
            scene = SceneEnum.SPATIOTEMPORAL_OPENPROD.getCode();
        } else {
            scene = SceneEnum.MATCHING_PLATFORM.getCode();
        }

        Result<Boolean> verifyResult = verificationCodeService.verifyCode(
                registerDTO.getPhone(), registerDTO.getCode(), SmsTypeEnum.REGISTER.getCode(), scene);
        if (!verifyResult.isSuccess()) {
            LogUtil.error("用户注册失败，验证码校验失败，手机号：[{}]", registerDTO.getPhone());
            throw new BizException(verifyResult.getErrorCode(), verifyResult.getErrorDesc());
        }

        try {
            // 生成用户编号
            String userNo = BizNoGenerateUtil.generateUserNo();
            LocalDateTime now = LocalDateTime.now();

            transactionTemplate.executeWithoutResult(action -> {
                // 创建用户基本信息
                UserDO user = createUser(registerDTO, userNo, now);

                // 创建账号信息
                AccountDO account = createAccount(registerDTO, userNo, now);

                // 创建用户账号关联表
                createUserAccount(user.getUserNo(), account.getAccountNo());

                // 根据用户类型创建相应的用户信息
                createUserByType(registerDTO, userNo, now);
            });

            LogUtil.info("用户注册成功，手机号：[{}]，用户编号：[{}]", registerDTO.getPhone(), userNo);
            return true;
        } catch (Exception e) {
            LogUtil.error("用户注册异常，手机号：[{}]，异常信息：{}", registerDTO.getPhone(), e.getMessage(), e);
            throw new BizException(BizErrorCode.REGISTER_FAILED.getCode(), "注册失败");
        }
    }

    @Override
    public List<UserMemberDTO> getUserInfoSimpleByUserNos(List<String> userNos) {
        if (CollectionUtils.isEmpty(userNos)) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        List<UserDO> userDOs = userRepository.list(Wrappers.lambdaQuery(UserDO.class)
                .in(UserDO::getUserNo, userNos));
        if (CollectionUtils.isEmpty(userDOs)) {
            return List.of();
        }

        return userDOs.stream()
                .map(u -> {
                    UserMemberDTO memberDTO = new UserMemberDTO();
                    memberDTO.setUserName(u.getUserName());
                    memberDTO.setUserNo(u.getUserNo());
                    return memberDTO;
                }).collect(Collectors.toList());
    }


    @Override
    public Boolean checkUserInitializedStatus() {
        // 获取当前登录用户的账号信息
        String accountNo = StpUtil.getLoginIdAsString();
        if (StringUtils.isBlank(accountNo)) {
            throw new BizException(BizErrorCode.ACCOUNT_NOT_LOGIN.getCode(), BizErrorCode.ACCOUNT_NOT_LOGIN.getDesc());
        }

        // 查询账号信息
        AccountDO account = accountRepository.getAccount(accountNo, null);
        if (account == null) {
            throw new BizException(BizErrorCode.ACCOUNT_NOT_FOUND.getCode(), BizErrorCode.ACCOUNT_NOT_FOUND.getDesc());
        }

        return account.getIsInitialized();
    }

    @Override
    public Boolean freezeAccounts(AccountFreezeDTO accountFreezeDTO) {
        dataPermissionProcessor.checkDataPermissionByUserNo();

        AccountDO account = accountRepository.getAccount(accountFreezeDTO.getAccountNo(), null);

        if (account == null) {
            throw new BizException(BizErrorCode.ACCOUNT_NOT_FOUND.getCode(), BizErrorCode.ACCOUNT_NOT_FOUND.getDesc());
        }

        // 检查是否有账号已经是冻结状态
        if (Objects.equals(account.getStatus(), AccountStatusEnum.FROZEN.getCode())) {
            return true;
        }

        // 将未冻结的账号状态改为冻结
        account.setStatus(AccountStatusEnum.FROZEN.getCode());
        accountRepository.updateById(account);

        // 如果账号当前在线，强制下线
        if (StpUtil.isLogin(account.getAccountNo())) {
            LogUtil.info("强制下线账号：{}", account.getAccount());
            StpUtil.logout(account.getAccountNo());
        }

        return true;
    }

    @Override
    public Boolean unfreezeAccounts(AccountFreezeDTO accountFreezeDTO) {
        dataPermissionProcessor.checkDataPermissionByUserNo();

        AccountDO account = accountRepository.getAccount(accountFreezeDTO.getAccountNo(), null);

        if (account == null) {
            throw new BizException(BizErrorCode.ACCOUNT_NOT_FOUND.getCode(), BizErrorCode.ACCOUNT_NOT_FOUND.getDesc());
        }

        // 检查是否有账号已经是冻结状态
        if (Objects.equals(account.getStatus(), AccountStatusEnum.NORMAL.getCode())) {
            return true;
        }

        // 将冻结的账号状态改为正常
        account.setStatus(AccountStatusEnum.NORMAL.getCode());
        accountRepository.updateById(account);
        return true;
    }

    @Override
    public List<DataScopeTreeNodeDTO> getAvailableDataScopeTree() {
        String userNo = (String) StpUtil.getExtra("userNo");
        Boolean isSuperAdminTag = false;
        Object isSuperAdmin = StpUtil.getExtra("isSuperAdmin");
        if (ObjectUtil.isNotNull(isSuperAdmin)) {
            isSuperAdminTag = (Boolean) StpUtil.getExtra("isSuperAdmin");
        }

        List<DepartmentDTO> departmentDTOS = departmentService.getDepartmentByTree(-1L);

        // 超级管理员特殊处理，能查看所有的数据
        if (isSuperAdminTag || DataScopeConstants.SUPER_ADMIN_NOS.contains(userNo)) {
            return departmentDTOS.stream().map(this::convertToTreeNode).collect(Collectors.toList());
        }

        DataAccessDTO dataAccessDTO = getAvailableQueryData();

        // 没有全局数据的直接查询返回
        if (2 == dataAccessDTO.getDataScope() || 1 == dataAccessDTO.getDataScope()) {
            QuerySubDepartmentDTO queryDTO = new QuerySubDepartmentDTO();
            queryDTO.setUserNo(userNo);
            queryDTO.setDepth(-1);
            departmentDTOS = departmentService.getSubDepartments(queryDTO);
            if (CollectionUtils.isEmpty(departmentDTOS)) {
                return List.of();
            }

            // 个人数据过滤一下成员
            if (1 == dataAccessDTO.getDataScope()) {
                departmentDTOS.forEach(d -> d.getMembers().removeIf(m -> !dataAccessDTO.getSupportQueryUserNos().contains(m.getRelatedUserNo())
                        && 4 != dataAccessDTO.getDataScope()));
                departmentDTOS.forEach(d -> d.setChildren(List.of()));
            }
        }

        return departmentDTOS.stream().map(this::convertToTreeNode).collect(Collectors.toList());
    }

    /**
     * 创建用户基本信息
     */
    private UserDO createUser(UserRegisterDTO registerDTO, String userNo, LocalDateTime now) {
        UserDO userDO = new UserDO();
        userDO.setUserNo(userNo);
        userDO.setUserName(registerDTO.getUserName());
        userDO.setPhone(registerDTO.getPhone());
        userDO.setUserType(registerDTO.getUserType());
        userDO.setGmtCreated(new Date());
        userDO.setGmtModified(new Date());
        userDO.setRegistrationTime(now);
        userDO.setIsDeleted(false);
        userDO.setTenantId(TenantIdUtil.getTenantId());
        userRepository.save(userDO);

        return userDO;
    }

    /**
     * 创建用户账号关联信息
     */
    private void createUserAccount(String userNo, String accountNo) {
        UserAccountDO userAccountDO = new UserAccountDO();
        userAccountDO.setAccountNo(accountNo);
        userAccountDO.setUserNo(userNo);
        userAccountDO.setGmtCreated(new Date());
        userAccountDO.setGmtModified(new Date());
        userAccountRepository.save(userAccountDO);
    }

    private DepartmentFlatDTO getDepartmentFlatDTO(DepartmentDO departmentDO) {
        DepartmentFlatDTO departmentFlatDTO = new DepartmentFlatDTO();
        departmentFlatDTO.setId(departmentDO.getId());
        departmentFlatDTO.setName(departmentDO.getName());
        departmentFlatDTO.setLevel(departmentDO.getLevel());
        departmentFlatDTO.setParentId(departmentDO.getParentId());
        String fullPath = departmentDO.getFullPath();
        List<Long> fullPathList = JSONUtil.toList(fullPath, Long.class);
        fullPathList.remove(ROOT_PARENT_ID);
        if (CollUtil.isNotEmpty(fullPathList)) {
            List<DepartmentDO> departmentDOs = departmentRepository.listByIds(fullPathList);
            List<String> departmentNames = departmentDOs.stream().map(DepartmentDO::getName)
                    .collect(Collectors.toList());
            departmentFlatDTO.setFullPathName(departmentNames);
        }
        departmentFlatDTO.setFullPath(JSONUtil.toJsonStr(fullPathList));
        return departmentFlatDTO;
    }

    /**
     * 创建账号信息
     */
    private AccountDO createAccount(UserRegisterDTO registerDTO, String userNo, LocalDateTime now) {
        String accountNo = BizNoGenerateUtil.generateAccountNo();
        AccountDO accountDO = new AccountDO();
        accountDO.setUserNo(userNo);
        accountDO.setAccountNo(accountNo);
        accountDO.setAccount(registerDTO.getPhone());
        accountDO.setPhone(registerDTO.getPhone());
        accountDO.setName(registerDTO.getUserName());
        // 密码加密存储
        accountDO.setPassword(SaSecureUtil.aesEncrypt(salt, registerDTO.getPassword()));
        accountDO.setVisible(true);
        accountDO.setUserType(registerDTO.getUserType());
        accountDO.setIsDeleted(false);
        accountDO.setGmtCreated(new Date());
        accountDO.setGmtModified(new Date());
        accountDO.setTenantId(TenantIdUtil.getTenantId());
        accountDO.setRegistrationTime(now);
        accountDO.setCreatorId(accountNo);
        accountRepository.save(accountDO);
        return accountDO;
    }

    /**
     * 根据用户类型创建相应的用户信息
     */
    private void createUserByType(UserRegisterDTO registerDTO, String userNo, LocalDateTime now) {
        if (StrUtil.equals(registerDTO.getUserType(), UserTypeEnum.CUSTOMER.getCode())) {
            // 创建客户信息
            createCustomer(userNo, registerDTO.getPhone(), now);
        } else if (StrUtil.equals(registerDTO.getUserType(), UserTypeEnum.SUPPLIER.getCode())) {
            // 创建服务商信息
            createSupplier(registerDTO, userNo, now);
        }
    }

    /**
     * 创建客户信息
     */
    private ProductServiceCustomerDO createCustomer(String userNo, String phone, LocalDateTime now) {
        ProductServiceCustomerDO customerDO = new ProductServiceCustomerDO();
        customerDO.setUserNo(userNo);
        customerDO.setTenantId(TenantIdUtil.getTenantId());
        customerDO.setIsDeleted(false);
        customerDO.setApprovalStatus(-1); // 设置初始状态为 -1（未认证）
        customerDO.setGmtCreated(new Date());
        customerDO.setGmtModified(new Date());
        customerDO.setRegistrationTime(now);
        customerDO.setPhone(phone);
        customerRepository.save(customerDO);
        return customerDO;
    }

    /**
     * 创建服务商信息
     */
    private ProductServiceSupplierDO createSupplier(UserRegisterDTO registerDTO, String userNo, LocalDateTime now) {
        ProductServiceSupplierDO supplierDO = new ProductServiceSupplierDO();
        supplierDO.setUserNo(userNo);
        supplierDO.setPhone(registerDTO.getPhone());
        supplierDO.setCompanyName(registerDTO.getUserName());
        supplierDO.setTenantId(TenantIdUtil.getTenantId());
        supplierDO.setApprovalStatus(-1); // 设置初始状态为 -1（未认证）
        supplierDO.setGmtModified(new Date());
        supplierDO.setGmtCreated(new Date());
        supplierDO.setRegistrationTime(now);
        supplierRepository.save(supplierDO);
        return supplierDO;
    }

    private List<UserMemberDTO> buildUserMemberDTOs(List<DepartmentMemberDO> departmentMembers, List<UserDepartmentDTO> userDepartments) {
        Map<Long, List<DepartmentMemberDO>> departmentMemberMap = departmentMembers.stream()
                .collect(Collectors.groupingBy(DepartmentMemberDO::getDepartmentId));
        Map<Long, List<UserDepartmentDTO>> userDepartmentMap = userDepartments.stream()
                .collect(Collectors.groupingBy(UserDepartmentDTO::getDepartmentId));

        List<UserMemberDTO> userMemberDTOs = Lists.newArrayList();
        departmentMemberMap.forEach((departmentId, members) -> {
            // 获取当前部门的所有用户
            List<UserDepartmentDTO> currentDepartmentUsers = userDepartmentMap.get(departmentId);
            if (CollectionUtils.isEmpty(currentDepartmentUsers)) {
                return;
            }

            for (DepartmentMemberDO member : members) {
                // 根据成员名称过滤出来当前部门对应的账号
                List<UserDepartmentDTO> userDepartmentDTOs = currentDepartmentUsers.stream()
                        .filter(u -> StringUtils.equals(member.getName(), u.getUserName()))
                        .collect(Collectors.toList());

                if (CollectionUtils.isEmpty(userDepartmentDTOs)) {
                    continue;
                }

                UserDepartmentDTO userDepartmentDTO = userDepartmentDTOs.get(0);
                UserMemberDTO userMemberDTO = new UserMemberDTO();
                userMemberDTO.setUserNo(userDepartmentDTO.getUserNo());
                userMemberDTO.setAccountNo(userDepartmentDTO.getAccountNo());
                userMemberDTO.setDepartmentId(userDepartmentDTO.getDepartmentId());
                userMemberDTO.setMemberId(member.getId());
                userMemberDTO.setUserName(userDepartmentDTO.getUserName());
                userMemberDTOs.add(userMemberDTO);
            }
        });
        return userMemberDTOs;
    }

    private List<UserDepartmentDTO> buildUserDepartmentDTO(List<Long> departmentIds) {
        List<UserDepartmentDO> userDepartments = userDepartmentRepository.list(Wrappers.lambdaQuery(UserDepartmentDO.class)
                .in(UserDepartmentDO::getDepartmentId, departmentIds));
        if (CollectionUtils.isEmpty(userDepartments)) {
            return null;
        }

        List<String> userNos = userDepartments.stream()
                .map(UserDepartmentDO::getUserNo)
                .distinct()
                .collect(Collectors.toList());
        List<AccountDO> accounts = accountRepository.list(Wrappers.lambdaQuery(AccountDO.class)
                .in(AccountDO::getUserNo, userNos));
        Map<String, AccountDO> accountMap = accounts.stream()
                .collect(Collectors.toMap(AccountDO::getUserNo, Function.identity()));

        List<UserDepartmentDTO> userDepartmentDTOs = Lists.newArrayList();
        for (UserDepartmentDO userDepartment : userDepartments) {
            AccountDO accountDO = accountMap.get(userDepartment.getUserNo());
            if (accountDO != null) {
                UserDepartmentDTO userDepartmentDTO = new UserDepartmentDTO();
                userDepartmentDTO.setAccountNo(accountDO.getAccountNo());
                userDepartmentDTO.setUserNo(userDepartment.getUserNo());
                userDepartmentDTO.setDepartmentId(userDepartment.getDepartmentId());
                userDepartmentDTO.setUserName(accountDO.getName());
                userDepartmentDTOs.add(userDepartmentDTO);
            }
        }

        return userDepartmentDTOs;
    }

    private UserInfoDTO buildUserInfoDTO(AccountDO accountDO, List<RoleDO> roles, List<PermissionDO> permissions, UserDepartmentDO userDepartment,
                                         UserMemberDO userMember, DepartmentFlatDTO departmentFlatDTO, String departmentName) {
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setUserNo(accountDO.getUserNo());
        userInfoDTO.setAccountNo(accountDO.getAccountNo());
        userInfoDTO.setAccount(accountDO.getAccount());
        userInfoDTO.setName(accountDO.getName());
        userInfoDTO.setPhone(accountDO.getPhone());
        userInfoDTO.setUserType(accountDO.getUserType());
        UserMemberBindDTO userMemberBindDTO = buildUserMemberBindDTO(userMember, accountDO.getName());
        userInfoDTO.setIsInitialized(accountDO.getIsInitialized());
        userInfoDTO.setUserMemberBindDTO(userMemberBindDTO);
        userInfoDTO.setRoles(roleConvert.toDTOList(roles));
        userInfoDTO.setPermissions(permissionConvert.toDTOList(permissions));
        UserDepartmentDTO userDepartmentDTO = userDepartmentConvert.convertToUserDepartmentDTO(userDepartment);
        if (userDepartmentDTO != null) {
            userDepartmentDTO.setDepartmentName(departmentName);
        }

        userInfoDTO.setUserDepartment(userDepartmentDTO);
        userInfoDTO.setIsSuperAdmin(accountDO.getIsSuperAdmin());
        if (departmentFlatDTO == null) {
            userInfoDTO.setDepartmentFlatDTOs(null);
        } else {
            userInfoDTO.setDepartmentFlatDTOs(Lists.newArrayList(departmentFlatDTO));
        }
        userInfoDTO.setStatus(accountDO.getStatus());
        return userInfoDTO;
    }

    private UserMemberBindDTO buildUserMemberBindDTO(UserMemberDO userMember, String name) {
        if (userMember == null) {
            return null;
        }

        DepartmentMemberDO member = departmentMemberRepository.getById(userMember.getMemberId());
        UserMemberBindDTO userMemberBindDTO = new UserMemberBindDTO();
        userMemberBindDTO.setName(name);
        userMemberBindDTO.setMemberId(userMember.getMemberId());
        userMemberBindDTO.setPhone(member.getPhone());
        userMemberBindDTO.setEmail(member.getEmail());
        userMemberBindDTO.setPosition(member.getPosition());
        return userMemberBindDTO;
    }

    private void collectAllInfo(DataScopeTreeNodeDTO treeNode, DataAccessDTO dataAccessDTO, String userNo) {
        // 初始化结果列表
        Set<String> allMembers = new HashSet<>();
        Set<Long> allDepartments = new HashSet<>();
        // 调用递归方法收集成员
        collectMembersRecursively(treeNode, allMembers, allDepartments, userNo);

        dataAccessDTO.setSupportQueryUserNos(Lists.newArrayList(allMembers));
        dataAccessDTO.setSupportQueryDepartmentIds(Lists.newArrayList(allDepartments));
    }

    private void collectMembersRecursively(DataScopeTreeNodeDTO treeNode, Set<String> allMembers, Set<Long> allDepartments, String userNo) {
        if (treeNode == null) {
            return;
        }

        // 添加当前部门的成员
        if (CollectionUtils.isNotEmpty(treeNode.getMemberUsers())) {
            allMembers.addAll(treeNode.getMemberUsers().stream()
                    .filter(m -> treeNode.isCanViewMembers())
                    .filter(m -> BooleanUtils.isTrue(m.getIsRelatedAccount()))
                    .map(DataScopeMemberUserDTO::getUserNo)
                    .collect(Collectors.toList()));
        }

        if (treeNode.isCanViewMembers()) {
            allDepartments.add(treeNode.getDepartmentId());
        }

        // 遍历子部门，递归收集成员
        if (treeNode.getChildren() != null) {
            for (DataScopeTreeNodeDTO child : treeNode.getChildren()) {
                collectMembersRecursively(child, allMembers, allDepartments, userNo);
            }
        }
    }

    private DataScopeTreeNodeDTO convertToTreeNode(DepartmentDTO departmentDTO) {
        if (departmentDTO == null) {
            return null;
        }

        // 创建 DataScopeTreeNode
        DataScopeTreeNodeDTO treeNode = new DataScopeTreeNodeDTO();
        treeNode.setDepartmentId(departmentDTO.getId());
        treeNode.setDepartmentName(departmentDTO.getName());

        // 转换部门成员为 DataScopeMemberUser
        List<DataScopeMemberUserDTO> memberUsers = departmentDTO.getMembers() == null
                ? null
                : departmentDTO.getMembers().stream()
                .map(this::convertToMemberUser)
                .collect(Collectors.toList());
        treeNode.setMemberUsers(memberUsers);

        // 递归转换子部门
        List<DataScopeTreeNodeDTO> children = departmentDTO.getChildren() == null
                ? null
                : departmentDTO.getChildren().stream()
                .map(this::convertToTreeNode)
                .collect(Collectors.toList());
        treeNode.setChildren(children);

        return treeNode;
    }

    private DataScopeMemberUserDTO convertToMemberUser(DepartmentMemberDTO member) {
        DataScopeMemberUserDTO user = new DataScopeMemberUserDTO();
        user.setMemberId(member.getId());
        user.setName(member.getName());
        user.setType(MemberTypeEnums.getByCode(member.getType()));
        user.setIsRelatedAccount(member.getIsRelatedAccount());
        user.setUserNo(member.getRelatedUserNo());
        return user;
    }

    private List<DataScopeTreeNodeDTO> filterAndPruneRelatedMember(List<DataScopeTreeNodeDTO> treeNodes, String userNo) {
        if (CollectionUtils.isEmpty(treeNodes)) {
            return Collections.emptyList();
        }

        Set<DataScopeTreeNodeDTO> visited = new HashSet<>();
        return treeNodes.stream()
                .map(d -> doFilterAndPruneRelatedMember(d, userNo, visited))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
    }

    private List<DataScopeTreeNodeDTO> doFilterAndPruneRelatedMember(DataScopeTreeNodeDTO treeNode, String userNo, Set<DataScopeTreeNodeDTO> visited) {
        if (treeNode == null || visited.contains(treeNode)) {
            return Collections.emptyList();
        }

        visited.add(treeNode);

        // 这里成员和账号一定是一一对应的
        List<DataScopeMemberUserDTO> members = treeNode.getMemberUsers().stream()
                .filter(e -> StrUtil.equals(e.getUserNo(), userNo))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(members)) {
            LogUtil.error("用户没有关联的成员信息，数据不完整，需要及时修复，用户编号：{}", userNo);
            throw new BizException(BizErrorCode.ACCOUNT_NOT_FOUND.getCode(), BizErrorCode.ACCOUNT_NOT_FOUND.getDesc());
        }

        DataScopeMemberUserDTO memberUser = members.get(0);
        // 只有部门负责人才能有权限看组织树内的数据
        if (memberUser.isResponsiblePerson()) {
            treeNode.viewRecursively();
        }

        return Collections.singletonList(treeNode);
    }
}
