package com.deepinnet.infra.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.api.enums.*;
import com.deepinnet.infra.dal.dataobject.FileAttachmentDO;
import com.deepinnet.infra.dal.repository.FileAttachmentRepository;
import com.deepinnet.infra.service.*;
import com.deepinnet.infra.service.error.BizErrorCode;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.nio.file.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件附件服务实现类
 *
 * <AUTHOR> wong
 * @create 2025/04/19
 */
@Service
@Profile("!qz-gov-inner-network")
public class FileAttachmentServiceImpl implements FileAttachmentService {

    private static final List<String> LOCAL_STORAGE_ENV = Lists.newArrayList("sz-gov-inner", "qz-gov-inner-network", "dp-gov-cloud");

    @Resource
    private FileAttachmentRepository fileAttachmentRepository;

    @Resource
    private GlobalStoreService globalStoreService;

    @Resource
    private Environment environment;

    @Value("${file.attachment.dir}")
    private String fileUploadDir;

    @Value("${file.attachment.url}")
    private String fileUrl;

    @Override
    public Result<FileUploadResultDTO> uploadFile(MultipartFile file, String userNo, String bizType, String bizNo) {
        if (file == null || file.isEmpty()) {
            LogUtil.error("文件上传失败，文件为空");
            return Result.fail(BizErrorCode.ILLEGAL_PARAMS.getCode(), "文件不能为空");
        }

        try {
            // 调用保存文件的方法
            FileUploadResultDTO resultDTO = saveFile(file, userNo, bizType, bizNo);
            return Result.success(resultDTO);
        } catch (Exception e) {
            LogUtil.error("文件上传异常，异常信息：{}", e.getMessage(), e);
            return Result.fail(BizErrorCode.FILE_UPLOAD_ERROR.getCode(), BizErrorCode.FILE_UPLOAD_ERROR.getDesc());
        }
    }

    @Override
    public Result<FileUploadResultDTO> uploadFileWithEnv(MultipartFile file, String userNo, String bizType, String bizNo) {
        if (file == null || file.isEmpty()) {
            LogUtil.error("文件上传失败，文件为空");
            return Result.fail(BizErrorCode.ILLEGAL_PARAMS.getCode(), "文件不能为空");
        }

        try {
            // 非内网环境走oss
            if (!LOCAL_STORAGE_ENV.contains(environment.getActiveProfiles()[0])) {
                // 上传到oss
                String url = uploadToOss(file);
                FileUploadResultDTO resultDTO = new FileUploadResultDTO();
                resultDTO.setOriginalFileName(file.getOriginalFilename());
                resultDTO.setFilePath(url);
                resultDTO.setFileSize(file.getSize());
                resultDTO.setFileType(file.getContentType());

                // 文件信息记录到数据库
                if (StrUtil.isNotBlank(bizNo) && StrUtil.isNotBlank(bizType)) {
                    FileAttachmentDO fileAttachmentDO = buildFileAttachment(userNo, bizType, bizNo, url, file.getOriginalFilename(), url);
                    fileAttachmentRepository.save(fileAttachmentDO);
                }
                return Result.success(resultDTO);
            }

            // 内网环境保存到本地磁盘
            if (StrUtil.isNotBlank(bizNo) && StrUtil.isNotBlank(bizType)) {
                FileUploadResultDTO resultDTO = saveFile(file, userNo, bizType, bizNo);
                return Result.success(resultDTO);
            }

            return null;
        } catch (Exception e) {
            LogUtil.error("文件上传失败：", e);
            throw new BizException(BizErrorCode.FILE_UPLOAD_ERROR.getCode(), BizErrorCode.FILE_UPLOAD_ERROR.getDesc());
        }
    }

    private String uploadToOss(MultipartFile file) throws IOException {
        String fileName = UUID.randomUUID().toString().replace("-", "") + "." + FilenameUtils.getExtension(file.getOriginalFilename());
        StoreUploadDTO storeUploadDTO = new StoreUploadDTO();
        storeUploadDTO.setBucketType(StoreBucketTypeEnum.PUBLIC_BUCKET.getBucketType());
        storeUploadDTO.setBizType(StoreBizTypeEnum.OSS_STORE.getBizType());
        storeUploadDTO.setBucketName("deepinnet-dev");
        storeUploadDTO.setKey(fileName);
        storeUploadDTO.setInputStream(file.getInputStream().readAllBytes());
        globalStoreService.putObject(storeUploadDTO);

        StoreQueryDTO queryDTO = new StoreQueryDTO();
        queryDTO.setBucketType(StoreBucketTypeEnum.PUBLIC_BUCKET.getBucketType());
        queryDTO.setBucketName("deepinnet-dev");
        queryDTO.setKey(storeUploadDTO.getKey());
        Result<String> objectUrl = globalStoreService.getObjectUrl(queryDTO);

        if (!objectUrl.isSuccess()) {
            LogUtil.error("上传文件到oss失败，失败原因：{}", objectUrl.getErrorDesc());
            throw new BizException(objectUrl.getErrorCode(), objectUrl.getErrorDesc());
        }

        return objectUrl.getData();
    }

    @Override
    public Result<List<FileUploadResultDTO>> batchUploadFiles(MultipartFile[] files, String userNo, String bizType, String bizNo) {
        if (files == null || files.length == 0) {
            LogUtil.error("批量文件上传失败，文件列表为空");
            return Result.fail(BizErrorCode.ILLEGAL_PARAMS.getCode(), "文件列表不能为空");
        }

        List<FileUploadResultDTO> resultList = new ArrayList<>();
        try {
            for (MultipartFile file : files) {
                if (file != null && !file.isEmpty()) {
                    FileUploadResultDTO resultDTO = saveFile(file, userNo, bizType, bizNo);
                    resultList.add(resultDTO);
                }
            }
            return Result.success(resultList);
        } catch (Exception e) {
            LogUtil.error("批量文件上传异常，异常信息：{}", e.getMessage(), e);
            return Result.fail(BizErrorCode.UNKNOWN_EXCEPTION.getCode(), "批量文件上传失败");
        }
    }

    @Override
    public ResponseEntity<org.springframework.core.io.Resource> getFileResource(String filePath) {
        if (StringUtils.isBlank(filePath)) {
            LogUtil.error("获取文件资源失败，文件路径为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "文件路径不能为空");
        }

        try {
            // 创建文件资源
            Path path = Paths.get(filePath);
            File file = path.toFile();

            // 检查文件是否存在
            if (!file.exists() || !file.isFile()) {
                LogUtil.error("获取文件资源失败，文件不存在，路径：[{}]", filePath);
                throw new BizException(BizErrorCode.FILE_NOT_FOUND.getCode(), "文件不存在");
            }

            // 创建文件资源
            FileSystemResource resource = new FileSystemResource(file);

            // 获取文件MIME类型
            String contentType = Files.probeContentType(path);
            MediaType mediaType = MediaType.APPLICATION_OCTET_STREAM;
            if (StringUtils.isNotBlank(contentType)) {
                try {
                    mediaType = MediaType.parseMediaType(contentType);
                } catch (Exception e) {
                    LogUtil.warn("解析文件类型异常，使用默认类型：{}", e.getMessage());
                }
            }

            // 获取文件名
            String fileName = file.getName();

            // 构建响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"");

            // 返回资源响应
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(file.length())
                    .contentType(mediaType)
                    .body(resource);
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            LogUtil.error("获取文件资源异常，文件路径：[{}]，异常信息：{}", filePath, e.getMessage(), e);
            throw new BizException(BizErrorCode.FILE_NOT_FOUND.getCode(), "文件访问失败");
        }
    }

    @Override
    public ResponseEntity<org.springframework.core.io.Resource> getFileResourceWithAuth(String filePath, String bizNo) {
        if (StringUtils.isBlank(filePath) || StringUtils.isBlank(bizNo)) {
            LogUtil.error("获取文件资源失败，文件路径或业务编号为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "文件路径或业务编号不能为空");
        }

        // 查询文件记录校验权限
        LambdaQueryWrapper<FileAttachmentDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileAttachmentDO::getFilePath, filePath);
        queryWrapper.eq(FileAttachmentDO::getBizNo, bizNo);
        FileAttachmentDO fileAttachment = fileAttachmentRepository.getOne(queryWrapper);

        if (fileAttachment == null) {
            LogUtil.error("获取文件资源失败，文件记录不存在或无权访问，路径：[{}]，业务编号：[{}]", filePath, bizNo);
            throw new BizException(BizErrorCode.PERMISSION_DENIED.getCode(), "无权访问该文件");
        }

        // 权限验证通过，获取文件资源
        return getFileResource(filePath);
    }

    @Override
    public Result<FileAttachmentDTO> getFileAttachment(Long id) {
        if(id == null) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "id 不可为空");
        }

        FileAttachmentDO fileAttachment = fileAttachmentRepository.getById(id);
        if (fileAttachment == null) {
            LogUtil.error("获取文件资源失败，文件记录不存在或无权访问，id：[{}]", id);
            throw new BizException(BizErrorCode.PERMISSION_DENIED.getCode(), "无权访问该文件");
        }

        FileAttachmentDTO res = new FileAttachmentDTO();
        BeanUtils.copyProperties(fileAttachment, res);
        return Result.success(res);
    }

    @Override
    public Result<List<FileAttachmentDTO>> queryFileAttachmentList(FileAttachmentQueryDTO queryDTO) {
        if(CollectionUtils.isEmpty(queryDTO.getIdList())) {
            return Result.success(List.of());
        }

        LambdaQueryWrapper<FileAttachmentDO> queryWrapper = Wrappers.<FileAttachmentDO>lambdaQuery()
                .in(FileAttachmentDO::getId, queryDTO.getIdList());

        List<FileAttachmentDO> list = fileAttachmentRepository.list(queryWrapper);

        return Result.success(CollectionUtils.emptyIfNull(list).stream()
                .map(f -> {
                    FileAttachmentDTO res = new FileAttachmentDTO();
                    BeanUtils.copyProperties(f, res);
                    return res;
                }).collect(Collectors.toList()));
    }

    /**
     * 保存文件到本地并记录到数据库
     *
     * @param file    文件
     * @param userNo  用户编号
     * @param bizType 业务类型
     * @param bizNo   业务编号
     * @return 文件上传结果
     * @throws IOException IO异常
     */
    private FileUploadResultDTO saveFile(MultipartFile file, String userNo, String bizType, String bizNo) throws IOException {
        // 1. 生成文件存储路径
        String originalFilename = file.getOriginalFilename();
        String extension = FilenameUtils.getExtension(originalFilename);
        String fileName = UUID.randomUUID().toString().replace("-", "") + (StringUtils.isNotBlank(extension) ? "." + extension : "");

        // 按日期组织子目录
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String dateDir = dateFormat.format(new Date());

        // 构建目录路径
        String dirPath = fileUploadDir + File.separator + dateDir;
        Path directory = Paths.get(dirPath);

        // 如果目录不存在，则创建
        if (!Files.exists(directory)) {
            Files.createDirectories(directory);
        }

        // 完整的文件路径
        String filePath = dirPath + File.separator + fileName;
        Path path = Paths.get(filePath);

        // 2. 将文件保存到指定路径
        Files.copy(file.getInputStream(), path);

        String fileAttUrl = fileUrl + dateDir + "/" + fileName;
        FileAttachmentDO fileAttachmentDO = null;
        // 3. 文件信息记录到数据库：传入了bizNo和bizType的才需要持久化到fileAttachment表
        if (StrUtil.isNotBlank(bizNo) && StrUtil.isNotBlank(bizType)) {
            fileAttachmentDO = buildFileAttachment(userNo, bizType, bizNo, filePath, originalFilename, fileAttUrl);
            fileAttachmentRepository.save(fileAttachmentDO);
        }

        // 4. 构建返回结果
        return FileUploadResultDTO.builder()
                .id(Optional.ofNullable(fileAttachmentDO).map(FileAttachmentDO::getId).orElse(null))
                .originalFileName(originalFilename)
                .filePath(fileAttUrl)
                .fileSize(file.getSize())
                .fileType(file.getContentType())
                .build();
    }

    private FileAttachmentDO buildFileAttachment(String userNo, String bizType, String bizNo, String filePath,
                                                 String originalFilename, String fileUrl) {
        FileAttachmentDO fileAttachmentDO = new FileAttachmentDO();
        fileAttachmentDO.setUserNo(userNo);
        fileAttachmentDO.setFilePath(filePath);
        fileAttachmentDO.setBizType(bizType);
        fileAttachmentDO.setBizNo(bizNo);
        fileAttachmentDO.setOriginalFilename(originalFilename);
        fileAttachmentDO.setFileUrl(fileUrl);
        return fileAttachmentDO;
    }
} 