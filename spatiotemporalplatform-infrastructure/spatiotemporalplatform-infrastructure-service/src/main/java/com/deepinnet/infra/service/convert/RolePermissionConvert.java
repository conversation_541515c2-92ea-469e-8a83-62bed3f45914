package com.deepinnet.infra.service.convert;

import com.deepinnet.infra.api.dto.RolePermissionDTO;
import com.deepinnet.infra.dal.dataobject.RolePermissionDO;
import org.mapstruct.*;

import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2024/11/25 17:38
 * @Description
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface RolePermissionConvert {
    RolePermissionDTO toDTO(RolePermissionDO rolePermissionDO);

    List<RolePermissionDTO> toDTOList(List<RolePermissionDO> roleDOs);
}
