package com.deepinnet.infra.service.enums;

/**
 * 操作类型枚举
 * 
 * <AUTHOR> wong
 * @create 2025/7/31
 */
public enum OperationTypeEnum {

    // 基础操作
    CREATE("新增"),
    UPDATE("编辑"),
    DELETE("删除"),
    QUERY("查询"),
    EXPORT("导出"),
    IMPORT("导入"),
    
    // 用户相关
    LOGIN("登录"),
    LOGOUT("退出登录"),
    CHANGE_PASSWORD("修改密码"),
    RESET_PASSWORD("重置密码"),
    
    // 审批相关
    SUBMIT("提交"),
    APPROVE("审批通过"),
    REJECT("审批拒绝"),
    WITHDRAW("撤回"),
    
    // 状态变更
    ENABLE("启用"),
    DISABLE("禁用"),
    PUBLISH("发布"),
    UNPUBLISH("取消发布"),
    
    // 权限相关
    GRANT("授权"),
    REVOKE("撤销权限"),
    
    // 其他
    COPY("复制"),
    MOVE("移动"),
    SYNC("同步"),
    BACKUP("备份"),
    RESTORE("恢复");

    private final String description;

    OperationTypeEnum(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public String getCode() {
        return this.name();
    }
}
