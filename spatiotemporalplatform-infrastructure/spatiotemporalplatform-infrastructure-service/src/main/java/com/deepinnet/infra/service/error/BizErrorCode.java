package com.deepinnet.infra.service.error;

import com.deepinnet.digitaltwin.common.error.*;
import lombok.*;

/**
 * <AUTHOR> wong
 * @create 2024/8/8 16:20
 * @Description
 */
@Getter
@AllArgsConstructor
public enum BizErrorCode {
    /*------------------------------------------------------------------------*/
    /*                        通用事件[0000开头]                                  */
    /*------------------------------------------------------------------------*/

    /**
     * 未知异常
     */
    UNKNOWN_EXCEPTION(ErrorLevel.ERROR, ErrorType.SYSTEM, "000", "未知异常"),

    /**
     * 请求参数非法
     */
    ILLEGAL_PARAMS(ErrorLevel.ERROR, ErrorType.BIZ, "001", "参数错误"),


    /*------------------------------------------------------------------------*/
    /*                        登录权限[10开头]                                  */
    /*------------------------------------------------------------------------*/
    ACCOUNT_NOT_FOUND(ErrorLevel.ERROR, ErrorType.BIZ, "101", "账户不存在"),

    INVALID_PASSWORD(ErrorLevel.ERROR, ErrorType.BIZ, "102", "密码错误"),

    NO_ACCESS(ErrorLevel.ERROR, ErrorType.BIZ, "103", "没有访问权限"),

    LENGTH_EXCEEDS_LIMIT(ErrorLevel.ERROR, ErrorType.BIZ, "104", "长度超过限制"),

    ORGANIZATION_NAME_ALREADY_EXIST(ErrorLevel.ERROR, ErrorType.BIZ, "105", "部门名称重复"),

    ORGANIZATION_NOT_EXIST(ErrorLevel.ERROR, ErrorType.BIZ, "106", "当前组织不存在"),

    EXIST_ORG_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "107", "当前账号已经存在组织，无法继续创建"),

    PERMISSION_DENIED(ErrorLevel.ERROR, ErrorType.BIZ, "108", "当前账号没有权限操作"),

    ROLE_NOT_FOUND(ErrorLevel.ERROR, ErrorType.BIZ, "109", "角色不存在"),

    ACCOUNT_NOT_LOGIN(ErrorLevel.ERROR, ErrorType.BIZ, "110", "当前账号未登录，请先登录"),

    ROLE_NAME_EXIST(ErrorLevel.ERROR, ErrorType.BIZ, "111", "角色名称已经存在"),

    FOUND_VALID_ACCOUNT(ErrorLevel.ERROR, ErrorType.BIZ, "112", "当前角色已经绑定了账号，无法删除"),

    ACCOUNT_LOGIN_EXPIRE(ErrorLevel.ERROR, ErrorType.BIZ, "119", "当前账号登录已经失效，请重新登录"),

    NOT_PERMISSION_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "120", "当前用户没有操作权限"),

    DUPLICATE_PHONE_NUMBER(ErrorLevel.ERROR, ErrorType.BIZ, "121", "账号不能重复"),

    ACCOUNT_OR_PASSWORD_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "122", "账号或密码错误，请重试"),

    DEPARTMENT_EXIST_MEMBER(ErrorLevel.ERROR, ErrorType.BIZ, "123", "组织下有绑定的账号，请解绑后再删除!"),

    CURRENT_MEMBER_ALREADY_EXIST(ErrorLevel.ERROR, ErrorType.BIZ, "124", "当前成员已存在该组织中，无需重复添加"),

    DEPARTMENT_NOT_FOUND(ErrorLevel.ERROR, ErrorType.BIZ, "125", "组织不存在"),

    MEMBER_PHONE_EXIST(ErrorLevel.ERROR, ErrorType.BIZ, "126", "成员手机号已经存在"),

    DUPLICATE_ROOT_ORGANIZATION(ErrorLevel.ERROR, ErrorType.BIZ, "127", "组织只能创建一个，请勿重复创建"),

    DUPLICATE_MEMBER_NAME(ErrorLevel.ERROR, ErrorType.BIZ, "128", "成员名称不能重复"),

    DUPLICATE_ACCOUNT_NAME(ErrorLevel.ERROR, ErrorType.BIZ, "129", "姓名不能重复"),

    OVER_MAX_DEPARTMENT_LEVEL(ErrorLevel.ERROR, ErrorType.BIZ, "130", "部门最多只能有5层"),

    SEND_SMS_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "1110", "短信发送失败"),

    PASSWORD_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "1111", "密码错误，请重新输入！"),

    BIND_MEMBER_NOT_FOUND(ErrorLevel.ERROR, ErrorType.BIZ, "1112", "绑定成员不存在，请选择其他成员"),

    ACCOUNT_NOT_BIND_MEMBER(ErrorLevel.ERROR, ErrorType.BIZ, "1113", "账号没有绑定的成员，请及时处理"),

    ACCOUNT_NOT_BIND_DEPARTMENT(ErrorLevel.ERROR, ErrorType.BIZ, "1114", "账号没有绑定的成员，请及时处理"),

    MEMBER_EMAIL_EXIST(ErrorLevel.ERROR, ErrorType.BIZ, "1115", "成员邮箱已被绑定，请重新设置其他邮箱"),

    USER_ROLE_NOT_EXIST(ErrorLevel.ERROR, ErrorType.BIZ, "1116", "用户没有关联角色"),

    USER_MEMBER_NOT_EXIST(ErrorLevel.ERROR, ErrorType.BIZ, "1117", "用户关联成员不存在"),

    DEPARTMENT_MEMBER_NOT_FOUND(ErrorLevel.ERROR, ErrorType.BIZ, "1118", "部门成员不存在"),


    /**
     * 验证码不存在
     */
    VERIFICATION_CODE_NOT_FOUND(ErrorLevel.ERROR, ErrorType.BIZ, "1110", "验证码不存在"),

    /**
     * 验证码已过期
     */
    VERIFICATION_CODE_EXPIRED(ErrorLevel.ERROR, ErrorType.BIZ, "1111", "验证码已过期"),

    /**
     * 验证码不正确
     */
    VERIFICATION_CODE_INCORRECT(ErrorLevel.ERROR, ErrorType.BIZ, "1112", "验证码不正确"),

    /**
     * 验证码发送失败
     */
    VERIFICATION_CODE_SEND_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "1113", "验证码发送失败"),

    VERIFY_CODE_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "1114", "验证码错误，请重新输入！"),

    NEED_LATEST_CODE(ErrorLevel.ERROR, ErrorType.BIZ, "1115", "当前验证码不是最新的，请使用最新的验证码"),
    VERIFICATION_CODE_IS_USED(ErrorLevel.ERROR, ErrorType.BIZ, "1116", "验证码已被使用，请重新获取"),

    // 用户注册相关错误码
    USER_ALREADY_EXISTS(ErrorLevel.ERROR, ErrorType.BIZ, "101", "该号码已注册，可直接登录系统！"),
    REGISTER_FAILED(ErrorLevel.ERROR, ErrorType.BIZ, "102", "注册失败"),
    INVALID_USER_TYPE(ErrorLevel.ERROR, ErrorType.BIZ, "103", "无效的用户类型"),
    USER_NOT_EXIST(ErrorLevel.ERROR, ErrorType.BIZ, "104", "该手机号码未注册！"),
    RESET_TOKEN_NOT_FOUND(ErrorLevel.ERROR, ErrorType.BIZ, "105", "重置token不存在"),
    RESET_TOKEN_INVALID(ErrorLevel.ERROR, ErrorType.BIZ, "106", "重置token不合法"),
    PHONE_NOT_FOUND(ErrorLevel.ERROR, ErrorType.BIZ, "107", "该手机号码未注册！"),
    UNIT_NAME_ALREADY_EXISTS(ErrorLevel.ERROR, ErrorType.BIZ, "108", "单位名称已存在"),

    // 文件相关错误码
    FILE_UPLOAD_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "2001", "文件上传失败"),
    FILE_NOT_FOUND(ErrorLevel.ERROR, ErrorType.BIZ, "2002", "文件不存在"),
    FILE_SIZE_EXCEEDED(ErrorLevel.ERROR, ErrorType.BIZ, "2003", "文件大小超过限制"),
    FILE_TYPE_NOT_ALLOWED(ErrorLevel.ERROR, ErrorType.BIZ, "2004", "文件类型不允许"),

    /**
     * 文件存储错误
     */
    STORE_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "3001", "store_error"),

    /**
     * 文件查询异常
     */
    FILE_QUERY_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "3003", "file_query_error"),

    /**
     * 文件超过限制
     */
    FILE_UPLOAD_SIZE_LIMITED(ErrorLevel.ERROR, ErrorType.BIZ, "3004", "size_limit_error"),

    // 审核相关错误码
    APPROVAL_NOT_FOUND(ErrorLevel.ERROR, ErrorType.BIZ, "4001", "审核记录不存在"),
    APPROVAL_STATUS_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "4002", "审核状态错误"),
    APPROVAL_ALREADY_PROCESSED(ErrorLevel.ERROR, ErrorType.BIZ, "4003", "审核记录已处理，不能重复审核"),

    // 账号冻结相关错误码
    ACCOUNT_FROZEN(ErrorLevel.ERROR, ErrorType.BIZ, "5001", "账号已被冻结，请联系管理员"),
    ACCOUNT_ALREADY_FROZEN(ErrorLevel.ERROR, ErrorType.BIZ, "5002", "账号已处于冻结状态"),
    ACCOUNT_ALREADY_ACTIVE(ErrorLevel.ERROR, ErrorType.BIZ, "5003", "账号已处于正常状态"),
    MEMBER_BIND_ACCOUNT(ErrorLevel.ERROR, ErrorType.BIZ, "5004", "成员已经绑定账号，无法删除，请先删除账号"),
    DELETE_SELF_ACCOUNT_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "5005", "无法删除自己的账号"),

    // 数据权限
    NO_PERMISSION_TO_OPERATE(ErrorLevel.ERROR, ErrorType.BIZ, "6001", "无权限操作"),

    USER_DEPARTMENT_NOT_FOUND(ErrorLevel.ERROR, ErrorType.BIZ, "6002", "用户关联的部门不存在");


    /**
     * 错误级别
     */
    private ErrorLevel errorLevel;

    /**
     * 错误类型
     */
    private ErrorType errorType;

    /**
     * 错误编码
     */
    private String code;

    /**
     * 错误描述
     */
    private String desc;
}
