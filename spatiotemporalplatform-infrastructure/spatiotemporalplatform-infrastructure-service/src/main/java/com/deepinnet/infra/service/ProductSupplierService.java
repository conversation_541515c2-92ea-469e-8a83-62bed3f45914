package com.deepinnet.infra.service;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.api.vo.SupplierInfoVO;

/**
 * 服务商信息服务接口
 *
 * <AUTHOR> wong
 * @create 2025/04/21
 */
public interface ProductSupplierService {

    /**
     * 保存服务商信息
     *
     * @param supplierInfoDTO 服务商信息
     * @return 处理结果
     */
    String saveSupplierInfo(SupplierInfoDTO supplierInfoDTO);

    /**
     * 更新服务商信息
     *
     * @param supplierInfoDTO 服务商信息
     * @return 处理结果
     */
    Boolean updateSupplierInfo(SupplierInfoDTO supplierInfoDTO);

    /**
     * 获取服务商详情
     *
     * @return 服务商详情
     */
    SupplierDetailDTO getSupplierDetail(SupplierQueryDetailDTO queryDetailDTO);


    /**
     * 分页查询服务商列表
     *
     * @param queryDTO 查询参数
     * @return 服务商列表分页数据
     */
    Result<CommonPage<SupplierInfoVO>> pageQuerySuppliers(SupplierQueryDTO queryDTO);
} 