package com.deepinnet.infra.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.*;
import cn.hutool.json.*;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.infra.api.constants.DataScopeConstants;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.api.enums.UserTypeEnum;
import com.deepinnet.infra.dal.dataobject.*;
import com.deepinnet.infra.dal.mapper.PermissionMapper;
import com.deepinnet.infra.dal.repository.*;
import com.deepinnet.infra.service.*;
import com.deepinnet.infra.service.constants.*;
import com.deepinnet.infra.service.convert.*;
import com.deepinnet.infra.service.enums.PermissionSystemEnum;
import com.deepinnet.infra.service.error.BizErrorCode;
import com.deepinnet.infra.service.log.*;
import com.deepinnet.infra.service.log.annotation.OperationLog;
import com.deepinnet.infra.service.util.*;
import com.github.pagehelper.*;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wong
 * @create 2024/8/8 16:10
 * @Description
 */
@Service
public class RolePermissionServiceImpl implements RolePermissionService {

    @Resource
    private PermissionMapper permissionMapper;

    @Resource
    private RoleRepository roleRepository;

    @Resource
    private AccountRepository accountRepository;

    @Resource
    private RolePermissionRepository rolePermissionRepository;

    @Resource
    private DepartmentService departmentService;

    @Resource
    private UserRoleRepository userRoleRepository;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private RoleConvert roleConvert;

    @Resource
    private RolePermissionConvert rolePermissionConvert;

    @Resource
    private DataPermissionProcessor dataPermissionProcessor;

    @Resource
    private UserDepartmentRepository userDepartmentRepository;

    @Resource
    private DepartmentRepository departmentRepository;

    @Override
    public CommonPage<RoleDTO> pageQueryRoleDTO(RoleQueryDTO roleQueryDTO) {
        List<String> needQueryCreatorIds = null;
        PermissionSystemEnum permissionSystemEnum = dataPermissionProcessor.routeToPermissionSystem();

        if (permissionSystemEnum == PermissionSystemEnum.NEED_DATA_PERMISSION) {
            DataAccessDTO availableQueryData = dataPermissionProcessor.getAvailableQueryData();
            if (availableQueryData == null || CollUtil.isEmpty(availableQueryData.getSupportQueryUserNos())) {
                return CommonPage.buildEmptyPage();
            }

            // 不是超管（不能看到所有数据）
            if (!availableQueryData.getSupportQueryUserNos().contains(DataScopeConstants.SUPER_ADMIN)) {
                List<String> needQueryUserNos = availableQueryData.getSupportQueryUserNos();
                needQueryCreatorIds = getAccountNoList(needQueryUserNos);
            }
        }

        String scene = null;
        if (ObjectUtil.isNotEmpty(StpUtil.getExtra("scene"))) {
            scene = (String) StpUtil.getExtra("scene");
        }

        Page<Object> page = PageHelper.startPage(roleQueryDTO.getPageNum(), roleQueryDTO.getPageSize());

        List<RoleDO> roleList = roleRepository.list(Wrappers.lambdaQuery(RoleDO.class)
                .like(StringUtils.hasText(roleQueryDTO.getRoleName()), RoleDO::getRoleName, roleQueryDTO.getRoleName())
                .like(StringUtils.hasText(roleQueryDTO.getDescription()), RoleDO::getRoleDesc, roleQueryDTO.getDescription())
                .in(CollUtil.isNotEmpty(needQueryCreatorIds), RoleDO::getCreatorId, needQueryCreatorIds)
                .eq(StrUtil.isNotBlank(scene), RoleDO::getScene, scene)
                .eq(RoleDO::getVisible, true)
                .orderByDesc(RoleDO::getId));
        if (CollectionUtils.isEmpty(roleList)) {
            return CommonPage.buildEmptyPage();
        }

        List<RoleDTO> roleDTOList = roleConvert.toRoleDTOList(roleList);
        PageInfo<RoleDTO> pageInfo = PageInfo.of(roleDTOList);
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPages(page.getPages());

        List<String> creatorIds = roleDTOList.stream()
                .map(RoleDTO::getCreatorId)
                .distinct()
                .collect(Collectors.toList());
        List<AccountDO> accountDOs = accountRepository.list(Wrappers.lambdaQuery(AccountDO.class)
                .in(AccountDO::getAccountNo, creatorIds));
        if (CollectionUtils.isNotEmpty(accountDOs)) {
            Map<String, AccountDO> accountMap = accountDOs.stream()
                    .collect(Collectors.toMap(AccountDO::getAccountNo, Function.identity()));

            roleDTOList.forEach(roleDTO -> {
                AccountDO accountDO = accountMap.get(roleDTO.getCreatorId());
                if (accountDO != null) {
                    roleDTO.setCreatorName(accountDO.getName());
                }
            });
        }

        List<RoleDTO> sortedRoleDTOs = roleDTOList.stream()
                .sorted(Comparator.comparing(RoleDTO::getCreateTime).reversed())
                .collect(Collectors.toList());

        return CommonPage.buildPage(roleQueryDTO.getPageNum(), roleQueryDTO.getPageSize(), pageInfo.getPages(), pageInfo.getTotal(), sortedRoleDTOs);
    }

    @Override
    @OperationLog
    public Boolean saveOrUpdateRole(RoleSaveDTO saveDTO) {
        // 数据权限校验
        if (saveDTO.getId() == null) {
            dataPermissionProcessor.checkSaveRoleDataPermission();
        } else {
            dataPermissionProcessor.checkUpdateOrDeleteRoleDataPermission(saveDTO.getId());
        }

        String scene = null;
        if (ObjectUtil.isNotEmpty(StpUtil.getExtra("scene"))) {
            scene = (String) StpUtil.getExtra("scene");
        }

        String roleName = saveDTO.getRoleName();
        List<RoleDO> roleList = roleRepository.list(Wrappers.<RoleDO>lambdaQuery()
                .eq(RoleDO::getRoleName, roleName)
                .eq(StrUtil.isNotBlank(scene), RoleDO::getScene, scene));

        // 新增
        if (saveDTO.getId() == null) {
            if (CollectionUtils.isNotEmpty(roleList)) {
                throw new BizException(BizErrorCode.ROLE_NAME_EXIST.getCode(), BizErrorCode.ROLE_NAME_EXIST.getDesc());
            }

            String roleCode = BizNoGenerateUtil.generateRoleCode();
            RoleDO roleDO = buildRoleDO(saveDTO, roleCode, scene);
            List<RolePermissionDO> rolePermissionDOList = buildRolePermissionDO(saveDTO, roleCode);
            transactionTemplate.executeWithoutResult(action -> {
                roleRepository.save(roleDO);
                rolePermissionRepository.saveBatch(rolePermissionDOList);
            });

            generateAddRoleLog(roleName);

        } else {
            if (CollectionUtils.isNotEmpty(roleList) && roleList.size() > 1) {
                throw new BizException(BizErrorCode.ROLE_NAME_EXIST.getCode(), BizErrorCode.ROLE_NAME_EXIST.getDesc());
            }

            if (CollectionUtils.isNotEmpty(roleList)) {
                RoleDO existRole = roleList.get(0);
                if (!Objects.equals(existRole.getId(), saveDTO.getId())) {
                    throw new BizException(BizErrorCode.ROLE_NAME_EXIST.getCode(), BizErrorCode.ROLE_NAME_EXIST.getDesc());
                }
            }

            RoleDO roleDO = roleRepository.getById(saveDTO.getId());
            if (roleDO == null) {
                throw new BizException(BizErrorCode.ROLE_NOT_FOUND.getCode(), BizErrorCode.ROLE_NOT_FOUND.getDesc());
            }

            roleDO.setRoleName(saveDTO.getRoleName());
            roleDO.setRoleDesc(saveDTO.getDesc());
            roleDO.setDataScope(saveDTO.getDataScope());
            transactionTemplate.executeWithoutResult(action -> {
                roleRepository.updateById(roleDO);

                rolePermissionRepository.remove(Wrappers.lambdaQuery(RolePermissionDO.class)
                        .eq(RolePermissionDO::getRoleCode, roleDO.getRoleCode()));

                List<RolePermissionDO> rolePermissionDOList = buildRolePermissionDO(saveDTO, roleDO.getRoleCode());
                rolePermissionRepository.saveBatch(rolePermissionDOList);
            });

            generateEditRoleLog(roleName, roleDO.getCreatorId());
        }

        return true;
    }

    @Override
    public RoleDTO getRoleDetail(RoleDetailQueryDTO queryDTO) {
        RoleDO roleDO = roleRepository.getById(queryDTO.getRoleId());
        if (roleDO == null) {
            throw new BizException(BizErrorCode.ROLE_NOT_FOUND.getCode(), BizErrorCode.ROLE_NOT_FOUND.getDesc());
        }

        List<RolePermissionDO> rolePermissionDOs = rolePermissionRepository.list(Wrappers.lambdaQuery(RolePermissionDO.class)
                .in(RolePermissionDO::getRoleCode, roleDO.getRoleCode()));
        List<RolePermissionDTO> rolePermissionDTOs = rolePermissionConvert.toDTOList(rolePermissionDOs);
        RoleDTO roleDTO = roleConvert.toRoleDTO(roleDO);
        roleDTO.setRolePermissionList(rolePermissionDTOs);
        return roleDTO;
    }

    @Override
    @OperationLog
    public Boolean deleteRole(Long id) {
        // 数据权限校验
        dataPermissionProcessor.checkUpdateOrDeleteRoleDataPermission(id);

        RoleDO roleDO = roleRepository.getById(id);
        if (roleDO == null) {
            throw new BizException(BizErrorCode.ROLE_NOT_FOUND.getCode(), BizErrorCode.ROLE_NOT_FOUND.getDesc());
        }

        List<UserRoleDO> userRoleList = userRoleRepository.list(Wrappers.lambdaQuery(UserRoleDO.class)
                .eq(UserRoleDO::getRoleCode, roleDO.getRoleCode()));
        if (CollectionUtils.isNotEmpty(userRoleList)) {
            throw new BizException(BizErrorCode.FOUND_VALID_ACCOUNT.getCode(), BizErrorCode.FOUND_VALID_ACCOUNT.getDesc());
        }

        boolean res = roleRepository.removeById(id);

        generateDeleteRoleLog(roleDO.getRoleName(), roleDO.getCreatorId());

        return res;
    }

    @Override
    public List<PermissionDTO> getPermissionTree() {
        String scene = null;
        if (ObjectUtil.isNotEmpty(StpUtil.getExtra("scene"))) {
            scene = (String) StpUtil.getExtra("scene");
        }

        List<PermissionDTO> permissionFlatList = permissionMapper.getPermissionTree(scene);
        if (CollectionUtils.isEmpty(permissionFlatList)) {
            return null;
        }

        // 天纵特殊逻辑：只有是政数局的人能看到周期规划管理，其他人看不到
        isAvailableSpecialPermission(permissionFlatList);

        List<PermissionDTO> sortedPermissionList = permissionFlatList.stream()
                .sorted(Comparator.comparing(PermissionDTO::getId))
                .collect(Collectors.toList());
        // 构建树形结构
        return buildTree(sortedPermissionList);
    }

    private void isAvailableSpecialPermission(List<PermissionDTO> permissionFlatList) {
        boolean existPlanningCycleMng = permissionFlatList.stream()
                .anyMatch(e -> StrUtil.contains(e.getCode(), PermissionCodeEnums.PLANNING_CYCLE_MNG.getCode()));
        if (existPlanningCycleMng) {
            // 看用户当前部门的根部门是否是政数局，政数局的部门会手动写入ext：GOVERNMENT_DATA_BUREAU=true
            List<UserRootDepartmentDTO> currentUserRootDepartments = getCurrentUserRootDepartment();
            if (CollectionUtils.isEmpty(currentUserRootDepartments)) {
                return;
            }

            UserRootDepartmentDTO userRootDepartmentDTO = currentUserRootDepartments.get(0);
            DepartmentFlatDTO departmentDTO = userRootDepartmentDTO.getDepartmentDTO();
            String ext = departmentDTO.getExt();
            if (StrUtil.isBlank(ext)) {
                permissionFlatList.removeIf(p -> StrUtil.contains(p.getCode(), PermissionCodeEnums.PLANNING_CYCLE_MNG.getCode()));
            } else {
                JSONObject jsonObject = JSONUtil.parseObj(ext);
                Object isGovernmentDataBureau = jsonObject.get(ExtConstants.GOVERNMENT_DATA_BUREAU);
                if (isGovernmentDataBureau == null) {
                    permissionFlatList.removeIf(p -> StrUtil.contains(p.getCode(), PermissionCodeEnums.PLANNING_CYCLE_MNG.getCode()));
                }
            }
        }
    }

    private List<UserRootDepartmentDTO> getCurrentUserRootDepartment() {
        UserRootDepartmentQueryDTO queryDTO = new UserRootDepartmentQueryDTO();
        String userNo = (String) StpUtil.getExtra("userNo");
        queryDTO.setUserNos(Lists.newArrayList(userNo));
        return departmentService.getUserRootDepartments(queryDTO);
    }

    private RoleDO buildRoleDO(RoleSaveDTO saveDTO, String roleCode, String scene) {
        RoleDO roleDO = new RoleDO();
        roleDO.setRoleCode(roleCode);
        roleDO.setRoleName(saveDTO.getRoleName());
        roleDO.setRoleDesc(saveDTO.getDesc());
        roleDO.setCreatorId(StpUtil.getLoginIdAsString());
        roleDO.setDataScope(saveDTO.getDataScope());
        roleDO.setScene(scene);
        return roleDO;
    }

    private List<RolePermissionDO> buildRolePermissionDO(RoleSaveDTO saveDTO, String roleCode) {
        List<RolePermissionDO> rolePermissionDOList = Lists.newArrayList();
        saveDTO.getPermissions().forEach(permissionDTO -> {
            RolePermissionDO rolePermissionDO = new RolePermissionDO();
            rolePermissionDO.setRoleCode(roleCode);
            rolePermissionDO.setPermissionCode(permissionDTO.getCode());
            rolePermissionDOList.add(rolePermissionDO);
        });

        return rolePermissionDOList;
    }

    private List<PermissionDTO> buildTree(List<PermissionDTO> flatList) {
        Map<Long, PermissionDTO> idToNodeMap = flatList.stream()
                .collect(Collectors.toMap(PermissionDTO::getId, dto -> {
                    if (dto.getChildren() == null) {
                        dto.setChildren(new ArrayList<>());
                    }
                    return dto;
                }));

        // 存储顶级节点
        List<PermissionDTO> rootNodes = new ArrayList<>();

        // 遍历每个节点，组装树形结构
        for (PermissionDTO node : flatList) {
            if (node.getParentId() == -1L) {
                // 顶级节点
                rootNodes.add(node);
            } else {
                // 非顶级节点，添加到父节点的 children 列表中
                PermissionDTO parent = idToNodeMap.get(node.getParentId());
                if (parent != null) {
                    // 确保父节点的 children 列表已初始化
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(node);
                }
            }
        }

        return rootNodes;
    }

    private List<String> getAccountNoList(List<String> needQueryUserNos) {
        List<String> needQueryCreatorIds;

        List<AccountDO> accountList = accountRepository.list(Wrappers.lambdaQuery(AccountDO.class)
                .and(wrapper -> wrapper
                        // 按照userNo查询
                        .in(AccountDO::getUserNo, needQueryUserNos)
                        .or()
                        // 或者按照userType和isSuperAdmin查询（超管创建的角色）
                        .nested(nestedWrapper -> nestedWrapper
                                .eq(AccountDO::getUserType, UserTypeEnum.CUSTOMER.getCode())
                                .eq(AccountDO::getIsSuperAdmin, true)
                        )
                ));
        needQueryCreatorIds = accountList.stream()
                .map(AccountDO::getAccountNo)
                .collect(Collectors.toList());
        return needQueryCreatorIds;
    }


    private void generateAddRoleLog(String roleName) {
        try {
            String userNo = (String) StpUtil.getExtra("userNo");
            Object isSuperAdmin = StpUtil.getExtra("isSuperAdmin");

            // 超管不记录操作日志
            if (isSuperAdmin != null && !(Boolean) isSuperAdmin) {
                UserDepartmentDO userDepartment = userDepartmentRepository.getOne(Wrappers.lambdaQuery(UserDepartmentDO.class)
                        .eq(UserDepartmentDO::getUserNo, userNo));

                String name = getDepartmentFullPathForLog(userDepartment.getDepartmentId());
                OperationLogUtil.roleAdd(name, roleName);
            }
        } catch (Exception e) {
            LogUtil.error("生成添加角色日志失败，异常：", e);
        }
    }

    private void generateEditRoleLog(String roleName, String creatorId) {
        try {
            Object isSuperAdmin = StpUtil.getExtra("isSuperAdmin");

            // 超管不记录操作日志
            if (isSuperAdmin != null && !(Boolean) isSuperAdmin) {
                AccountDO account = accountRepository.getAccount(creatorId, null);
                UserDepartmentDO userDepartment = userDepartmentRepository.getOne(Wrappers.lambdaQuery(UserDepartmentDO.class)
                        .eq(UserDepartmentDO::getUserNo, account.getUserNo()));
                if (userDepartment != null) {
                    String name = getDepartmentFullPathForLog(userDepartment.getDepartmentId());
                    OperationLogUtil.roleEdit(name, roleName);
                }
            }
        } catch (Exception e) {
            LogUtil.error("生成编辑角色日志失败，异常：", e);
        }

    }

    private void generateDeleteRoleLog(String roleName, String creatorId) {
        try {
            Object isSuperAdmin = StpUtil.getExtra("isSuperAdmin");

            // 超管不记录操作日志
            if (isSuperAdmin != null && !(Boolean) isSuperAdmin) {
                AccountDO account = accountRepository.getAccount(creatorId, null);
                UserDepartmentDO userDepartment = userDepartmentRepository.getOne(Wrappers.lambdaQuery(UserDepartmentDO.class)
                        .eq(UserDepartmentDO::getUserNo, account.getUserNo()));
                if (userDepartment != null) {
                    String name = getDepartmentFullPathForLog(userDepartment.getDepartmentId());
                    OperationLogUtil.roleDelete(name, roleName);
                }
            }
        } catch (Exception e) {
            LogUtil.error("生成编辑角色日志失败，异常：", e);
        }

    }

    private String getDepartmentFullPathForLog(Long departmentId) {
        try {
            DepartmentDO departmentDO = departmentRepository.getById(departmentId);
            String fullPath = departmentDO.getFullPath();
            List<Long> fullPathList = JSONUtil.toList(fullPath, Long.class);

            // 根节点
            if (fullPathList.contains(-1L)) {
                return departmentDO.getName();
            }

            // 非根节点，不会包括-1
            StringBuilder sb = new StringBuilder();
            List<DepartmentDO> departmentDOs = departmentRepository.listByIds(fullPathList);
            DepartmentDO finalDepartmentDO = departmentDO;
            boolean existCurrentDepartment = departmentDOs.stream()
                    .anyMatch(d -> Objects.equals(d.getId(), finalDepartmentDO.getId()));

            for (int i = 0; i < departmentDOs.size(); i++) {
                DepartmentDO department = departmentDOs.get(i);
                if (i < departmentDOs.size() - 1) {
                    sb.append(department.getName()).append("-");
                } else {
                    sb.append(department.getName());
                }
            }

            if (!existCurrentDepartment) {
                sb.append("-").append(departmentDO.getName());
            }

            return sb.toString();
        } catch (Exception e) {
            LogUtil.error("生成日志错误，id={}，异常：{}", departmentId, e);
        }

        return null;
    }
}
