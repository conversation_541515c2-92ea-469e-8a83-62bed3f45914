package com.deepinnet.infra.service.util;

import cn.hutool.core.util.ReflectUtil;
import com.deepinnet.infra.service.constants.SmsConstants;
import com.deepinnet.infra.api.template.SmsBaseTemplate;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR> wong
 * @create 2023/11/9 20:10
 * @Description
 */
public class SmsSendUtil {

    public static Map<String, String> buildSmsRequestParam(SmsBaseTemplate smsTemplate) {
        if (smsTemplate == null) {
            return null;
        }

        // 使用反射构建参数列表，key为参数名，value为参数值
        Map<String, String> paramMap = buildParamMapByReflect(smsTemplate);
        // 移除掉templateId这个参数
        paramMap.remove(SmsConstants.TEMPLATE_ID);
        return paramMap;
    }

    public static String convertMap2Json(Map<String, String> paramMap) {
        StringBuilder sb = new StringBuilder();
        AtomicInteger count = new AtomicInteger();
        paramMap.forEach((key, value) -> {
            sb.append(SmsConstants.STAR).append(key).append(SmsConstants.STAR).append(SmsConstants.COLON).append(value);
            if (count.get() < paramMap.size() - 1) {
                sb.append(SmsConstants.COMMA);
            }
            count.getAndIncrement();
        });

        return sb.toString();
    }

    private static Map<String, String> buildParamMapByReflect(SmsBaseTemplate smsTemplate) {
        Map<String, String> paramMap = new HashMap<>();
        Class<? extends SmsBaseTemplate> clazz = smsTemplate.getClass();
        Map<String, Field> fieldMap = ReflectUtil.getFieldMap(clazz);
        fieldMap.forEach((name, filed) -> {
            Object value = ReflectUtil.getFieldValue(smsTemplate, filed);
            if (value instanceof String) {
                paramMap.put(name, (String) value);
            } else if (value instanceof List) {
                List<String> listValues = (List<String>) value;
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < listValues.size(); i++) {
                    String v = listValues.get(i);
                    if (i != listValues.size() - 1) {
                        sb.append(v).append(SmsConstants.COMMA);
                    } else {
                        sb.append(v);
                    }
                }
                paramMap.put(name, sb.toString());
            }
        });
        return paramMap;
    }
}
