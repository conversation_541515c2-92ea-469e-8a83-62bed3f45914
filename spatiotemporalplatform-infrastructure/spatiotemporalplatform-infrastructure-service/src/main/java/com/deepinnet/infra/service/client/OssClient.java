package com.deepinnet.infra.service.client;

import com.aliyun.oss.model.OSSObject;

import java.io.InputStream;
import java.util.Date;

/**
 * Oss操作客户端
 *
 * <AUTHOR>
 * @version 2022-11-05
 */
public interface OssClient {

    /**
     * 上传到oss
     *
     * @param bucketName
     * @param key
     * @param input
     * @return
     */
    String putObject(String bucketName, String key, byte[] input);

    /**
     * 上传到oss，指定过期时间
     *
     * @param bucketName
     * @param key
     * @param input
     * @param expirationTime 过期时间
     * @return
     */
    String putObject(String bucketName, String key, byte[] input, Date expirationTime);

    /**
     * 从oss下载
     *
     * @param bucketName
     * @param key
     * @return
     */
    InputStream getObject(String bucketName, String key);

    /**
     * 指定对象是否在oss中已存在
     *
     * @param bucketName
     * @param key
     * @return
     */
    boolean exists(String bucketName, String key);

    /**
     * 拿到当前的region
     *
     * @return
     */
    String getRegion();

    /**
     * 获取URL
     *
     * @param bucketName
     * @param key
     * @return
     */
    String getUrl(String bucketName, String key);

    /**
     * 获取url并指定过期时间
     *
     * @param bucketName
     * @param key
     * @param expirationMillisecond
     * @return
     */
    String getUrlWithExpireTime(String bucketName, String key, Long expirationMillisecond);

    OSSObject getObjectDetail(String bucketName, String key);
}
