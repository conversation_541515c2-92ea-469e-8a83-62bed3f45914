package com.deepinnet.infra.service;


import com.deepinnet.infra.api.dto.*;

import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2024/11/26 15:19
 * @Description
 */
public interface DepartmentService {
    Long saveDepartment(DepartmentSaveDTO saveDTO);

    Boolean addMember(DepartmentAddMemberDTO addMemberDTO);

    Boolean updateUserMemberInfo(UpdateUserMemberInfoDTO updateDTO);

    Boolean removeMember(Long memberId);

    Boolean updateDepartment(DepartmentUpdateDTO updateDTO);

    Boolean deleteDepartment(Long id);

    List<DepartmentDTO> getDepartmentByTree(Long parentId);

    List<DepartmentDTO> getDepartmentByTree(QueryDepartmentTreeDTO queryDTO);

    List<DepartmentDTO> getSubDepartments(QuerySubDepartmentDTO queryDTO);

    List<DepartmentTreeDTO> listSimpleDepartmentsByIds(List<Long> ids);

    List<UserRootDepartmentDTO> getUserRootDepartments(UserRootDepartmentQueryDTO queryDTO);

    WholeDepartmentDTO getWholeDepartmentInfo(WholeDepartmentQueryDTO queryDTO);

    List<SimpleUserInfoDTO> getDepartmentBindUsers(QueryDepartmentBindUserDTO queryDTO);

    List<DepartmentDTO> getDepartmentByTreeWithDataPermission(QueryDepartmentTreeDTO queryDTO);

    /**
     * 根据部门ID查询成员列表
     *
     * @param queryDTO 查询条件
     * @return 部门成员列表
     */
    List<DepartmentMemberDTO> listDepartmentMembers(DepartmentMemberQueryDTO queryDTO);
}
