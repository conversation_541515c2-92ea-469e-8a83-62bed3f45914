package com.deepinnet.infra.service;


import com.deepinnet.infra.api.dto.*;

import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2024/11/26 15:19
 * @Description
 */
public interface DepartmentService {
    Long saveDepartment(DepartmentSaveDTO saveDTO);

    Boolean addMember(DepartmentAddMemberDTO addMemberDTO);

    Boolean updateUserMemberInfo(UpdateUserMemberInfoDTO updateDTO);

    Boolean removeMember(Long memberId);

    Boolean updateDepartment(DepartmentUpdateDTO updateDTO);

    Boolean deleteDepartment(Long id);

    List<DepartmentDTO> getDepartmentByTree(Long parentId);

    List<DepartmentDTO> getDepartmentByTree(QueryDepartmentTreeDTO queryDTO);

    List<DepartmentDTO> getSubDepartments(QuerySubDepartmentDTO queryDTO);

    List<DepartmentTreeDTO> listSimpleDepartmentsByIds(List<Long> ids);

    List<UserRootDepartmentDTO> getUserRootDepartments(UserRootDepartmentQueryDTO queryDTO);

    WholeDepartmentDTO getWholeDepartmentInfo(WholeDepartmentQueryDTO queryDTO);

    List<SimpleUserInfoDTO> getDepartmentBindUsers(QueryDepartmentBindUserDTO queryDTO);

    List<DepartmentDTO> getDepartmentByTreeWithDataPermission(QueryDepartmentTreeDTO queryDTO);

    /**
     * 开给俊恒的，定时任务用的接口，查询所有的部门的根节点，所有根节点，交警的也查询
     *
     * @return
     */
    List<DepartmentFlatDTO> getAllRootDepartmentsWithoutTenantId();

    /**
     * 根据部门ID查询成员列表
     *
     * @param queryDTO 查询条件
     * @return 部门成员列表
     */
    List<DepartmentMemberDTO> listDepartmentMembers(DepartmentMemberQueryDTO queryDTO);

    /**
     * 给俊恒开的接口，根据传入的权限和数据作用范围，查询用于这些权限作用范围的用户
     *
     * @param queryDTO 查询参数
     * @return 授权用户的部门树结构
     */
    AuthorizedUserDepartmentTree listAuthorizedUsersWithDepartmentTree(AuthorizedUserQueryDTO queryDTO);

    /**
     * 给家驹的查询用户关联部门全路径的接口
     *
     * @param queryDTO
     * @return
     */
    List<UserDepartmentFullPathDTO> getUserRelatedDepartmentFullPath(QueryUserDepartmentFullPathDTO queryDTO);
}
