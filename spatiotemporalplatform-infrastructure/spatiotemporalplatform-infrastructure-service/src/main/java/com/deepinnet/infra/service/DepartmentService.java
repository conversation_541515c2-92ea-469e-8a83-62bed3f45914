package com.deepinnet.infra.service;


import com.deepinnet.infra.api.dto.*;

import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2024/11/26 15:19
 * @Description
 */
public interface DepartmentService {
    Long saveDepartment(DepartmentSaveDTO saveDTO);

    Boolean addMember(DepartmentAddMemberDTO addMemberDTO);

    Boolean updateUserMemberInfo(UpdateUserMemberInfoDTO updateDTO);

    Boolean removeMember(RemoveMemberDTO removeMemberDTO);

    Boolean updateDepartment(DepartmentUpdateDTO updateDTO);

    Boolean deleteDepartment(DeleteDepartmentDTO deleteDepartmentDTO);

    List<DepartmentDTO> getDepartmentByTree(Long parentId);

    List<DepartmentDTO> getDepartmentByTree(QueryDepartmentTreeDTO queryDTO);

    /**
     * 获取部门，根据数据权限过滤
     *
     * @param queryDTO
     * @return
     */
    List<DepartmentDTO> getDepartmentByTreeWithDataPermission(QueryDepartmentTreeDTO queryDTO);

    List<DepartmentDTO> getSubDepartments(QuerySubDepartmentDTO queryDTO);

    List<DepartmentTreeDTO> listSimpleDepartmentsByIds(List<Long> ids);

    List<UserRootDepartmentDTO> getUserRootDepartments(UserRootDepartmentQueryDTO queryDTO);

    List<SimpleUserInfoDTO> getDepartmentBindUsers(QueryDepartmentBindUserDTO queryDTO);

    /**
     * 根据部门ID查询成员列表
     *
     * @param queryDTO 查询条件
     * @return 部门成员列表
     */
    List<DepartmentMemberDTO> listDepartmentMembers(DepartmentMemberQueryDTO queryDTO);
}
