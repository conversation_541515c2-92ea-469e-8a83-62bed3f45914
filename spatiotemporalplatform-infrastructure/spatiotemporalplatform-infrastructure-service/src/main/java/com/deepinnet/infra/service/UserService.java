package com.deepinnet.infra.service;


import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.api.dto.UserRegisterDTO;

import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2024/8/8 16:04
 * @Description
 */
public interface UserService {

    UserLoginSuccessDTO login(UserLoginDTO loginDTO);

    UserLoginSuccessDTO loginByCode(UserLoginDTO loginDTO);

    UserInfoDTO getUserInfo();

    Boolean changePassword(UserPasswordChangeDTO passwordChangeDTO);

    Boolean forgetPassword(UserPasswordForgetDTO passwordChangeDTO);

    List<SimpleDepartmentDTO> getUserDepartments();

    Boolean logout();

    List<UserMemberDTO> getUserInfoByMemberId(List<Long> memberIds);

    List<UserMemberDTO> getUserInfoByUserNo(List<String> userNos);

    Boolean register(UserRegisterDTO registerDTO);

    Boolean checkUserInfoStatus();

    List<UserApprovalStatusDTO> getUserApprovalStatus(QueryUserApprovalStatusDTO queryDTO);

    List<UserDetailDTO> getUserDetailList(UserQueryDTO queryDTO);

    List<UserDetailDTO> getUserDetailListWithoutTenantId(UserQueryDTO queryDTO);

    /**
     * 开给家驹定时任务的接口，查询系统的用户，不带租户id
     *
     * @return
     */
    List<SimpleUserInfoDTO> getSimpleUserInfoWithoutTenantId(QueryUserInfoDTO queryDTO);

    DataAccessDTO getAvailableQueryData();

    List<UserMemberDTO> getUserInfoSimpleByUserNos(List<String> userNos);

    Boolean checkUserInitializedStatus();

    /**
     * 冻结账号
     *
     * @param accountFreezeDTO 冻结账号参数
     * @return 操作结果
     */
    Boolean freezeAccounts(AccountFreezeDTO accountFreezeDTO);

    /**
     * 解冻账号
     *
     * @param accountFreezeDTO 解冻账号参数
     * @return 操作结果
     */
    Boolean unfreezeAccounts(AccountFreezeDTO accountFreezeDTO);

    List<DataScopeTreeNodeDTO> getAvailableDataScopeTree();
}
