package com.deepinnet.infra.service.aspect;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.infra.service.annotation.OperationLog;
import com.deepinnet.infra.service.context.OperationLogContext;
import com.deepinnet.infra.service.service.OperationLogService;
import com.deepinnet.infra.service.util.SpELUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 操作日志切面
 * 
 * <AUTHOR> wong
 * @create 2025/7/31
 */
@Aspect
@Component
@Order(1)
public class OperationLogAspect {

    @Resource
    private OperationLogService operationLogService;

    @Around("@annotation(operationLog)")
    public Object around(ProceedingJoinPoint joinPoint, OperationLog operationLog) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        // 初始化上下文
        OperationLogContext.clear();
        
        try {
            // 执行目标方法
            Object result = joinPoint.proceed();
            
            // 记录成功日志
            recordOperationLog(joinPoint, operationLog, result, null, startTime);
            
            return result;
        } catch (Exception e) {
            // 记录失败日志
            recordOperationLog(joinPoint, operationLog, null, e, startTime);
            throw e;
        } finally {
            // 清理上下文
            OperationLogContext.clear();
        }
    }

    private void recordOperationLog(ProceedingJoinPoint joinPoint, OperationLog operationLog, 
                                   Object result, Exception exception, long startTime) {
        try {
            // 构建日志数据
            Map<String, Object> logData = buildLogData(joinPoint, operationLog, result, exception, startTime);
            
            // 异步或同步记录日志
            if (operationLog.async()) {
                operationLogService.recordLogAsync(logData);
            } else {
                operationLogService.recordLog(logData);
            }
        } catch (Exception e) {
            LogUtil.error("记录操作日志失败", e);
        }
    }

    private Map<String, Object> buildLogData(ProceedingJoinPoint joinPoint, OperationLog operationLog,
                                           Object result, Exception exception, long startTime) {
        Map<String, Object> logData = new HashMap<>();
        
        // 基本信息
        logData.put("module", operationLog.template().getModule());
        logData.put("type", operationLog.template().getType());
        logData.put("operationTime", LocalDateTime.now());
        logData.put("executionTime", System.currentTimeMillis() - startTime);
        
        // 用户信息
        try {
            if (StpUtil.isLogin()) {
                logData.put("userNo", StpUtil.getExtra("userNo"));
                logData.put("userName", StpUtil.getExtra("name"));
                logData.put("tenantId", StpUtil.getExtra("tenantId"));
                logData.put("scene", StpUtil.getExtra("scene"));
            }
        } catch (Exception e) {
            LogUtil.warn("获取用户信息失败", e);
        }
        
        // 请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            logData.put("ipAddress", getClientIpAddress(request));
            logData.put("userAgent", request.getHeader("User-Agent"));
            logData.put("requestUri", request.getRequestURI());
            logData.put("requestMethod", request.getMethod());
        }
        
        // 操作详情
        String detail = parseOperationDetail(joinPoint, operationLog, result, exception);
        logData.put("operationDetail", detail);
        
        // 操作结果
        if (exception != null) {
            logData.put("operationResult", "FAILURE");
            logData.put("errorMessage", exception.getMessage());
        } else {
            logData.put("operationResult", "SUCCESS");
        }
        
        // 请求参数
        if (operationLog.recordParams()) {
            String params = buildRequestParams(joinPoint, operationLog.excludeParams());
            logData.put("requestParams", params);
        }
        
        return logData;
    }

    private String parseOperationDetail(ProceedingJoinPoint joinPoint, OperationLog operationLog,
                                      Object result, Exception exception) {
        // 获取模板
        String template = operationLog.template().getTemplate();

        // 获取上下文变量
        OperationLogContext context = OperationLogContext.getCurrentContext();
        Map<String, Object> variables = context.getAllVariables();

        // 使用简单模板替换
        return SpELUtil.parseTemplate(template, variables);
    }

    private String buildRequestParams(ProceedingJoinPoint joinPoint, String[] excludeParams) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            Parameter[] parameters = method.getParameters();
            Object[] args = joinPoint.getArgs();
            
            Map<String, Object> paramMap = new HashMap<>();
            for (int i = 0; i < parameters.length; i++) {
                String paramName = parameters[i].getName();
                
                // 检查是否需要排除该参数
                if (shouldExcludeParam(paramName, excludeParams)) {
                    continue;
                }
                
                paramMap.put(paramName, args[i]);
            }
            
            return JSONUtil.toJsonStr(paramMap);
        } catch (Exception e) {
            LogUtil.warn("构建请求参数失败", e);
            return null;
        }
    }

    private boolean shouldExcludeParam(String paramName, String[] excludeParams) {
        for (String excludePattern : excludeParams) {
            if (paramName.matches(excludePattern)) {
                return true;
            }
        }
        return false;
    }

    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StrUtil.isNotBlank(xForwardedFor) && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (StrUtil.isNotBlank(xRealIp) && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
