package com.deepinnet.infra.service.log.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.infra.api.constants.DataScopeConstants;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.dal.dataobject.*;
import com.deepinnet.infra.dal.repository.*;
import com.deepinnet.infra.service.convert.OperationLogConvert;
import com.deepinnet.infra.service.enums.PermissionSystemEnum;
import com.deepinnet.infra.service.log.OperationLogService;
import com.deepinnet.infra.service.log.context.OperationLogContext;
import com.deepinnet.infra.service.util.DataPermissionProcessor;
import com.deepinnet.tenant.TenantIdUtil;
import com.github.pagehelper.*;
import com.google.common.collect.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.regex.*;
import java.util.stream.Collectors;

/**
 * 操作日志服务实现类
 *
 * <AUTHOR>
 */
@Service
public class OperationLogServiceImpl implements OperationLogService {

    @Resource
    private OperationLogRepository operationLogRepository;

    @Resource
    private AccountRepository accountRepository;

    @Resource
    private OperationLogConvert operationLogConvert;

    @Resource
    private DataPermissionProcessor dataPermissionProcessor;

    /**
     * 模板变量匹配正则表达式
     */
    private static final Pattern TEMPLATE_PATTERN = Pattern.compile("\\{([^}]+)\\}");

    @Override
    public void recordOperationLog(OperationLogContext context, String requestUri, String requestMethod,
                                   String ipAddress, String userAgent) {
        try {
            // 如果上下文为空或者禁用了日志记录，则不记录
            if (context == null || !context.isEnabled() || context.getTemplate() == null) {
                return;
            }

            // 渲染操作详情
            String operationDetail = renderTemplate(context);

            // 构建操作日志实体
            OperationLogDO operationLogDO = buildOperationDO(context, requestUri, requestMethod, ipAddress, userAgent, operationDetail);

            // 保存操作日志
            operationLogRepository.save(operationLogDO);

            LogUtil.info("操作日志记录成功: 用户[{}] 在模块[{}] 执行操作: {}",
                    operationLogDO.getUserNo(), operationLogDO.getModule(), operationDetail);

        } catch (Exception e) {
            LogUtil.error("记录操作日志失败", e);
        }
    }

    @Override
    public CommonPage<OperationLogDTO> pageQueryOperationLog(OperationLogQueryDTO queryDTO) {
        // 数据权限校验
        List<String> userNos = Lists.newArrayList();
        Map<String, AccountDO> accountMap = Maps.newHashMap();

        // 判断是否需要进行数据权限校验
        PermissionSystemEnum permissionSystemEnum = dataPermissionProcessor.routeToPermissionSystem();

        if (permissionSystemEnum == PermissionSystemEnum.NEED_DATA_PERMISSION) {
            DataAccessDTO availableQueryData = dataPermissionProcessor.getAvailableQueryData();
            if (availableQueryData == null || CollUtil.isEmpty(availableQueryData.getSupportQueryUserNos())) {
                return CommonPage.buildEmptyPage();
            }

            // 不是超管（不能看到所有数据）
            if (!availableQueryData.getSupportQueryUserNos().contains(DataScopeConstants.SUPER_ADMIN)) {
                userNos = availableQueryData.getSupportQueryUserNos();
            }
        }

        if (StrUtil.isNotBlank(queryDTO.getUserName())) {
            List<AccountDO> accountList = accountRepository.list(Wrappers.lambdaQuery(AccountDO.class)
                    .like(StrUtil.isNotBlank(queryDTO.getUserName()), AccountDO::getName, queryDTO.getUserName())
                    .in(CollUtil.isNotEmpty(userNos), AccountDO::getUserNo, userNos)
            );

            if (CollUtil.isEmpty(accountList)) {
                return CommonPage.buildEmptyPage();
            }

            userNos = accountList.stream().map(AccountDO::getUserNo)
                    .distinct()
                    .collect(Collectors.toList());

            accountMap = accountList.stream()
                    .collect(Collectors.toMap(AccountDO::getUserNo, Function.identity()));
        }

        Page<OperationLogDO> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        LambdaQueryWrapper<OperationLogDO> queryWrapper = Wrappers.lambdaQuery(OperationLogDO.class)
                .in(CollUtil.isNotEmpty(userNos), OperationLogDO::getUserNo, userNos)
                .eq(StrUtil.isNotBlank(queryDTO.getModule()), OperationLogDO::getModule, queryDTO.getModule())
                .orderByDesc(OperationLogDO::getOperationTime);

        List<OperationLogDO> operationLogDOs = operationLogRepository.list(queryWrapper);
        if (CollUtil.isEmpty(operationLogDOs)) {
            return CommonPage.buildEmptyPage();
        }

        // 这里用户名可能要变更，所以要实时查询
        if (CollUtil.isEmpty(accountMap)) {
            accountMap = buildAccountMap(operationLogDOs);
        }

        List<OperationLogDTO> operationLogDTOs = operationLogConvert.convertToDTO(operationLogDOs);
        for (OperationLogDTO operationLogDTO : operationLogDTOs) {
            AccountDO accountDO = accountMap.get(operationLogDTO.getUserNo());
            if (accountDO != null) {
                operationLogDTO.setUserName(accountDO.getName());
                operationLogDTO.setAccount(accountDO.getAccount());
            }
        }

        PageInfo<OperationLogDTO> pageInfo = buildPageInfo(operationLogDTOs, page);

        return CommonPage.buildPage(pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getPages(), pageInfo.getTotal(), operationLogDTOs);
    }


    @Override
    public OperationLogDTO getOperationLogById(Long id) {
        OperationLogDO operationLogDO = operationLogRepository.getById(id);
        return operationLogConvert.convertToDTO(operationLogDO);
    }

    @Override
    public String renderTemplate(OperationLogContext context) {
        if (context == null || context.getTemplate() == null) {
            return "";
        }

        String template = context.getTemplate().getTemplate();
        Map<String, Object> variables = context.getVariables();

        if (variables == null || variables.isEmpty()) {
            return template;
        }

        // 使用正则表达式替换模板变量
        Matcher matcher = TEMPLATE_PATTERN.matcher(template);
        StringBuilder result = new StringBuilder();

        while (matcher.find()) {
            String variableName = matcher.group(1);
            Object variableValue = variables.get(variableName);
            String replacement = variableValue != null ? variableValue.toString() : "{" + variableName + "}";
            matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(result);

        return result.toString();
    }

    private OperationLogDO buildOperationDO(OperationLogContext context, String requestUri, String requestMethod, String ipAddress, String userAgent, String operationDetail) {
        OperationLogDO operationLogDO = new OperationLogDO();
        String userNo = (String) StpUtil.getExtra("userNo");
        operationLogDO.setUserNo(userNo);
        operationLogDO.setModule(context.getTemplate().getModule());
        operationLogDO.setOperationDetail(operationDetail);
        operationLogDO.setOperationTime(LocalDateTime.now());
        operationLogDO.setRequestUri(requestUri);
        operationLogDO.setRequestMethod(requestMethod);
        operationLogDO.setIpAddress(ipAddress);
        operationLogDO.setUserAgent(userAgent);
        operationLogDO.setTenantId(TenantIdUtil.getTenantId());
        operationLogDO.setGmtCreated(LocalDateTime.now());
        operationLogDO.setGmtModified(LocalDateTime.now());
        return operationLogDO;
    }

    private Map<String, AccountDO> buildAccountMap(List<OperationLogDO> operationLogDOs) {
        List<String> opUserNos = operationLogDOs.stream()
                .distinct()
                .map(OperationLogDO::getUserNo)
                .collect(Collectors.toList());

        List<AccountDO> accountList = accountRepository.list(Wrappers.lambdaQuery(AccountDO.class)
                .in(AccountDO::getUserNo, opUserNos)
        );

        return accountList.stream()
                .collect(Collectors.toMap(AccountDO::getUserNo, Function.identity()));
    }

    private PageInfo<OperationLogDTO> buildPageInfo(List<OperationLogDTO> operationLogDTOs, Page<OperationLogDO> page) {
        PageInfo<OperationLogDTO> pageInfo = PageInfo.of(operationLogDTOs);
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPages(page.getPages());
        return pageInfo;
    }
}
