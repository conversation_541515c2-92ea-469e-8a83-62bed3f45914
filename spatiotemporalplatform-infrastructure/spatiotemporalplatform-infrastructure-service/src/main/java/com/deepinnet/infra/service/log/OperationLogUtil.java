package com.deepinnet.infra.service.log;

import com.deepinnet.infra.service.enums.OperationLogTemplateEnum;
import com.deepinnet.infra.service.log.context.*;

/**
 * <AUTHOR> wong
 * @create 2025/8/11 17:55
 * @Description
 *
 */

public class OperationLogUtil {

    /**
     * 便捷方法：登录成功
     */
    public static void loginSuccess(String name, String account) {
        OperationLogContextHolder.setContext(new OperationLogContext(OperationLogTemplateEnum.LOGIN_SUCCESS)
                .setVariable("account", account)
                .setVariable("name", name));
    }

    /**
     * 便捷方法：退出成功
     */
    public static void logoutSuccess(String name, String account) {
        OperationLogContextHolder.setContext(new OperationLogContext(OperationLogTemplateEnum.LOGOUT_SUCCESS)
                .setVariable("account", account)
                .setVariable("name", name));
    }

    /**
     * 便捷方法：更新个人信息
     */
    public static void updateProfile(String name, String account) {
        OperationLogContextHolder.setContext(new OperationLogContext(OperationLogTemplateEnum.PROFILE_UPDATE)
                .setVariable("name", name)
                .setVariable("account", account));
    }

    /**
     * 便捷方法：添加组织
     */
    public static void organizationAdd(String departmentName) {
        OperationLogContextHolder.setContext(new OperationLogContext(OperationLogTemplateEnum.ORGANIZATION_ADD)
                .setVariable("organizationName", departmentName));
    }

    /**
     * 便捷方法：删除组织
     */
    public static void organizationDelete(String departmentName) {
        OperationLogContextHolder.setContext(new OperationLogContext(OperationLogTemplateEnum.ORGANIZATION_DELETE)
                .setVariable("organizationName", departmentName));
    }

    /**
     * 便捷方法：编辑组织
     */
    public static void organizationEdit(String originalDepartmentName, String departmentName) {
        OperationLogContextHolder.setContext(new OperationLogContext(OperationLogTemplateEnum.ORGANIZATION_EDIT)
                .setVariable("originalOrganizationName", originalDepartmentName)
                .setVariable("organizationName", departmentName));
    }

    /**
     * 便捷方法：成员管理 - 添加成员
     */
    public static void generateMemberAddLog(String organizationName, String memberName) {
        OperationLogContextHolder.setContext(new OperationLogContext(OperationLogTemplateEnum.MEMBER_ADD)
                .setVariable("organizationName", organizationName)
                .setVariable("memberName", memberName));
    }

    /**
     * 便捷方法：成员管理 - 删除成员
     */
    public static void memberDelete(String organizationName, String memberName) {
        OperationLogContextHolder.setContext(new OperationLogContext(OperationLogTemplateEnum.MEMBER_DELETE)
                .setVariable("organizationName", organizationName)
                .setVariable("memberName", memberName));
    }

    /**
     * 便捷方法：成员管理 - 编辑成员
     */
    public static void memberEdit(String organizationName, String memberName) {
        OperationLogContextHolder.setContext(new OperationLogContext(OperationLogTemplateEnum.MEMBER_EDIT)
                .setVariable("organizationName", organizationName)
                .setVariable("memberName", memberName));
    }

    /**
     * 便捷方法：账号管理 - 添加账号
     */
    public static void accountAdd(String memberName, String account) {
        OperationLogContextHolder.setContext(new OperationLogContext(OperationLogTemplateEnum.ACCOUNT_ADD)
                .setVariable("memberName", memberName)
                .setVariable("account", account));
    }

    public static void accountDelete(String memberName, String account) {
        OperationLogContextHolder.setContext(new OperationLogContext(OperationLogTemplateEnum.ACCOUNT_DELETE)
                .setVariable("memberName", memberName)
                .setVariable("account", account));
    }

    public static void accountFreeze(String memberName, String account) {
        OperationLogContextHolder.setContext(new OperationLogContext(OperationLogTemplateEnum.ACCOUNT_FREEZE)
                .setVariable("memberName", memberName)
                .setVariable("account", account));
    }

    public static void accountUnfreeze(String memberName, String account) {
        OperationLogContextHolder.setContext(new OperationLogContext(OperationLogTemplateEnum.ACCOUNT_UNFREEZE)
                .setVariable("memberName", memberName)
                .setVariable("account", account));
    }

    public static void accountEdit(String memberName, String account) {
        OperationLogContextHolder.setContext(new OperationLogContext(OperationLogTemplateEnum.ACCOUNT_EDIT)
                .setVariable("memberName", memberName)
                .setVariable("account", account));
    }

    /**
     * 角色
     *
     * @param departmentName
     * @return
     */
    public static void roleAdd(String departmentName, String roleName) {
        OperationLogContextHolder.setContext(new OperationLogContext(OperationLogTemplateEnum.ROLE_ADD)
                .setVariable("organizationName", departmentName)
                .setVariable("roleName", roleName));
    }

    public static void roleDelete(String departmentName, String roleName) {
        OperationLogContextHolder.setContext(new OperationLogContext(OperationLogTemplateEnum.ROLE_DELETE)
                .setVariable("organizationName", departmentName)
                .setVariable("roleName", roleName));
    }

    public static void roleEdit(String departmentName, String roleName) {
        OperationLogContextHolder.setContext(new OperationLogContext(OperationLogTemplateEnum.ROLE_EDIT)
                .setVariable("organizationName", departmentName)
                .setVariable("roleName", roleName));
    }
}
