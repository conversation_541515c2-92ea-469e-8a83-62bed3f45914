package com.deepinnet.infra.service.sms.result;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

@Data
@AllArgsConstructor
public class SmsRequestResult {
    boolean success;

    String msg;

    static SmsRequestResult requestDone(boolean success, String msg) {
        return new SmsRequestResult(success, msg);
    }

    @JsonIgnore
    boolean isFail() {
        return !success;
    }
}