package com.deepinnet.infra.service;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.api.dto.PermissionDTO;

import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2024/8/8 16:04
 * @Description
 */
public interface RolePermissionService {
    /**
     * 分页查询/搜索角色列表
     *
     * @param roleQueryDTO
     * @return
     */
    CommonPage<RoleDTO> pageQueryRoleDTO(RoleQueryDTO roleQueryDTO);

    /**
     * 保存或者更新角色
     *
     * @param saveDTO
     * @return
     */
    Boolean saveOrUpdateRole(RoleSaveDTO saveDTO);

    /**
     * 获取角色详情
     *
     * @param queryDTO
     * @return
     */
    RoleDTO getRoleDetail(RoleDetailQueryDTO queryDTO);

    /**
     * 删除角色
     *
     * @param id
     * @return
     */
    Boolean deleteRole(Long id);

    /**
     * 获取权限树
     *
     * @return
     */
    List<PermissionDTO> getPermissionTree();
}
