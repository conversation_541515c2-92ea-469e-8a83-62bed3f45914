package com.deepinnet.infra.service.strategy;

import cn.dev33.satoken.stp.*;
import cn.hutool.core.util.StrUtil;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.api.enums.*;
import com.deepinnet.infra.dal.dataobject.AccountDO;
import com.deepinnet.infra.dal.repository.AccountRepository;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 默认登录策略
 */
@Component
public class SpatiotemporalOpenProdStrategy extends AbstractLoginStrategy {

    @Resource
    private AccountRepository accountRepository;

    @Override
    public boolean supports(String scene) {
        // 支持所有未指定的场景或交警业务场景
        return StrUtil.equals(scene, SceneEnum.SPATIOTEMPORAL_OPENPROD.getCode());
    }

    @Override
    public AccountDO getAccount(UserLoginDTO loginDTO) {
        return accountRepository.getAccountByUserType(loginDTO.getAccount(), Lists.newArrayList(UserTypeEnum.SPATIOTEMPORAL_OPENPROD.getCode()));
    }

    @Override
    public UserLoginSuccessDTO processLogin(UserLoginDTO loginDTO, AccountDO account) {
        // 获取部门IDs
        List<Long> departmentIds = getDepartmentIds(account.getUserNo());

        // 登录
        StpUtil.login(account.getAccountNo(), SaLoginConfig.setExtra("name", account.getName())
                .setExtra("account", account.getAccount())
                .setExtra("userNo", account.getUserNo())
                .setExtra("departmentIds", departmentIds)
                .setExtra("scene", loginDTO.getScene())
                .setExtra("isSuperAdmin", account.getIsSuperAdmin()));

        // 构建并返回登录成功信息
        return buildLoginSuccessDTO(account, departmentIds);
    }
} 