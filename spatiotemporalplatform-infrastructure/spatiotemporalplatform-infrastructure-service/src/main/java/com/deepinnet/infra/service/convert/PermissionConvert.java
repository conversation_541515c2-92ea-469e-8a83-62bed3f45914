package com.deepinnet.infra.service.convert;

import com.deepinnet.infra.api.dto.PermissionFlatDTO;
import com.deepinnet.infra.dal.dataobject.PermissionDO;
import org.mapstruct.*;

import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2024/11/25 17:38
 * @Description
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface PermissionConvert {
    PermissionFlatDTO toDTO(PermissionDO permissionDO);

    List<PermissionFlatDTO> toDTOList(List<PermissionDO> roleDOs);
}
