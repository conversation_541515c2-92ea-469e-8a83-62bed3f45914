package com.deepinnet.infra.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.*;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.api.enums.*;
import com.deepinnet.infra.api.vo.ApprovalVO;
import com.deepinnet.infra.dal.dataobject.*;
import com.deepinnet.infra.dal.repository.*;
import com.deepinnet.infra.service.*;
import com.deepinnet.infra.service.convert.ApprovalConvert;
import com.deepinnet.infra.service.error.BizErrorCode;
import com.github.pagehelper.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 审核服务实现类
 *
 * <AUTHOR> wong
 * @create 2025/04/25
 */
@Service
public class ApprovalServiceImpl implements ApprovalService {

    @Resource
    private ApprovalRepository approvalRepository;

    @Resource
    private ApprovalDataSnapshotRepository approvalDataSnapshotRepository;

    @Resource
    private ProductServiceCustomerRepository productServiceCustomerRepository;

    @Resource
    private ProductServiceSupplierRepository productServiceSupplierRepository;

    @Resource
    private ProductCustomerService productCustomerService;

    @Resource
    private ProductSupplierService productSupplierService;

    @Resource
    private ApprovalConvert approvalConvert;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Override
    public CommonPage<ApprovalVO> pageQuery(ApprovalQueryDTO queryDTO) {
        if (queryDTO == null || queryDTO.getPageNum() == null || queryDTO.getPageSize() == null) {
            LogUtil.error("分页查询审核列表参数错误");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 使用PageHelper进行分页查询
        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 构建查询条件
        LambdaQueryWrapper<ApprovalDO> queryWrapper = buildQueryWrapper(queryDTO);
        List<ApprovalDO> approvalList = approvalRepository.list(queryWrapper);

        // 如果查询结果为空，返回空分页结果
        if (CollectionUtils.isEmpty(approvalList)) {
            return CommonPage.buildEmptyPage();
        }

        // 转换为VO对象
        List<ApprovalDTO> approvalDTOList = approvalConvert.convert(approvalList);
        List<ApprovalVO> approvalVOList = approvalConvert.convertToVO(approvalDTOList);

        // 构建分页结果
        PageInfo<ApprovalVO> pageInfo = new PageInfo<>(approvalVOList);
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPages(page.getPages());

        // 返回分页结果
        return CommonPage.buildPage(
                queryDTO.getPageNum(),
                queryDTO.getPageSize(),
                pageInfo.getPages(),
                pageInfo.getTotal(),
                approvalVOList
        );
    }

    @Override
    public ApprovalDetailDTO getDetail(String approvalId) {
        if (StringUtils.isBlank(approvalId)) {
            LogUtil.error("获取审核详情失败，申请ID为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "申请ID不能为空");
        }

        // 查询审核记录
        LambdaQueryWrapper<ApprovalDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ApprovalDO::getApprovalId, approvalId);
        ApprovalDO approvalDO = approvalRepository.getOne(queryWrapper);
        if (approvalDO == null) {
            LogUtil.error("获取审核详情失败，审核记录不存在，申请ID：{}", approvalId);
            throw new BizException(BizErrorCode.APPROVAL_NOT_FOUND.getCode(), BizErrorCode.APPROVAL_NOT_FOUND.getDesc());
        }

        // 构建详情DTO
        ApprovalDetailDTO detailDTO = approvalConvert.convertToDetail(approvalDO);

        // 根据审核类型获取对应的业务详情
        if (ApprovalTypeEnum.CUSTOMER_INFO.getCode().equals(approvalDO.getApprovalType())) {
            // 获取客户详情
            CustomerDetailDTO customerDetail = productCustomerService.getCustomerDetail(
                    CustomerQueryDetailDTO.builder().userNo(approvalDO.getBizId()).build()
            );
            if (customerDetail != null) {
                detailDTO.setCustomerDetail(customerDetail);
            }
        } else if (ApprovalTypeEnum.SUPPLIER_INFO.getCode().equals(approvalDO.getApprovalType())) {
            SupplierQueryDetailDTO supplierQueryDetailDTO = new SupplierQueryDetailDTO();
            supplierQueryDetailDTO.setUserNo(approvalDO.getBizId());
            detailDTO.setSupplierDetail(productSupplierService.getSupplierDetail(supplierQueryDetailDTO));
        }

        // 查询当前用户的历史审核记录
        List<ApprovalHistoryDTO> approvalHistoryList = getApprovalHistory(approvalDO.getBizId(), approvalDO.getApprovalType());
        detailDTO.setApprovalHistoryList(approvalHistoryList);

        return detailDTO;
    }

    @Override
    public Boolean process(ApprovalProcessDTO processDTO) {
        if (processDTO == null || StringUtils.isBlank(processDTO.getApprovalId())
                || processDTO.getStatus() == null) {
            LogUtil.error("审核处理失败，参数错误");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 查询审核记录
        LambdaQueryWrapper<ApprovalDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ApprovalDO::getApprovalId, processDTO.getApprovalId());
        ApprovalDO approvalDO = approvalRepository.getOne(queryWrapper);
        if (approvalDO == null) {
            LogUtil.error("审核处理失败，审核记录不存在，申请ID：{}", processDTO.getApprovalId());
            throw new BizException(BizErrorCode.APPROVAL_NOT_FOUND.getCode(), BizErrorCode.APPROVAL_NOT_FOUND.getDesc());
        }

        // 检查审核状态
        if (!ApprovalStatusEnum.PENDING.getCode().equals(approvalDO.getStatus())) {
            LogUtil.error("审核处理失败，审核记录状态错误，当前状态：{}，申请ID：{}",
                    approvalDO.getStatus(), processDTO.getApprovalId());
            throw new BizException(BizErrorCode.APPROVAL_ALREADY_PROCESSED.getCode(),
                    BizErrorCode.APPROVAL_ALREADY_PROCESSED.getDesc());
        }

        // 检查审核状态是否合法
        if (!ApprovalStatusEnum.APPROVED.getCode().equals(processDTO.getStatus())
                && !ApprovalStatusEnum.REJECTED.getCode().equals(processDTO.getStatus())) {
            LogUtil.error("审核处理失败，审核状态错误，状态：{}", processDTO.getStatus());
            throw new BizException(BizErrorCode.APPROVAL_STATUS_ERROR.getCode(),
                    BizErrorCode.APPROVAL_STATUS_ERROR.getDesc());
        }

        // 获取当前操作人
        String operator = (String) StpUtil.getExtra("userName");
        if (StringUtils.isBlank(operator)) {
            operator = "系统";
        }

        // 使用事务更新审核记录状态
        String finalOperator = operator;
        return transactionTemplate.execute(status -> {
            // 更新审核记录
            approvalDO.setStatus(processDTO.getStatus());
            approvalDO.setApprovalTime(LocalDateTime.now());
            approvalDO.setApprover(finalOperator);
            approvalDO.setApprovalComment(processDTO.getApprovalComment());
            approvalDO.setGmtModified(new Date());
            approvalRepository.updateById(approvalDO);

            // 根据审核类型处理业务逻辑
            handleBusinessLogic(approvalDO, processDTO.getStatus());

            return true;
        });
    }

    @Override
    public String createCustomerApproval(String bizId, String organizationName, String socialCreditCode) {
        if (StringUtils.isBlank(bizId) || StringUtils.isBlank(organizationName)
                || StringUtils.isBlank(socialCreditCode)) {
            LogUtil.error("创建客户审核记录失败，参数错误");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 生成申请ID，格式：yyyyMMdd + 6位随机数字
        String approvalId = generateApprovalId();

        // 使用事务保存审核记录和数据快照
        return transactionTemplate.execute(status -> {
            // 创建审核记录
            ApprovalDO approvalDO = buildApprovalDO(approvalId, ApprovalTypeEnum.CUSTOMER_INFO, organizationName, socialCreditCode, bizId);
            approvalRepository.save(approvalDO);

            // 保存客户数据快照
            saveCustomerDataSnapshot(approvalId, bizId);

            return approvalId;
        });
    }

    private ApprovalDO buildApprovalDO(String approvalId, ApprovalTypeEnum approvalTypeEnum, String organizationName, String socialCreditCode, String bizId) {
        ApprovalDO approvalDO = new ApprovalDO();
        approvalDO.setApprovalId(approvalId);
        approvalDO.setApprovalType(approvalTypeEnum.getCode());
        approvalDO.setOrganizationName(organizationName);
        approvalDO.setSocialCreditCode(socialCreditCode);
        approvalDO.setStatus(ApprovalStatusEnum.PENDING.getCode());
        approvalDO.setApplyTime(LocalDateTime.now());
        approvalDO.setBizId(bizId);
        approvalDO.setGmtCreated(new Date());
        approvalDO.setGmtModified(new Date());
        return approvalDO;
    }

    @Override
    public String createSupplierApproval(String bizId, String organizationName, String socialCreditCode) {
        if (StringUtils.isBlank(bizId) || StringUtils.isBlank(organizationName)
                || StringUtils.isBlank(socialCreditCode)) {
            LogUtil.error("创建服务商审核记录失败，参数错误");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 生成申请ID，格式：yyyyMMdd + 6位随机数字
        String approvalId = generateApprovalId();

        // 使用事务保存审核记录和数据快照
        return transactionTemplate.execute(status -> {
            // 创建审核记录
            ApprovalDO approvalDO = buildApprovalDO(approvalId, ApprovalTypeEnum.SUPPLIER_INFO, organizationName, socialCreditCode, bizId);
            approvalRepository.save(approvalDO);

            // 保存服务商数据快照
            saveSupplierDataSnapshot(approvalId, bizId);

            return approvalId;
        });
    }

    /**
     * 构建查询条件
     *
     * @param queryDTO 查询参数
     * @return 查询条件
     */
    private LambdaQueryWrapper<ApprovalDO> buildQueryWrapper(ApprovalQueryDTO queryDTO) {
        LambdaQueryWrapper<ApprovalDO> queryWrapper = Wrappers.lambdaQuery(ApprovalDO.class);

        // 按审核类型查询
        if (queryDTO.getApprovalType() != null) {
            queryWrapper.eq(ApprovalDO::getApprovalType, queryDTO.getApprovalType());
        }

        // 按单位/企业名称查询
        if (StringUtils.isNotBlank(queryDTO.getOrganizationName())) {
            queryWrapper.like(ApprovalDO::getOrganizationName, queryDTO.getOrganizationName());
        }

        // 按审核状态查询
        if (queryDTO.getStatus() != null) {
            queryWrapper.eq(ApprovalDO::getStatus, queryDTO.getStatus());
        }

        // 按申请时间倒序排序
        queryWrapper.orderByDesc(ApprovalDO::getApplyTime);

        return queryWrapper;
    }

    /**
     * 生成申请ID
     *
     * @return 申请ID
     */
    private String generateApprovalId() {
        return DateUtil.format(new Date(), "yyyyMMdd") + RandomUtil.randomNumbers(6);
    }

    /**
     * 处理业务逻辑
     *
     * @param approvalDO 审核记录
     * @param status     审核状态
     */
    private void handleBusinessLogic(ApprovalDO approvalDO, Integer status) {
        // 根据审核类型处理业务逻辑
        if (ApprovalTypeEnum.CUSTOMER_INFO.getCode().equals(approvalDO.getApprovalType())) {
            // 更新客户状态
            LambdaQueryWrapper<ProductServiceCustomerDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProductServiceCustomerDO::getUserNo, approvalDO.getBizId());
            ProductServiceCustomerDO customerDO = productServiceCustomerRepository.getOne(queryWrapper);
            if (customerDO != null) {
                // 实时更新审核状态字段
                customerDO.setApprovalStatus(status);
                customerDO.setGmtModified(new Date());
                productServiceCustomerRepository.updateById(customerDO);
                
                LogUtil.info("客户审核状态更新成功，用户编号：{}，审核状态：{}", 
                           approvalDO.getBizId(), status);
            }
        } else if (ApprovalTypeEnum.SUPPLIER_INFO.getCode().equals(approvalDO.getApprovalType())) {
            // 更新服务商状态
            LambdaQueryWrapper<ProductServiceSupplierDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProductServiceSupplierDO::getUserNo, approvalDO.getBizId());
            ProductServiceSupplierDO supplierDO = productServiceSupplierRepository.getOne(queryWrapper);
            if (supplierDO != null) {
                // 实时更新审核状态字段
                supplierDO.setApprovalStatus(status);
                supplierDO.setGmtModified(new Date());
                productServiceSupplierRepository.updateById(supplierDO);
                
                LogUtil.info("服务商审核状态更新成功，用户编号：{}，审核状态：{}", 
                           approvalDO.getBizId(), status);
            }
        }
    }

    /**
     * 保存客户数据快照
     *
     * @param approvalId 审核ID
     * @param bizId      业务ID
     */
    private void saveCustomerDataSnapshot(String approvalId, String bizId) {
        try {
            // 调用客户详情查询接口获取完整数据
            CustomerQueryDetailDTO queryDetailDTO = CustomerQueryDetailDTO.builder().userNo(bizId).build();
            CustomerDetailDTO customerDetail = productCustomerService.getCustomerDetail(queryDetailDTO);

            if (customerDetail == null) {
                LogUtil.error("保存客户数据快照失败，客户不存在，bizId：{}", bizId);
                return;
            }

            // 保存数据快照
            ApprovalDataSnapshotDO snapshotDO = new ApprovalDataSnapshotDO();
            snapshotDO.setApprovalId(approvalId);
            snapshotDO.setSnapshotType(SnapshotTypeEnum.CUSTOMER_DATA.getCode());
            snapshotDO.setDataSnapshot(JSONUtil.toJsonStr(customerDetail));
            snapshotDO.setGmtCreated(new Date());
            snapshotDO.setGmtModified(new Date());

            approvalDataSnapshotRepository.save(snapshotDO);

            LogUtil.info("客户数据快照保存成功，审核ID：{}，业务ID：{}", approvalId, bizId);
        } catch (Exception e) {
            LogUtil.error("保存客户数据快照失败，审核ID：{}，业务ID：{}，错误：{}", approvalId, bizId, e.getMessage());
            throw e;
        }
    }

    /**
     * 保存服务商数据快照
     *
     * @param approvalId 审核ID
     * @param bizId      业务ID
     */
    private void saveSupplierDataSnapshot(String approvalId, String bizId) {
        try {
            // 调用服务商详情查询接口获取完整数据
            SupplierQueryDetailDTO queryDetailDTO = new SupplierQueryDetailDTO();
            queryDetailDTO.setUserNo(bizId);
            SupplierDetailDTO supplierDetail = productSupplierService.getSupplierDetail(queryDetailDTO);

            if (supplierDetail == null) {
                LogUtil.error("保存服务商数据快照失败，服务商不存在，bizId：{}", bizId);
                return;
            }

            // 保存数据快照
            ApprovalDataSnapshotDO snapshotDO = new ApprovalDataSnapshotDO();
            snapshotDO.setApprovalId(approvalId);
            snapshotDO.setSnapshotType(SnapshotTypeEnum.SUPPLIER_DATA.getCode());
            snapshotDO.setDataSnapshot(JSONUtil.toJsonStr(supplierDetail));
            snapshotDO.setGmtCreated(new Date());
            snapshotDO.setGmtModified(new Date());

            approvalDataSnapshotRepository.save(snapshotDO);

            LogUtil.info("服务商数据快照保存成功，审核ID：{}，业务ID：{}", approvalId, bizId);
        } catch (Exception e) {
            LogUtil.error("保存服务商数据快照失败，审核ID：{}，业务ID：{}，错误：{}", approvalId, bizId, e.getMessage());
            throw e;
        }
    }

    /**
     * 获取审核历史记录
     *
     * @param bizId        业务ID
     * @param approvalType 审核类型
     * @return 历史审核记录列表
     */
    private List<ApprovalHistoryDTO> getApprovalHistory(String bizId, Integer approvalType) {
        try {
            // 查询当前用户该类型的所有审核记录，按申请时间倒序
            LambdaQueryWrapper<ApprovalDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ApprovalDO::getBizId, bizId)
                    .eq(ApprovalDO::getApprovalType, approvalType)
                    .orderByDesc(ApprovalDO::getApplyTime);

            List<ApprovalDO> approvalList = approvalRepository.list(queryWrapper);

            // 转换为DTO
            return approvalConvert.convertToHistoryList(approvalList);
        } catch (Exception e) {
            LogUtil.error("查询审核历史记录失败，业务ID：{}，审核类型：{}，错误：{}", bizId, approvalType, e.getMessage());
            return new ArrayList<>();
        }
    }
} 