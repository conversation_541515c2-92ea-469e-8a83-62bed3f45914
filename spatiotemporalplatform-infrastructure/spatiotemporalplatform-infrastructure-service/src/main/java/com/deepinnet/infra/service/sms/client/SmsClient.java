package com.deepinnet.infra.service.sms.client;

import com.deepinnet.infra.api.dto.SmsBatchSendDTO;
import com.deepinnet.infra.api.template.SmsBaseTemplate;

import java.util.List;

/**
 * Creator zengjuerui
 * Date 2024-01-12
 **/
public interface SmsClient {

    void sendByTemplate(String mobile, SmsBaseTemplate smsTemplate);

    void batchSendByTemplate(List<String> mobileList, SmsBaseTemplate smsTemplate);

    void batchSendByTemplate(List<SmsBatchSendDTO> batchSendDTOs);
}
