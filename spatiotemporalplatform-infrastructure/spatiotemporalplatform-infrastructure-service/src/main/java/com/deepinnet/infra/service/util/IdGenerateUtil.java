package com.deepinnet.infra.service.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;

import java.util.Date;
import java.util.UUID;

/**
 * Description:
 * Date: 2025/4/16
 * Author: lijunheng
 */
public class IdGenerateUtil {

    public static String getId(String businessType) {
        return businessType + "_" + UUID.randomUUID().toString().replace("-", "");
    }


    public static String getId() {
        return  DateUtil.format(new Date(), "yyyyMMdd") + RandomUtil.randomNumbers(8);
    }
}
