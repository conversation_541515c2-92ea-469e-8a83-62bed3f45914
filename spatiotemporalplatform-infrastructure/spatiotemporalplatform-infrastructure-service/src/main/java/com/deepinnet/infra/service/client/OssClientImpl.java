package com.deepinnet.infra.service.client;

import cn.hutool.core.date.*;
import com.aliyun.oss.*;
import com.aliyun.oss.model.*;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.URL;
import java.util.Date;

/**
 * Oss客户端实现
 *
 * <AUTHOR>
 * @version 2022-11-05
 */
@Component
public class OssClientImpl implements OssClient, InitializingBean {

    @Value("${oss.endpoint}")
    private String ossEndPoint;

    @Value("${oss.accessKey}")
    private String ossAccessKey;

    @Value("${oss.accessSecret}")
    private String ossAccessSecret;

    @Value("${oss.expireMilliSecond}")
    private Long expireMilliSecond;

    private OSS ossClient;

    @Override
    public void afterPropertiesSet() throws Exception {
        ossClient = new OSSClientBuilder().build(ossEndPoint, ossAccessKey, ossAccessSecret);
    }

    @Override
    public String putObject(String bucketName, String key, byte[] input) {
        return putObject(bucketName, key, input, null);
    }

    @Override
    public String putObject(String bucketName, String key, byte[] input, Date expirationTime) {
        ObjectMetadata objectMetadata = null;
        if (expirationTime != null) {
            objectMetadata = new ObjectMetadata();
        }
        PutObjectResult putObjectResult = ossClient.putObject(new PutObjectRequest(bucketName, key, new ByteArrayInputStream(input), objectMetadata));
        return putObjectResult.getETag();
    }

    @Override
    public InputStream getObject(String bucketName, String key) {
        OSSObject object = ossClient.getObject(bucketName, key);
        return object.getObjectContent();
    }

    @Override
    public boolean exists(String bucketName, String key) {
        return ossClient.doesObjectExist(bucketName, key);
    }

    @Override
    public String getRegion() {
        return ossEndPoint;
    }

    @Override
    public String getUrl(String bucketName, String key) {
        DateTime date = DateUtil.date(DateUtil.date().getTime() + expireMilliSecond);
        URL url = ossClient.generatePresignedUrl(bucketName, key, date);
        return "https://" + url.getHost().replace("-internal", "") + url.getFile();
    }

    @Override
    public String getUrlWithExpireTime(String bucketName, String key, Long expirationMillisecond) {
        DateTime date = DateUtil.date(DateUtil.date().getTime() + expirationMillisecond);
        URL url = ossClient.generatePresignedUrl(bucketName, key, date);
        return "https://" + url.getHost().replace("-internal", "") + url.getFile();
    }

    @Override
    public OSSObject getObjectDetail(String bucketName, String key) {
        OSSObject object = ossClient.getObject(bucketName, key);
        return object;
    }

}
