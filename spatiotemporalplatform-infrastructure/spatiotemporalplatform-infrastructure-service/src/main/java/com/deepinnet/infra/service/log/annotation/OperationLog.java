package com.deepinnet.infra.service.log.annotation;


import com.deepinnet.infra.service.enums.OperationLogTemplateEnum;

import java.lang.annotation.*;

/**
 * 操作日志注解
 * 用于标记需要记录操作日志的方法
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperationLog {

    /**
     * 操作日志模板
     * 如果不指定，则从上下文中获取
     */
    OperationLogTemplateEnum template() default OperationLogTemplateEnum.LOGIN_SUCCESS;

    /**
     * 是否使用模板（默认false，表示从上下文获取）
     */
    boolean useTemplate() default false;

    /**
     * 是否记录请求参数（默认false）
     */
    boolean logParams() default false;

    /**
     * 是否记录返回结果（默认false）
     */
    boolean logResult() default false;

    /**
     * 操作描述（可选，用于补充说明）
     */
    String description() default "";
}
