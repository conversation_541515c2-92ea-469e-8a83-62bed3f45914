package com.deepinnet.infra.service.annotation;

import java.lang.annotation.*;

/**
 * 操作日志注解
 *
 * <AUTHOR> wong
 * @create 2025/7/31
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperationLog {

    /**
     * 操作模块
     */
    String module();

    /**
     * 操作类型
     */
    String type();

    /**
     * 操作详情模板，支持简单占位符 {参数名} 或 SpEL表达式 #{表达式}
     *
     * 简单占位符示例：
     * - "登录成功"
     * - "新增了成员【{name}】"
     * - "删除了规划【{planName}】"
     *
     * SpEL表达式示例（高级用法）：
     * - "#{#request.type == 'CREATE' ? '新增' : '编辑'}了规划【{planName}】"
     * - "编辑了成员【{name}】的角色为【#{T(String).join('、', #roleNames)}】"
     */
    String detail();

    /**
     * 是否记录请求参数（默认不记录，避免敏感信息泄露）
     */
    boolean recordParams() default false;

    /**
     * 参数过滤器，指定不记录的参数名
     * 例如：{"password", "token", "secret"}
     */
    String[] excludeParams() default {"password", "token", "secret", "key"};

    /**
     * 是否异步记录日志（默认异步，提高性能）
     */
    boolean async() default true;
}
