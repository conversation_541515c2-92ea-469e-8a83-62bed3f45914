package com.deepinnet.infra.service.annotation;

import com.deepinnet.infra.service.enums.OperationLogTemplateEnum;

import java.lang.annotation.*;

/**
 * 操作日志注解
 * 使用预定义模板，通过上下文填充变量
 *
 * <AUTHOR> wong
 * @create 2025/7/31
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperationLog {

    /**
     * 操作日志模板
     * 使用预定义的模板枚举，包含了模块、类型、详情模板和所需变量
     */
    OperationLogTemplateEnum template();

    /**
     * 是否记录请求参数（默认不记录，避免敏感信息泄露）
     */
    boolean recordParams() default false;

    /**
     * 参数过滤器，指定不记录的参数名
     * 例如：{"password", "token", "secret"}
     */
    String[] excludeParams() default {"password", "token", "secret", "key"};

    /**
     * 是否异步记录日志（默认异步，提高性能）
     */
    boolean async() default true;
}
