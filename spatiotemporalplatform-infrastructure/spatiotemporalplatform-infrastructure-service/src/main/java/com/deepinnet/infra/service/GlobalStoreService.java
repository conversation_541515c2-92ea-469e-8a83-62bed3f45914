package com.deepinnet.infra.service;


import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.api.enums.StoreBizTypeEnum;

/**
 * 存储服务
 *
 * <AUTHOR>
 * @version 2022/11/7 10:29
 */
public interface GlobalStoreService {

    /**
     * 文件上传服务
     *
     * @Param storePutDTO 文件上传入参
     * @Param inputStream 文件流
     * @see StoreBizTypeEnum
     */
    Result<OssStore> putObject(StoreUploadDTO storePutDTO);

    /**
     * 判断文件是否在OSS中存在
     *
     * @return Result<DeepinStoreExists>
     * @Param tpStoreQueryDTO
     */
    Result<Boolean> exists(StoreQueryDTO tpStoreQueryDTO);

    /**
     * 获取文件文件的URL
     *
     * @param tpStoreQueryDTO
     * @return
     */
    Result<String> getObjectUrl(StoreQueryDTO tpStoreQueryDTO);

    /**
     * 获取文件文件的URL带过期时间
     *
     * @param tpStoreQueryDTO
     * @return
     */
    Result<String> getObjectUrlWithExpireTime(StoreQueryDTO tpStoreQueryDTO);

    /**
     * 获取文件详情
     *
     * @param tpStoreQueryDTO 查询条件
     * @return 文件对象
     */
    Result<OssObject> getObjectDetail(StoreQueryDTO tpStoreQueryDTO);

}
