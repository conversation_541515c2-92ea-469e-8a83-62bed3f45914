package com.deepinnet.infra.service.util;

import cn.hutool.core.util.IdUtil;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR> wong
 * @create 2024/11/28 11:35
 * @Description
 */
public class BizNoGenerateUtil {

    public static String generateUserNo() {
        return "uid_" + IdUtil.getSnowflakeNextIdStr();
    }

    public static String generateAccountNo() {
        return "aid_" + IdUtil.getSnowflakeNextIdStr();
    }

    public static String generateRoleCode() {
        String timestamp = new SimpleDateFormat("yyyyMMdd").format(new Date());
        // 生成3位随机数
        int randomNumber = new Random().nextInt(900) + 100;
        // 拼接最终的角色编码
        return "role_" + timestamp + "_" + randomNumber;
    }

    public static String generateResetToken() {
        String timestamp = new SimpleDateFormat("yyyyMMdd").format(new Date());
        // 生成3位随机数
        int randomNumber = new Random().nextInt(900000) + 100;
        // 拼接最终的角色编码
        return "reset" + timestamp + randomNumber;
    }

    public static String generateApprovalNo() {
        String timestamp = new SimpleDateFormat("yyyyMMdd").format(new Date());
        // 生成3位随机数
        int randomNumber = new Random().nextInt(900000) + 100;
        // 拼接最终的角色编码
        return "approval_" + timestamp + randomNumber;
    }
}
