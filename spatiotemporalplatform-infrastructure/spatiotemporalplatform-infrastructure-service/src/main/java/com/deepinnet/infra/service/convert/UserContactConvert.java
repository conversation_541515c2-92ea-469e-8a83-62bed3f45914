package com.deepinnet.infra.service.convert;

import com.deepinnet.infra.api.dto.ContactInfoDTO;
import com.deepinnet.infra.dal.dataobject.UserContactInfoDO;
import org.mapstruct.*;

import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2025/4/22 09:50
 * @Description
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface UserContactConvert {

    ContactInfoDTO convertToUserContactDTO(UserContactInfoDO userContactInfoDO);

    List<ContactInfoDTO> convertToUserContactDTOs(List<UserContactInfoDO> userContactInfoDOs);
}
