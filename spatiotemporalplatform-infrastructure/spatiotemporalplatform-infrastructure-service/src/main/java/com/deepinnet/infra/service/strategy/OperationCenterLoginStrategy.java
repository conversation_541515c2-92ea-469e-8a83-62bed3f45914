package com.deepinnet.infra.service.strategy;

import cn.dev33.satoken.stp.SaLoginConfig;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import com.deepinnet.infra.api.dto.UserLoginDTO;
import com.deepinnet.infra.api.dto.UserLoginSuccessDTO;
import com.deepinnet.infra.api.enums.SceneEnum;
import com.deepinnet.infra.api.enums.UserTypeEnum;
import com.deepinnet.infra.dal.dataobject.AccountDO;
import com.deepinnet.infra.dal.repository.AccountRepository;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 运营中心登录策略
 */
@Component
public class OperationCenterLoginStrategy extends AbstractLoginStrategy {
    
    @Resource
    private AccountRepository accountRepository;
    
    @Override
    public boolean supports(String scene) {
        return StrUtil.equals(scene, SceneEnum.OPERATION_CENTER.getCode());
    }
    
    @Override
    public AccountDO getAccount(UserLoginDTO loginDTO) {
        return accountRepository.getAccountByUserType(
                loginDTO.getAccount(), 
                Lists.newArrayList(UserTypeEnum.OPERATION.getCode())
        );
    }
    
    @Override
    public UserLoginSuccessDTO processLogin(UserLoginDTO loginDTO, AccountDO account) {
        // 获取部门IDs
        List<Long> departmentIds = getDepartmentIds(account.getUserNo());
        
        // 登录
        StpUtil.login(account.getAccountNo(), SaLoginConfig.setExtra("name", account.getName())
                .setExtra("account", account.getAccount())
                .setExtra("userNo", account.getUserNo())
                .setExtra("departmentIds", departmentIds)
                .setExtra("userType", account.getUserType())
                .setExtra("scene", loginDTO.getScene())
                .setTimeout(timeout)
                .setExtra("isSuperAdmin", account.getIsSuperAdmin()));
        
        // 构建并返回登录成功信息
        return buildLoginSuccessDTO(account, departmentIds);
    }
} 