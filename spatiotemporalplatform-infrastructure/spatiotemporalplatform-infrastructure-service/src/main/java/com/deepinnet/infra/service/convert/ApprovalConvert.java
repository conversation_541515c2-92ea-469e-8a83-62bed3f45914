package com.deepinnet.infra.service.convert;

import com.deepinnet.infra.api.dto.ApprovalDTO;
import com.deepinnet.infra.api.dto.ApprovalDetailDTO;
import com.deepinnet.infra.api.dto.ApprovalHistoryDTO;
import com.deepinnet.infra.api.enums.ApprovalStatusEnum;
import com.deepinnet.infra.api.enums.ApprovalTypeEnum;
import com.deepinnet.infra.api.vo.ApprovalVO;
import com.deepinnet.infra.dal.dataobject.ApprovalDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

/**
 * 审核对象转换器
 *
 * <AUTHOR> wong
 * @create 2025/04/25
 */
@Mapper(componentModel = "spring")
public interface ApprovalConvert {

    /**
     * DO转DTO
     *
     * @param approvalDO 审核DO
     * @return 审核DTO
     */
    ApprovalDTO convert(ApprovalDO approvalDO);

    /**
     * DO集合转DTO集合
     *
     * @param approvalDOs 审核DO集合
     * @return 审核DTO集合
     */
    List<ApprovalDTO> convert(List<ApprovalDO> approvalDOs);

    /**
     * DTO转VO
     *
     * @param approvalDTO 审核DTO
     * @return 审核VO
     */
    ApprovalVO convertToVO(ApprovalDTO approvalDTO);

    /**
     * DTO集合转VO集合
     *
     * @param approvalDTOs 审核DTO集合
     * @return 审核VO集合
     */
    List<ApprovalVO> convertToVO(List<ApprovalDTO> approvalDTOs);

    /**
     * DO转详情DTO
     *
     * @param approvalDO 审核DO
     * @return 审核详情DTO
     */
    @Mapping(target = "customerDetail", ignore = true)
    @Mapping(target = "supplierDetail", ignore = true)
    @Mapping(target = "approvalHistoryList", ignore = true)
    ApprovalDetailDTO convertToDetail(ApprovalDO approvalDO);

    /**
     * DO转历史记录DTO
     *
     * @param approvalDO 审核DO
     * @return 历史记录DTO
     */
    @Mapping(target = "approvalTypeDesc", source = "approvalType", qualifiedByName = "getApprovalTypeDesc")
    @Mapping(target = "statusDesc", source = "status", qualifiedByName = "getStatusDesc")
    ApprovalHistoryDTO convertToHistory(ApprovalDO approvalDO);

    /**
     * DO集合转历史记录DTO集合
     *
     * @param approvalDOs 审核DO集合
     * @return 历史记录DTO集合
     */
    List<ApprovalHistoryDTO> convertToHistoryList(List<ApprovalDO> approvalDOs);

    /**
     * 获取审核类型描述
     *
     * @param approvalType 审核类型
     * @return 审核类型描述
     */
    @Named("getApprovalTypeDesc")
    default String getApprovalTypeDesc(Integer approvalType) {
        if (approvalType == null) {
            return "";
        }
        ApprovalTypeEnum typeEnum = ApprovalTypeEnum.getByCode(approvalType);
        return typeEnum != null ? typeEnum.getDesc() : "";
    }

    /**
     * 获取审核状态描述
     *
     * @param status 审核状态
     * @return 审核状态描述
     */
    @Named("getStatusDesc")
    default String getStatusDesc(Integer status) {
        if (status == null) {
            return "";
        }
        ApprovalStatusEnum statusEnum = ApprovalStatusEnum.getByCode(status);
        return statusEnum != null ? statusEnum.getDesc() : "";
    }
} 