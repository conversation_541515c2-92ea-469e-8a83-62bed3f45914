package com.deepinnet.infra.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.*;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.api.enums.*;
import com.deepinnet.infra.api.vo.CustomerInfoVO;
import com.deepinnet.infra.dal.dataobject.*;
import com.deepinnet.infra.dal.mapper.ProductServiceCustomerMapper;
import com.deepinnet.infra.dal.repository.*;
import com.deepinnet.infra.service.*;
import com.deepinnet.infra.service.convert.*;
import com.deepinnet.infra.service.error.BizErrorCode;
import com.github.pagehelper.*;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户单位信息服务实现类
 *
 * <AUTHOR> wong
 * @create 2025/04/22
 */
@Service
public class ProductCustomerServiceImpl implements ProductCustomerService {

    @Resource
    private ProductServiceCustomerRepository productServiceCustomerRepository;

    @Resource
    private UserContactInfoRepository userContactInfoRepository;

    @Resource
    private FileAttachmentRepository fileAttachmentRepository;

    @Resource
    private ProductServiceCustomerMapper productServiceCustomerMapper;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private ProductServiceSupplierConvert productServiceSupplierConvert;

    @Resource
    private UserContactConvert userContactConvert;

    @Resource
    private ApprovalService approvalService;

    @Resource
    private ApprovalRepository approvalRepository;

    @Override
    public Result<String> saveCustomerInfo(CustomerInfoDTO customerInfoDTO) {
        // 参数校验
        if (customerInfoDTO == null) {
            LogUtil.error("保存客户单位信息失败，参数为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "客户单位信息不能为空");
        }

        // 获取用户编号
        String userNo = (String) StpUtil.getExtra("userNo");

        // 使用事务保存数据
        return transactionTemplate.execute(status -> {
            // 更新客户基本信息
            updateCustomer(customerInfoDTO, userNo);

            // 保存联系人信息
            saveContactInfo(customerInfoDTO.getContactInfoList(), userNo);

            // 保存附件信息
            saveAttachments(customerInfoDTO.getIdentityPicPathList(), userNo);

            if (customerInfoDTO.getNeedAudit()) {
                // 如果信息发生变更，创建新的审核记录
                String approvalId = approvalService.createCustomerApproval(
                        userNo,
                        customerInfoDTO.getUnitName(),
                        customerInfoDTO.getSocialCreditCode()
                );
                LogUtil.info("客户信息变更，已创建审核记录，申请ID：{}", approvalId);
            }

            return Result.success(userNo);
        });
    }

    @Override
    public CustomerDetailDTO getCustomerDetail(CustomerQueryDetailDTO queryDetailDTO) {
        String userNo = (String) StpUtil.getExtra("userNo");
        if (queryDetailDTO != null && StrUtil.isNotBlank(queryDetailDTO.getUserNo())) {
            userNo = queryDetailDTO.getUserNo();
        }

        if (StringUtils.isBlank(userNo)) {
            LogUtil.error("获取客户单位详情失败，用户编号为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "用户编号不能为空");
        }

        // 查询客户基本信息
        LambdaQueryWrapper<ProductServiceCustomerDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductServiceCustomerDO::getUserNo, userNo);
        ProductServiceCustomerDO customerDO = productServiceCustomerRepository.getOne(queryWrapper);
        if (customerDO == null) {
            LogUtil.error("获取客户单位详情失败，客户不存在，用户编号：{}", userNo);
            throw new BizException(BizErrorCode.USER_ALREADY_EXISTS.getCode(), "客户不存在");
        }

        // 构建详情DTO
        CustomerDetailDTO detailDTO = new CustomerDetailDTO();
        BeanUtils.copyProperties(customerDO, detailDTO);

        // 设置审核状态（直接从客户表字段获取）
        detailDTO.setApprovalStatus(customerDO.getApprovalStatus());
        detailDTO.setApprovalStatusDesc(getApprovalStatusDescription(customerDO.getApprovalStatus()));

        // 查询联系人信息
        List<ContactInfoDTO> contactInfos = getContactInfo(userNo);
        detailDTO.setContactInfoDTOs(contactInfos);

        // 查询附件信息
        List<String> identityPicPaths = getAttachmentPaths(userNo);
        detailDTO.setIdentifiedPictureList(identityPicPaths);
        return detailDTO;
    }

    @Override
    public Result<CommonPage<CustomerInfoVO>> pageQueryCustomers(CustomerQueryDTO queryDTO) {
        if (queryDTO == null || queryDTO.getPageNum() == null || queryDTO.getPageSize() == null) {
            LogUtil.error("分页查询客户列表参数错误");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 使用PageHelper进行分页查询
        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 判断是否需要联表查询（有联系人姓名或联系人电话条件时）
        List<ProductServiceCustomerDO> customerList;
        if (StringUtils.isNotBlank(queryDTO.getContactName()) || StringUtils.isNotBlank(queryDTO.getContactPhone())) {
            // 使用联表查询
            customerList = productServiceCustomerMapper.pageQueryCustomersWithContact(queryDTO);
        } else {
            // 不需要联表查询时使用普通查询
            LambdaQueryWrapper<ProductServiceCustomerDO> queryWrapper = buildQueryWrapper(queryDTO);
            customerList = productServiceCustomerRepository.list(queryWrapper);
        }

        // 如果查询结果为空，返回空分页结果
        if (CollectionUtils.isEmpty(customerList)) {
            return Result.success(CommonPage.buildEmptyPage());
        }

        // 获取所有客户的用户编号
        List<String> userNoList = customerList.stream()
                .map(ProductServiceCustomerDO::getUserNo)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // 查询所有客户对应的联系人信息
        Map<String, List<UserContactInfoDO>> contactInfoMap = Collections.emptyMap();
        if (!CollectionUtils.isEmpty(userNoList)) {
            List<UserContactInfoDO> contactInfoList = userContactInfoRepository.list(
                    Wrappers.lambdaQuery(UserContactInfoDO.class)
                            .in(UserContactInfoDO::getUserNo, userNoList)
            );

            // 按userNo分组
            contactInfoMap = contactInfoList.stream()
                    .collect(Collectors.groupingBy(UserContactInfoDO::getUserNo));
        }

        // 转换为VO对象
        List<CustomerInfoVO> customerVOList = convertToVOList(customerList, contactInfoMap);

        // 构建分页结果
        PageInfo<CustomerInfoVO> pageInfo = new PageInfo<>(customerVOList);
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPages(page.getPages());

        // 返回分页结果
        CommonPage<CustomerInfoVO> result = CommonPage.buildPage(
                queryDTO.getPageNum(),
                queryDTO.getPageSize(),
                pageInfo.getPages(),
                pageInfo.getTotal(),
                customerVOList
        );

        return Result.success(result);
    }

    /**
     * 构建查询条件
     *
     * @param queryDTO 查询参数
     * @return 查询条件
     */
    private LambdaQueryWrapper<ProductServiceCustomerDO> buildQueryWrapper(CustomerQueryDTO queryDTO) {
        LambdaQueryWrapper<ProductServiceCustomerDO> queryWrapper = Wrappers.lambdaQuery(ProductServiceCustomerDO.class);

        // 按登录手机号查询
        if (StringUtils.isNotBlank(queryDTO.getPhone())) {
            queryWrapper.like(ProductServiceCustomerDO::getPhone, queryDTO.getPhone());
        }

        // 按单位名称查询
        if (StringUtils.isNotBlank(queryDTO.getOrganizationName())) {
            queryWrapper.like(ProductServiceCustomerDO::getOrganizationName, queryDTO.getOrganizationName());
        }

        // 按审核状态查询
        if (queryDTO.getApprovalStatus() != null) {
            queryWrapper.eq(ProductServiceCustomerDO::getApprovalStatus, queryDTO.getApprovalStatus());
        }

        // 按创建时间倒序排序
        queryWrapper.orderByDesc(ProductServiceCustomerDO::getRegistrationTime);
        queryWrapper.eq(ProductServiceCustomerDO::getIsDeleted, false);

        return queryWrapper;
    }

    /**
     * 将DO对象转换为VO对象
     *
     * @param customerList   客户列表
     * @param contactInfoMap 联系人信息映射
     * @return VO对象列表
     */
    private List<CustomerInfoVO> convertToVOList(List<ProductServiceCustomerDO> customerList,
                                                 Map<String, List<UserContactInfoDO>> contactInfoMap) {
        List<CustomerInfoVO> resultList = new ArrayList<>(customerList.size());

        for (ProductServiceCustomerDO customer : customerList) {
            CustomerInfoVO vo = new CustomerInfoVO();

            // 设置客户基本信息
            vo.setCustomerId(customer.getUserNo());
            vo.setPhone(customer.getPhone());
            vo.setCustomerName(customer.getOrganizationName());
            vo.setRegisterTime(LocalDateTimeUtil.toEpochMilli(customer.getRegistrationTime()));
            // 设置身份类型
            vo.setIdentityType(customer.getIdentityType());
            // 设置统一社会信用代码
            vo.setSocialCreditCode(customer.getSocialCreditCode());

            // 设置审核状态（直接从客户表字段获取）
            vo.setApprovalStatus(customer.getApprovalStatus());
            vo.setApprovalStatusDesc(getApprovalStatusDescription(customer.getApprovalStatus()));

            // 设置联系人信息
            if (contactInfoMap.containsKey(customer.getUserNo()) && !contactInfoMap.get(customer.getUserNo()).isEmpty()) {
                List<UserContactInfoDO> userContactInfoDOList = contactInfoMap.get(customer.getUserNo());
                List<ContactInfoDTO> contactInfoDTOs = userContactConvert.convertToUserContactDTOs(userContactInfoDOList);
                vo.setContactInfoDTOs(contactInfoDTOs);
            }

            resultList.add(vo);
        }

        return resultList;
    }

    /**
     * 保存或更新客户信息
     *
     * @param customerInfoDTO 客户信息DTO
     * @param userNo          用户编号
     * @return 是否发生信息变更
     */
    private void updateCustomer(CustomerInfoDTO customerInfoDTO, String userNo) {
        // 查询客户是否存在
        LambdaQueryWrapper<ProductServiceCustomerDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductServiceCustomerDO::getUserNo, userNo);
        ProductServiceCustomerDO existCustomer = productServiceCustomerRepository.getOne(queryWrapper);

        if (existCustomer == null) {
            throw new BizException(BizErrorCode.USER_NOT_EXIST.getCode());
        }

        // 检查单位名称唯一性
        LambdaQueryWrapper<ProductServiceCustomerDO> nameQueryWrapper = new LambdaQueryWrapper<>();
        nameQueryWrapper.eq(ProductServiceCustomerDO::getOrganizationName, customerInfoDTO.getUnitName())
                .ne(ProductServiceCustomerDO::getUserNo, userNo);
        if (productServiceCustomerRepository.count(nameQueryWrapper) > 0) {
            LogUtil.error("保存客户单位信息失败，单位名称已存在：{}", customerInfoDTO.getUnitName());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "单位名称已存在");
        }

        // 检查信息是否发生变更
        // boolean isInfoChanged = isCustomerInfoChanged(existCustomer, customerInfoDTO);

        // 更新客户信息
        existCustomer.setOrganizationName(customerInfoDTO.getUnitName());
        existCustomer.setAddress(customerInfoDTO.getDetailAddress());
        existCustomer.setIdentifiedPictureList(JSONUtil.toJsonStr(customerInfoDTO.getIdentityPicPathList()));
        existCustomer.setGmtModified(new Date());
        existCustomer.setRegionCode(customerInfoDTO.getRegionCode());
        existCustomer.setRegionName(customerInfoDTO.getRegionName());
        existCustomer.setCooperationAgreement(true);
        // 更新身份类型
        existCustomer.setIdentityType(customerInfoDTO.getIdentityType());
        // 更新统一社会信用代码
        existCustomer.setSocialCreditCode(customerInfoDTO.getSocialCreditCode());

        // 设置审核状态
        if (customerInfoDTO.getNeedAudit() != null && customerInfoDTO.getNeedAudit()) {
            existCustomer.setApprovalStatus(0); // 0-审核中
        } else {
            // 如果不需要审核或首次保存，设置为初始状态
            if (existCustomer.getApprovalStatus() == null) {
                existCustomer.setApprovalStatus(-1); // -1-初始状态（未认证）
            }
        }

        if (customerInfoDTO.getNeedAudit() != null && !customerInfoDTO.getNeedAudit()) {
            existCustomer.setApprovalStatus(ApprovalStatusEnum.APPROVED.getCode());
        }

        productServiceCustomerRepository.updateById(existCustomer);
    }

    /**
     * 检查客户信息是否发生变更
     *
     * @param existCustomer   现有客户信息
     * @param customerInfoDTO 新的客户信息
     * @return 是否发生变更
     */
    private boolean isCustomerInfoChanged(ProductServiceCustomerDO existCustomer, CustomerInfoDTO customerInfoDTO) {
        // 检查基本信息是否变更
        boolean basicInfoChanged = !Objects.equals(existCustomer.getOrganizationName(), customerInfoDTO.getUnitName()) ||
                !Objects.equals(existCustomer.getIdentityType(), customerInfoDTO.getIdentityType()) ||
                !Objects.equals(existCustomer.getSocialCreditCode(), customerInfoDTO.getSocialCreditCode()) ||
                !Objects.equals(existCustomer.getAddress(), customerInfoDTO.getDetailAddress()) ||
                !Objects.equals(existCustomer.getRegionCode(), customerInfoDTO.getRegionCode()) ||
                !Objects.equals(existCustomer.getRegionName(), customerInfoDTO.getRegionName());

        // 如果基本信息已经变更，直接返回true
        if (basicInfoChanged) {
            return true;
        }

        // 检查联系人信息是否变更
        boolean contactInfoChanged = isContactInfoChanged(existCustomer.getUserNo(), customerInfoDTO.getContactInfoList());
        if (contactInfoChanged) {
            LogUtil.info("检测到联系人信息变更，用户编号：{}", existCustomer.getUserNo());
            return true;
        }

        // 检查证明材料是否变更
        boolean attachmentChanged = isAttachmentChanged(existCustomer.getUserNo(), customerInfoDTO.getIdentityPicPathList());
        if (attachmentChanged) {
            LogUtil.info("检测到证明材料变更，用户编号：{}", existCustomer.getUserNo());
            return true;
        }

        return false;
    }

    /**
     * 检查联系人信息是否发生变更
     *
     * @param userNo             用户编号
     * @param newContactInfoList 新的联系人信息列表
     * @return 是否发生变更
     */
    private boolean isContactInfoChanged(String userNo, List<ContactInfoDTO> newContactInfoList) {
        // 查询现有联系人信息
        LambdaQueryWrapper<UserContactInfoDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserContactInfoDO::getUserNo, userNo);
        List<UserContactInfoDO> existingContactList = userContactInfoRepository.list(queryWrapper);

        // 如果数量不同，说明有变更
        if (existingContactList.size() != (newContactInfoList != null ? newContactInfoList.size() : 0)) {
            return true;
        }

        // 如果都为空，说明没有变更
        if (CollectionUtils.isEmpty(existingContactList) && CollectionUtils.isEmpty(newContactInfoList)) {
            return false;
        }

        // 如果新联系人信息为空但现有联系人不为空，说明有变更
        if (CollectionUtils.isEmpty(newContactInfoList)) {
            return true;
        }

        // 将现有联系人信息转换为可比较的格式
        Set<String> existingContactSet = existingContactList.stream()
                .map(contact -> contact.getName() + "|" + contact.getPhone() + "|" +
                        (contact.getPosition() != null ? contact.getPosition() : ""))
                .collect(Collectors.toSet());

        // 将新联系人信息转换为可比较的格式
        Set<String> newContactSet = newContactInfoList.stream()
                .map(contact -> contact.getName() + "|" + contact.getPhone() + "|" +
                        (contact.getPosition() != null ? contact.getPosition() : ""))
                .collect(Collectors.toSet());

        // 比较两个集合是否相同
        return !existingContactSet.equals(newContactSet);
    }

    /**
     * 检查证明材料是否发生变更
     *
     * @param userNo              用户编号
     * @param newIdentityPicPaths 新的证明材料路径列表
     * @return 是否发生变更
     */
    private boolean isAttachmentChanged(String userNo, List<String> newIdentityPicPaths) {
        // 查询现有证明材料
        LambdaQueryWrapper<FileAttachmentDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileAttachmentDO::getUserNo, userNo)
                .eq(FileAttachmentDO::getBizType, AttachmentBizTypeEnum.UNIT_IDENTITY.getCode());
        List<FileAttachmentDO> existingAttachments = fileAttachmentRepository.list(queryWrapper);

        // 提取现有附件路径
        Set<String> existingPaths = existingAttachments.stream()
                .map(FileAttachmentDO::getFilePath)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 处理新的附件路径
        Set<String> newPaths = new HashSet<>();
        if (!CollectionUtils.isEmpty(newIdentityPicPaths)) {
            newPaths = newIdentityPicPaths.stream()
                    .filter(Objects::nonNull)
                    .filter(path -> !path.trim().isEmpty())
                    .collect(Collectors.toSet());
        }

        // 比较两个路径集合是否相同
        boolean pathsChanged = !existingPaths.equals(newPaths);

        if (pathsChanged) {
            LogUtil.info("证明材料变更详情 - 用户：{}，原有文件数：{}，新文件数：{}",
                    userNo, existingPaths.size(), newPaths.size());
        }

        return pathsChanged;
    }

    /**
     * 获取审核状态描述
     *
     * @param status 审核状态
     * @return 状态描述
     */
    private String getApprovalStatusDescription(Integer status) {
        if (status == null) {
            return "未知状态";
        }

        switch (status) {
            case -1:
                return "未认证";
            case 0:
                return "审核中";
            case 1:
                return "已认证";
            case 2:
                return "不通过，请重新认证";
            default:
                return "未知状态";
        }
    }

    /**
     * 保存联系人信息
     *
     * @param contactInfoDTOList 联系人信息DTO
     * @param userNo             用户编号
     */
    private void saveContactInfo(List<ContactInfoDTO> contactInfoDTOList, String userNo) {
        // 删除现有联系人
        userContactInfoRepository.remove(
                Wrappers.lambdaQuery(UserContactInfoDO.class)
                        .eq(UserContactInfoDO::getUserNo, userNo)
        );

        // 创建新联系人
        List<UserContactInfoDO> userContactInfoDOList = Lists.newArrayList();
        for (ContactInfoDTO contactInfoDTO : contactInfoDTOList) {
            UserContactInfoDO contactInfoDO = new UserContactInfoDO();
            contactInfoDO.setUserNo(userNo);
            contactInfoDO.setName(contactInfoDTO.getName());
            contactInfoDO.setPhone(contactInfoDTO.getPhone());
            contactInfoDO.setPosition(contactInfoDTO.getPosition());
            userContactInfoDOList.add(contactInfoDO);
        }

        userContactInfoRepository.saveBatch(userContactInfoDOList);
    }

    /**
     * 获取联系人信息
     *
     * @param userNo 用户编号
     * @return 联系人信息DTO
     */
    private List<ContactInfoDTO> getContactInfo(String userNo) {
        LambdaQueryWrapper<UserContactInfoDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserContactInfoDO::getUserNo, userNo);

        List<UserContactInfoDO> userContactInfoDOList = userContactInfoRepository.list(queryWrapper);
        return productServiceSupplierConvert.convertToContactInfoDTOs(userContactInfoDOList);
    }

    /**
     * 保存附件信息
     *
     * @param picPaths 图片路径列表
     * @param userNo   用户编号
     */
    private void saveAttachments(List<String> picPaths, String userNo) {
        if (!CollectionUtils.isEmpty(picPaths)) {
            // 删除现有附件
            fileAttachmentRepository.remove(
                    Wrappers.lambdaQuery(FileAttachmentDO.class)
                            .eq(FileAttachmentDO::getUserNo, userNo)
                            .eq(FileAttachmentDO::getBizType, AttachmentBizTypeEnum.UNIT_IDENTITY.getCode())
            );

            // 保存新附件
            List<FileAttachmentDO> attachments = picPaths.stream().map(path -> {
                FileAttachmentDO attachment = new FileAttachmentDO();
                attachment.setUserNo(userNo);
                attachment.setFilePath(path);
                attachment.setBizType(AttachmentBizTypeEnum.UNIT_IDENTITY.getCode());
                attachment.setBizNo(userNo);
                return attachment;
            }).collect(Collectors.toList());

            if (!attachments.isEmpty()) {
                fileAttachmentRepository.saveBatch(attachments);
            }
        }
    }

    /**
     * 获取附件路径列表
     *
     * @param userNo 用户编号
     * @return 附件路径列表
     */
    private List<String> getAttachmentPaths(String userNo) {
        LambdaQueryWrapper<FileAttachmentDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileAttachmentDO::getUserNo, userNo);
        queryWrapper.eq(FileAttachmentDO::getBizType, AttachmentBizTypeEnum.UNIT_IDENTITY.getCode());

        List<FileAttachmentDO> attachments = fileAttachmentRepository.list(queryWrapper);

        if (CollectionUtils.isEmpty(attachments)) {
            return new ArrayList<>();
        }

        return attachments.stream()
                .map(FileAttachmentDO::getFilePath)
                .collect(Collectors.toList());
    }
} 