package com.deepinnet.infra.service.enums;

/**
 * 操作模块枚举
 * 
 * <AUTHOR> wong
 * @create 2025/7/31
 */
public enum OperationModuleEnum {

    // 系统管理
    USER_MANAGEMENT("用户管理"),
    ROLE_MANAGEMENT("角色管理"),
    PERMISSION_MANAGEMENT("权限管理"),
    DEPARTMENT_MANAGEMENT("部门管理"),
    
    // 业务模块
    FLIGHT_PLANNING("飞行规划"),
    FLIGHT_DEMAND("飞行需求"),
    FLIGHT_MONITORING("飞行监测"),
    PLANNING_CYCLE("规划周期"),
    
    // 基础功能
    LOGIN("登录"),
    SYSTEM_CONFIG("系统配置"),
    DATA_MANAGEMENT("数据管理"),
    LOG_MANAGEMENT("日志管理"),
    
    // 其他
    OTHER("其他");

    private final String description;

    OperationModuleEnum(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public String getCode() {
        return this.name();
    }
}
