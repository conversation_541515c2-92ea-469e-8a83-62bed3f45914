package com.deepinnet.infra.service;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.FileAttachmentDTO;
import com.deepinnet.infra.api.dto.FileAttachmentQueryDTO;
import com.deepinnet.infra.api.dto.FileUploadResultDTO;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件附件服务接口
 *
 * <AUTHOR> wong
 * @create 2025/04/19
 */
public interface FileAttachmentService {

    /**
     * 上传单个文件
     *
     * @param file    文件
     * @param userNo  用户编号
     * @param bizType 业务类型
     * @param bizNo   业务编号
     * @return 上传结果
     */
    Result<FileUploadResultDTO> uploadFile(MultipartFile file, String userNo, String bizType, String bizNo);

    /**
     * 区分环境上传文件，公网环境上传oss，内网环境上传到本地磁盘
     *
     * @param file
     * @param userNo
     * @param bizType
     * @param bizNo
     * @return
     */
    Result<FileUploadResultDTO> uploadFileWithEnv(MultipartFile file, String userNo, String bizType, String bizNo);

    /**
     * 批量上传文件
     *
     * @param files   文件列表
     * @param userNo  用户编号
     * @param bizType 业务类型
     * @param bizNo   业务编号
     * @return 上传结果列表
     */
    Result<List<FileUploadResultDTO>> batchUploadFiles(MultipartFile[] files, String userNo, String bizType, String bizNo);

    /**
     * 根据文件路径获取文件资源
     *
     * @param filePath 文件路径
     * @return 文件资源响应实体
     */
    ResponseEntity<Resource> getFileResource(String filePath);

    /**
     * 根据文件路径和业务编号获取文件资源
     *
     * @param filePath 文件路径
     * @param bizNo    业务编号，用于权限校验
     * @return 文件资源响应实体
     */
    ResponseEntity<Resource> getFileResourceWithAuth(String filePath, String bizNo);

    /**
     * 获取文件附件对象
     *
     * @param id 文件 id
     * @return 文件附件对象
     */
    Result<FileAttachmentDTO> getFileAttachment(Long id);

    /**
     * 获取文件附件对象列表
     *
     * @param queryDTO 查询对象
     * @return 文件附件对象列表
     */
    Result<List<FileAttachmentDTO>> queryFileAttachmentList(FileAttachmentQueryDTO queryDTO);

} 