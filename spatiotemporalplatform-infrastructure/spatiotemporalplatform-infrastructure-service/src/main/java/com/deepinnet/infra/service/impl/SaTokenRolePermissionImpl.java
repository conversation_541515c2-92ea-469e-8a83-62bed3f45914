package com.deepinnet.infra.service.impl;

import cn.dev33.satoken.stp.*;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.infra.dal.dataobject.*;
import com.deepinnet.infra.dal.repository.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wong
 * @create 2025/7/15 10:06
 * @Description
 */
@Service
public class SaTokenRolePermissionImpl implements StpInterface {

    @Resource
    private UserRoleRepository userRoleRepository;

    @Resource
    private RolePermissionRepository rolePermissionRepository;

    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        String userNo = (String) StpUtil.getExtra("userNo");
        List<UserRoleDO> userRoleList = userRoleRepository.list(Wrappers.lambdaQuery(UserRoleDO.class)
                .eq(UserRoleDO::getUserNo, userNo));
        if (CollUtil.isEmpty(userRoleList)) {
            return null;
        }

        List<String> roleCodes = userRoleList.stream().map(UserRoleDO::getRoleCode)
                .collect(Collectors.toList());
        List<RolePermissionDO> rolePermissionDOList = rolePermissionRepository.list(Wrappers.lambdaQuery(RolePermissionDO.class)
                .in(RolePermissionDO::getRoleCode, roleCodes));
        if (CollUtil.isEmpty(rolePermissionDOList)) {
            return null;
        }

        return rolePermissionDOList.stream()
                .map(RolePermissionDO::getPermissionCode)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        String userNo = (String) StpUtil.getExtra("userNo");
        List<UserRoleDO> userRoleList = userRoleRepository.list(Wrappers.lambdaQuery(UserRoleDO.class)
                .eq(UserRoleDO::getUserNo, userNo));
        if (CollUtil.isEmpty(userRoleList)) {
            return null;
        }

        return userRoleList.stream()
                .map(UserRoleDO::getRoleCode)
                .collect(Collectors.toList());
    }
}
