package com.deepinnet.infra.service.util;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.infra.api.constants.DataScopeConstants;
import com.deepinnet.infra.api.dto.DataAccessDTO;
import com.deepinnet.infra.dal.dataobject.*;
import com.deepinnet.infra.dal.mapper.DepartmentMapper;
import com.deepinnet.infra.dal.repository.*;
import com.deepinnet.infra.service.UserService;
import com.deepinnet.infra.service.enums.*;
import com.deepinnet.infra.service.error.BizErrorCode;
import com.google.common.collect.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wong
 * @create 2025/7/25 17:49
 * @Description
 */
@Component
public class DataPermissionProcessor {

    @Resource
    private UserDepartmentRepository userDepartmentRepository;

    @Resource
    private DepartmentRepository departmentRepository;

    @Resource
    private UserRoleRepository userRoleRepository;

    @Resource
    private AccountRepository accountRepository;

    @Resource
    private RoleRepository roleRepository;

    @Resource
    private UserService userService;

    @Resource
    private DepartmentMapper departmentMapper;

    // RequestContext缓存键常量
    private static final String CACHE_KEY_USER_ROLES = "DATA_PERMISSION_USER_ROLES_";
    private static final String CACHE_KEY_USER_DEPARTMENTS = "DATA_PERMISSION_USER_DEPARTMENTS_";
    private static final String CACHE_KEY_ROLE_LIST = "DATA_PERMISSION_ROLE_LIST_";

    public DepartmentDO getRootDepartment(String userNo) {
        Long rootId = getRootDepartmentId(userNo);
        return departmentRepository.getById(rootId);
    }

    public void checkDataPermissionByDepartmentId(Long departmentId) {
        if (isSuperAdmin()) {
            return;
        }

        String userNo = (String) StpUtil.getExtra("userNo");
        List<RoleDO> roleList = getCachedRoleList(userNo);

        // 存在dataScope为null的角色，说明是老系统，无需数据权限
        boolean hasNoDataScopeRole = roleList.stream()
                .anyMatch(r -> r.getDataScope() == null);
        if (hasNoDataScopeRole) {
            return;
        }

        if (CollUtil.isEmpty(roleList)) {
            throw new BizException(BizErrorCode.NO_PERMISSION_TO_OPERATE.getCode(), BizErrorCode.NO_PERMISSION_TO_OPERATE.getDesc());
        }

        // 个人权限不允许任何的写操作
        boolean existNonPersonalPermission = roleList.stream()
                .anyMatch(r -> r.getDataScope() != 1);

        if (!existNonPersonalPermission) {
            throw new BizException(BizErrorCode.NO_PERMISSION_TO_OPERATE.getCode(), BizErrorCode.NO_PERMISSION_TO_OPERATE.getDesc());
        }

        DataAccessDTO availableQueryData = userService.getAvailableQueryData();
        if (availableQueryData == null) {
            throw new BizException(BizErrorCode.NO_PERMISSION_TO_OPERATE.getCode(), BizErrorCode.NO_PERMISSION_TO_OPERATE.getDesc());
        }

        List<String> supportQueryUserNos = availableQueryData.getSupportQueryUserNos();
        if (CollUtil.isNotEmpty(supportQueryUserNos) && supportQueryUserNos.contains(DataScopeConstants.SUPER_ADMIN)) {
            return;
        }

        List<Long> supportQueryDepartmentIds = availableQueryData.getSupportQueryDepartmentIds();
        if (CollUtil.isNotEmpty(supportQueryDepartmentIds) && supportQueryDepartmentIds.contains(departmentId)) {
            return;
        }

        throw new BizException(BizErrorCode.NO_PERMISSION_TO_OPERATE.getCode(), BizErrorCode.NO_PERMISSION_TO_OPERATE.getDesc());
    }

    public void checkDataPermissionByUserNo(String userNo) {
        Boolean isSuperAdmin = isSuperAdmin();
        if (isSuperAdmin) {
            return;
        }

        UserDepartmentDO userDepartment = userDepartmentRepository.getOne(Wrappers.lambdaQuery(UserDepartmentDO.class)
                .eq(UserDepartmentDO::getUserNo, userNo));
        if (userDepartment == null) {
            throw new BizException(BizErrorCode.USER_DEPARTMENT_NOT_FOUND.getCode(), BizErrorCode.USER_DEPARTMENT_NOT_FOUND.getDesc());
        }

        checkDataPermissionByDepartmentId(userDepartment.getDepartmentId());
    }

    public void checkSaveRoleDataPermission() {
        String userNo = (String) StpUtil.getExtra("userNo");
        List<RoleDO> roleList = getCachedRoleList(userNo);

        // 存在dataScope为null的角色，说明是老系统，无需数据权限
        boolean hasNoDataScopeRole = roleList.stream()
                .anyMatch(r -> r.getDataScope() == null);
        if (hasNoDataScopeRole) {
            return;
        }

        if (CollUtil.isEmpty(roleList)) {
            throw new BizException(BizErrorCode.NO_PERMISSION_TO_OPERATE.getCode(), BizErrorCode.NO_PERMISSION_TO_OPERATE.getDesc());
        }

        // 非个人权限都能新增角色
        boolean existNonPersonalPermission = roleList.stream()
                .anyMatch(r -> r.getDataScope() != 1);

        if (!existNonPersonalPermission) {
            throw new BizException(BizErrorCode.NO_PERMISSION_TO_OPERATE.getCode(), BizErrorCode.NO_PERMISSION_TO_OPERATE.getDesc());
        }
    }

    public void checkUpdateOrDeleteRoleDataPermission(Long roleId) {
        // 具体能不能删除和更新角色走具体的数据权限，这里需要找到具体角色的创建人
        RoleDO roleDO = roleRepository.getById(roleId);
        String creatorAccountNo = roleDO.getCreatorId();
        AccountDO accountDO = accountRepository.getOne(Wrappers.lambdaQuery(AccountDO.class)
                .eq(AccountDO::getAccountNo, creatorAccountNo));
        if (accountDO == null) {
            throw new BizException(BizErrorCode.NO_PERMISSION_TO_OPERATE.getCode(), BizErrorCode.NO_PERMISSION_TO_OPERATE.getDesc());
        }

        PermissionSystemEnum permissionSystemEnum = routeToPermissionSystem();
        if (permissionSystemEnum == PermissionSystemEnum.NO_DATA_PERMISSION) {
            return;
        }

        // 创建人是超管，超管创建的角色只能超管改
        if (accountDO.getIsSuperAdmin()) {
            Boolean currentUserIsSuperAdmin = isSuperAdmin();
            if (!currentUserIsSuperAdmin) {
                throw new BizException(BizErrorCode.NO_PERMISSION_TO_OPERATE.getCode(), BizErrorCode.NO_PERMISSION_TO_OPERATE.getDesc());
            }
        }

        checkDataPermissionByUserNo(accountDO.getUserNo());
    }

    public DataAccessDTO getAvailableQueryData() {
        return processWithDataPermission();
    }

    public PermissionSystemEnum routeToPermissionSystem() {
        String userNo = (String) StpUtil.getExtra("userNo");
        List<RoleDO> roleList = getCachedRoleList(userNo);

        // 存在dataScope为null的角色，说明是老系统，无需数据权限
        boolean hasNoDataScopeRole = roleList.stream()
                .anyMatch(r -> r.getDataScope() == null);
        if (hasNoDataScopeRole) {
            return PermissionSystemEnum.NO_DATA_PERMISSION;
        }

        return PermissionSystemEnum.NEED_DATA_PERMISSION;
    }

    private DataAccessDTO processWithDataPermission() {
        DataAccessDTO dataAccessDTO = new DataAccessDTO();

        String userNo = (String) StpUtil.getExtra("userNo");
        Boolean isSuperAdminTag = isSuperAdmin();

        // 超级管理员特殊处理，能查看所有的数据
        if (isSuperAdminTag || DataScopeConstants.SUPER_ADMIN_NOS.contains(userNo)) {
            dataAccessDTO.setSupportQueryUserNos(Lists.newArrayList(DataScopeConstants.SUPER_ADMIN));
            return dataAccessDTO;
        }

        // 查询用户的所有角色（使用缓存）
        List<UserRoleDO> userRoles = getCachedUserRoles(userNo);

        if (CollectionUtils.isEmpty(userRoles)) {
            LogUtil.error("用户没有绑定角色，userNo={}", userNo);
            throw new BizException(BizErrorCode.USER_ROLE_NOT_EXIST.getCode(), BizErrorCode.USER_ROLE_NOT_EXIST.getDesc());
        }

        List<RoleDO> roles = getCachedRoleList(userNo);

        if (CollectionUtils.isEmpty(roles)) {
            LogUtil.error("用户绑定的角色不存在，userNo={}，roles={}", userNo, JSONUtil.toJsonStr(roles));
            throw new BizException(BizErrorCode.ROLE_NOT_FOUND.getCode(), BizErrorCode.ROLE_NOT_FOUND.getDesc());
        }

        // 计算最高数据权限作用域（取最大值）
        Integer maxDataScope = roles.stream()
                .filter(role -> role.getDataScope() != null)
                .mapToInt(RoleDO::getDataScope)
                .max()
                .orElse(1); // 默认为个人数据权限

        // 根据数据权限作用域返回可查询的数据范围
        return buildDataAccessByScope(maxDataScope, userNo, dataAccessDTO);
    }

    private Boolean isSuperAdmin() {
        Boolean isSuperAdminTag = false;
        Object isSuperAdmin = StpUtil.getExtra("isSuperAdmin");
        if (ObjectUtil.isNotNull(isSuperAdmin)) {
            isSuperAdminTag = (Boolean) StpUtil.getExtra("isSuperAdmin");
        }

        return isSuperAdminTag;
    }


    private Long getRootDepartmentId(String userNo) {
        List<UserDepartmentDO> userDepartments = getCachedUserDepartments(userNo);
        if (CollUtil.isEmpty(userDepartments)) {
            throw new BizException(BizErrorCode.USER_DEPARTMENT_NOT_FOUND.getCode(), BizErrorCode.USER_DEPARTMENT_NOT_FOUND.getDesc());
        }

        UserDepartmentDO userDepartment = userDepartments.get(0);
        DepartmentDO departmentDO = departmentRepository.getById(userDepartment.getDepartmentId());
        if (departmentDO == null) {
            throw new BizException(BizErrorCode.DEPARTMENT_NOT_FOUND.getCode(), BizErrorCode.DEPARTMENT_NOT_FOUND.getDesc());
        }

        String fullPath = departmentDO.getFullPath();
        List<Long> departmentIds = JSONUtil.toList(fullPath, Long.class);
        departmentIds.remove(-1L);

        return departmentIds.get(0);
    }

    /**
     * 根据数据权限作用域构建数据访问权限
     */
    private DataAccessDTO buildDataAccessByScope(Integer dataScope, String userNo, DataAccessDTO dataAccessDTO) {
        // 全局数据：查看所有数据
        if (dataScope == 4) {
            dataAccessDTO.setSupportQueryUserNos(Lists.newArrayList(DataScopeConstants.SUPER_ADMIN));
            dataAccessDTO.setDataScope(4);
        } else {
            // 组织数据（本组织全局）：查看本部门的所有数据
            buildCurrentDepartmentAllData(userNo, dataAccessDTO);
            dataAccessDTO.setDataScope(3);
        }

        return dataAccessDTO;
    }

    /**
     * 构建所有部门数据访问权限
     */
    private void buildCurrentDepartmentAllData(String userNo, DataAccessDTO dataAccessDTO) {
        String scene = (String) StpUtil.getExtra("scene");

        List<UserDepartmentDO> userDepartmentDOs = getCachedUserDepartments(userNo);

        if (CollectionUtils.isEmpty(userDepartmentDOs)) {
            LogUtil.error("用户绑定的部门为空，userNo={}", userNo);
            throw new BizException(BizErrorCode.USER_DEPARTMENT_NOT_FOUND.getCode(), BizErrorCode.USER_DEPARTMENT_NOT_FOUND.getDesc());
        }

        // 根据部门的full_path路径找到当前部门的根节点的id
        Long rootId = getRootDepartmentId(userNo);

        // 根据根节点id，获取这棵子树上所有部门id
        List<Long> subDepartmentIds = departmentMapper.getAllSubDepartmentIds(rootId, scene);
        List<UserDepartmentDO> subDepartmentList = userDepartmentRepository.list(Wrappers.lambdaQuery(UserDepartmentDO.class)
                .in(UserDepartmentDO::getDepartmentId, subDepartmentIds));

        if (CollectionUtils.isNotEmpty(subDepartmentList)) {
            List<String> allUserNos = subDepartmentList.stream()
                    .map(UserDepartmentDO::getUserNo)
                    .distinct()
                    .collect(Collectors.toList());

            dataAccessDTO.setSupportQueryUserNos(allUserNos);
            dataAccessDTO.setSupportQueryDepartmentIds(Lists.newArrayList(subDepartmentIds));
        } else {
            dataAccessDTO.setSupportQueryUserNos(Lists.newArrayList(userNo));
        }
    }

    /**
     * 获取缓存的用户角色关联关系
     */
    @SuppressWarnings("unchecked")
    private List<UserRoleDO> getCachedUserRoles(String userNo) {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return queryUserRoles(userNo);
        }

        String cacheKey = CACHE_KEY_USER_ROLES + userNo;
        List<UserRoleDO> cachedRoles = (List<UserRoleDO>) requestAttributes.getAttribute(cacheKey, RequestAttributes.SCOPE_REQUEST);

        if (cachedRoles == null) {
            cachedRoles = queryUserRoles(userNo);
            requestAttributes.setAttribute(cacheKey, cachedRoles, RequestAttributes.SCOPE_REQUEST);
        }

        return cachedRoles;
    }

    /**
     * 获取缓存的用户部门关联关系
     */
    @SuppressWarnings("unchecked")
    private List<UserDepartmentDO> getCachedUserDepartments(String userNo) {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return queryUserDepartments(userNo);
        }

        String cacheKey = CACHE_KEY_USER_DEPARTMENTS + userNo;
        List<UserDepartmentDO> cachedDepartments = (List<UserDepartmentDO>) requestAttributes.getAttribute(cacheKey, RequestAttributes.SCOPE_REQUEST);

        if (cachedDepartments == null) {
            cachedDepartments = queryUserDepartments(userNo);
            requestAttributes.setAttribute(cacheKey, cachedDepartments, RequestAttributes.SCOPE_REQUEST);
        }

        return cachedDepartments;
    }

    /**
     * 获取缓存的角色列表
     */
    @SuppressWarnings("unchecked")
    private List<RoleDO> getCachedRoleList(String userNo) {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return queryRoleList(userNo);
        }

        String cacheKey = CACHE_KEY_ROLE_LIST + userNo;
        List<RoleDO> cachedRoles = (List<RoleDO>) requestAttributes.getAttribute(cacheKey, RequestAttributes.SCOPE_REQUEST);

        if (cachedRoles == null) {
            cachedRoles = queryRoleList(userNo);
            requestAttributes.setAttribute(cacheKey, cachedRoles, RequestAttributes.SCOPE_REQUEST);
        }

        return cachedRoles;
    }

    /**
     * 查询用户角色关联关系
     */
    private List<UserRoleDO> queryUserRoles(String userNo) {
        return userRoleRepository.list(Wrappers.lambdaQuery(UserRoleDO.class)
                .eq(UserRoleDO::getUserNo, userNo));
    }

    /**
     * 查询用户部门关联关系
     */
    private List<UserDepartmentDO> queryUserDepartments(String userNo) {
        return userDepartmentRepository.list(Wrappers.lambdaQuery(UserDepartmentDO.class)
                .eq(UserDepartmentDO::getUserNo, userNo));
    }

    /**
     * 查询角色列表
     */
    private List<RoleDO> queryRoleList(String userNo) {
        List<UserRoleDO> userRoles = getCachedUserRoles(userNo);

        if (CollUtil.isEmpty(userRoles)) {
            LogUtil.error("当前用户关联的角色不存在，userNo={}", userNo);
            throw new BizException(BizErrorCode.USER_ROLE_NOT_EXIST.getCode(), BizErrorCode.USER_ROLE_NOT_EXIST.getDesc());
        }

        List<String> roleCodes = userRoles.stream()
                .map(UserRoleDO::getRoleCode)
                .collect(Collectors.toList());

        return roleRepository.list(Wrappers.lambdaQuery(RoleDO.class)
                .in(RoleDO::getRoleCode, roleCodes));
    }
}
