package com.deepinnet.infra.service.util;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.infra.api.constants.DataScopeConstants;
import com.deepinnet.infra.api.dto.DataAccessDTO;
import com.deepinnet.infra.dal.dataobject.*;
import com.deepinnet.infra.dal.mapper.DepartmentMapper;
import com.deepinnet.infra.dal.repository.*;
import com.deepinnet.infra.service.UserService;
import com.deepinnet.infra.service.enums.PermissionSystemEnum;
import com.deepinnet.infra.service.error.BizErrorCode;
import com.google.common.collect.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wong
 * @create 2025/7/25 17:49
 * @Description
 */
@Component
public class DataPermissionProcessor {

    @Resource
    private UserDepartmentRepository userDepartmentRepository;

    @Resource
    private DepartmentRepository departmentRepository;

    @Resource
    private UserRoleRepository userRoleRepository;

    @Resource
    private RoleRepository roleRepository;

    @Resource
    private UserService userService;

    @Resource
    private DepartmentMapper departmentMapper;

    public DepartmentDO getRootDepartment(String userNo) {
        Long rootId = getRootDepartmentId(userNo);
        return departmentRepository.getById(rootId);
    }

    public Long getRootDepartmentId(String userNo) {
        UserDepartmentDO userDepartment = userDepartmentRepository.getOne(Wrappers.lambdaQuery(UserDepartmentDO.class)
                .eq(UserDepartmentDO::getUserNo, userNo));
        if (userDepartment == null) {
            throw new BizException(BizErrorCode.USER_DEPARTMENT_NOT_FOUND.getCode(), BizErrorCode.USER_DEPARTMENT_NOT_FOUND.getDesc());
        }

        DepartmentDO departmentDO = departmentRepository.getById(userDepartment.getDepartmentId());
        if (departmentDO == null) {
            throw new BizException(BizErrorCode.DEPARTMENT_NOT_FOUND.getCode(), BizErrorCode.DEPARTMENT_NOT_FOUND.getDesc());
        }

        String fullPath = departmentDO.getFullPath();
        List<Long> departmentIds = JSONUtil.toList(fullPath, Long.class);
        departmentIds.remove(-1L);

        return departmentIds.get(0);
    }

    public void checkDataPermission(Long departmentId) {
        if (isSuperAdmin()) {
            return;
        }

        List<RoleDO> roleList = getRoleDOList();

        // 存在dataScope为null的角色，说明是老系统，无需数据权限
        boolean hasNoDataScopeRole = roleList.stream()
                .anyMatch(r -> r.getDataScope() == null);
        if (hasNoDataScopeRole) {
            return;
        }

        if (CollUtil.isEmpty(roleList)) {
            throw new BizException(BizErrorCode.NO_PERMISSION_TO_OPERATE.getCode(), BizErrorCode.NO_PERMISSION_TO_OPERATE.getDesc());
        }

        DataAccessDTO availableQueryData = userService.getAvailableQueryData();
        if (availableQueryData == null) {
            throw new BizException(BizErrorCode.NO_PERMISSION_TO_OPERATE.getCode(), BizErrorCode.NO_PERMISSION_TO_OPERATE.getDesc());
        }

        List<String> supportQueryUserNos = availableQueryData.getSupportQueryUserNos();
        if (CollUtil.isNotEmpty(supportQueryUserNos) && supportQueryUserNos.contains(DataScopeConstants.SUPER_ADMIN)) {
            return;
        }

        List<Long> supportQueryDepartmentIds = availableQueryData.getSupportQueryDepartmentIds();
        if (CollUtil.isNotEmpty(supportQueryDepartmentIds) && supportQueryDepartmentIds.contains(departmentId)) {
            return;
        }

        throw new BizException(BizErrorCode.NO_PERMISSION_TO_OPERATE.getCode(), BizErrorCode.NO_PERMISSION_TO_OPERATE.getDesc());
    }

    public DataAccessDTO getAvailableQueryData() {
        return processWithDataAccessPermissionSystem();
    }

    private DataAccessDTO processWithDataAccessPermissionSystem() {
        DataAccessDTO dataAccessDTO = new DataAccessDTO();

        String userNo = (String) StpUtil.getExtra("userNo");
        Boolean isSuperAdminTag = isSuperAdmin();

        // 超级管理员特殊处理，能查看所有的数据
        if (isSuperAdminTag || DataScopeConstants.SUPER_ADMIN_NOS.contains(userNo)) {
            dataAccessDTO.setSupportQueryUserNos(Lists.newArrayList(DataScopeConstants.SUPER_ADMIN));
            return dataAccessDTO;
        }

        // 查询用户的所有角色
        List<UserRoleDO> userRoles = userRoleRepository.list(Wrappers.lambdaQuery(UserRoleDO.class)
                .eq(UserRoleDO::getUserNo, userNo));

        if (CollectionUtils.isEmpty(userRoles)) {
            // 用户没有角色，只能查看个人数据
            dataAccessDTO.setSupportQueryUserNos(Lists.newArrayList(userNo));
            return dataAccessDTO;
        }

        // 查询角色信息，计算最高数据权限作用域（功能权限取并集，数据权限作用域取最高）
        List<String> roleCodes = userRoles.stream()
                .map(UserRoleDO::getRoleCode)
                .collect(Collectors.toList());

        List<RoleDO> roles = roleRepository.list(Wrappers.lambdaQuery(RoleDO.class)
                .in(RoleDO::getRoleCode, roleCodes));

        if (CollectionUtils.isEmpty(roles)) {
            // 角色不存在，只能查看个人数据
            dataAccessDTO.setSupportQueryUserNos(Lists.newArrayList(userNo));
            return dataAccessDTO;
        }

        // 计算最高数据权限作用域（取最大值）
        Integer maxDataScope = roles.stream()
                .filter(role -> role.getDataScope() != null)
                .mapToInt(RoleDO::getDataScope)
                .max()
                .orElse(1); // 默认为个人数据权限

        // 根据数据权限作用域返回可查询的数据范围
        return buildDataAccessByScope(maxDataScope, userNo, dataAccessDTO);
    }

    public PermissionSystemEnum routeToPermissionSystem() {
        List<RoleDO> roleList = getRoleDOList();

        // 存在dataScope为null的角色，说明是老系统，无需数据权限
        boolean hasNoDataScopeRole = roleList.stream()
                .anyMatch(r -> r.getDataScope() == null);
        if (hasNoDataScopeRole) {
            return PermissionSystemEnum.NO_DATA_PERMISSION;
        }

        return PermissionSystemEnum.NEED_DATA_PERMISSION;
    }

    private Boolean isSuperAdmin() {
        Boolean isSuperAdminTag = false;
        Object isSuperAdmin = StpUtil.getExtra("isSuperAdmin");
        if (ObjectUtil.isNotNull(isSuperAdmin)) {
            isSuperAdminTag = (Boolean) StpUtil.getExtra("isSuperAdmin");
        }

        return isSuperAdminTag;
    }

    private List<RoleDO> getRoleDOList() {
        String userNo = (String) StpUtil.getExtra("userNo");
        List<UserRoleDO> userRoles = userRoleRepository.list(Wrappers.lambdaQuery(UserRoleDO.class)
                .eq(UserRoleDO::getUserNo, userNo));

        if (CollUtil.isEmpty(userRoles)) {
            LogUtil.error("当前用户关联的角色不存在，userNo={}", userNo);
            throw new BizException(BizErrorCode.USER_ROLE_NOT_EXIST.getCode(), BizErrorCode.USER_ROLE_NOT_EXIST.getDesc());
        }

        List<String> roleCodes = userRoles.stream()
                .map(UserRoleDO::getRoleCode)
                .collect(Collectors.toList());

        return roleRepository.list(Wrappers.lambdaQuery(RoleDO.class)
                .in(RoleDO::getRoleCode, roleCodes));
    }

    /**
     * 根据数据权限作用域构建数据访问权限
     */
    private DataAccessDTO buildDataAccessByScope(Integer dataScope, String userNo, DataAccessDTO dataAccessDTO) {
        // 全局数据：查看所有数据
        if (dataScope == 4) {
            dataAccessDTO.setSupportQueryUserNos(Lists.newArrayList(DataScopeConstants.SUPER_ADMIN));
            dataAccessDTO.setDataScope(4);
        } else {
            // 组织数据（本组织全局）：查看本部门的所有数据
            buildCurrentDepartmentAllData(userNo, dataAccessDTO);
            dataAccessDTO.setDataScope(3);
        }

        return dataAccessDTO;
    }

    /**
     * 构建所有部门数据访问权限
     */
    private void buildCurrentDepartmentAllData(String userNo, DataAccessDTO dataAccessDTO) {
        String scene = (String) StpUtil.getExtra("scene");

        List<UserDepartmentDO> userDepartmentDOs = userDepartmentRepository.list(Wrappers.lambdaQuery(UserDepartmentDO.class)
                .eq(UserDepartmentDO::getUserNo, userNo));

        if (CollectionUtils.isEmpty(userDepartmentDOs)) {
            dataAccessDTO.setSupportQueryUserNos(Lists.newArrayList(userNo));
            return;
        }

        // 根据部门的full_path路径找到当前部门的根节点的id
        List<Long> departmentIds = userDepartmentDOs.stream()
                .map(UserDepartmentDO::getDepartmentId)
                .collect(Collectors.toList());

        List<DepartmentDO> departmentDOs = departmentRepository.listByIds(departmentIds);
        List<Long> rootDepartmentIds = Lists.newArrayList();

        departmentDOs.forEach(department -> {
            List<Long> parentDepartmentList = JSONUtil.toList(department.getFullPath(), Long.class);
            Long rootId = parentDepartmentList.get(0);
            if (rootId == -1) {
                rootId = parentDepartmentList.get(1);
            }

            rootDepartmentIds.add(rootId);
        });

        // 根据根节点id，获取这棵子树上所有部门id
        Set<Long> allSubDepartmentIds = Sets.newHashSet();
        rootDepartmentIds.forEach(rootId -> {
            List<Long> currentSubDepartmentIds = departmentMapper.getAllSubDepartmentIds(rootId, scene);
            allSubDepartmentIds.addAll(currentSubDepartmentIds);
        });

        List<UserDepartmentDO> allUserDepartments = userDepartmentRepository.list(Wrappers.lambdaQuery(UserDepartmentDO.class)
                .in(UserDepartmentDO::getDepartmentId, allSubDepartmentIds));

        if (CollectionUtils.isNotEmpty(allUserDepartments)) {
            List<String> allUserNos = allUserDepartments.stream()
                    .map(UserDepartmentDO::getUserNo)
                    .distinct()
                    .collect(Collectors.toList());

            dataAccessDTO.setSupportQueryUserNos(allUserNos);
            dataAccessDTO.setSupportQueryDepartmentIds(Lists.newArrayList(allSubDepartmentIds));
        } else {
            dataAccessDTO.setSupportQueryUserNos(Lists.newArrayList(userNo));
        }
    }
}
