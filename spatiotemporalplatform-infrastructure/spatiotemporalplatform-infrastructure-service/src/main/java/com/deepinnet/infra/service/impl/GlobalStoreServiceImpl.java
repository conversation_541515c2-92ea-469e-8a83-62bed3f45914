package com.deepinnet.infra.service.impl;

import cn.hutool.core.util.*;
import com.aliyun.oss.model.OSSObject;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.service.GlobalStoreService;
import com.deepinnet.infra.service.client.OssClient;
import com.deepinnet.infra.service.error.BizErrorCode;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 2022/11/7 10:30
 */

@RequiredArgsConstructor
@Service
public class GlobalStoreServiceImpl implements GlobalStoreService {

    private final OssClient ossClient;

    @Override
    public Result<OssStore> putObject(StoreUploadDTO storePutDTO) {
        try {
            String bucketName = getBucketName(storePutDTO.getBucketType());
            String eTag = ossClient.putObject(bucketName, storePutDTO.getKey(), storePutDTO.getInputStream(), storePutDTO.getExpireDate());
            OssStore build = OssStore.builder().etag(eTag).key(storePutDTO.getKey()).build();
            return Result.success(build);
        } catch (Exception e) {
            return Result.fail(BizErrorCode.STORE_ERROR.getCode(), BizErrorCode.STORE_ERROR.getDesc());
        }
    }

    private String getBucketName(Integer bucketType) {
        switch (bucketType) {
            case 0:
                return "deepinnet-tp-public";
            case 1:
                return "deepinnet-tp-private";
            default:
                return null;
        }
    }

    @Override
    public Result<String> getObjectUrl(StoreQueryDTO StoreQueryDTO) {
        try {
            String bucketName = getBucketName(StoreQueryDTO.getBucketType());
            String url = ossClient.getUrl(bucketName, StoreQueryDTO.getKey());
            if (StrUtil.isBlank(url)) {
                return Result.fail(BizErrorCode.FILE_QUERY_ERROR.getCode(), BizErrorCode.FILE_QUERY_ERROR.getDesc());
            }
            return Result.success(url);
        } catch (Exception e) {
            LogUtil.error(BizErrorCode.FILE_QUERY_ERROR.getCode(), e);
            return Result.fail(BizErrorCode.FILE_QUERY_ERROR.getCode(), BizErrorCode.FILE_QUERY_ERROR.getDesc());
        }
    }

    @Override
    public Result<String> getObjectUrlWithExpireTime(StoreQueryDTO StoreQueryDTO) {
        try {
            String bucketName = getBucketName(StoreQueryDTO.getBucketType());
            String url = ossClient.getUrlWithExpireTime(bucketName, StoreQueryDTO.getKey(), StoreQueryDTO.getExpirationMillisecond());
            if (StrUtil.isBlank(url)) {
                return Result.fail(BizErrorCode.FILE_QUERY_ERROR.getCode(), BizErrorCode.FILE_QUERY_ERROR.getDesc());
            }
            return Result.success(url);
        } catch (Exception e) {
            LogUtil.error(BizErrorCode.FILE_QUERY_ERROR.getCode(), e);
            return Result.fail(BizErrorCode.FILE_QUERY_ERROR.getCode(), BizErrorCode.FILE_QUERY_ERROR.getDesc());
        }
    }

    @Override
    public Result<OssObject> getObjectDetail(StoreQueryDTO StoreQueryDTO) {
        try {
            String bucketName = getBucketName(StoreQueryDTO.getBucketType());
            OSSObject ossObject = ossClient.getObjectDetail(bucketName, StoreQueryDTO.getKey());
            if (ObjectUtil.isNull(ossObject)) {
                return Result.fail(BizErrorCode.FILE_QUERY_ERROR.getCode(), BizErrorCode.FILE_QUERY_ERROR.getDesc());
            }
            return Result.success(OssObject.builder()
                    .bucketName(ossObject.getBucketName())
                    .contentType(ossObject.getObjectMetadata().getContentType())
                    .contentLength(ossObject.getObjectMetadata().getContentLength())
                    .contentMd5(ossObject.getObjectMetadata().getContentMD5())
                    .eTag(ossObject.getObjectMetadata().getETag())
                    .key(ossObject.getKey())
                    .build());
        } catch (Exception e) {
            LogUtil.error(BizErrorCode.FILE_QUERY_ERROR.getCode(), e);
            return Result.fail(BizErrorCode.FILE_QUERY_ERROR.getCode(), BizErrorCode.FILE_QUERY_ERROR.getDesc());
        }
    }

    @Override
    public Result<Boolean> exists(StoreQueryDTO StoreQueryDTO) {
        try {
            String bucketName = getBucketName(StoreQueryDTO.getBucketType());
            return Result.success(ossClient.exists(bucketName, StoreQueryDTO.getKey()));
        } catch (Exception e) {
            LogUtil.error(BizErrorCode.FILE_QUERY_ERROR.getCode(), e);
            return Result.fail(BizErrorCode.FILE_QUERY_ERROR.getCode(), BizErrorCode.FILE_QUERY_ERROR.getDesc());
        }
    }
}
