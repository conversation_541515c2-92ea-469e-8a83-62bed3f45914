package com.deepinnet.infra.service;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.api.vo.CustomerInfoVO;

/**
 * 客户单位信息服务接口
 *
 * <AUTHOR> wong
 * @create 2025/04/22
 */
public interface ProductCustomerService {

    /**
     * 保存客户单位信息
     *
     * @param customerInfoDTO 客户单位信息
     * @return 处理结果
     */
    Result<String> saveCustomerInfo(CustomerInfoDTO customerInfoDTO);

    /**
     * 获取客户单位详情
     *
     * @return 客户单位详情
     */
    CustomerDetailDTO getCustomerDetail(CustomerQueryDetailDTO queryDetailDTO);

    /**
     * 分页查询客户列表
     *
     * @param queryDTO 查询参数
     * @return 客户列表分页数据
     */
    Result<CommonPage<CustomerInfoVO>> pageQueryCustomers(CustomerQueryDTO queryDTO);
} 