package com.deepinnet.infra.service.strategy;

import com.deepinnet.infra.api.dto.UserLoginDTO;
import com.deepinnet.infra.api.dto.UserLoginSuccessDTO;
import com.deepinnet.infra.dal.dataobject.AccountDO;

/**
 * 登录策略接口
 */
public interface LoginStrategy {

    /**
     * 判断是否支持当前登录场景
     *
     * @param scene 登录场景
     * @return 是否支持
     */
    boolean supports(String scene);

    /**
     * 获取账号信息
     *
     * @param loginDTO 登录参数
     * @return 账号信息
     */
    AccountDO getAccount(UserLoginDTO loginDTO);

    /**
     * 验证登录信息
     *
     * @param loginDTO 登录参数
     * @param account  账号信息
     */
    void validateLogin(UserLoginDTO loginDTO, AccountDO account);

    /**
     * 处理登录
     *
     * @param loginDTO 登录参数
     * @param account  账号信息
     * @return 登录成功信息
     */
    UserLoginSuccessDTO processLogin(UserLoginDTO loginDTO, AccountDO account);
} 