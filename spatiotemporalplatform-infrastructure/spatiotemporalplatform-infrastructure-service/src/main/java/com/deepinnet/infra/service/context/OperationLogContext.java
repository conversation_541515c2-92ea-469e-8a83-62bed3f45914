package com.deepinnet.infra.service.context;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 操作日志上下文
 * 用于在方法执行过程中传递额外的日志信息
 * 
 * <AUTHOR> wong
 * @create 2025/7/31
 */
@Data
public class OperationLogContext {

    private static final ThreadLocal<OperationLogContext> CONTEXT = new ThreadLocal<>();

    /**
     * 额外的上下文数据，可以在SpEL表达式中使用
     */
    private Map<String, Object> contextData = new HashMap<>();

    /**
     * 操作详情（可以在方法执行过程中动态设置）
     */
    private String operationDetail;

    /**
     * 操作结果
     */
    private String operationResult;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 获取当前线程的上下文
     */
    public static OperationLogContext getCurrentContext() {
        OperationLogContext context = CONTEXT.get();
        if (context == null) {
            context = new OperationLogContext();
            CONTEXT.set(context);
        }
        return context;
    }

    /**
     * 设置上下文数据
     */
    public static void setContextData(String key, Object value) {
        getCurrentContext().getContextData().put(key, value);
    }

    /**
     * 获取上下文数据
     */
    public static Object getContextData(String key) {
        return getCurrentContext().getContextData().get(key);
    }

    /**
     * 设置操作详情
     */
    public static void setOperationDetail(String detail) {
        getCurrentContext().setOperationDetail(detail);
    }

    /**
     * 设置操作结果
     */
    public static void setOperationResult(String result) {
        getCurrentContext().setOperationResult(result);
    }

    /**
     * 设置错误信息
     */
    public static void setErrorMessage(String errorMessage) {
        getCurrentContext().setErrorMessage(errorMessage);
    }

    /**
     * 清除当前线程的上下文
     */
    public static void clear() {
        CONTEXT.remove();
    }
}
