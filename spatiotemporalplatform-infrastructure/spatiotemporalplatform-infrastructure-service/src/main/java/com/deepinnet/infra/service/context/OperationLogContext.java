package com.deepinnet.infra.service.context;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 操作日志上下文
 * 用于在方法执行过程中设置模板变量
 *
 * <AUTHOR> wong
 * @create 2025/7/31
 */
@Data
public class OperationLogContext {

    private static final ThreadLocal<OperationLogContext> CONTEXT = new ThreadLocal<>();

    /**
     * 模板变量数据，用于填充预定义模板中的占位符
     */
    private Map<String, Object> variables = new HashMap<>();

    /**
     * 操作结果
     */
    private String operationResult;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 获取当前线程的上下文
     */
    public static OperationLogContext getCurrentContext() {
        OperationLogContext context = CONTEXT.get();
        if (context == null) {
            context = new OperationLogContext();
            CONTEXT.set(context);
        }
        return context;
    }

    /**
     * 设置模板变量
     */
    public static void setVariable(String key, Object value) {
        getCurrentContext().getVariables().put(key, value);
    }

    /**
     * 批量设置模板变量
     */
    public static void setVariables(Map<String, Object> variables) {
        getCurrentContext().getVariables().putAll(variables);
    }

    /**
     * 获取模板变量
     */
    public static Object getVariable(String key) {
        return getCurrentContext().getVariables().get(key);
    }

    /**
     * 获取所有模板变量
     */
    public static Map<String, Object> getAllVariables() {
        return getCurrentContext().getVariables();
    }

    /**
     * 设置操作结果
     */
    public static void setOperationResult(String result) {
        getCurrentContext().setOperationResult(result);
    }

    /**
     * 设置错误信息
     */
    public static void setErrorMessage(String errorMessage) {
        getCurrentContext().setErrorMessage(errorMessage);
    }

    /**
     * 清除当前线程的上下文
     */
    public static void clear() {
        CONTEXT.remove();
    }
}
