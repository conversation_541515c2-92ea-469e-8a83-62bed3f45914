package com.deepinnet.infra.service.enums;

/**
 * 操作日志模板枚举
 * 预定义所有操作日志的文案模板和所需变量
 * 
 * <AUTHOR> wong
 * @create 2025/7/31
 */
public enum OperationLogTemplateEnum {

    // ========== 登录相关 ==========
    LOGIN_SUCCESS("登录", "登录", "登录成功"),
    LOGIN_FAILED("登录", "登录", "登录失败"),
    LOGOUT("登录", "退出", "退出登录"),

    // ========== 成员管理 ==========
    MEMBER_CREATE("成员管理", "新增", "新增了成员【{memberName}】", "memberName"),
    MEMBER_UPDATE("成员管理", "编辑", "编辑了成员【{memberName}】", "memberName"),
    MEMBER_DELETE("成员管理", "删除", "删除了成员【{memberName}】", "memberName"),
    MEMBER_BATCH_DELETE("成员管理", "删除", "批量删除了 {count} 个成员", "count"),
    MEMBER_ENABLE("成员管理", "启用", "启用了成员【{memberName}】", "memberName"),
    MEMBER_DISABLE("成员管理", "禁用", "禁用了成员【{memberName}】", "memberName"),
    MEMBER_RESET_PASSWORD("成员管理", "重置密码", "重置了成员【{memberName}】的密码", "memberName"),

    // ========== 角色管理 ==========
    ROLE_CREATE("角色管理", "新增", "新增了角色【{roleName}】", "roleName"),
    ROLE_UPDATE("角色管理", "编辑", "编辑了角色【{roleName}】", "roleName"),
    ROLE_DELETE("角色管理", "删除", "删除了角色【{roleName}】", "roleName"),
    ROLE_ASSIGN_PERMISSION("角色管理", "授权", "为角色【{roleName}】分配了权限【{permissions}】", "roleName", "permissions"),
    ROLE_REVOKE_PERMISSION("角色管理", "撤销权限", "撤销了角色【{roleName}】的权限【{permissions}】", "roleName", "permissions"),

    // ========== 部门管理 ==========
    DEPARTMENT_CREATE("部门管理", "新增", "新增了部门【{departmentName}】", "departmentName"),
    DEPARTMENT_UPDATE("部门管理", "编辑", "编辑了部门【{departmentName}】", "departmentName"),
    DEPARTMENT_DELETE("部门管理", "删除", "删除了部门【{departmentName}】", "departmentName"),
    DEPARTMENT_MOVE("部门管理", "移动", "将部门【{departmentName}】移动到【{parentDepartmentName}】下", "departmentName", "parentDepartmentName"),

    // ========== 飞行规划 ==========
    FLIGHT_PLAN_CREATE("飞行规划", "新增", "新增了飞行规划【{planName}】", "planName"),
    FLIGHT_PLAN_UPDATE("飞行规划", "编辑", "编辑了飞行规划【{planName}】", "planName"),
    FLIGHT_PLAN_DELETE("飞行规划", "删除", "删除了飞行规划【{planName}】", "planName"),
    FLIGHT_PLAN_SUBMIT("飞行规划", "提交", "提交了飞行规划【{planName}】", "planName"),
    FLIGHT_PLAN_APPROVE("飞行规划", "审批通过", "审批通过了飞行规划【{planName}】", "planName"),
    FLIGHT_PLAN_REJECT("飞行规划", "审批拒绝", "审批拒绝了飞行规划【{planName}】，原因：{reason}", "planName", "reason"),
    FLIGHT_PLAN_WITHDRAW("飞行规划", "撤回", "撤回了飞行规划【{planName}】", "planName"),
    FLIGHT_PLAN_PUBLISH("飞行规划", "发布", "发布了飞行规划【{planName}】", "planName"),
    FLIGHT_PLAN_CANCEL("飞行规划", "取消", "取消了飞行规划【{planName}】", "planName"),

    // ========== 飞行需求 ==========
    FLIGHT_DEMAND_CREATE("飞行需求", "新增", "新增了飞行需求【{demandName}】", "demandName"),
    FLIGHT_DEMAND_UPDATE("飞行需求", "编辑", "编辑了飞行需求【{demandName}】", "demandName"),
    FLIGHT_DEMAND_DELETE("飞行需求", "删除", "删除了飞行需求【{demandName}】", "demandName"),
    FLIGHT_DEMAND_SUBMIT("飞行需求", "提交", "提交了飞行需求【{demandName}】", "demandName"),

    // ========== 规划周期 ==========
    PLANNING_CYCLE_CREATE("规划周期", "新增", "新增了规划周期【{cycleName}】", "cycleName"),
    PLANNING_CYCLE_UPDATE("规划周期", "编辑", "编辑了规划周期【{cycleName}】", "cycleName"),
    PLANNING_CYCLE_DELETE("规划周期", "删除", "删除了规划周期【{cycleName}】", "cycleName"),
    PLANNING_CYCLE_RENAME("规划周期", "重命名", "将规划周期【{oldName}】重命名为【{newName}】", "oldName", "newName"),

    // ========== 系统配置 ==========
    SYSTEM_CONFIG_UPDATE("系统配置", "编辑", "修改了系统配置【{configName}】", "configName"),
    SYSTEM_BACKUP("系统管理", "备份", "执行了系统备份"),
    SYSTEM_RESTORE("系统管理", "恢复", "执行了系统恢复"),

    // ========== 数据管理 ==========
    DATA_EXPORT("数据管理", "导出", "导出了【{dataType}】数据", "dataType"),
    DATA_IMPORT("数据管理", "导入", "导入了【{dataType}】数据，共 {count} 条", "dataType", "count"),
    DATA_SYNC("数据管理", "同步", "同步了【{dataType}】数据", "dataType"),

    // ========== 组织管理 ==========
    ORG_CREATE("组织管理", "新增", "新增了组织【{orgName}】", "orgName"),
    ORG_UPDATE("组织管理", "编辑", "编辑了组织【{orgName}】", "orgName"),
    ORG_DELETE("组织管理", "删除", "删除了组织【{orgName}】", "orgName");

    private final String module;
    private final String type;
    private final String template;
    private final String[] requiredVariables;

    OperationLogTemplateEnum(String module, String type, String template, String... requiredVariables) {
        this.module = module;
        this.type = type;
        this.template = template;
        this.requiredVariables = requiredVariables;
    }

    public String getModule() {
        return module;
    }

    public String getType() {
        return type;
    }

    public String getTemplate() {
        return template;
    }

    public String[] getRequiredVariables() {
        return requiredVariables;
    }

    /**
     * 获取模板所需的变量列表（用于开发时提示）
     */
    public String getRequiredVariablesInfo() {
        if (requiredVariables.length == 0) {
            return "无需变量";
        }
        return "需要变量: " + String.join(", ", requiredVariables);
    }
}
