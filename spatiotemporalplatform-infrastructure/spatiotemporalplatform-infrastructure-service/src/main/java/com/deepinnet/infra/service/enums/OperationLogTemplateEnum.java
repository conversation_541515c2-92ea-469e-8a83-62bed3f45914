package com.deepinnet.infra.service.enums;

import lombok.*;

/**
 * 操作日志模板枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OperationLogTemplateEnum {

    // 成员管理
    MEMBER_ADD("MEMBER_ADD", "成员管理", "在【{organizationName}】添加了成员【{memberName}】"),
    MEMBER_DELETE("MEMBER_DELETE", "成员管理", "在【{organizationName}】删除了成员【{memberName}】"),
    MEMBER_EDIT("MEMBER_EDIT", "成员管理", "在【{organizationName}】编辑了成员【{memberName}】"),

    // 账号管理
    ACCOUNT_ADD("ACCOUNT_ADD", "账号管理", "添加了【{memberName}】的管理账号【{account}】"),
    ACCOUNT_DELETE("ACCOUNT_DELETE", "账号管理", "删除了【{memberName}】的管理账号【{account}】"),
    ACCOUNT_FREEZE("ACCOUNT_FREEZE", "账号管理", "冻结了【{memberName}】的管理账号【{account}】"),
    ACCOUNT_UNFREEZE("ACCOUNT_UNFREEZE", "账号管理", "解冻了【{memberName}】的管理账号【{account}】"),
    ACCOUNT_EDIT("ACCOUNT_EDIT", "账号管理", "编辑了【{memberName}】的管理账号【{account}】"),

    // 角色管理
    ROLE_ADD("ROLE_ADD", "角色管理", "在【{organizationName}】添加了角色【{roleName}】"),
    ROLE_DELETE("ROLE_DELETE", "角色管理", "在【{organizationName}】删除了角色【{roleName}】"),
    ROLE_EDIT("ROLE_EDIT", "角色管理", "在【{organizationName}】编辑了角色【{roleName}】"),

    // 组织管理
    ORGANIZATION_ADD("ORGANIZATION_ADD", "组织管理", "新增了组织【{organizationName}】"),
    ORGANIZATION_DELETE("ORGANIZATION_DELETE", "组织管理", "删除了组织【{organizationName}】"),
    ORGANIZATION_EDIT("ORGANIZATION_EDIT", "组织管理", "将组织【{originalOrganizationName}】编辑成了【{organizationName}】"),

    // 登录
    LOGIN_SUCCESS("LOGIN_SUCCESS", "登录", "【{name}({account})】登录成功"),
    LOGOUT_SUCCESS("LOGOUT_SUCCESS", "登录", "【{name}({account})】退出成功"),

    // 信息管理
    PROFILE_UPDATE("PROFILE_UPDATE", "信息管理", "【{name}({account})】更新了个人信息");

    /**
     * 模板代码
     */
    private final String code;

    /**
     * 操作模块
     */
    private final String module;

    /**
     * 操作详情模板
     */
    private final String template;

    /**
     * 根据代码获取枚举
     */
    public static OperationLogTemplateEnum getByCode(String code) {
        for (OperationLogTemplateEnum template : values()) {
            if (template.getCode().equals(code)) {
                return template;
            }
        }
        return null;
    }
}
