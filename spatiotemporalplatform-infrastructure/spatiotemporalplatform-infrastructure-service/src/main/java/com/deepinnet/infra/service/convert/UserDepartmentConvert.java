package com.deepinnet.infra.service.convert;

import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.dal.dataobject.*;
import org.mapstruct.*;

import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2025/4/22 09:50
 * @Description
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface UserDepartmentConvert {

    UserDepartmentDTO convertToUserDepartmentDTO(UserDepartmentDO userDepartmentDO);

    List<UserDepartmentDTO> convertToUserDepartmentDTOs(List<UserDepartmentDO> userDepartmentDOs);
}
