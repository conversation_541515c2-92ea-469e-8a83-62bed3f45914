package com.deepinnet.infra.service;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.infra.api.dto.*;

/**
 * <AUTHOR> wong
 * @create 2024/8/8 16:04
 * @Description
 */
public interface AdminService {
    Boolean deleteAccount(String accountNo);

    CommonPage<UserInfoDTO> pageQueryUserAccount(UserAccountQueryDTO queryDTO);

    Boolean saveOrUpdateAccount(UserAccountSaveDTO saveDTO);

    AccountDTO getAccountDetail(String accountNo);
}
