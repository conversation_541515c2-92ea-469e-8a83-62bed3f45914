package com.deepinnet.infra.service.util;

import com.deepinnet.infra.service.context.OperationLogContext;

import java.util.Map;

/**
 * 操作日志工具类
 * 提供更友好的API来记录操作日志
 * 
 * <AUTHOR> wong
 * @create 2025/7/31
 */
public class OperationLogUtil {

    /**
     * 设置操作详情（会覆盖注解中的detail）
     * 
     * @param detail 操作详情
     */
    public static void setDetail(String detail) {
        OperationLogContext.setOperationDetail(detail);
    }

    /**
     * 设置操作详情（支持占位符）
     * 
     * @param template 详情模板，如："新增了成员【{name}】"
     * @param params 参数映射
     */
    public static void setDetail(String template, Map<String, Object> params) {
        String detail = SpELUtil.parseTemplate(template, params);
        OperationLogContext.setOperationDetail(detail);
    }

    /**
     * 设置操作详情（支持单个参数）
     * 
     * @param template 详情模板，如："新增了成员【{name}】"
     * @param paramName 参数名
     * @param paramValue 参数值
     */
    public static void setDetail(String template, String paramName, Object paramValue) {
        Map<String, Object> params = Map.of(paramName, paramValue);
        setDetail(template, params);
    }

    /**
     * 设置操作详情（支持两个参数）
     */
    public static void setDetail(String template, String param1Name, Object param1Value, 
                               String param2Name, Object param2Value) {
        Map<String, Object> params = Map.of(param1Name, param1Value, param2Name, param2Value);
        setDetail(template, params);
    }

    /**
     * 设置操作详情（支持三个参数）
     */
    public static void setDetail(String template, String param1Name, Object param1Value, 
                               String param2Name, Object param2Value,
                               String param3Name, Object param3Value) {
        Map<String, Object> params = Map.of(
            param1Name, param1Value, 
            param2Name, param2Value,
            param3Name, param3Value
        );
        setDetail(template, params);
    }

    /**
     * 添加上下文数据（用于SpEL表达式）
     * 
     * @param key 键
     * @param value 值
     */
    public static void addContext(String key, Object value) {
        OperationLogContext.setContextData(key, value);
    }

    /**
     * 批量添加上下文数据
     * 
     * @param contextData 上下文数据
     */
    public static void addContext(Map<String, Object> contextData) {
        contextData.forEach(OperationLogContext::setContextData);
    }

    /**
     * 标记操作成功
     */
    public static void success() {
        OperationLogContext.setOperationResult("SUCCESS");
    }

    /**
     * 标记操作成功并设置详情
     * 
     * @param detail 操作详情
     */
    public static void success(String detail) {
        success();
        setDetail(detail);
    }

    /**
     * 标记操作失败
     * 
     * @param errorMessage 错误信息
     */
    public static void failure(String errorMessage) {
        OperationLogContext.setOperationResult("FAILURE");
        OperationLogContext.setErrorMessage(errorMessage);
    }

    /**
     * 标记操作失败并设置详情
     * 
     * @param detail 操作详情
     * @param errorMessage 错误信息
     */
    public static void failure(String detail, String errorMessage) {
        failure(errorMessage);
        setDetail(detail);
    }

    // ========== 便捷方法：常用操作类型 ==========

    /**
     * 记录新增操作
     * 
     * @param itemName 新增的项目名称
     */
    public static void logCreate(String itemName) {
        setDetail("新增了【{name}】", "name", itemName);
    }

    /**
     * 记录编辑操作
     * 
     * @param itemName 编辑的项目名称
     */
    public static void logUpdate(String itemName) {
        setDetail("编辑了【{name}】", "name", itemName);
    }

    /**
     * 记录删除操作
     * 
     * @param itemName 删除的项目名称
     */
    public static void logDelete(String itemName) {
        setDetail("删除了【{name}】", "name", itemName);
    }

    /**
     * 记录批量操作
     * 
     * @param action 操作类型（如：删除、导出）
     * @param count 操作数量
     * @param itemType 项目类型（如：成员、规划）
     */
    public static void logBatch(String action, int count, String itemType) {
        setDetail("批量{action}了 {count} 个{itemType}", 
                 "action", action, "count", count, "itemType", itemType);
    }

    /**
     * 记录状态变更操作
     * 
     * @param itemName 项目名称
     * @param fromStatus 原状态
     * @param toStatus 新状态
     */
    public static void logStatusChange(String itemName, String fromStatus, String toStatus) {
        setDetail("将【{name}】的状态从【{from}】变更为【{to}】", 
                 "name", itemName, "from", fromStatus, "to", toStatus);
    }

    /**
     * 记录审批操作
     * 
     * @param itemName 审批项目名称
     * @param action 审批动作（通过、拒绝）
     * @param reason 审批原因（可选）
     */
    public static void logApproval(String itemName, String action, String reason) {
        if (reason != null && !reason.trim().isEmpty()) {
            setDetail("{action}了【{name}】，原因：{reason}", 
                     "action", action, "name", itemName, "reason", reason);
        } else {
            setDetail("{action}了【{name}】", "action", action, "name", itemName);
        }
    }
}
