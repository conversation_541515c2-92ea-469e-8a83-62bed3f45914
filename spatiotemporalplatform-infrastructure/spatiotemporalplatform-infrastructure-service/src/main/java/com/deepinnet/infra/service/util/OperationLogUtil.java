package com.deepinnet.infra.service.util;

import com.deepinnet.infra.service.context.OperationLogContext;

import java.util.Map;

/**
 * 操作日志工具类
 * 用于设置预定义模板的变量
 *
 * <AUTHOR> wong
 * @create 2025/7/31
 */
public class OperationLogUtil {

    /**
     * 设置单个模板变量
     *
     * @param key 变量名
     * @param value 变量值
     */
    public static void setVariable(String key, Object value) {
        OperationLogContext.setVariable(key, value);
    }

    /**
     * 批量设置模板变量
     *
     * @param variables 变量映射
     */
    public static void setVariables(Map<String, Object> variables) {
        OperationLogContext.setVariables(variables);
    }

    /**
     * 标记操作成功
     */
    public static void success() {
        OperationLogContext.setOperationResult("SUCCESS");
    }

    /**
     * 标记操作失败
     *
     * @param errorMessage 错误信息
     */
    public static void failure(String errorMessage) {
        OperationLogContext.setOperationResult("FAILURE");
        OperationLogContext.setErrorMessage(errorMessage);
    }

    // ========== 便捷方法：常用变量设置 ==========

    /**
     * 设置成员名称
     */
    public static void setMemberName(String memberName) {
        setVariable("memberName", memberName);
    }

    /**
     * 设置角色名称
     */
    public static void setRoleName(String roleName) {
        setVariable("roleName", roleName);
    }

    /**
     * 设置部门名称
     */
    public static void setDepartmentName(String departmentName) {
        setVariable("departmentName", departmentName);
    }

    /**
     * 设置飞行规划名称
     */
    public static void setPlanName(String planName) {
        setVariable("planName", planName);
    }

    /**
     * 设置飞行需求名称
     */
    public static void setDemandName(String demandName) {
        setVariable("demandName", demandName);
    }

    /**
     * 设置规划周期名称
     */
    public static void setCycleName(String cycleName) {
        setVariable("cycleName", cycleName);
    }

    /**
     * 设置组织名称
     */
    public static void setOrgName(String orgName) {
        setVariable("orgName", orgName);
    }

    /**
     * 设置权限列表
     */
    public static void setPermissions(String permissions) {
        setVariable("permissions", permissions);
    }

    /**
     * 设置数量
     */
    public static void setCount(int count) {
        setVariable("count", count);
    }

    /**
     * 设置数据类型
     */
    public static void setDataType(String dataType) {
        setVariable("dataType", dataType);
    }

    /**
     * 设置原因
     */
    public static void setReason(String reason) {
        setVariable("reason", reason);
    }

    /**
     * 设置配置名称
     */
    public static void setConfigName(String configName) {
        setVariable("configName", configName);
    }

    /**
     * 设置重命名相关变量
     */
    public static void setRename(String oldName, String newName) {
        setVariable("oldName", oldName);
        setVariable("newName", newName);
    }

    /**
     * 设置父部门名称
     */
    public static void setParentDepartmentName(String parentDepartmentName) {
        setVariable("parentDepartmentName", parentDepartmentName);
    }

    // ========== 链式调用支持 ==========

    /**
     * 创建一个构建器，支持链式调用
     */
    public static VariableBuilder builder() {
        return new VariableBuilder();
    }

    public static class VariableBuilder {

        public VariableBuilder memberName(String memberName) {
            setMemberName(memberName);
            return this;
        }

        public VariableBuilder roleName(String roleName) {
            setRoleName(roleName);
            return this;
        }

        public VariableBuilder departmentName(String departmentName) {
            setDepartmentName(departmentName);
            return this;
        }

        public VariableBuilder planName(String planName) {
            setPlanName(planName);
            return this;
        }

        public VariableBuilder count(int count) {
            setCount(count);
            return this;
        }

        public VariableBuilder permissions(String permissions) {
            setPermissions(permissions);
            return this;
        }

        public VariableBuilder reason(String reason) {
            setReason(reason);
            return this;
        }

        public VariableBuilder variable(String key, Object value) {
            setVariable(key, value);
            return this;
        }

        public void success() {
            OperationLogUtil.success();
        }

        public void failure(String errorMessage) {
            OperationLogUtil.failure(errorMessage);
        }
    }
}
