package com.deepinnet.infra.service.examples;

import com.deepinnet.infra.service.annotation.OperationLog;
import com.deepinnet.infra.service.enums.OperationModuleEnum;
import com.deepinnet.infra.service.enums.OperationTypeEnum;
import com.deepinnet.infra.service.util.OperationLogUtil;

import java.util.List;

/**
 * 操作日志使用示例
 * 展示各种友好的使用方式
 * 
 * <AUTHOR> wong
 * @create 2025/7/31
 */
public class OperationLogExamples {

    // ========== 最简单的使用方式 ==========
    
    @OperationLog(module = "登录", type = "登录", detail = "登录成功")
    public void login() {
        // 业务逻辑
    }

    @OperationLog(module = "登录", type = "退出", detail = "退出登录")
    public void logout() {
        // 业务逻辑
    }

    // ========== 使用枚举，更规范 ==========
    
    @OperationLog(
        module = OperationModuleEnum.USER_MANAGEMENT, 
        type = OperationTypeEnum.CREATE, 
        detail = "新增用户"
    )
    public void createUser(String userName) {
        // 业务逻辑
        
        // 使用工具类设置详细信息
        OperationLogUtil.logCreate(userName);
    }

    // ========== 使用占位符，简单直观 ==========
    
    @OperationLog(
        module = "飞行规划", 
        type = "新增", 
        detail = "新增了飞行规划【{planName}】"
    )
    public void createFlightPlan(String planName) {
        // 业务逻辑
        // 注解会自动从参数中获取 planName
    }

    @OperationLog(
        module = "成员管理", 
        type = "编辑", 
        detail = "编辑了成员【{memberName}】的信息"
    )
    public void updateMember(String memberId, String memberName) {
        // 业务逻辑
        // 注解会自动从参数中获取 memberName
    }

    // ========== 动态设置详情 ==========
    
    @OperationLog(module = "角色管理", type = "编辑", detail = "编辑角色权限")
    public void updateRolePermissions(String roleId, List<String> permissionIds) {
        // 业务逻辑
        String roleName = getRoleName(roleId);
        String permissions = getPermissionNames(permissionIds);
        
        // 动态设置详细的操作信息
        OperationLogUtil.setDetail("编辑了角色【{roleName}】的权限为【{permissions}】", 
                                  "roleName", roleName, "permissions", permissions);
    }

    // ========== 批量操作 ==========
    
    @OperationLog(module = "成员管理", type = "删除", detail = "批量删除成员")
    public void batchDeleteMembers(List<String> memberIds) {
        // 业务逻辑
        
        // 记录批量操作
        OperationLogUtil.logBatch("删除", memberIds.size(), "成员");
    }

    // ========== 状态变更 ==========
    
    @OperationLog(module = "飞行规划", type = "审批", detail = "审批飞行规划")
    public void approveFlightPlan(String planId, boolean approved, String reason) {
        // 业务逻辑
        String planName = getPlanName(planId);
        String action = approved ? "通过" : "拒绝";
        
        // 记录审批操作
        OperationLogUtil.logApproval(planName, action, reason);
    }

    // ========== 复杂业务场景 ==========
    
    @OperationLog(module = "规划周期", type = "编辑", detail = "编辑规划周期")
    public void updatePlanningCycle(String cycleId, PlanningCycleRequest request) {
        try {
            // 业务逻辑
            String oldName = getCurrentCycleName(cycleId);
            String newName = request.getName();
            
            if (!oldName.equals(newName)) {
                OperationLogUtil.setDetail("将规划周期【{oldName}】重命名为【{newName}】", 
                                         "oldName", oldName, "newName", newName);
            } else {
                OperationLogUtil.setDetail("编辑了规划周期【{name}】的配置", "name", newName);
            }
            
            // 标记操作成功
            OperationLogUtil.success();
            
        } catch (Exception e) {
            // 标记操作失败
            OperationLogUtil.failure("编辑规划周期失败", e.getMessage());
            throw e;
        }
    }

    // ========== 条件判断的操作类型 ==========
    
    @OperationLog(
        module = "成员管理", 
        type = "#{#request.action}", 
        detail = "成员状态变更"
    )
    public void changeMemberStatus(MemberStatusRequest request) {
        // 业务逻辑
        String memberName = getMemberName(request.getMemberId());
        String action = request.getAction(); // "启用" 或 "禁用"
        
        OperationLogUtil.setDetail("{action}了成员【{name}】", "action", action, "name", memberName);
    }

    // ========== 记录敏感操作 ==========
    
    @OperationLog(
        module = "系统管理", 
        type = "重置密码", 
        detail = "重置用户密码",
        recordParams = true,  // 记录参数，但会自动排除密码字段
        excludeParams = {"password", "newPassword"}
    )
    public void resetPassword(String userId, String newPassword) {
        // 业务逻辑
        String userName = getUserName(userId);
        OperationLogUtil.setDetail("重置了用户【{name}】的密码", "name", userName);
    }

    // ========== 工具方法示例 ==========
    
    private String getRoleName(String roleId) { return "管理员"; }
    private String getPermissionNames(List<String> permissionIds) { return "用户管理、角色管理"; }
    private String getPlanName(String planId) { return "测试飞行规划"; }
    private String getCurrentCycleName(String cycleId) { return "2025年第一季度"; }
    private String getMemberName(String memberId) { return "张三"; }
    private String getUserName(String userId) { return "李四"; }

    // 示例请求类
    public static class PlanningCycleRequest {
        private String name;
        public String getName() { return name; }
    }

    public static class MemberStatusRequest {
        private String memberId;
        private String action;
        public String getMemberId() { return memberId; }
        public String getAction() { return action; }
    }
}
