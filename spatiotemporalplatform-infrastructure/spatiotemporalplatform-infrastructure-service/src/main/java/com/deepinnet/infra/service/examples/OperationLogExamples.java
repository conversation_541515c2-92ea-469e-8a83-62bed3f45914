package com.deepinnet.infra.service.examples;

import com.deepinnet.infra.service.annotation.OperationLog;
import com.deepinnet.infra.service.enums.OperationLogTemplateEnum;
import com.deepinnet.infra.service.util.OperationLogUtil;

import java.util.List;

/**
 * 操作日志使用示例
 * 使用预定义模板的方式
 *
 * <AUTHOR> wong
 * @create 2025/7/31
 */
public class OperationLogExamples {

    // ========== 最简单的使用方式：无需变量的模板 ==========

    @OperationLog(template = OperationLogTemplateEnum.LOGIN_SUCCESS)
    public void login() {
        // 业务逻辑
        // 无需设置任何变量，模板会自动生成："登录成功"
    }

    @OperationLog(template = OperationLogTemplateEnum.LOGOUT)
    public void logout() {
        // 业务逻辑
        // 无需设置任何变量，模板会自动生成："退出登录"
    }

    // ========== 需要变量的模板 ==========

    @OperationLog(template = OperationLogTemplateEnum.MEMBER_CREATE)
    public void createMember(String memberName) {
        // 业务逻辑

        // 设置模板变量
        OperationLogUtil.setMemberName(memberName);
        // 最终生成："新增了成员【张三】"
    }

    @OperationLog(template = OperationLogTemplateEnum.MEMBER_UPDATE)
    public void updateMember(String memberId, String newName) {
        // 业务逻辑

        // 设置模板变量
        OperationLogUtil.setMemberName(newName);
        // 最终生成："编辑了成员【李四】"
    }

    @OperationLog(template = OperationLogTemplateEnum.MEMBER_DELETE)
    public void deleteMember(String memberId) {
        // 业务逻辑
        String memberName = getMemberName(memberId);

        // 设置模板变量
        OperationLogUtil.setMemberName(memberName);
        // 最终生成："删除了成员【王五】"
    }

    // ========== 飞行规划相关 ==========

    @OperationLog(template = OperationLogTemplateEnum.FLIGHT_PLAN_CREATE)
    public void createFlightPlan(String planName) {
        // 业务逻辑

        OperationLogUtil.setPlanName(planName);
        // 最终生成："新增了飞行规划【测试规划】"
    }

    @OperationLog(template = OperationLogTemplateEnum.FLIGHT_PLAN_APPROVE)
    public void approveFlightPlan(String planId) {
        // 业务逻辑
        String planName = getPlanName(planId);

        OperationLogUtil.setPlanName(planName);
        // 最终生成："审批通过了飞行规划【测试规划】"
    }

    @OperationLog(template = OperationLogTemplateEnum.FLIGHT_PLAN_REJECT)
    public void rejectFlightPlan(String planId, String reason) {
        // 业务逻辑
        String planName = getPlanName(planId);

        OperationLogUtil.setPlanName(planName);
        OperationLogUtil.setReason(reason);
        // 最终生成："审批拒绝了飞行规划【测试规划】，原因：不符合要求"
    }

    // ========== 批量操作 ==========

    @OperationLog(template = OperationLogTemplateEnum.MEMBER_BATCH_DELETE)
    public void batchDeleteMembers(List<String> memberIds) {
        // 业务逻辑

        OperationLogUtil.setCount(memberIds.size());
        // 最终生成："批量删除了 5 个成员"
    }

    // ========== 角色权限管理 ==========

    @OperationLog(template = OperationLogTemplateEnum.ROLE_ASSIGN_PERMISSION)
    public void assignRolePermissions(String roleId, List<String> permissionIds) {
        // 业务逻辑
        String roleName = getRoleName(roleId);
        String permissions = getPermissionNames(permissionIds);

        OperationLogUtil.setRoleName(roleName);
        OperationLogUtil.setPermissions(permissions);
        // 最终生成："为角色【管理员】分配了权限【用户管理、角色管理】"
    }

    // ========== 复杂业务场景：条件判断使用不同模板 ==========

    public void updatePlanningCycle(String cycleId, PlanningCycleRequest request) {
        String oldName = getCurrentCycleName(cycleId);
        String newName = request.getName();

        if (!oldName.equals(newName)) {
            // 重命名操作
            updatePlanningCycleWithRename(cycleId, oldName, newName);
        } else {
            // 普通编辑操作
            updatePlanningCycleNormal(cycleId, newName);
        }
    }

    @OperationLog(template = OperationLogTemplateEnum.PLANNING_CYCLE_RENAME)
    private void updatePlanningCycleWithRename(String cycleId, String oldName, String newName) {
        // 业务逻辑
        OperationLogUtil.setRename(oldName, newName);
        // 最终生成："将规划周期【2025年第一季度】重命名为【2025年Q1】"
    }

    @OperationLog(template = OperationLogTemplateEnum.PLANNING_CYCLE_UPDATE)
    private void updatePlanningCycleNormal(String cycleId, String cycleName) {
        // 业务逻辑
        OperationLogUtil.setCycleName(cycleName);
        // 最终生成："编辑了规划周期【2025年Q1】"
    }

    // ========== 链式调用示例 ==========

    @OperationLog(template = OperationLogTemplateEnum.ROLE_ASSIGN_PERMISSION)
    public void complexRoleOperation(String roleId, List<String> permissionIds) {
        // 业务逻辑
        String roleName = getRoleName(roleId);
        String permissions = getPermissionNames(permissionIds);

        // 使用链式调用设置多个变量
        OperationLogUtil.builder()
            .roleName(roleName)
            .permissions(permissions)
            .success();
    }

    // ========== 异常处理示例 ==========

    @OperationLog(template = OperationLogTemplateEnum.MEMBER_RESET_PASSWORD)
    public void resetPassword(String memberId, String newPassword) {
        try {
            // 业务逻辑
            String memberName = getMemberName(memberId);
            OperationLogUtil.setMemberName(memberName);

            // 执行重置密码操作
            doResetPassword(memberId, newPassword);

            // 标记成功
            OperationLogUtil.success();

        } catch (Exception e) {
            // 标记失败
            OperationLogUtil.failure(e.getMessage());
            throw e;
        }
    }

    // ========== 工具方法示例 ==========
    
    private String getRoleName(String roleId) { return "管理员"; }
    private String getPermissionNames(List<String> permissionIds) { return "用户管理、角色管理"; }
    private String getPlanName(String planId) { return "测试飞行规划"; }
    private String getCurrentCycleName(String cycleId) { return "2025年第一季度"; }
    private String getMemberName(String memberId) { return "张三"; }
    private String getUserName(String userId) { return "李四"; }

    // 示例请求类
    public static class PlanningCycleRequest {
        private String name;
        public String getName() { return name; }
    }

    public static class MemberStatusRequest {
        private String memberId;
        private String action;
        public String getMemberId() { return memberId; }
        public String getAction() { return action; }
    }
}
