package com.deepinnet.infra.service.convert;

import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.api.vo.*;
import com.deepinnet.infra.dal.dataobject.*;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 服务商信息转换器
 *
 * <AUTHOR> wong
 * @create 2025/04/21
 */
@Mapper(componentModel = "spring")
public interface ProductServiceSupplierConvert {

    /**
     * SupplierInfoDTO 转换为 ProductServiceSupplierDO
     *
     * @param dto DTO对象
     * @return DO对象
     */
    ProductServiceSupplierDO toProductServiceSupplierDO(SupplierInfoDTO dto);

    /**
     * SupplierContactInfoDTO 转换为 SupplierContactInfoDO
     *
     * @param dto DTO对象
     * @return DO对象
     */
    UserContactInfoDO toSupplierContactInfoDO(SupplierContactInfoDTO dto);

    /**
     * SupplierContactInfoDTO 列表转换为 SupplierContactInfoDO 列表
     *
     * @param dtoList DTO列表
     * @return DO列表
     */
    List<UserContactInfoDO> toSupplierContactInfoDOList(List<SupplierContactInfoDTO> dtoList);

    /**
     * SupplierContactInfoDO 转换为 SupplierContactInfoDTO
     *
     * @param dO DO对象
     * @return DTO对象
     */
    SupplierContactInfoDTO toSupplierContactInfoDTO(UserContactInfoDO dO);

    /**
     * SupplierContactInfoDO 列表转换为 SupplierContactInfoDTO 列表
     *
     * @param doList DO列表
     * @return DTO列表
     */
    List<SupplierContactInfoDTO> toSupplierContactInfoDTOList(List<UserContactInfoDO> doList);

    /**
     * SupplierFlightServiceDO 转换为 SupplierServiceDTO
     *
     * @param dO DO对象
     * @return DTO对象
     */
    SupplierServiceDTO toSupplierServiceDTO(SupplierFlightServiceDO dO);

    /**
     * SupplierFlightServiceDO 列表转换为 SupplierServiceDTO 列表
     *
     * @param doList DO列表
     * @return DTO列表
     */
    List<SupplierServiceDTO> toSupplierServiceDTOList(List<SupplierFlightServiceDO> doList);

    /**
     * ProductServiceSupplierDO 转换为 SupplierDetailDTO
     *
     * @param dO DO对象
     * @return DTO对象
     */
    SupplierDetailDTO toSupplierDetailDTO(ProductServiceSupplierDO dO);

    /**
     * SupplierContactInfoDTO 转换为 SupplierContactInfoVO
     *
     * @param dto DTO对象
     * @return VO对象
     */
    SupplierContactInfoVO toSupplierContactInfoVO(SupplierContactInfoDTO dto);

    /**
     * SupplierContactInfoDTO 列表转换为 SupplierContactInfoVO 列表
     *
     * @param dtoList DTO列表
     * @return VO列表
     */
    List<SupplierContactInfoVO> toSupplierContactInfoVOList(List<SupplierContactInfoDTO> dtoList);

    /**
     * SupplierServiceDTO 转换为 SupplierServiceVO
     *
     * @param dto DTO对象
     * @return VO对象
     */
    SupplierServiceVO toSupplierServiceVO(SupplierServiceDTO dto);

    /**
     * SupplierServiceDTO 列表转换为 SupplierServiceVO 列表
     *
     * @param dtoList DTO列表
     * @return VO列表
     */
    List<SupplierServiceVO> toSupplierServiceVOList(List<SupplierServiceDTO> dtoList);


    SupplierDetailDTO convertToSupplierDetailDTO(ProductServiceSupplierDO supplierDO);

    ContactInfoDTO convertToContactInfoDTO(UserContactInfoDO supplierDO);

    List<ContactInfoDTO> convertToContactInfoDTOs(List<UserContactInfoDO> supplierDOs);
}