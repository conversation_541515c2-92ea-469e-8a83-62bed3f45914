package com.deepinnet.infra.service.util;

import cn.hutool.core.util.StrUtil;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Map;

/**
 * SpEL表达式解析工具
 * 
 * <AUTHOR> wong
 * @create 2025/7/31
 */
public class SpELUtil {

    private static final ExpressionParser PARSER = new SpelExpressionParser();

    /**
     * 解析SpEL表达式
     * 
     * @param expressionString 表达式字符串
     * @param joinPoint 切点
     * @param result 方法返回结果
     * @param exception 异常信息
     * @param contextData 上下文数据
     * @return 解析结果
     */
    public static String parseExpression(String expressionString, ProceedingJoinPoint joinPoint, 
                                       Object result, Exception exception, Map<String, Object> contextData) {
        if (StrUtil.isBlank(expressionString)) {
            return expressionString;
        }

        try {
            // 创建评估上下文
            EvaluationContext context = createEvaluationContext(joinPoint, result, exception, contextData);
            
            // 解析表达式
            Expression expression = PARSER.parseExpression(expressionString);
            Object value = expression.getValue(context);
            
            return value != null ? value.toString() : expressionString;
        } catch (Exception e) {
            LogUtil.warn("SpEL表达式解析失败: {}", expressionString, e);
            return expressionString;
        }
    }

    /**
     * 创建SpEL评估上下文
     */
    private static EvaluationContext createEvaluationContext(ProceedingJoinPoint joinPoint, 
                                                           Object result, Exception exception, 
                                                           Map<String, Object> contextData) {
        StandardEvaluationContext context = new StandardEvaluationContext();
        
        // 添加方法参数
        addMethodParameters(context, joinPoint);
        
        // 添加返回结果
        if (result != null) {
            context.setVariable("result", result);
        }
        
        // 添加异常信息
        if (exception != null) {
            context.setVariable("exception", exception);
        }
        
        // 添加上下文数据
        if (contextData != null) {
            contextData.forEach(context::setVariable);
        }
        
        return context;
    }

    /**
     * 添加方法参数到上下文
     */
    private static void addMethodParameters(StandardEvaluationContext context, ProceedingJoinPoint joinPoint) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            Parameter[] parameters = method.getParameters();
            Object[] args = joinPoint.getArgs();
            
            for (int i = 0; i < parameters.length; i++) {
                String paramName = parameters[i].getName();
                context.setVariable(paramName, args[i]);
            }
        } catch (Exception e) {
            LogUtil.warn("添加方法参数到SpEL上下文失败", e);
        }
    }

    /**
     * 简单的字符串模板解析（不使用SpEL）
     * 用于简单的占位符替换，如：新增了规划周期【{planName}】
     */
    public static String parseTemplate(String template, Map<String, Object> variables) {
        if (StrUtil.isBlank(template) || variables == null || variables.isEmpty()) {
            return template;
        }

        String result = template;
        for (Map.Entry<String, Object> entry : variables.entrySet()) {
            String placeholder = "{" + entry.getKey() + "}";
            String value = entry.getValue() != null ? entry.getValue().toString() : "";
            result = result.replace(placeholder, value);
        }
        
        return result;
    }
}
