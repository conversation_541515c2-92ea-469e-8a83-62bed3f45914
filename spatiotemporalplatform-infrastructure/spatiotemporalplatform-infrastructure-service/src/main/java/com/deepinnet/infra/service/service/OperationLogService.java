package com.deepinnet.infra.service.service;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.infra.api.dto.OperationLogQueryDTO;
import com.deepinnet.infra.api.dto.OperationLogDTO;

import java.util.Map;

/**
 * 操作日志服务接口
 * 
 * <AUTHOR> wong
 * @create 2025/7/31
 */
public interface OperationLogService {

    /**
     * 记录操作日志（同步）
     */
    void recordLog(Map<String, Object> logData);

    /**
     * 记录操作日志（异步）
     */
    void recordLogAsync(Map<String, Object> logData);

    /**
     * 分页查询操作日志
     */
    CommonPage<OperationLogDTO> pageQuery(OperationLogQueryDTO queryDTO);

    /**
     * 清理过期日志
     * @param retentionDays 保留天数
     */
    void cleanExpiredLogs(int retentionDays);
}
