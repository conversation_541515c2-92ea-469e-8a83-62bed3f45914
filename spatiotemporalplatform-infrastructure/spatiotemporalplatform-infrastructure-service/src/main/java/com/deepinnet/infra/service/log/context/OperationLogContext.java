package com.deepinnet.infra.service.log.context;

import com.deepinnet.infra.service.enums.OperationLogTemplateEnum;
import lombok.Data;

import java.util.*;

/**
 * 操作日志上下文
 * 用于存储操作日志的模板变量
 *
 * <AUTHOR>
 */
@Data
public class OperationLogContext {

    /**
     * 操作日志模板
     */
    private OperationLogTemplateEnum template;

    /**
     * 模板变量
     */
    private Map<String, Object> variables;

    /**
     * 是否记录日志（默认true）
     */
    private boolean enabled = true;

    /**
     * 操作结果（默认成功）
     */
    private String operationResult = "SUCCESS";

    /**
     * 错误信息
     */
    private String errorMessage;

    public OperationLogContext() {
        this.variables = new HashMap<>();
    }

    public OperationLogContext(OperationLogTemplateEnum template) {
        this.template = template;
        this.variables = new HashMap<>();
    }

    /**
     * 设置模板变量
     */
    public OperationLogContext setVariable(String key, Object value) {
        this.variables.put(key, value);
        return this;
    }

    /**
     * 批量设置模板变量
     */
    public OperationLogContext setVariables(Map<String, Object> variables) {
        this.variables.putAll(variables);
        return this;
    }

    /**
     * 获取模板变量
     */
    public Object getVariable(String key) {
        return this.variables.get(key);
    }

    /**
     * 设置操作失败
     */
    public OperationLogContext setFailure(String errorMessage) {
        this.operationResult = "FAILURE";
        this.errorMessage = errorMessage;
        return this;
    }

    /**
     * 设置操作成功
     */
    public OperationLogContext setSuccess() {
        this.operationResult = "SUCCESS";
        this.errorMessage = null;
        return this;
    }

    /**
     * 禁用日志记录
     */
    public OperationLogContext disable() {
        this.enabled = false;
        return this;
    }

    /**
     * 启用日志记录
     */
    public OperationLogContext enable() {
        this.enabled = true;
        return this;
    }
}
