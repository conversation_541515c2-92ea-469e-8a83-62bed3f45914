package com.deepinnet.infra.service.sms.client;

import cn.hutool.json.JSONUtil;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.infra.api.dto.SmsBatchSendDTO;
import com.deepinnet.infra.service.error.BizErrorCode;
import com.deepinnet.infra.service.sms.request.TpSmsBatchSendRequest;
import com.deepinnet.infra.api.template.SmsBaseTemplate;
import com.deepinnet.infra.service.util.SmsSendUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import java.util.*;

/**
 * Creator zengjuerui
 * Date 2024-01-12
 **/
public abstract class AbstractSmsClient implements SmsClient {

    @Override
    public void sendByTemplate(String mobile, SmsBaseTemplate smsTemplate) {
        Map<String, String> bizParamMap = SmsSendUtil.buildSmsRequestParam(smsTemplate);

        String param = generateParamJSON(bizParamMap);

        SmsRequestResult result = doSend(mobile, smsTemplate, param);
        LogUtil.info("发送短信，发送手机号为：{}，模板id为：{}，参数为：{},发送结果为：{}", mobile, smsTemplate.getTemplateId(), param, result.isSuccess());
        if (result.isFail()) {
            LogUtil.error("短信发送失败，手机号：{}，模板参数：{}，错误信息为：{}", mobile, param, result.getMsg());
            throw new BizException(BizErrorCode.SEND_SMS_ERROR.getCode(), BizErrorCode.SEND_SMS_ERROR.getDesc());
        }
    }

    @Override
    public void batchSendByTemplate(List<String> mobileList, SmsBaseTemplate smsTemplate) {
        Map<String, String> bizParamMap = SmsSendUtil.buildSmsRequestParam(smsTemplate);

        String param = generateParamJSON(bizParamMap);

        SmsRequestResult result = doBatchSend(mobileList, smsTemplate.getTemplateId(), param);
        LogUtil.info("发送短信，发送手机号为：{}，模板id为：{}，参数为：{},发送结果为：{}", JSONUtil.toJsonStr(mobileList), smsTemplate.getTemplateId(), param, result.isSuccess());
        if (result.isFail()) {
            LogUtil.error("短信发送失败，手机号：{}，模板参数：{}，错误信息为：{}", JSONUtil.toJsonStr(mobileList), param, result.getMsg());
            throw new BizException(BizErrorCode.SEND_SMS_ERROR.getCode(), BizErrorCode.SEND_SMS_ERROR.getDesc());
        }
    }

    @Override
    public void batchSendByTemplate(List<SmsBatchSendDTO> batchSendDTOs) {
        List<TpSmsBatchSendRequest> batchSendRequests = new ArrayList<>();
        batchSendDTOs.forEach(sendDTO -> {
            Map<String, String> bizParamMap = SmsSendUtil.buildSmsRequestParam(sendDTO.getSmsTemplate());
            String param = generateParamJSON(bizParamMap);
            batchSendRequests.add(new TpSmsBatchSendRequest(sendDTO.getMobile(), sendDTO.getSmsTemplate(), param));
        });

        SmsRequestResult result = doBatchSend(batchSendRequests);
        LogUtil.info("发送短信，短信内容为：{}，发送结果为：{}", JSONUtil.toJsonStr(batchSendRequests), result.isSuccess());
        if (result.isFail()) {
            LogUtil.error("短信发送失败，手机号：{}，模板参数：{}，错误信息为：{}", JSONUtil.toJsonStr(batchSendDTOs), result.getMsg());
            throw new BizException(BizErrorCode.SEND_SMS_ERROR.getCode(), BizErrorCode.SEND_SMS_ERROR.getDesc());
        }
    }


    protected abstract SmsRequestResult doSend(String mobile, SmsBaseTemplate smsTemplate, String param);

    protected abstract SmsRequestResult doBatchSend(List<String> mobileList, String templateUniqNo, String param);

    protected abstract SmsRequestResult doBatchSend(List<TpSmsBatchSendRequest> batchSendRequests);

    protected abstract String generateParamJSON(Map<String, String> paramMap);


    @Data
    @AllArgsConstructor
    protected static class SmsRequestResult {
        boolean success;

        String msg;

        static SmsRequestResult requestDone(boolean success, String msg) {
            return new SmsRequestResult(success, msg);
        }

        @JsonIgnore
        boolean isFail() {
            return !success;
        }
    }
}
