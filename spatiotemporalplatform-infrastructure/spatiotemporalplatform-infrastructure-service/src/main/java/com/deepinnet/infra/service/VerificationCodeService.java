package com.deepinnet.infra.service;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.SendSmsResultDTO;

/**
 * 验证码服务接口
 *
 * <AUTHOR> wong
 * @create 2025/04/18
 */
public interface VerificationCodeService {

    /**
     * 发送验证码
     *
     * @param phone 手机号
     * @return 发送结果
     */
    Result<SendSmsResultDTO> sendVerificationCode(String phone, String bizType, String scene);

    /**
     * 验证验证码
     *
     * @param phone 手机号
     * @param code  验证码
     * @return 验证结果
     */
    Result<Boolean> verifyCode(String phone, String code, String bizType, String scene);
} 