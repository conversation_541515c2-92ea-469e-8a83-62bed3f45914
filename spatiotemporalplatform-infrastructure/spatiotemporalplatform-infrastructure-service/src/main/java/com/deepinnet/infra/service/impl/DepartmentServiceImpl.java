package com.deepinnet.infra.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.api.enums.*;
import com.deepinnet.infra.dal.dataobject.*;
import com.deepinnet.infra.dal.mapper.DepartmentMapper;
import com.deepinnet.infra.dal.repository.*;
import com.deepinnet.infra.service.*;
import com.deepinnet.infra.service.context.TenantContext;
import com.deepinnet.infra.service.error.BizErrorCode;
import com.deepinnet.infra.service.util.DataPermissionProcessor;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wong
 * @create 2024/11/26 15:20
 * @Description
 */
@Service
public class DepartmentServiceImpl implements DepartmentService {

    @Resource
    private DepartmentMapper departmentMapper;

    @Resource
    private DepartmentRepository departmentRepository;

    @Resource
    private UserDepartmentRepository userDepartmentRepository;

    @Resource
    private DepartmentMemberRepository departmentMemberRepository;

    @Resource
    private AccountRepository accountRepository;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private UserRepository userRepository;

    @Resource
    private UserMemberRepository userMemberRepository;

    @Resource
    private UserRoleRepository userRoleRepository;

    @Resource
    private UserAccountRepository userAccountRepository;

    @Resource
    private ProductServiceCustomerRepository productServiceCustomerRepository;

    @Resource
    private UserService userService;

    @Resource
    private RoleRepository roleRepository;

    @Resource
    private DataPermissionProcessor dataPermissionProcessor;

    @Override
    public Long saveDepartment(DepartmentSaveDTO saveDTO) {
        String scene = getScene();

        // 数据权限校验
        dataPermissionProcessor.checkDataPermissionByDepartmentId(saveDTO.getParentId());

        // 根节点不能重复
        if (saveDTO.getParentId() == -1) {
            List<DepartmentDO> existOrganizations = departmentRepository.list(Wrappers.lambdaQuery(DepartmentDO.class)
                    .eq(DepartmentDO::getName, saveDTO.getName())
                    .eq(StrUtil.isNotBlank(scene), DepartmentDO::getScene, scene));
            if (CollectionUtils.isNotEmpty(existOrganizations)) {
                throw new BizException(BizErrorCode.ORGANIZATION_NAME_ALREADY_EXIST.getCode(), BizErrorCode.ORGANIZATION_NAME_ALREADY_EXIST.getDesc());
            }
        } else {
            // 根节点以下的名字组织内不能重复
            checkDuplicatedNameInDepartments(saveDTO, scene);
        }

        int level;
        List<Long> path;
        if (saveDTO.getParentId() != -1) {
            DepartmentDO parentDepartment = departmentRepository.getById(saveDTO.getParentId());
            if (parentDepartment == null) {
                throw new BizException(BizErrorCode.ORGANIZATION_NOT_EXIST.getCode(), BizErrorCode.ORGANIZATION_NOT_EXIST.getDesc());
            }
            level = parentDepartment.getLevel() + 1;
            path = departmentMapper.getDepartmentFullPathFromRoot(saveDTO.getParentId());
        } else {
            // 支持创建多个顶级组织，移除唯一性校验
            level = 0;
            path = Lists.newArrayList(-1L);
        }

        if (level > 5) {
            throw new BizException(BizErrorCode.OVER_MAX_DEPARTMENT_LEVEL.getCode(), BizErrorCode.OVER_MAX_DEPARTMENT_LEVEL.getDesc());
        }

        AtomicReference<Long> departmentId = new AtomicReference<>(0L);
        transactionTemplate.executeWithoutResult(action -> {
            DepartmentDO departmentDO = new DepartmentDO();
            departmentDO.setName(saveDTO.getName());
            departmentDO.setLevel(level);
            departmentDO.setParentId(saveDTO.getParentId());
            departmentDO.setScene(scene);
            departmentRepository.save(departmentDO);
            departmentId.set(departmentDO.getId());

            path.add(departmentDO.getId());
            departmentDO.setFullPath(JSONUtil.toJsonStr(path));
            departmentRepository.updateById(departmentDO);
        });

        return departmentId.get();

    }

    @Override
    public Boolean updateDepartment(DepartmentUpdateDTO updateDTO) {
        // 数据权限校验
        dataPermissionProcessor.checkDataPermissionByDepartmentId(updateDTO.getDepartmentId());

        String scene = getScene();

        List<DepartmentDO> existOrganizations = departmentRepository.list(Wrappers.lambdaQuery(DepartmentDO.class)
                .eq(DepartmentDO::getName, updateDTO.getName())
                .eq(StrUtil.isNotBlank(scene), DepartmentDO::getScene, scene));
        boolean existSameOrg = existOrganizations.stream()
                .anyMatch(o -> !Objects.equals(o.getId(), updateDTO.getDepartmentId()));
        if (existSameOrg) {
            throw new BizException(BizErrorCode.ORGANIZATION_NAME_ALREADY_EXIST.getCode(), "待更新的组织名称已经存在，无法更新");
        }

        DepartmentDO currentDepartment = departmentRepository.getById(updateDTO.getDepartmentId());
        if (currentDepartment == null) {
            LogUtil.error("要更新的组织不存在，入参为：{}", JSONUtil.toJsonStr(updateDTO));
            throw new BizException(BizErrorCode.ORGANIZATION_NOT_EXIST.getCode(), BizErrorCode.ORGANIZATION_NOT_EXIST.getDesc());
        }

        currentDepartment.setName(updateDTO.getName());
        departmentRepository.updateById(currentDepartment);
        return true;
    }

    @Override
    public Boolean deleteDepartment(Long id) {
        // 数据权限校验
        dataPermissionProcessor.checkDataPermissionByDepartmentId(id);

        List<Long> departmentIds = Lists.newArrayList(id);
        List<DepartmentTreeDTO> subDepartmentList = departmentMapper.getDepartmentTreeByParentId(id, null);
        if (CollectionUtils.isNotEmpty(subDepartmentList)) {
            departmentIds = subDepartmentList.stream()
                    .map(DepartmentTreeDTO::getDepartmentId)
                    .collect(Collectors.toList());
        }

        List<UserDepartmentDO> userDepartmentDos = userDepartmentRepository.list(Wrappers.lambdaQuery(UserDepartmentDO.class)
                .in(UserDepartmentDO::getDepartmentId, departmentIds));
        if (CollectionUtils.isNotEmpty(userDepartmentDos)) {
            throw new BizException(BizErrorCode.DEPARTMENT_EXIST_MEMBER.getCode(), BizErrorCode.DEPARTMENT_EXIST_MEMBER.getDesc());
        }

        List<Long> finalDepartmentIds = departmentIds;
        transactionTemplate.executeWithoutResult(action -> {
            departmentMemberRepository.remove(Wrappers.lambdaQuery(DepartmentMemberDO.class)
                    .in(DepartmentMemberDO::getDepartmentId, finalDepartmentIds));

            // 需要将子树都一起移除掉
            departmentRepository.removeBatchByIds(finalDepartmentIds);
        });

        return true;
    }

    @Override
    public Boolean addMember(DepartmentAddMemberDTO addMemberDTO) {
        // 数据权限校验
        dataPermissionProcessor.checkDataPermissionByDepartmentId(addMemberDTO.getDepartmentId());

        String scene = getScene();

        // 新增
        if (addMemberDTO.getId() == null) {
            List<DepartmentMemberDO> departmentMemberDOs = departmentMemberRepository.list(Wrappers.lambdaQuery(DepartmentMemberDO.class)
                    .eq(DepartmentMemberDO::getPhone, addMemberDTO.getPhone())
                    .eq(StrUtil.isNotBlank(scene), DepartmentMemberDO::getScene, scene));
            if (CollectionUtils.isNotEmpty(departmentMemberDOs)) {
                throw new BizException(BizErrorCode.MEMBER_PHONE_EXIST.getCode(), BizErrorCode.MEMBER_PHONE_EXIST.getDesc());
            }

            DepartmentDO departmentDO = departmentRepository.getById(addMemberDTO.getDepartmentId());
            if (departmentDO == null) {
                throw new BizException(BizErrorCode.DEPARTMENT_NOT_FOUND.getCode(), BizErrorCode.DEPARTMENT_NOT_FOUND.getDesc());
            }

            DepartmentMemberDO departmentMemberDO = buildDepartmentMember(addMemberDTO, scene, MemberOccupyStatusEnum.AVAILABLE.getCode());
            departmentMemberRepository.save(departmentMemberDO);
            return true;
        }

        // 更新逻辑
        duplicateCheck(addMemberDTO.getId(), addMemberDTO.getPhone(), addMemberDTO.getEmail(), scene);

        DepartmentMemberDO departmentMemberDO = buildDepartmentMember(addMemberDTO, scene, null);

        // 一同更新绑定的用户和账号的userNo
        UserMemberDO userMemberDO = userMemberRepository.getOne(Wrappers.lambdaQuery(UserMemberDO.class)
                .eq(UserMemberDO::getMemberId, departmentMemberDO.getId()));

        transactionTemplate.executeWithoutResult(action -> {
            if (userMemberDO != null) {
                String userNo = userMemberDO.getUserNo();

                AccountDO account = accountRepository.getOne(Wrappers.lambdaQuery(AccountDO.class)
                        .eq(AccountDO::getUserNo, userNo));
                UserDO user = userRepository.getOne(Wrappers.lambdaQuery(UserDO.class)
                        .eq(UserDO::getUserNo, userNo));

                account.setName(departmentMemberDO.getName());
                user.setUserName(departmentMemberDO.getName());

                userRepository.updateById(user);
                accountRepository.updateById(account);
            }

            departmentMemberRepository.updateById(departmentMemberDO);
        });


        return true;
    }

    private void duplicateCheck(Long id, String phone, String email, String scene) {
        if (StringUtils.isNotBlank(phone)) {
            List<DepartmentMemberDO> samePhoneMembers = departmentMemberRepository.list(Wrappers.lambdaQuery(DepartmentMemberDO.class)
                    .eq(DepartmentMemberDO::getPhone, phone)
                    .eq(StrUtil.isNotBlank(scene), DepartmentMemberDO::getScene, scene));
            if (CollectionUtils.isNotEmpty(samePhoneMembers)) {
                boolean containsDuplicatePhone = samePhoneMembers.stream()
                        .anyMatch(m -> !Objects.equals(m.getId(), id));
                if (containsDuplicatePhone) {
                    throw new BizException(BizErrorCode.MEMBER_PHONE_EXIST.getCode(), BizErrorCode.MEMBER_PHONE_EXIST.getDesc());
                }
            }
        }

        if (StringUtils.isNotBlank(email)) {
            List<DepartmentMemberDO> sameEmailMembers = departmentMemberRepository.list(Wrappers.lambdaQuery(DepartmentMemberDO.class)
                    .eq(DepartmentMemberDO::getEmail, email)
                    .eq(StrUtil.isNotBlank(scene), DepartmentMemberDO::getScene, scene));

            if (CollectionUtils.isNotEmpty(sameEmailMembers)) {
                boolean containsDuplicatePhone = sameEmailMembers.stream()
                        .anyMatch(m -> !Objects.equals(m.getId(), id));
                if (containsDuplicatePhone) {
                    throw new BizException(BizErrorCode.MEMBER_EMAIL_EXIST.getCode(), BizErrorCode.MEMBER_EMAIL_EXIST.getDesc());
                }
            }
        }
    }

    @Override
    public Boolean updateUserMemberInfo(UpdateUserMemberInfoDTO updateDTO) {
        String scene = getScene();
        String accountNo = StpUtil.getLoginIdAsString();

        // 校验重复
        duplicateCheck(updateDTO.getId(), updateDTO.getPhone(), updateDTO.getEmail(), scene);

        AccountDO account = accountRepository.getAccount(accountNo, null);
        account.setName(updateDTO.getName());

        UserDO user = userRepository.getOne(Wrappers.lambdaQuery(UserDO.class)
                .in(UserDO::getUserNo, account.getUserNo()));
        user.setUserName(updateDTO.getName());

        DepartmentMemberDO departmentMemberDO = buildDepartmentMember(updateDTO);

        transactionTemplate.executeWithoutResult(action -> {
            accountRepository.updateById(account);
            userRepository.updateById(user);
            departmentMemberRepository.updateById(departmentMemberDO);
        });
        return true;
    }


    @Override
    public Boolean removeMember(Long memberId) {
        // 查找成员信息
        DepartmentMemberDO member = departmentMemberRepository.getById(memberId);
        if (member == null) {
            LogUtil.error("要删除的成员不存在，memberId={}", memberId);
            throw new BizException(BizErrorCode.BIND_MEMBER_NOT_FOUND.getCode(), "要删除的成员不存在");
        }

        // 数据权限校验
        dataPermissionProcessor.checkDataPermissionByDepartmentId(member.getDepartmentId());

        // 查找与该成员绑定的账号
        List<UserMemberDO> userMemberList = userMemberRepository.list(Wrappers.lambdaQuery(UserMemberDO.class)
                .eq(UserMemberDO::getMemberId, memberId));

        if (CollectionUtils.isNotEmpty(userMemberList)) {
            LogUtil.error("成员已经绑定账号，无法删除");
            throw new BizException(BizErrorCode.MEMBER_BIND_ACCOUNT.getCode(), BizErrorCode.MEMBER_BIND_ACCOUNT.getDesc());
        }

        // 没有绑定账号，直接删除成员
        departmentMemberRepository.removeById(memberId);

        return true;
    }

    /**
     * 批量删除用户账号及相关数据
     */
    private void deleteUserAccounts(List<String> userNos) {
        if (CollectionUtils.isEmpty(userNos)) {
            LogUtil.warn("批量删除用户账号，用户编号列表为空");
            return;
        }

        LogUtil.info("开始批量删除用户账号，用户数量={}, userNos={}", userNos.size(), userNos);

        // 查找所有账号信息
        List<AccountDO> accounts = accountRepository.list(Wrappers.lambdaQuery(AccountDO.class)
                .in(AccountDO::getUserNo, userNos));

        if (CollectionUtils.isEmpty(accounts)) {
            LogUtil.warn("批量删除用户账号，未找到任何账号信息，userNos={}", userNos);
            return;
        }

        // 获取要释放占用状态的成员信息
        List<UserMemberDO> userMemberList = userMemberRepository.list(Wrappers.lambdaQuery(UserMemberDO.class)
                .in(UserMemberDO::getUserNo, userNos));

        List<Long> memberIds = userMemberList.stream()
                .map(UserMemberDO::getMemberId)
                .distinct()
                .collect(Collectors.toList());

        // 记录要删除的账号信息
        for (AccountDO account : accounts) {
            LogUtil.info("准备删除用户账号，accountNo={}, userNo={}, name={}",
                    account.getAccountNo(), account.getUserNo(), account.getName());
        }

        // 批量删除用户角色关联
        userRoleRepository.remove(Wrappers.lambdaQuery(UserRoleDO.class)
                .in(UserRoleDO::getUserNo, userNos));
        LogUtil.info("批量删除用户角色关联完成，用户数量={}", userNos.size());

        // 批量删除用户部门关联
        userDepartmentRepository.remove(Wrappers.lambdaQuery(UserDepartmentDO.class)
                .in(UserDepartmentDO::getUserNo, userNos));
        LogUtil.info("批量删除用户部门关联完成，用户数量={}", userNos.size());

        // 批量删除用户与成员的关联关系
        userMemberRepository.remove(Wrappers.lambdaQuery(UserMemberDO.class)
                .in(UserMemberDO::getUserNo, userNos));
        LogUtil.info("批量删除用户成员关联完成，用户数量={}", userNos.size());

        // 批量释放成员占用状态
        if (CollectionUtils.isNotEmpty(memberIds)) {
            departmentMemberRepository.update(Wrappers.lambdaUpdate(DepartmentMemberDO.class)
                    .in(DepartmentMemberDO::getId, memberIds)
                    .set(DepartmentMemberDO::getOccupyStatus, MemberOccupyStatusEnum.AVAILABLE.getCode()));
            LogUtil.info("批量释放成员占用状态完成，成员数量={}", memberIds.size());
        }

        // 批量删除用户账户关联
        userAccountRepository.remove(Wrappers.lambdaQuery(UserAccountDO.class)
                .in(UserAccountDO::getUserNo, userNos));
        LogUtil.info("批量删除用户账户关联完成，用户数量={}", userNos.size());

        // 批量删除客户需求方（筛选客户类型）
        List<String> customerUserNos = accounts.stream()
                .filter(account -> StrUtil.equals(account.getUserType(), UserTypeEnum.CUSTOMER.getCode()))
                .map(AccountDO::getUserNo)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(customerUserNos)) {
            productServiceCustomerRepository.remove(Wrappers.lambdaQuery(ProductServiceCustomerDO.class)
                    .in(ProductServiceCustomerDO::getUserNo, customerUserNos));
            LogUtil.info("批量删除客户信息完成，客户数量={}", customerUserNos.size());
        }

        // 批量删除用户
        userRepository.remove(Wrappers.lambdaQuery(UserDO.class)
                .in(UserDO::getUserNo, userNos));
        LogUtil.info("批量删除用户完成，用户数量={}", userNos.size());

        // 批量删除账号
        accountRepository.remove(Wrappers.lambdaQuery(AccountDO.class)
                .in(AccountDO::getUserNo, userNos));
        LogUtil.info("批量删除账号完成，账号数量={}", accounts.size());

        LogUtil.info("批量删除用户账号及相关数据完成，成功删除用户数量={}，释放成员占用状态数量={}", userNos.size(), memberIds.size());
    }

    @Override
    public List<DepartmentDTO> getDepartmentByTree(Long parentId) {
        QueryDepartmentTreeDTO queryDTO = new QueryDepartmentTreeDTO();
        queryDTO.setParentId(parentId);
        queryDTO.setMemberStatus(MemberStatusQueryEnum.ALL.getCode());
        return getDepartmentByTree(queryDTO);
    }

    @Override
    public List<DepartmentDTO> getDepartmentByTree(QueryDepartmentTreeDTO queryDTO) {
        String scene = getScene();
        List<DepartmentTreeDTO> flatList;
        String memberStatus = queryDTO.getMemberStatus();
        Long parentId = queryDTO.getParentId();

        // 根据是否有成员状态筛选条件选择不同的查询方法
        if (StringUtils.isNotBlank(memberStatus) && !StringUtils.equals(memberStatus, MemberStatusQueryEnum.ALL.getCode())) {
            // 使用带成员状态筛选的查询
            flatList = departmentMapper.getDepartmentTreeByParentIdWithMemberStatus(parentId, scene, memberStatus);
        } else {
            // 使用原有查询方法
            flatList = departmentMapper.getDepartmentTreeByParentId(parentId, scene);
        }

        if (CollectionUtils.isEmpty(flatList)) {
            return Lists.newArrayList();
        }
        judgeMemberRelatedWithAccount(flatList);

        // 查询所有部门数据（用于路径补全）
        Map<Long, DepartmentDTO> allDepartmentsMap = getAllDepartmentsAsMap();

        // 组装为树形结构，支持多个根部门
        List<DepartmentDTO> roots = buildMultiRootTree(flatList, allDepartmentsMap);

        // 对每个根部门进行排序
        for (DepartmentDTO root : roots) {
            sortTree(root);
        }

        LogUtil.info("查询部门树完成，parentId={}, memberStatus={}, 根部门数量={}", parentId, memberStatus, roots.size());
        return roots;
    }

    @Override
    public List<DepartmentDTO> getDepartmentByTreeWithDataPermission(QueryDepartmentTreeDTO queryDTO) {
        String userNo = (String) StpUtil.getExtra("userNo");
        Boolean isSuperAdmin = (Boolean) StpUtil.getExtra("isSuperAdmin");
        if (isSuperAdmin != null && isSuperAdmin) {
            return getDepartmentByTree(queryDTO);
        }

        List<RoleDO> roleDOs = getRoles(userNo);

        // 全局的权限查询所有部门数据
        boolean fullAccess = roleDOs.stream()
                .anyMatch(role -> Integer.valueOf(4).equals(role.getDataScope()));
        if (fullAccess) {
            return getDepartmentByTree(queryDTO);
        }

        // 其他情况只查询本部门的数据
        DepartmentDO rootDepartment = dataPermissionProcessor.getRootDepartment(userNo);
        // 只有查询根节点的时候才需要数据权限，此时设置为根节点的id
        if (queryDTO.getParentId() == -1) {
            queryDTO.setParentId(rootDepartment.getId());
        }
        return getDepartmentByTree(queryDTO);
    }


    @Override
    public List<DepartmentDTO> getSubDepartments(QuerySubDepartmentDTO queryDTO) {
        String userNo = queryDTO.getUserNo();
        List<UserDepartmentDO> userDepartments = userDepartmentRepository.list(Wrappers.lambdaQuery(UserDepartmentDO.class)
                .eq(UserDepartmentDO::getUserNo, userNo));
        if (CollectionUtils.isEmpty(userDepartments)) {
            return null;
        }

        List<Long> departmentIds = userDepartments.stream()
                .map(UserDepartmentDO::getDepartmentId)
                .collect(Collectors.toList());
        List<DepartmentTreeDTO> subDepartments = departmentMapper.getSubDepartmentsAndMembers(departmentIds, queryDTO.getDepth());
        if (CollectionUtils.isEmpty(subDepartments)) {
            return null;
        }

        List<DepartmentTreeDTO> departmentMembers = subDepartments.stream()
                .filter(m -> m.getMemberId() != null)
                .collect(Collectors.toList());
        // 判断当前成员是否关联了账号和用户
        judgeMemberRelatedWithAccount(departmentMembers);
        return buildDepartmentTree(subDepartments);
    }

    @Override
    public List<DepartmentTreeDTO> listSimpleDepartmentsByIds(List<Long> ids) {
        return departmentMapper.getSubDepartmentsAndMembers(ids, 0);
    }

    @Override
    public List<UserRootDepartmentDTO> getUserRootDepartments(UserRootDepartmentQueryDTO queryDTO) {
        try {
            // 该接口禁用租户id，可能是定时任务跑的
            TenantContext.disableTenantLine();

            List<UserDO> userDOs = userRepository.list(Wrappers.lambdaQuery(UserDO.class)
                    .in(UserDO::getUserNo, queryDTO.getUserNos()));
            if (CollectionUtils.isEmpty(userDOs)) {
                return null;
            }

            List<UserDepartmentDO> userDepartmentDOs = userDepartmentRepository.list(Wrappers.lambdaQuery(UserDepartmentDO.class)
                    .in(UserDepartmentDO::getUserNo, queryDTO.getUserNos()));
            if (CollectionUtils.isEmpty(userDepartmentDOs)) {
                return null;
            }

            Map<String, Long> userDepartmentMap = userDepartmentDOs.stream()
                    .collect(Collectors.toMap(UserDepartmentDO::getUserNo, UserDepartmentDO::getDepartmentId));

            List<Long> departmentIds = userDepartmentDOs.stream().map(UserDepartmentDO::getDepartmentId)
                    .collect(Collectors.toList());
            List<DepartmentDO> departmentDOs = departmentRepository.listByIds(departmentIds);
            Map<Long, DepartmentDO> departmentMap = departmentDOs.stream()
                    .collect(Collectors.toMap(DepartmentDO::getId, Function.identity()));

            List<UserRootDepartmentDTO> userRootDepartmentDTOs = Lists.newArrayList();
            for (String userNo : queryDTO.getUserNos()) {
                UserRootDepartmentDTO userRootDepartmentDTO = new UserRootDepartmentDTO();
                userRootDepartmentDTO.setUserNo(userNo);

                Long departmentId = userDepartmentMap.get(userNo);
                if (departmentId == null) {
                    userRootDepartmentDTO.setDepartmentDTO(null);
                } else {
                    // 找到用户所属部门的根部门
                    DepartmentDO userDepartment = departmentMap.get(departmentId);
                    if (userDepartment != null) {
                        DepartmentDO rootDepartment = findRootDepartment(userDepartment);
                        DepartmentFlatDTO departmentFlatDTO = convertToDepartmentFlatDTO(rootDepartment);
                        userRootDepartmentDTO.setDepartmentDTO(departmentFlatDTO);
                    } else {
                        userRootDepartmentDTO.setDepartmentDTO(null);
                    }
                }

                userRootDepartmentDTOs.add(userRootDepartmentDTO);
            }

            return userRootDepartmentDTOs;
        } finally {
            TenantContext.clear();
        }
    }

    @Override
    public WholeDepartmentDTO getWholeDepartmentInfo(WholeDepartmentQueryDTO queryDTO) {
        DepartmentDO rootDepartment = dataPermissionProcessor.getRootDepartment(queryDTO.getUserNo());
        List<DepartmentTreeDTO> subDepartments = getSubDepartments(rootDepartment.getId());
        if (CollUtil.isEmpty(subDepartments)) {
            return null;
        }

        List<Long> subDepartmentIds = getSubDepartmentIds(subDepartments);
        List<String> userNos = getUserNos(subDepartmentIds);
        if (CollUtil.isEmpty(userNos)) {
            return null;
        }

        WholeDepartmentDTO wholeDepartmentDTO = new WholeDepartmentDTO();
        wholeDepartmentDTO.setDepartmentIds(subDepartmentIds);
        wholeDepartmentDTO.setUserNos(userNos);
        return wholeDepartmentDTO;
    }

    @Override
    public List<SimpleUserInfoDTO> getDepartmentBindUsers(QueryDepartmentBindUserDTO queryDTO) {
        String scene = getScene();
        List<DepartmentTreeDTO> subDepartments = departmentMapper.getDepartmentTreeByParentId(queryDTO.getDepartmentId(), scene);
        if (CollectionUtils.isEmpty(subDepartments)) {
            return null;
        }

        List<SimpleUserInfoDTO> simpleUserInfoDTOs = Lists.newArrayList();

        List<Long> departmentIds = subDepartments.stream()
                .map(DepartmentTreeDTO::getDepartmentId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        departmentIds.add(queryDTO.getDepartmentId());

        List<UserDepartmentDO> userDepartmentDOs = userDepartmentRepository.list(Wrappers.lambdaQuery(UserDepartmentDO.class)
                .in(UserDepartmentDO::getDepartmentId, departmentIds));

        if (CollectionUtils.isNotEmpty(userDepartmentDOs)) {
            List<String> userNos = userDepartmentDOs.stream()
                    .map(UserDepartmentDO::getUserNo)
                    .distinct()
                    .collect(Collectors.toList());
            List<UserDO> userDOs = userRepository.list(Wrappers.lambdaQuery(UserDO.class)
                    .in(UserDO::getUserNo, userNos));
            userDOs.forEach(u -> {
                SimpleUserInfoDTO simpleUserInfoDTO = buildSimpleUserInfoDTO(u);
                simpleUserInfoDTOs.add(simpleUserInfoDTO);
            });
        }

        return simpleUserInfoDTOs;
    }

    private SimpleUserInfoDTO buildSimpleUserInfoDTO(UserDO u) {
        SimpleUserInfoDTO simpleUserInfoDTO = new SimpleUserInfoDTO();
        simpleUserInfoDTO.setUserNo(u.getUserNo());
        simpleUserInfoDTO.setName(u.getUserName());
        simpleUserInfoDTO.setPhone(u.getPhone());
        simpleUserInfoDTO.setTenantId(u.getTenantId());
        return simpleUserInfoDTO;
    }

    private DepartmentFlatDTO convertToDepartmentFlatDTO(DepartmentDO rootDepartment) {
        DepartmentFlatDTO departmentFlatDTO = new DepartmentFlatDTO();
        departmentFlatDTO.setId(rootDepartment.getId());
        departmentFlatDTO.setName(rootDepartment.getName());
        departmentFlatDTO.setLevel(rootDepartment.getLevel());
        departmentFlatDTO.setExt(rootDepartment.getExt());
        departmentFlatDTO.setParentId(rootDepartment.getParentId());
        return departmentFlatDTO;
    }

    private void judgeMemberRelatedWithAccount(List<DepartmentTreeDTO> departmentMembers) {
        if (CollectionUtils.isEmpty(departmentMembers)) {
            return;
        }

        // 初始化为false，现在账号一定是关联了成员的，如果没有下面会报错，需要修复数据
        departmentMembers.forEach(m -> m.setIsRelatedAccount(false));

        List<Long> memberIds = departmentMembers.stream()
                .map(DepartmentTreeDTO::getMemberId)
                .distinct()
                .collect(Collectors.toList());

        List<UserMemberDO> userMembers = userMemberRepository.list(Wrappers.lambdaQuery(UserMemberDO.class)
                .in(UserMemberDO::getMemberId, memberIds));

        // 目前一个成员只能关联一个用户
        Map<Long, UserMemberDO> userMemberMap = userMembers.stream()
                .collect(Collectors.toMap(UserMemberDO::getMemberId, Function.identity()));

        departmentMembers.forEach(departmentTreeDTO -> {
            Long memberId = departmentTreeDTO.getMemberId();
            if (memberId != null) {
                UserMemberDO userMemberDO = userMemberMap.get(memberId);
                if (userMemberDO == null) {
                    LogUtil.warn("当前成员没有绑定用户，成员编号为：{}", departmentTreeDTO.getMemberId());
                } else {
                    departmentTreeDTO.setIsRelatedAccount(true);
                    departmentTreeDTO.setRelatedUserNo(userMemberDO.getUserNo());
                }
            }
        });
    }


    public List<DepartmentDTO> buildDepartmentTree(List<DepartmentTreeDTO> rawData) {
        // 1. 分组：根据 departmentId 分组，构造部门和成员映射
        Map<Long, DepartmentDTO> departmentMap = new HashMap<>();
        Map<Long, List<DepartmentMemberDTO>> memberMap = new HashMap<>();

        for (DepartmentTreeDTO item : rawData) {
            Long departmentId = item.getDepartmentId();

            // 初始化部门
            departmentMap.putIfAbsent(departmentId, new DepartmentDTO(
                    departmentId, item.getDepartmentName(), item.getLevel(), item.getParentId(), new ArrayList<>(), new ArrayList<>()
            ));

            // 如果有成员数据，添加成员
            if (item.getMemberId() != null) {
                DepartmentMemberDTO member = new DepartmentMemberDTO();
                member.setId(item.getMemberId());
                member.setName(item.getMemberName());
                member.setType(item.getMemberType());
                member.setIsRelatedAccount(item.getIsRelatedAccount());
                member.setPhone(item.getMemberPhone());
                member.setEmail(item.getMemberEmail());
                member.setPosition(item.getMemberPosition());
                member.setRelatedUserNo(item.getRelatedUserNo());
                // 优先使用查询结果中的占用状态，如果没有则从数据库单独查询
                if (StringUtils.isNotBlank(item.getOccupyStatus())) {
                    member.setOccupyStatus(item.getOccupyStatus());
                } else {
                    member.setOccupyStatus(getOccupyStatusForMember(item.getMemberId()));
                }

                memberMap.computeIfAbsent(departmentId, k -> new ArrayList<>()).add(member);
            }
        }

        // 2. 将成员数据加入对应的部门
        memberMap.forEach((departmentId, members) -> {
            DepartmentDTO department = departmentMap.get(departmentId);
            if (department != null) {
                department.setMembers(members);
            }
        });

        // 3. 构建树形结构
        List<DepartmentDTO> result = new ArrayList<>();
        for (DepartmentDTO department : departmentMap.values()) {
            if (department.getParentId() == null || !departmentMap.containsKey(department.getParentId())) {
                // 根节点
                result.add(department);
            } else {
                // 添加到父节点的子节点中
                DepartmentDTO parent = departmentMap.get(department.getParentId());
                if (parent != null) {
                    parent.getChildren().add(department);
                }
            }
        }

        return result;
    }

    /**
     * 获取成员的占用状态
     */
    private String getOccupyStatusForMember(Long memberId) {
        DepartmentMemberDO member = departmentMemberRepository.getById(memberId);
        if (member != null && member.getOccupyStatus() != null) {
            return member.getOccupyStatus();
        }
        // 如果数据库中没有设置，默认为未占用
        return MemberOccupyStatusEnum.AVAILABLE.getCode();
    }

    private DepartmentDTO buildTree(List<DepartmentTreeDTO> flatList, Map<Long, DepartmentDTO> allDepartmentsMap) {
        Map<Long, DepartmentDTO> idToNodeMap = new HashMap<>();
        for (DepartmentTreeDTO flat : flatList) {
            idToNodeMap.computeIfAbsent(flat.getDepartmentId(), id -> {
                DepartmentDTO dto = new DepartmentDTO();
                dto.setId(flat.getDepartmentId());
                dto.setName(flat.getDepartmentName());
                dto.setLevel(flat.getLevel());
                dto.setParentId(flat.getParentId());
                dto.setMembers(new ArrayList<>());
                dto.setPath(new ArrayList<>());
                dto.setChildren(new ArrayList<>());
                return dto;
            });

            // 添加成员信息
            if (flat.getMemberName() != null) {
                DepartmentMemberDTO member = new DepartmentMemberDTO();
                member.setName(flat.getMemberName());
                member.setType(flat.getMemberType());
                member.setPhone(flat.getMemberPhone());
                member.setEmail(flat.getMemberEmail());
                member.setPosition(flat.getMemberPosition());
                member.setId(flat.getMemberId());
                member.setIsRelatedAccount(flat.getIsRelatedAccount());
                // 优先使用查询结果中的占用状态，如果没有则从数据库单独查询
                if (StringUtils.isNotBlank(flat.getOccupyStatus())) {
                    member.setOccupyStatus(flat.getOccupyStatus());
                } else {
                    member.setOccupyStatus(getOccupyStatusForMember(flat.getMemberId()));
                }
                idToNodeMap.get(flat.getDepartmentId()).getMembers().add(member);
            }
        }

        // 构建树并生成路径
        DepartmentDTO root = null;
        for (DepartmentDTO node : idToNodeMap.values()) {
            if (node.getParentId() == -1 || !idToNodeMap.containsKey(node.getParentId())) {
                root = node;
            } else {
                idToNodeMap.get(node.getParentId()).getChildren().add(node);
            }

            if (MapUtils.isNotEmpty(allDepartmentsMap)) {
                // 补全路径
                generateFullPath(node, allDepartmentsMap);
            }
        }

        return root;
    }

    private List<DepartmentDTO> buildMultiRootTree(List<DepartmentTreeDTO> flatList, Map<Long, DepartmentDTO> allDepartmentsMap) {
        Map<Long, DepartmentDTO> idToNodeMap = new HashMap<>();

        // 构建所有部门节点
        for (DepartmentTreeDTO flat : flatList) {
            idToNodeMap.computeIfAbsent(flat.getDepartmentId(), id -> {
                DepartmentDTO dto = new DepartmentDTO();
                dto.setId(flat.getDepartmentId());
                dto.setName(flat.getDepartmentName());
                dto.setLevel(flat.getLevel());
                dto.setParentId(flat.getParentId());
                dto.setMembers(new ArrayList<>());
                dto.setPath(new ArrayList<>());
                dto.setChildren(new ArrayList<>());
                return dto;
            });

            // 添加成员信息
            if (flat.getMemberName() != null) {
                DepartmentMemberDTO member = new DepartmentMemberDTO();
                member.setName(flat.getMemberName());
                member.setType(flat.getMemberType());
                member.setPhone(flat.getMemberPhone());
                member.setEmail(flat.getMemberEmail());
                member.setPosition(flat.getMemberPosition());
                member.setId(flat.getMemberId());
                member.setIsRelatedAccount(flat.getIsRelatedAccount());
                // 优先使用查询结果中的占用状态，如果没有则从数据库单独查询
                if (StringUtils.isNotBlank(flat.getOccupyStatus())) {
                    member.setOccupyStatus(flat.getOccupyStatus());
                } else {
                    member.setOccupyStatus(getOccupyStatusForMember(flat.getMemberId()));
                }
                member.setRelatedUserNo(flat.getRelatedUserNo());
                idToNodeMap.get(flat.getDepartmentId()).getMembers().add(member);
            }
        }

        // 构建树形结构并找出所有根节点
        List<DepartmentDTO> roots = new ArrayList<>();
        for (DepartmentDTO node : idToNodeMap.values()) {
            if (node.getParentId() == -1 || !idToNodeMap.containsKey(node.getParentId())) {
                roots.add(node);
            } else {
                idToNodeMap.get(node.getParentId()).getChildren().add(node);
            }

            // 补全路径
            if (MapUtils.isNotEmpty(allDepartmentsMap)) {
                generateFullPath(node, allDepartmentsMap);
            }
        }

        // 对根节点按ID排序
        roots.sort(Comparator.comparing(DepartmentDTO::getId));

        LogUtil.info("查询到{}个根部门，场景={}", roots.size(), getScene());

        return roots;
    }


    private void sortTree(DepartmentDTO root) {
        if (root == null) {
            return;
        }

        // 对子节点进行排序
        List<DepartmentDTO> children = root.getChildren();
        if (children != null && !children.isEmpty()) {
            // 按 ID 升序排序子节点
            children.sort(Comparator.comparing(DepartmentDTO::getId));
            // 递归对子节点进行排序
            for (DepartmentDTO child : children) {
                sortTree(child);
            }
        }

        // 对当前节点的成员进行排序
        List<DepartmentMemberDTO> members = root.getMembers();
        if (members != null && !members.isEmpty()) {
            // 按 ID 升序排序成员
            members.sort(Comparator.comparing(DepartmentMemberDTO::getId));
        }
    }

    private Map<Long, DepartmentDTO> getAllDepartmentsAsMap() {
        String scene = getScene();
        List<DepartmentDO> allDepartments = departmentRepository.list(Wrappers.lambdaQuery(DepartmentDO.class)
                .eq(DepartmentDO::getScene, scene));
        Map<Long, DepartmentDTO> departmentMap = new HashMap<>();
        for (DepartmentDO department : allDepartments) {
            DepartmentDTO dto = new DepartmentDTO();
            dto.setId(department.getId());
            dto.setName(department.getName());
            dto.setLevel(department.getLevel());
            dto.setParentId(department.getParentId());
            departmentMap.put(department.getId(), dto);
        }
        return departmentMap;
    }

    private void generateFullPath(DepartmentDTO node, Map<Long, DepartmentDTO> allDepartmentsMap) {
        if (node == null) {
            return;
        }

        List<DepartmentFlatDTO> path = new ArrayList<>();
        DepartmentDTO current = node;
        while (current != null) {
            DepartmentFlatDTO departmentFlatDTO = buildFlatDepartmentDTO(current);
            path.add(0, departmentFlatDTO);
            current = allDepartmentsMap.get(current.getParentId());
        }

        node.setPath(path);
    }

    private DepartmentFlatDTO buildFlatDepartmentDTO(DepartmentDTO departmentDTO) {
        DepartmentFlatDTO departmentFlatDTO = new DepartmentFlatDTO();
        departmentFlatDTO.setId(departmentDTO.getId());
        departmentFlatDTO.setName(departmentDTO.getName());
        departmentFlatDTO.setLevel(departmentDTO.getLevel());
        departmentFlatDTO.setParentId(departmentDTO.getParentId());
        return departmentFlatDTO;
    }

    private String getScene() {
        String scene;
        if (ObjectUtil.isNotEmpty(StpUtil.getExtra("scene"))) {
            scene = (String) StpUtil.getExtra("scene");
        } else {
            scene = null;
        }
        return scene;
    }

    private DepartmentDO findRootDepartment(DepartmentDO department) {
        if (department == null) {
            return null;
        }

        // 如果已经是根部门，直接返回
        if (department.getParentId() == -1) {
            return department;
        }

        // 向上查找直到找到根部门
        DepartmentDO current = department;
        while (current != null && current.getParentId() != -1) {
            current = departmentRepository.getById(current.getParentId());
        }

        return current;
    }

    @Override
    public List<DepartmentMemberDTO> listDepartmentMembers(DepartmentMemberQueryDTO queryDTO) {
        // 获取当前场景
        String scene = getScene();

        // 构建查询条件
        LambdaQueryWrapper<DepartmentMemberDO> queryWrapper = Wrappers.lambdaQuery(DepartmentMemberDO.class)
                .eq(DepartmentMemberDO::getDepartmentId, queryDTO.getDepartmentId())
                .eq(StrUtil.isNotBlank(scene), DepartmentMemberDO::getScene, scene);

        // 根据成员状态筛选
        if (StrUtil.isNotBlank(queryDTO.getMemberStatus()) &&
                !MemberStatusQueryEnum.ALL.getCode().equals(queryDTO.getMemberStatus())) {
            if (MemberOccupyStatusEnum.AVAILABLE.getCode().equals(queryDTO.getMemberStatus())) {
                queryWrapper.eq(DepartmentMemberDO::getOccupyStatus, MemberOccupyStatusEnum.AVAILABLE.getCode());
            } else if (MemberOccupyStatusEnum.OCCUPIED.getCode().equals(queryDTO.getMemberStatus())) {
                queryWrapper.eq(DepartmentMemberDO::getOccupyStatus, MemberOccupyStatusEnum.OCCUPIED.getCode());
            }
        }

        // 执行查询
        List<DepartmentMemberDO> memberList = departmentMemberRepository.list(queryWrapper);

        if (CollectionUtils.isEmpty(memberList)) {
            LogUtil.info("未查询到部门成员，部门ID={}", queryDTO.getDepartmentId());
            return Lists.newArrayList();
        }

        // 查询成员是否关联了账号
        List<DepartmentMemberDTO> resultList = memberList.stream()
                .map(this::convertToDepartmentMemberDTO)
                .collect(Collectors.toList());

        return resultList;
    }

    /**
     * 将DepartmentMemberDO转换为DepartmentMemberDTO
     */
    private DepartmentMemberDTO convertToDepartmentMemberDTO(DepartmentMemberDO memberDO) {
        DepartmentMemberDTO memberDTO = new DepartmentMemberDTO();
        memberDTO.setId(memberDO.getId());
        memberDTO.setName(memberDO.getName());
        memberDTO.setType(memberDO.getType());
        memberDTO.setPhone(memberDO.getPhone());
        memberDTO.setEmail(memberDO.getEmail());
        memberDTO.setPosition(memberDO.getPosition());
        memberDTO.setOccupyStatus(memberDO.getOccupyStatus());
        return memberDTO;
    }

    private DepartmentMemberDO buildDepartmentMember(DepartmentAddMemberDTO addMemberDTO, String scene, String status) {
        DepartmentMemberDO departmentMemberDO = new DepartmentMemberDO();
        departmentMemberDO.setDepartmentId(addMemberDTO.getDepartmentId());
        departmentMemberDO.setPhone(addMemberDTO.getPhone());
        departmentMemberDO.setEmail(addMemberDTO.getEmail());
        departmentMemberDO.setPosition(addMemberDTO.getPosition());
        departmentMemberDO.setName(addMemberDTO.getName());
        departmentMemberDO.setType(addMemberDTO.getType());
        departmentMemberDO.setId(addMemberDTO.getId());
        departmentMemberDO.setScene(scene);
        // 新创建的成员默认为未占用状态
        if (StrUtil.isNotBlank(status)) {
            departmentMemberDO.setOccupyStatus(status);
        }
        return departmentMemberDO;
    }

    private DepartmentMemberDO buildDepartmentMember(UpdateUserMemberInfoDTO updateDTO) {
        DepartmentMemberDO departmentMemberDO = new DepartmentMemberDO();
        departmentMemberDO.setDepartmentId(updateDTO.getDepartmentId());
        departmentMemberDO.setPhone(updateDTO.getPhone());
        departmentMemberDO.setEmail(updateDTO.getEmail());
        departmentMemberDO.setPosition(updateDTO.getPosition());
        departmentMemberDO.setName(updateDTO.getName());
        departmentMemberDO.setId(updateDTO.getId());
        return departmentMemberDO;
    }

    private void checkDuplicatedNameInDepartments(DepartmentSaveDTO saveDTO, String scene) {
        List<DepartmentDO> allDepartments = departmentRepository.list(Wrappers.lambdaQuery(DepartmentDO.class)
                .eq(StrUtil.isNotBlank(scene), DepartmentDO::getScene, scene));

        AtomicReference<Long> rootId = new AtomicReference<>();
        for (DepartmentDO departmentDO : allDepartments) {
            List<Long> fullPathList = JSONUtil.toList(departmentDO.getFullPath(), Long.class);
            if (fullPathList.contains(saveDTO.getParentId())) {
                if (fullPathList.contains(-1L)) {
                    rootId.set(fullPathList.get(1));
                } else {
                    rootId.set(fullPathList.get(0));
                }
                break;
            }
        }

        List<DepartmentDO> currentDepartmentList = allDepartments.stream().filter(e -> {
            List<Long> fullPathList = JSONUtil.toList(e.getFullPath(), Long.class);
            return fullPathList.contains(rootId.get());
        }).collect(Collectors.toList());

        boolean existDuplicatedName = currentDepartmentList.stream()
                .anyMatch(e -> StrUtil.equals(saveDTO.getName(), e.getName()));
        if (existDuplicatedName) {
            throw new BizException(BizErrorCode.ORGANIZATION_NAME_ALREADY_EXIST.getCode(), BizErrorCode.ORGANIZATION_NAME_ALREADY_EXIST.getDesc());
        }
    }

    private List<DepartmentTreeDTO> getSubDepartments(Long rootId) {
        String scene = (String) StpUtil.getExtra("scene");
        List<DepartmentTreeDTO> subDepartments = departmentMapper.getDepartmentTreeByParentId(rootId, scene);
        if (CollectionUtils.isEmpty(subDepartments)) {
            return null;
        }

        return subDepartments;
    }

    private List<Long> getSubDepartmentIds(List<DepartmentTreeDTO> subDepartments) {
        List<DepartmentTreeDTO> subDepartmentList = subDepartments.stream()
                .filter(d -> d.getDepartmentId() != null)
                .collect(Collectors.toList());

        return subDepartmentList.stream()
                .map(DepartmentTreeDTO::getDepartmentId)
                .distinct()
                .collect(Collectors.toList());
    }

    private List<String> getUserNos(List<Long> subDepartmentIds) {
        List<UserDepartmentDO> userDepartments = userDepartmentRepository.list(Wrappers.lambdaQuery(UserDepartmentDO.class)
                .in(UserDepartmentDO::getDepartmentId, subDepartmentIds));
        if (CollUtil.isEmpty(userDepartments)) {
            return null;
        }

        return userDepartments.stream()
                .map(UserDepartmentDO::getUserNo)
                .distinct()
                .collect(Collectors.toList());
    }

    private List<RoleDO> getRoles(String userNo) {
        List<UserRoleDO> userRoles = userRoleRepository.list(Wrappers.lambdaQuery(UserRoleDO.class)
                .in(UserRoleDO::getUserNo, userNo));
        if (CollectionUtils.isEmpty(userRoles)) {
            throw new BizException(BizErrorCode.USER_ROLE_NOT_EXIST.getCode(), BizErrorCode.USER_ROLE_NOT_EXIST.getDesc());
        }

        List<String> roleCodes = userRoles.stream()
                .map(UserRoleDO::getRoleCode)
                .collect(Collectors.toList());
        List<RoleDO> roleDOs = roleRepository.list(Wrappers.lambdaQuery(RoleDO.class)
                .in(RoleDO::getRoleCode, roleCodes));
        if (CollectionUtils.isEmpty(roleDOs)) {
            throw new BizException(BizErrorCode.ROLE_NOT_FOUND.getCode(), BizErrorCode.ROLE_NOT_FOUND.getDesc());
        }
        return roleDOs;
    }

}
