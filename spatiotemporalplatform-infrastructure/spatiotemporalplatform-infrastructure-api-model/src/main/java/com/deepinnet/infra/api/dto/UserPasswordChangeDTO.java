package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> wong
 * @create 2024/11/26 16:37
 * @Description
 */
@Data
public class UserPasswordChangeDTO {
    @ApiModelProperty(value = "旧密码")
    private String oldPassword;

    @ApiModelProperty(value = "新密码")
    @NotBlank(message = "新密码不能为空")
    private String newPassword;

    /**
     * 应用场景
     */
    @ApiModelProperty(value = "matching_platform-撮合平台，operation_center-撮合运营中心,traffic_police-交警")
    private String scene;
}
