package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2024/11/26 11:15
 * @Description
 */
@Data
public class RoleSaveDTO {
    @ApiModelProperty(value = "角色id")
    private Long id;

    @ApiModelProperty(value = "角色名称")
    @NotBlank(message = "角色名称不能为空")
    private String roleName;

    @ApiModelProperty(value = "角色描述信息")
    private String desc;

    @ApiModelProperty(value = "数据权限范围：1-个人数据，2-组织数据（本级及下属），3-组织数据（全局），4-全局数据")
    private Integer dataScope;

    @ApiModelProperty(value = "权限列表")
    @NotEmpty(message = "权限列表不能为空")
    private List<PermissionFlatDTO> permissions;
}
