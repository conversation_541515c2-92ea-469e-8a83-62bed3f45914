package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 部门成员查询DTO
 *
 * <AUTHOR> wong
 * @create 2025/07/08
 */
@Data
@ApiModel("部门成员查询DTO")
public class DepartmentMemberQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门ID
     */
    @NotNull(message = "部门ID不能为空")
    @ApiModelProperty(value = "部门ID", required = true, example = "1")
    private Long departmentId;

    /**
     * 成员状态
     * ALL - 全部成员
     * AVAILABLE - 未被占用的成员
     * OCCUPIED - 已被占用的成员
     */
    @ApiModelProperty(value = "成员状态：all-全部成员，available-未被占用的成员，occupied-已被占用的成员", example = "ALL")
    private String memberStatus;
}