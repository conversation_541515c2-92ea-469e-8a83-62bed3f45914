package com.deepinnet.infra.api.enums;

/**
 * 快照类型枚举
 *
 * <AUTHOR> wong
 * @create 2025/04/25
 */
public enum SnapshotTypeEnum {

    /**
     * 客户数据快照
     */
    CUSTOMER_DATA(1, "客户数据快照"),

    /**
     * 服务商数据快照
     */
    SUPPLIER_DATA(2, "服务商数据快照");

    private final Integer code;
    private final String desc;

    SnapshotTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取对应的枚举
     *
     * @param code 状态码
     * @return 对应的枚举值
     */
    public static SnapshotTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SnapshotTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 