package com.deepinnet.infra.api.dto;

import com.deepinnet.infra.api.enums.StoreBucketTypeEnum;
import lombok.*;

import javax.validation.constraints.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2022/11/7 13:58
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StoreQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 0-public ｜ 1-private
     *
     * @see StoreBucketTypeEnum
     */
    @NotNull
    @Min(0)
    @Max(1)
    private Integer bucketType;

    /**
     * bucketName
     */
    private String bucketName;

    /**
     * 过期时间间隔，毫秒
     */
    private Long expirationMillisecond;

    /**
     * key
     */
    @NotNull
    private String key;

}
