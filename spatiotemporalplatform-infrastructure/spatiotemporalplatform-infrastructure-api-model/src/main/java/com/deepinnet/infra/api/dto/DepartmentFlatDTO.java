package com.deepinnet.infra.api.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2024/12/18 14:51
 * @Description
 */
@Data
public class DepartmentFlatDTO {

    /**
     * 自增ID
     */
    private Long id;

    /**
     * 部门名称
     */
    private String name;

    /**
     * 部门层级
     */
    private Integer level;

    /**
     * 父级的id
     */
    private Long parentId;

    /**
     * 当前部门完整路径
     */
    private String fullPath;

    /**
     * 拓展字段
     */
    private String ext;

    /**
     * 当前部门完整路径名称
     */
    private List<String> fullPathName;
}
