package com.deepinnet.infra.api.enums;

import cn.hutool.core.util.EnumUtil;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> wong
 * @create 2024/12/2 10:30
 * @Description
 */
@Getter
@AllArgsConstructor
public enum MemberTypeEnums {

    RESPONSIBLE_PERSON("responsible_person", "负责人"),

    NORMAL_PERSON("normal_person", "普通成员"),
    ;
    private final String code;

    private final String desc;


    public static MemberTypeEnums getByCode(String code) {
        return EnumUtil.getBy(MemberTypeEnums.class, c -> StringUtils.equals(c.getCode(), code));
    }
}
