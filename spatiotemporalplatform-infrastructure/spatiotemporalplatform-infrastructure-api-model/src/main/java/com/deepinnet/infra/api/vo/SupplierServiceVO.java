package com.deepinnet.infra.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 服务商服务VO
 *
 * <AUTHOR> wong
 * @create 2025/04/21
 */
@Data
@ApiModel("服务商服务VO")
public class SupplierServiceVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 服务名称
     */
    @ApiModelProperty(value = "服务名称")
    private String serviceName;

    /**
     * 服务编码
     */
    @ApiModelProperty(value = "服务编码")
    private String serviceCode;
} 