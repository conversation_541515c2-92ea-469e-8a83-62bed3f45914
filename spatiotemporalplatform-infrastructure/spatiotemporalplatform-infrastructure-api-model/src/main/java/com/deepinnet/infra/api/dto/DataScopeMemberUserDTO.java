package com.deepinnet.infra.api.dto;

import com.deepinnet.infra.api.enums.MemberTypeEnums;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * <PERSON><PERSON> z<PERSON>
 * Date 2025-01-07
 **/

@Data
public class DataScopeMemberUserDTO {

    private String userNo;

    private String name;

    private Long memberId;

    private MemberTypeEnums type;

    private Boolean isRelatedAccount;


    @JsonIgnore
    public boolean isResponsiblePerson() {
        return MemberTypeEnums.RESPONSIBLE_PERSON.equals(type);
    }


    @JsonIgnore
    public boolean isNormalAndRelatedAccount() {
        return MemberTypeEnums.NORMAL_PERSON.equals(type) && isRelatedAccount;
    }

}
