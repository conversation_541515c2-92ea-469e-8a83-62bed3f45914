package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2024/12/24 15:24
 * @Description
 */
@Data
public class SimpleDepartmentDTO {

    @ApiModelProperty(value = "部门id")
    private Long id;

    @ApiModelProperty(value = "部门名称")
    private String name;

    private String userNo;

    private String userName;

    private String memberPhone;

    private List<Long> fullPath;

    private List<String> fullPathName;
}
