package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2025/4/22 20:01
 * @Description
 */
@Data
public class UserQueryDTO {

    /**
     * 根据userNo精确查询
     */
    @ApiModelProperty(value = "根据userNo精确查询")
    private List<String> userNos;

    /**
     * 根据userName模糊查询
     */
    @ApiModelProperty(value = "根据userName模糊查询")
    private String userName;

    /**
     * @see com.deepinnet.infra.api.enums.UserTypeEnum
     * 要查询的用户类型，不传查询所有
     */
    @ApiModelProperty(value = "要查询的用户类型，不传查询所有：supplier-服务商，customer-客户，operation-运营")
    private String userType;
}
