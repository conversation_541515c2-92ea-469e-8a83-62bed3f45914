package com.deepinnet.infra.api.client;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.api.vo.ApprovalVO;
import io.swagger.annotations.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR> wong
 * @create 2025/2/11 10:05
 * @Description
 */
@FeignClient(name = "approvalClient", url = "${stpf.service.url}")
public interface ApprovalClient {

    @ApiOperation("分页查询审核记录")
    @PostMapping("/approval/page")
    Result<CommonPage<ApprovalVO>> pageQuery(@RequestBody @Valid ApprovalQueryDTO queryDTO);

    @ApiOperation("获取审核记录详情")
    @GetMapping("/approval/detail")
    Result<ApprovalDetailDTO> getDetail(@RequestParam("approvalId") String approvalId);

    @ApiOperation("提交审核处理")
    @PostMapping("/approval/process")
    Result<Boolean> process(@RequestBody @Valid ApprovalProcessDTO processDTO);
}
