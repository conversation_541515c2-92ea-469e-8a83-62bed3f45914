package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 服务商查询DTO
 *
 * <AUTHOR> wong
 * @create 2024/08/10
 */
@Data
@ApiModel(description = "服务商查询参数")
public class SupplierQueryDTO {

    /**
     * 页码
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码最小为1")
    @ApiModelProperty(value = "页码", required = true, example = "1")
    private Integer pageNum;

    /**
     * 每页条数
     */
    @NotNull(message = "每页条数不能为空")
    @Min(value = 1, message = "每页条数最小为1")
    @Max(value = 1000, message = "每页条数最大为1000")
    @ApiModelProperty(value = "每页条数", required = true, example = "10")
    private Integer pageSize;

    /**
     * 登录手机号码
     */
    @ApiModelProperty(value = "登录手机号码", example = "18273888921")
    private String phone;

    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称", example = "深圳市高度创新科技有限公司")
    private String companyName;

    /**
     * 联系人姓名
     */
    @ApiModelProperty(value = "联系人姓名", example = "张获胜")
    private String contactName;

    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话", example = "17263772833")
    private String contactPhone;

    /**
     * 审核状态：-1-初始状态（未认证），0-审核中，1-已认证，2-不通过
     */
    @ApiModelProperty(value = "审核状态：-1-初始状态（未认证），0-审核中，1-已认证，2-不通过", example = "1")
    private Integer approvalStatus;
} 