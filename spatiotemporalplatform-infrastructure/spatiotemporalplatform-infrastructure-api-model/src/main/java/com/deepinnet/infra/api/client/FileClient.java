package com.deepinnet.infra.api.client;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.FileInfoDTO;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.core.io.Resource;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR> wong
 * @create 2025/2/11 10:05
 * @Description
 */
@FeignClient(name = "fileClient", url = "${stpf.service.url}")
public interface FileClient {

    /**
     * 上传文件
     *
     * @param file       文件
     * @return 文件URL
     */
    @PostMapping(value = "/api/file/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation("上传文件")
    Result<FileInfoDTO> uploadFile(
            @ApiParam(value = "文件", required = true) @RequestPart("file") MultipartFile file);

    /**
     * 下载文件
     *
     * @param objectName 对象名称
     * @param filename   下载时的文件名（可选，默认为对象名称）
     * @return 文件流
     */
    @GetMapping("/api/file/download/objectName")
    @ApiOperation("下载文件")
    ResponseEntity<Resource> downloadFileByObjectName(
            @ApiParam(value = "对象名称", required = true) @RequestParam("objectName") String objectName,
            @ApiParam(value = "下载文件名（可选）") @RequestParam(value = "filename", required = false) String filename);

    /**
     * 下载文件
     *
     * @param fileUrl   文件路径
     * @return 文件流
     */
    @GetMapping("/api/file/download/fileUrl")
    @ApiOperation("下载文件")
    ResponseEntity<Resource> downloadFileByUrl(
            @ApiParam(value = "文件路径", required = true) @RequestParam("fileUrl") String fileUrl);

    /**
     * 获取文件URL
     *
     * @param objectName 对象名称
     * @return 文件URL
     */
    @GetMapping("/api/file/url")
    @ApiOperation("获取文件URL")
    Result<String> getFileUrl(
            @ApiParam(value = "对象名称", required = true) @RequestParam("objectName") String objectName);

}
