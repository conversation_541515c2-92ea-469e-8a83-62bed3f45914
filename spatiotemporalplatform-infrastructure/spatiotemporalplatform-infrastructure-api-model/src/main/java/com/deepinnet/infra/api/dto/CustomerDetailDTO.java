package com.deepinnet.infra.api.dto;

import io.swagger.annotations.*;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 客户单位详情DTO
 *
 * <AUTHOR> wong
 * @create 2025/04/22
 */
@Data
@ApiModel("客户单位详情DTO")
public class CustomerDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户编号
     */
    @ApiModelProperty(value = "用户编号")
    private String userNo;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String organizationName;

    /**
     * 区域code
     */
    @ApiModelProperty(value = "区域code")
    private String regionCode;

    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称")
    private String regionName;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    private String address;

    /**
     * 单位身份证明照片路径列表
     */
    @ApiModelProperty(value = "单位身份证明照片路径列表")
    private List<String> identifiedPictureList;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String phone;

    /**
     * 联系人信息
     */
    @ApiModelProperty(value = "联系人信息")
    private List<ContactInfoDTO> contactInfoDTOs;

    /**
     * 服务类型列表
     */
    @ApiModelProperty(value = "是否同意合作协议")
    private Boolean cooperationAgreement;

    /**
     * 身份类型：1-工商企业，2-政府/事业单位
     */
    @ApiModelProperty(value = "身份类型：1-工商企业，2-政府/事业单位")
    private Integer identityType;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    private String socialCreditCode;

    /**
     * 审核状态：-1-初始状态（未认证），0-审核中，1-已认证，2-不通过
     */
    @ApiModelProperty(value = "审核状态：-1-初始状态（未认证），0-审核中，1-已认证，2-不通过")
    private Integer approvalStatus;

    /**
     * 审核状态描述
     */
    @ApiModelProperty(value = "审核状态描述")
    private String approvalStatusDesc;
} 