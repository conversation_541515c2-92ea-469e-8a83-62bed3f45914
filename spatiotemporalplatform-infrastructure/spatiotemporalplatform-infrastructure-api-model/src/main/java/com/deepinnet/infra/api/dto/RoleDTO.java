package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.*;

/**
 * <AUTHOR> wong
 * @create 2024/11/26 11:00
 * @Description
 */
@Data
public class RoleDTO {

    @ApiModelProperty(value = "角色id")
    private Long id;

    @ApiModelProperty(value = "角色code")
    private String roleCode;

    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "角色描述")
    private String roleDesc;

    @ApiModelProperty(value = "创建人id")
    private String creatorId;

    @ApiModelProperty(value = "创建人名称")
    private String creatorName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "数据权限范围：1-个人数据，2-组织数据（本级及下属），3-组织数据（全局），4-全局数据")
    private Integer dataScope;

    @ApiModelProperty(value = "权限列表")
    private List<RolePermissionDTO> rolePermissionList;
}
