package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> wong
 * @create 2025/4/22 20:02
 * @Description
 */
@Data
public class UserDetailDTO {

    /**
     * 用户编号
     */
    @ApiModelProperty(value = "用户编号")
    private String userNo;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String phone;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    /**
     * 用户类型
     *
     * @see com.deepinnet.infra.api.enums.UserTypeEnum
     */
    @ApiModelProperty(value = "用户类型")
    private String userType;

    /**
     * 注册时间
     */
    @ApiModelProperty(value = "注册时间")
    private Long registrationTime;

    @ApiModelProperty(value = "需要展示的名称")
    private String showName;
    
    /**
     * 账号状态：0-正常，1-冻结
     * @see com.deepinnet.infra.api.enums.AccountStatusEnum
     */
    @ApiModelProperty(value = "账号状态：0-正常，1-冻结")
    private Integer status;

    /**
     * 产品需求方，当userType=customer的时候存在值
     */
    @ApiModelProperty(value = "产品需求方，当userType=customer的时候存在值")
    private CustomerDetailDTO customerDetailDTO;

    /**
     * 产品服务商，当userType=supplier的时候存在值
     */
    @ApiModelProperty(value = "产品服务商，当userType=supplier的时候存在值")
    private SupplierDetailDTO supplierDetailDTO;
}
