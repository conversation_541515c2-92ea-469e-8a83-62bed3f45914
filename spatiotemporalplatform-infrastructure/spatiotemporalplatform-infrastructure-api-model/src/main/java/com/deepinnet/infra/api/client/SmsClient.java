package com.deepinnet.infra.api.client;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR> wong
 * @create 2025/2/11 10:05
 * @Description
 */
@FeignClient(name = "smsClient", url = "${stpf.service.url}")
public interface SmsClient {

    /**
     * 发送验证码
     *
     * @param requestDTO 请求参数
     * @return 发送结果
     */
    @PostMapping("/domain/sms/code/send")
    Result<SendSmsResultDTO> sendVerificationCode(@Valid @RequestBody VerifyCodeRequestDTO requestDTO);

    /**
     * 验证验证码
     *
     * @param requestDTO 请求参数
     * @return 验证结果
     */
    @PostMapping("/domain/sms/code/verify")
    Result<Boolean> verifyCode(@RequestBody VerifyCodeRequestDTO requestDTO);
}
