package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "文件信息")
public class FileInfoDTO {
    
    @ApiModelProperty("文件URL")
    private String url;
    
    @ApiModelProperty("原始文件名")
    private String filename;
    
    @ApiModelProperty("对象名称")
    private String objectName;
    
    @ApiModelProperty("文件大小(字节)")
    private Long size;
    
    @ApiModelProperty("内容类型")
    private String contentType;
    
    @ApiModelProperty("存储桶名称")
    private String bucketName;
} 