package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.List;

/**
 * 服务商信息DTO
 *
 * <AUTHOR> wong
 * @create 2025/04/21
 */
@Data
@ApiModel("服务商信息DTO")
public class SupplierInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 公司名称
     */
    @NotBlank(message = "公司名称不能为空")
    @ApiModelProperty(value = "公司名称", required = true, example = "深圳市高度创新科技有限公司")
    @Size(max = 30, message = "公司名称不能超过20个字")
    private String companyName;

    /**
     * 营业执照号
     */
    @ApiModelProperty(value = "营业执照号", required = true, example = "91440300MA5F7TXQ2B")
    private String businessLicenseNo;

    /**
     * 公司地址
     */
    @NotBlank(message = "公司地址不能为空")
    @ApiModelProperty(value = "公司地址", required = true, example = "深圳市龙岗区XX街道")
    @Size(max = 50, message = "公司地址不能超过50个字")
    private String companyAddress;

    /**
     * 区域code
     */
    @NotBlank(message = "区域code不能为空")
    @ApiModelProperty(value = "区域code")
    private String regionCode;

    /**
     * 区域名称
     */
    @NotBlank(message = "区域名称不能为空")
    @ApiModelProperty(value = "区域名称")
    private String regionName;

    /**
     * 营业执照照片路径
     */
    @ApiModelProperty(value = "营业执照照片路径")
    private List<String> businessLicensePicPathList;

    /**
     * 统一社会信用代码
     */
    @NotBlank(message = "统一社会信用代码不能为空")
    @ApiModelProperty(value = "统一社会信用代码", required = true, example = "SZ283893940400")
    @Size(max = 30, message = "统一社会信用代码不能超过30个字母")
    private String socialCreditCode;

    /**
     * 低空飞行相关资质照片路径列表，多张照片路径用英文逗号分隔
     */
    @ApiModelProperty(value = "低空飞行相关资质照片路径列表，多张照片路径用英文逗号分隔")
    private List<String> qualificationPicPathList;

    /**
     * 联系人信息列表
     */
    @NotEmpty(message = "联系人信息不能为空")
    @Valid
    @ApiModelProperty(value = "联系人信息列表", required = true)
    private List<SupplierContactInfoDTO> contactInfoList;

    /**
     * 服务类型列表
     */
    @ApiModelProperty(value = "服务类型列表", required = true)
    private List<String> serviceCodeList;

    @NotNull(message = "是否需要审核")
    @ApiModelProperty(value = "是否需要审核", required = true)
    private Boolean needAudit;
} 