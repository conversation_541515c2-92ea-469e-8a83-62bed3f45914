package com.deepinnet.infra.api.client;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2025/2/11 10:05
 * @Description
 */
@FeignClient(name = "userClient", url = "${stpf.service.url}")
public interface UserClient {
    /**
     * 用户登录
     */
    @PostMapping("/domain/user/login")
    Result<UserLoginSuccessDTO> login(@Valid @RequestBody UserLoginDTO loginDTO);

    /**
     * 获取用户信息
     */
    @PostMapping("/domain/user/info")
    Result<UserInfoDTO> getAdminInfo();

    /**
     * 修改用户密码
     */
    @PostMapping("/domain/user/password")
    Result<Boolean> changePassword(@Valid @RequestBody UserPasswordChangeDTO passwordChangeDTO);

    @PostMapping("/domain/user/password/forget")
    Result<Boolean> forgetPassword(@RequestBody UserPasswordForgetDTO passwordForgetDTO);

    /**
     * 用户登出
     */
    @PostMapping("/domain/user/logout")
    Result<Boolean> logout();

    /**
     * 根据成员id获取用户信息
     *
     * @param memberIds
     * @return
     */
    @GetMapping("/domain/user/query/memberId")
    Result<List<UserMemberDTO>> getUserInfoByMemberId(@RequestParam("memberIds") List<Long> memberIds);

    /**
     * 根据用户编号列表获取用户
     *
     * @param userNos
     * @return
     */
    @GetMapping("/domain/user/query/userNos")
    Result<List<UserMemberDTO>> getUserInfoByUserNo(@RequestParam("userNos") List<String> userNos);

    /**
     * 注册用户
     *
     * @param registerDTO
     * @return
     */
    @GetMapping("/domain/user/register")
    Result<Boolean> register(@Valid @RequestBody UserRegisterDTO registerDTO);

    /**
     * 校验用户信息状态
     *
     * @return
     */
    @PostMapping("/domain/user/checkInfo")
    Result<Boolean> checkUserInfoStatus();

    /**
     * 用户信息是否填写
     */
    @ApiOperation("获取用户绑定的部门")
    @PostMapping("/domain/user/department")
    Result<List<SimpleDepartmentDTO>> getUserDepartments();

    @ApiOperation("查询用户关联的部门以及查询用户部门的fullPath")
    @PostMapping("/domain/user/department/fullPath")
    Result<List<SimpleDepartmentDTO>> getUserRelatedDepartment(@Valid @RequestBody QueryUserRelatedDepartmentDTO queryDTO);

    @PostMapping("/domain/user/data/access")
    Result<DataAccessDTO> getAvailableQueryData();

    @PostMapping("/domain/user/detail/list")
    Result<List<UserDetailDTO>> getUserDetailList(@RequestBody UserQueryDTO queryDTO);

    /**
     * 根据用户编号列表获取用户简略信息
     *
     * @param userNos
     * @return
     */
    @GetMapping("/domain/user/querySimple/userNos")
    Result<List<UserMemberDTO>> getUserInfoSimpleByUserNos(@RequestParam("userNos") List<String> userNos);

    @PostMapping("/domain/user/detail/list/tenantId")
    Result<List<UserDetailDTO>> getUserDetailListWithoutTenantId(@RequestBody UserQueryDTO queryDTO);

    @PostMapping("/domain/user/approval/status")
    Result<List<UserApprovalStatusDTO>> getUserApprovalStatus(@Valid @RequestBody QueryUserApprovalStatusDTO queryDTO);

    /**
     * 检查当前用户密码状态（是否已完成初始化）
     *
     * @return 密码状态检查结果
     */
    @GetMapping("/domain/user/account/initialized")
    Result<Boolean> checkUserInitializedStatus();

    @PostMapping("/domain/user/list")
    Result<List<SimpleUserInfoDTO>> getSimpleUserInfoWithoutTenantId(@RequestBody QueryUserInfoDTO queryDTO);

    @PostMapping("/domain/user/data/accessTree")
     Result<List<DataScopeTreeNodeDTO>> getAvailableDataScopeTree();
}
