package com.deepinnet.infra.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 成员身份类型枚举
 *
 * <AUTHOR> wong
 * @create 2025/8/13 
 * @Description 用于标识部门成员的身份类型
 */
@Getter
@AllArgsConstructor
public enum MemberIdentityTypeEnum {
    
    /**
     * 飞手
     */
    PILOT("pilot", "飞手");
    
    /**
     * 身份类型代码
     */
    private final String code;
    
    /**
     * 身份类型名称
     */
    private final String name;
    
    /**
     * 根据代码获取身份类型
     *
     * @param code 身份类型代码
     * @return 身份类型枚举
     */
    public static MemberIdentityTypeEnum getByCode(String code) {
        for (MemberIdentityTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
