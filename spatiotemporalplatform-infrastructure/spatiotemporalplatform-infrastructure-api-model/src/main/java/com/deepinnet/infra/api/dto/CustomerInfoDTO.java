package com.deepinnet.infra.api.dto;

import io.swagger.annotations.*;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.List;

/**
 * 客户单位信息DTO
 *
 * <AUTHOR> wong
 * @create 2025/04/22
 */
@Data
@ApiModel("客户单位信息DTO")
public class CustomerInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 单位名称
     */
    @NotBlank(message = "单位名称不能为空")
    @Size(max = 30, message = "单位名称不能超过30个字")
    @ApiModelProperty(value = "单位名称", required = true, example = "深圳市龙岗区交警支队")
    private String unitName;

    /**
     * 省市区code
     */
    @NotBlank(message = "省市区code，用-分割")
    @ApiModelProperty(value = "省市区code", required = true)
    private String regionCode;

    /**
     * 省市区名称
     */
    @NotBlank(message = "省市区名称，用-分割")
    @ApiModelProperty(value = "省市区名称", required = true)
    private String regionName;

    /**
     * 详细地址
     */
    @NotBlank(message = "详细地址不能为空")
    @ApiModelProperty(value = "详细地址", required = true)
    @Size(max = 50, message = "详细地址不能超过50个字")
    private String detailAddress;

    /**
     * 证明材料照片路径列表
     */
    @NotNull(message = "证明材料不能为空")
    @ApiModelProperty(value = "证明材料照片路径列表", required = true)
    private List<String> identityPicPathList;

    /**
     * 联系人信息
     */
    @NotNull(message = "联系人信息不能为空")
    @Valid
    @ApiModelProperty(value = "联系人信息", required = true)
    private List<ContactInfoDTO> contactInfoList;

    /**
     * 身份类型：1-工商企业，2-政府/事业单位
     */
    @NotNull(message = "身份类型不能为空")
    @ApiModelProperty(value = "身份类型：1-工商企业，2-政府/事业单位", required = true, example = "1")
    private Integer identityType;

    /**
     * 统一社会信用代码
     */
    @NotBlank(message = "统一社会信用代码不能为空")
    @ApiModelProperty(value = "统一社会信用代码", required = true, example = "91440300XXXXXXXXXX")
    private String socialCreditCode;

    @NotNull(message = "是否需要审核")
    @ApiModelProperty(value = "是否需要审核", required = true)
    private Boolean needAudit;
} 