package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * C<PERSON> zeng<PERSON><PERSON>ui
 * Date 2025-06-23
 **/

@Data
public class FileAttachmentDTO {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("用户 no")
    private String userNo;

    @ApiModelProperty("文件保存路径")
    private String filePath;

    @ApiModelProperty("业务类型")
    private String bizType;

    @ApiModelProperty("业务 no")
    private String bizNo;

    private String tenantId;

    @ApiModelProperty("原始文件名称")
    private String originalFilename;

    @ApiModelProperty("文件访问连接")
    private String fileUrl;
}
