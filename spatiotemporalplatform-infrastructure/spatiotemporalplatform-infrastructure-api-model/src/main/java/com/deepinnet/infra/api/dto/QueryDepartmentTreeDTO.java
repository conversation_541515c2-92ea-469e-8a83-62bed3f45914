package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> wong
 * @create 2025/7/8 11:05
 * @Description
 */
@Data
public class QueryDepartmentTreeDTO {

    @ApiModelProperty("父id")
    private Long parentId;

    @ApiModelProperty("要查询的成员状态，all-全部，occupied-已占用，available-未占用")
    private String memberStatus;

    @ApiModelProperty("是否需要根据数据权限过滤")
    private Boolean requiresDataPermission = false;
}
