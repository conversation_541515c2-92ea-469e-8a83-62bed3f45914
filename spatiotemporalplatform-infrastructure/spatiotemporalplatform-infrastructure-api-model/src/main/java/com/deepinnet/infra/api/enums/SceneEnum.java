package com.deepinnet.infra.api.enums;

import lombok.*;

/**
 * <AUTHOR> wong
 * @create 2025/4/19 14:30
 * @Description
 */
@AllArgsConstructor
@Getter
public enum SceneEnum {

    MATCHING_PLATFORM("matching_platform", "撮合平台"),

    SUPPLIER_DASHBOARD("supplier_dashboard", "撮合平台服务商工作台"),

    CUSTOMER_DASHBOARD("customer_dashboard", "撮合平台客户工作台"),

    OPERATION_CENTER("operation_center", "撮合运营中心"),

    TRAFFIC_POLICE("traffic_police", "交警业务"),

    SPATIOTEMPORAL_OPENPROD("spatiotemporal_openprod", "开放平台"),

    OPEN_OPERATION_PLATFORM("open_operation_platform", "运营开放平台"),

    ;

    private final String code;

    private final String desc;
}
