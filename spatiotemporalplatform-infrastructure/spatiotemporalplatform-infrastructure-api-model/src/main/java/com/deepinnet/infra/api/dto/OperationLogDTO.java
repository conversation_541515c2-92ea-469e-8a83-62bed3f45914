package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 操作日志DTO
 * 
 * <AUTHOR> wong
 * @create 2025/7/31
 */
@Data
@ApiModel("操作日志")
public class OperationLogDTO {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("操作人用户编号")
    private String userNo;

    @ApiModelProperty("操作人姓名")
    private String userName;

    @ApiModelProperty("操作模块")
    private String operationModule;

    @ApiModelProperty("操作类型")
    private String operationType;

    @ApiModelProperty("操作详情")
    private String operationDetail;

    @ApiModelProperty("操作时间")
    private LocalDateTime operationTime;

    @ApiModelProperty("IP地址")
    private String ipAddress;

    @ApiModelProperty("用户代理")
    private String userAgent;

    @ApiModelProperty("请求URI")
    private String requestUri;

    @ApiModelProperty("请求方法")
    private String requestMethod;

    @ApiModelProperty("请求参数")
    private String requestParams;

    @ApiModelProperty("操作结果")
    private String operationResult;

    @ApiModelProperty("错误信息")
    private String errorMessage;

    @ApiModelProperty("执行耗时（毫秒）")
    private Long executionTime;
}
