package com.deepinnet.infra.api.dto;

import io.swagger.annotations.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 操作日志传输对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "操作日志")
public class OperationLogDTO {

    /**
     * 用户编号
     */
    @ApiModelProperty("用户编号")
    private String userNo;

    @ApiModelProperty("账号")
    private String account;

    /**
     * 操作用户名称
     */
    @ApiModelProperty("用户名称")
    private String userName;

    /**
     * 操作模块
     */
    @ApiModelProperty("操作模块")
    private String module;

    /**
     * 操作详情
     */
    @ApiModelProperty("操作详情")
    private String operationDetail;

    /**
     * 操作时间
     */
    @ApiModelProperty("操作时间")
    private LocalDateTime operationTime;

    /**
     * 请求IP地址
     */
    @ApiModelProperty("请求IP地址")
    private String ipAddress;

    /**
     * 用户代理信息
     */
    @ApiModelProperty("用户代理信息")
    private String userAgent;

    /**
     * 请求URI
     */
    @ApiModelProperty("请求URI")
    private String requestUri;

    /**
     * 请求方法
     */
    @ApiModelProperty("请求方法")
    private String requestMethod;

    /**
     * 租户ID
     */
    @ApiModelProperty("租户ID")
    private String tenantId;
}
