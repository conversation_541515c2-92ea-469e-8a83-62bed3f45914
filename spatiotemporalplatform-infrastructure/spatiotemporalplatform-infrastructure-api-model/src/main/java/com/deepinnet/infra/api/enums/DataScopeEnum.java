package com.deepinnet.infra.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据权限作用域枚举
 *
 * <AUTHOR> wong
 * @create 2024/12/18 15:45
 * @Description 数据权限作用域，用于控制用户可见的数据范围
 */
@Getter
@AllArgsConstructor
public enum DataScopeEnum {
    
    /**
     * 个人数据 - 只能看到自己相关的数据
     */
    PERSONAL(1, "个人数据"),
    
    /**
     * 组织数据（本级及下属） - 可以看到本级组织及下属组织的数据
     */
    DEPT_AND_CHILD(2, "组织数据（本级及下属）"),
    
    /**
     * 组织数据（本组织全局） - 可以看到当前组织的所有数据
     */
    DEPT_ALL(3, "组织数据（全局）"),
    
    /**
     * 全局数据 - 可以看到系统中所有数据
     */
    GLOBAL(4, "全局数据");
    
    /**
     * 权限级别（数值越大权限越高）
     */
    private final Integer level;
    
    /**
     * 权限名称
     */
    private final String name;
    
    /**
     * 根据级别获取数据权限作用域
     *
     * @param level 权限级别
     * @return 数据权限作用域
     */
    public static DataScopeEnum getByLevel(Integer level) {
        for (DataScopeEnum scope : values()) {
            if (scope.getLevel().equals(level)) {
                return scope;
            }
        }
        return PERSONAL; // 默认返回个人数据权限
    }
}