package com.deepinnet.infra.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 审核类型枚举
 *
 * <AUTHOR> wong
 * @create 2025/04/25
 */
@Getter
@AllArgsConstructor
public enum ApprovalTypeEnum {

    /**
     * 客户信息认证
     */
    CUSTOMER_INFO(1, "客户信息认证"),

    /**
     * 服务商信息认证
     */
    SUPPLIER_INFO(2, "服务商信息认证");

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举对象
     */
    public static ApprovalTypeEnum getByCode(Integer code) {
        for (ApprovalTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 