package com.deepinnet.infra.api.vo;

import io.swagger.annotations.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 审核记录VO
 *
 * <AUTHOR> wong
 * @create 2025/04/25
 */
@Data
@ApiModel(description = "审核记录")
public class ApprovalVO {

    /**
     * 申请ID
     */
    @ApiModelProperty(value = "申请ID", example = "202504250001")
    private String approvalId;

    /**
     * 审核类型：1-客户信息认证，2-服务商信息认证
     */
    @ApiModelProperty(value = "审核类型：1-客户信息认证，2-服务商信息认证", example = "1")
    private Integer approvalType;

    /**
     * 审核类型描述
     */
    @ApiModelProperty(value = "审核类型描述", example = "客户信息认证")
    private String approvalTypeDesc;

    /**
     * 单位/企业名称
     */
    @ApiModelProperty(value = "单位/企业名称", example = "深圳市龙岗区交警支队")
    private String organizationName;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码", example = "91440300XXXXXXXXXX")
    private String socialCreditCode;

    /**
     * 审核状态：0-待审核，1-审核通过，2-审核驳回
     */
    @ApiModelProperty(value = "审核状态：0-待审核，1-审核通过，2-审核驳回", example = "0")
    private Integer status;

    /**
     * 审核状态描述
     */
    @ApiModelProperty(value = "审核状态描述", example = "待审核")
    private String statusDesc;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间", example = "2025/04/25 10:00:00")
    private LocalDateTime applyTime;
} 