package com.deepinnet.infra.api.enums;

import lombok.*;

/**
 * <AUTHOR>
 * @version 2023-04-28
 */
@AllArgsConstructor
@Getter
public enum StoreBizTypeEnum {

    /**
     * OSS对象存储
     */
    OSS_STORE("oss_store", "对象存储"),

    /**
     * 本地存储
     */
    LOCAL_STORE("local_store", "本地存储"),

    ;

    /**
     * 存储类型
     */
    private String bizType;

    /**
     * 描述
     */
    private String description;

    /**
     * 根据bizType查询枚举
     * @param bizType 单据类型
     * @return 枚举
     */
    public static StoreBizTypeEnum getByBizType(String bizType) {
        if (bizType == null) {
            return null;
        }
        for (StoreBizTypeEnum an : values()) {
            if (an.getBizType().equals(bizType)){
                return an;
            }
        }
        return null;
    }
}
