package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 服务商信息查询DTO
 *
 * <AUTHOR> wong
 * @create 2025/04/21
 */
@Data
@ApiModel("服务商信息查询DTO")
public class SupplierInfoQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码", required = true, example = "1")
    private Integer pageNum = 1;

    /**
     * 每页数量
     */
    @ApiModelProperty(value = "每页数量", required = true, example = "10")
    private Integer pageSize = 10;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 联系人姓名
     */
    @ApiModelProperty(value = "联系人姓名")
    private String contactName;

    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话")
    private String contactPhone;
} 