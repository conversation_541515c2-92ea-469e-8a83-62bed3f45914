package com.deepinnet.infra.api.dto;

import com.deepinnet.infra.api.enums.MemberTypeEnums;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * C<PERSON> zeng<PERSON>ui
 * Date 2025-01-07
 * 数据权限树节点
 **/

@Data
public class DataScopeTreeNodeDTO {

    private Long departmentId;

    private String departmentName;
    /**
     * 在当前节点为负责人时可以查看部门所有成员，否则只有自己
     */
    private boolean canViewMembers;

    private List<DataScopeTreeNodeDTO> children;

    private List<DataScopeMemberUserDTO> memberUsers;

    public void viewRecursively() {
        this.canViewMembers = true;
        if (CollectionUtils.isNotEmpty(children)) children.forEach(DataScopeTreeNodeDTO::viewRecursively);
    }

    @JsonIgnore
    public boolean hasRelatedAccountResponsible() {
        return CollectionUtils.emptyIfNull(getMemberUsers()).stream()
                .anyMatch(m -> MemberTypeEnums.RESPONSIBLE_PERSON.equals(m.getType()) && m.getIsRelatedAccount());
    }
}
