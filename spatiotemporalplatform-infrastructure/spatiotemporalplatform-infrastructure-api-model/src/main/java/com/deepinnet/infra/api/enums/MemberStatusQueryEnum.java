package com.deepinnet.infra.api.enums;

import cn.hutool.core.util.EnumUtil;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

/**
 * 成员状态查询枚举
 *
 * <AUTHOR> wong
 * @create 2025/01/01 
 * @Description 用于查询部门树时筛选成员状态
 */
@Getter
@AllArgsConstructor
public enum MemberStatusQueryEnum {

    /**
     * 全部 - 查询所有成员
     */
    ALL("all", "全部"),

    /**
     * 已占用 - 只查询已绑定账号的成员
     */
    OCCUPIED("occupied", "已占用"),

    /**
     * 未占用 - 只查询未绑定账号的成员
     */
    AVAILABLE("available", "未占用"),
    ;
    
    private final String code;

    private final String desc;

    public static MemberStatusQueryEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return ALL; // 默认返回全部
        }
        return EnumUtil.getBy(MemberStatusQueryEnum.class, c -> StringUtils.equals(c.getCode(), code));
    }
} 