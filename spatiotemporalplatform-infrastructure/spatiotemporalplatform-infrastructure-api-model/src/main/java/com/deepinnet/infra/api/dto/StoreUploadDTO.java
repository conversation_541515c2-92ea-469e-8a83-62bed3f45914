package com.deepinnet.infra.api.dto;

import com.deepinnet.infra.api.enums.*;
import lombok.*;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2022/11/7 10:51
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StoreUploadDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 0-public ｜ 1-private
     *
     * @see StoreBucketTypeEnum
     */
    @NotNull
    @Min(0)
    @Max(1)
    private Integer bucketType;

    /**
     * 上传存储类型(oss_store, local_store)
     *
     * @see StoreBizTypeEnum
     */
    @NotNull
    private String bizType;

    /**
     * bucketName
     */
    private String bucketName;

    /**
     * key
     */
    private String key;

    /**
     * expireDate
     */
    private Date expireDate;

    /**
     * 输入流
     */
    private byte[] inputStream;

}
