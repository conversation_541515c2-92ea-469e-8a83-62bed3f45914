package com.deepinnet.infra.api.dto;

import io.swagger.annotations.*;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 审核记录DTO
 *
 * <AUTHOR> wong
 * @create 2025/04/25
 */
@Data
@ApiModel("审核记录DTO")
public class ApprovalDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 申请ID
     */
    @ApiModelProperty(value = "申请ID", example = "************")
    private String approvalId;

    /**
     * 审核类型：1-客户信息认证，2-服务商信息认证
     */
    @ApiModelProperty(value = "审核类型：1-客户信息认证，2-服务商信息认证", example = "1")
    private Integer approvalType;

    /**
     * 单位/企业名称
     */
    @ApiModelProperty(value = "单位/企业名称", example = "深圳市龙岗区交警支队")
    private String organizationName;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码", example = "91440300XXXXXXXXXX")
    private String socialCreditCode;

    /**
     * 审核状态：0-待审核，1-审核通过，2-审核驳回
     */
    @ApiModelProperty(value = "审核状态：0-待审核，1-审核通过，2-审核驳回", example = "0")
    private Integer status;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    private LocalDateTime applyTime;

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime approvalTime;

    /**
     * 审核人
     */
    @ApiModelProperty(value = "审核人")
    private String approver;

    /**
     * 审核意见
     */
    @ApiModelProperty(value = "审核意见")
    private String approvalComment;

    /**
     * 业务关联ID，客户ID或服务商ID
     */
    @ApiModelProperty(value = "业务关联ID，客户ID或服务商ID")
    private String bizId;
} 