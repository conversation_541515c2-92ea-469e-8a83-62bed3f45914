package com.deepinnet.infra.api.client;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.FileAttachmentDTO;
import com.deepinnet.infra.api.dto.FileAttachmentQueryDTO;
import com.deepinnet.infra.api.dto.FileUploadResultDTO;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2025/2/11 10:05
 * @Description
 */
@FeignClient(name = "fileAttachmentClient", url = "${stpf.service.url}")
public interface FileAttachmentClient {
    /**
     * 上传单个文件
     *
     * @param file    文件
     * @param bizType 业务类型
     * @param bizNo   业务编号
     * @return 上传结果
     */
    @PostMapping(value = "/file/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result<FileUploadResultDTO> uploadFile(
            @ApiParam(value = "文件", required = true) @RequestPart("file") MultipartFile file,
            @ApiParam(value = "业务类型") @RequestParam(value = "bizType", required = false) String bizType,
            @ApiParam(value = "业务编号") @RequestParam(value = "bizNo", required = false) String bizNo);

    /**
     * 批量上传文件
     *
     * @param files   文件列表
     * @param bizType 业务类型
     * @param bizNo   业务编号
     * @return 上传结果列表
     */
    @PostMapping(value = "/file/batchUpload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result<List<FileUploadResultDTO>> batchUploadFiles(
            @ApiParam(value = "文件列表", required = true) @RequestPart("files") MultipartFile[] files,
            @ApiParam(value = "业务类型") @RequestParam(value = "bizType", required = false) String bizType,
            @ApiParam(value = "业务编号") @RequestParam(value = "bizNo", required = false) String bizNo);

    /**
     * 根据文件路径获取文件资源
     *
     * @param filePath 文件路径
     * @return 文件资源响应实体
     */
    @GetMapping("/file/download")
    ResponseEntity<org.springframework.core.io.Resource> getFileResource(
            @ApiParam(value = "文件路径", required = true) @RequestParam("filePath") String filePath);

    /**
     * 获取文件对象
     *
     * @param id 文件id
     * @return 文件对象
     */
    @ApiOperation("获取文件对象")
    @GetMapping("/file/fileAttachment")
    Result<FileAttachmentDTO> getFileAttachment(@ApiParam(value = "文件id", required = true) @RequestParam("id") Long id);

    /**
     * 获取文件对象列表
     *
     * @param queryDTO 查询对象
     * @return 文件对象
     */
    @ApiOperation("获取文件对象列表")
    @PostMapping("/file/queryFileAttachmentList")
    Result<List<FileAttachmentDTO>> queryFileAttachmentList(@ApiParam(value = "查询对象", required = true)
                                                                   @RequestBody FileAttachmentQueryDTO queryDTO);
}
