package com.deepinnet.infra.api.dto;

import io.swagger.annotations.*;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 审核处理DTO
 *
 * <AUTHOR> wong
 * @create 2025/04/25
 */
@Data
@ApiModel("审核处理DTO")
public class ApprovalProcessDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请ID
     */
    @NotBlank(message = "申请ID不能为空")
    @ApiModelProperty(value = "申请ID", required = true, example = "************")
    private String approvalId;

    /**
     * 审核状态：1-审核通过，2-审核驳回
     */
    @NotNull(message = "审核状态不能为空")
    @ApiModelProperty(value = "审核状态：1-审核通过，2-审核驳回", required = true, example = "1")
    private Integer status;

    /**
     * 审核意见
     */
    @ApiModelProperty(value = "审核意见", example = "资料齐全，审核通过")
    private String approvalComment;
} 