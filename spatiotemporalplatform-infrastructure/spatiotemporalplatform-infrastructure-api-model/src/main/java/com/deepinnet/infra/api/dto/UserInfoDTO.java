package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.*;

/**
 * <AUTHOR> wong
 * @create 2024/8/8 16:51
 * @Description
 */
@Data
public class UserInfoDTO {

    @ApiModelProperty(value = "用户编号")
    private String userNo;

    @ApiModelProperty(value = "账户编号")
    private String accountNo;

    @ApiModelProperty(value = "账号")
    private String account;

    @ApiModelProperty(value = "账号名称")
    private String name;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建账户编号")
    private String creatorId;

    @ApiModelProperty(value = "创建人名称")
    private String creatorName;

    @ApiModelProperty(value = "角色列表")
    private List<RoleFlatDTO> roles;

    @ApiModelProperty("是否已经初始化，返回false跳转到完善信息和修改密码页面")
    private Boolean isInitialized;

    @ApiModelProperty(value = "用户类型：supplier-服务商，customer-客户，operation-运营，other-其他")
    private String userType;

    @ApiModelProperty(value = "是否是超管账号")
    private Boolean isSuperAdmin;

    @ApiModelProperty(value = "部门id完整路径")
    private List<String> departmentIdPath;

    @ApiModelProperty(value = "部门名称完整路径")
    private List<List<String>> departmentNamePath;

    @ApiModelProperty(value = "用户关联部门信息")
    private List<List<SimpleDepartmentDTO>> departments;

    @ApiModelProperty(value = "用户关联部门完整信息")
    private List<DepartmentFlatDTO> departmentFlatDTOs;

    @ApiModelProperty(value = "权限列表")
    private List<PermissionFlatDTO> permissions;

    @ApiModelProperty(value = "用户绑定的成员信息")
    private UserMemberBindDTO userMemberBindDTO;

    @ApiModelProperty(value = "当前用户所属组织")
    private UserDepartmentDTO userDepartment;

    @ApiModelProperty(value = "账号状态：0-正常，1-冻结")
    private Integer status;
}

