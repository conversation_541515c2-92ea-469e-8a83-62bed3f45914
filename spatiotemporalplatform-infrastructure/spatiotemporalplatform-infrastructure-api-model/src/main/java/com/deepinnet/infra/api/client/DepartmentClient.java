package com.deepinnet.infra.api.client;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2025/2/11 10:05
 * @Description
 */
@FeignClient(name = "departmentClient", url = "${stpf.service.url}")
public interface DepartmentClient {
    /**
     * 保存部门
     */
    @PostMapping("/domain/admin/department/save")
    Result<Long> saveDepartment(@RequestBody DepartmentSaveDTO saveDTO);

    /**
     * 添加部门成员
     */
    @PostMapping("/domain/admin/department/add/member")
    Result<Boolean> addMember(@RequestBody DepartmentAddMemberDTO addMemberDTO);

    /**
     * 更新部门信息
     */
    @PostMapping("/domain/admin/department/update")
    Result<Boolean> updateDepartment(@RequestBody DepartmentUpdateDTO updateDTO);

    /**
     * 更新用户成员信息
     *
     * @param updateDTO
     * @return
     */
    @PostMapping("/domain/admin/department/update/user/member")
    Result<Boolean> updateUserMemberInfo(@RequestBody UpdateUserMemberInfoDTO updateDTO);

    /**
     * 删除部门
     */
    @PostMapping("/domain/admin/department/delete")
    Result<Boolean> deleteDepartment(@RequestBody DeleteDepartmentDTO deleteDTO);

    /**
     * 删除部门成员
     */
    @PostMapping("/domain/admin/department/delete/member")
    Result<Boolean> deleteMember(@RequestBody RemoveMemberDTO removeMemberDTO);

    /**
     * 获取部门树
     *
     * @return
     */
    @PostMapping("/domain/admin/department/tree")
    Result<List<DepartmentDTO>> getDepartmentByTree(@RequestBody QueryDepartmentTreeDTO queryDTO);


    /**
     * 获取子部门
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/domain/admin/department/subTree")
    Result<List<DepartmentDTO>> getSubDepartments(@RequestBody QuerySubDepartmentDTO queryDTO);

    /**
     * 根据id获取部门列表
     *
     * @param ids
     * @return
     */
    @GetMapping("/domain/admin/department/tree/ids")
    Result<List<DepartmentTreeDTO>> listSimpleDepartmentsByIds(@RequestParam("ids") List<Long> ids);

    /**
     * 获取用户根节点的部门信息
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/domain/admin/department/root")
    Result<List<UserRootDepartmentDTO>> getUserRootDepartments(@RequestBody UserRootDepartmentQueryDTO queryDTO);

    /**
     * 根据部门ID查询成员列表
     *
     * @param queryDTO 查询条件
     * @return 部门成员列表
     */
    @PostMapping("/domain/admin/department/members")
    Result<List<DepartmentMemberDTO>> listDepartmentMembers(@RequestBody DepartmentMemberQueryDTO queryDTO);

    /**
     * 返回传入部门以及子部门所有绑定的用户
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/domain/admin/department/bind/users")
    Result<List<SimpleUserInfoDTO>> getDepartmentBindUsers(@RequestBody @Valid QueryDepartmentBindUserDTO queryDTO);
}
