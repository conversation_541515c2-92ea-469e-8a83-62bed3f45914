package com.deepinnet.infra.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 飞行服务类型枚举
 *
 * <AUTHOR> wong
 * @create 2025/04/21
 */
@Getter
@AllArgsConstructor
public enum FlightServiceTypeEnum {

    /**
     * 飞手服务
     */
    DRONE_PILOT("drone_pilot", "飞手服务"),


    /**
     * 其他服务
     */
    OTHER("other", "其他服务");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举对象
     */
    public static FlightServiceTypeEnum getByCode(String code) {
        for (FlightServiceTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 