package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2024/11/27 11:07
 * @Description
 */
@Data
public class UserAccountSaveDTO {
    @ApiModelProperty(value = "账户编号")
    private String accountNo;

    @ApiModelProperty(value = "用户绑定成员Id")
    @NotNull(message = "用户绑定成员Id不能为空")
    private Long bindMemberId;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "角色code列表")
    @NotEmpty(message = "角色列表不能为空")
    private List<String> roleCodeList;

    @ApiModelProperty(value = "用户类型：supplier-服务商，customer-客户，operation-运营，other-其他")
    private String userType;

    @ApiModelProperty(value = "部门列表")
    @NotEmpty(message = "部门列表不能为空")
    private List<Long> departmentIdList;
}
