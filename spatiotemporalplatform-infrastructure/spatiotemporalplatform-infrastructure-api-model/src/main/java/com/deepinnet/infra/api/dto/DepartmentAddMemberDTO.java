package com.deepinnet.infra.api.dto;

import com.deepinnet.infra.api.enums.MemberIdentityTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2024/12/2 10:33
 * @Description
 */
@Data
public class DepartmentAddMemberDTO {

    @ApiModelProperty(value = "成员id")
    private Long id;

    @ApiModelProperty(value = "部门id")
    @NotNull(message = "部门id不能为空")
    private Long departmentId;

    @ApiModelProperty(value = "成员名称")
    @NotBlank(message = "成员名称不能为空")
    private String name;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "电子邮箱")
    private String email;

    @ApiModelProperty(value = "职务")
    private String position;

    @ApiModelProperty(value = "类型：responsible_person-负责人，normal_person-普通成员")
    @NotBlank(message = "成员类型不能为空")
    private String type;

    /**
     * 身份类型列表
     *
     * @see MemberIdentityTypeEnum
     */
    @ApiModelProperty(value = "成员身份信息：pilot-飞手")
    private List<String> identityTypes;
}
