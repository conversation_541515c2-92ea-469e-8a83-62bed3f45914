package com.deepinnet.infra.api.client;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.api.vo.SupplierInfoVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR> wong
 * @create 2025/2/11 10:05
 * @Description
 */
@FeignClient(name = "productSupplierClient", url = "${stpf.service.url}")
public interface ProductSupplierClient {

    @PostMapping("/user/supplier/save")
    Result<String> saveSupplierInfo(@Valid @RequestBody SupplierInfoDTO supplierInfoDTO);

    @PostMapping("/user/supplier/detail")
    Result<SupplierDetailDTO> getSupplierDetail(@RequestBody SupplierQueryDetailDTO queryDetailDTO);

    /**
     * 分页查询服务商列表
     * 支持按照以下条件查询：
     * 1. 登录手机号码（phone）
     * 2. 企业名称（companyName）
     * 3. 联系人姓名（contactName）
     * 4. 联系人电话（contactPhone）
     *
     * @param queryDTO 查询条件
     * @return 服务商列表分页数据
     */
    @PostMapping("/user/supplier/page")
    Result<CommonPage<SupplierInfoVO>> pageQuerySuppliers(@Valid @RequestBody SupplierQueryDTO queryDTO);
}
