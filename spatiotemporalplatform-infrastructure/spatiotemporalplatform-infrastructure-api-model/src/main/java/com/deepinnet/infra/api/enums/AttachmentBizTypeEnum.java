package com.deepinnet.infra.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 附件业务类型枚举
 *
 * <AUTHOR> wong
 * @create 2025/04/22
 */
@Getter
@AllArgsConstructor
public enum AttachmentBizTypeEnum {

    /**
     * 证明材料
     */
    UNIT_IDENTITY("unit_identity", "证明材料"),

    /**
     * 营业执照
     */
    BUSINESS_LICENSE("business_license", "营业执照"),

    /**
     * 低空飞行资质
     */
    LOW_ALTITUDE_FLIGHT_LICENSE("low_altitude_flight_license", "低空飞行资质");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举对象
     */
    public static AttachmentBizTypeEnum getByCode(String code) {
        for (AttachmentBizTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
