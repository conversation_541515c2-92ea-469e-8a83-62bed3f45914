package com.deepinnet.infra.api.dto;

import cn.hutool.core.util.StrUtil;
import com.deepinnet.infra.api.enums.FlightServiceTypeEnum;
import io.swagger.annotations.*;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 服务商详情DTO
 *
 * <AUTHOR> wong
 * @create 2025/04/21
 */
@Data
@ApiModel("服务商详情DTO")
public class SupplierDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户编号
     */
    @ApiModelProperty(value = "用户编号")
    private String userNo;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 营业执照号
     */
    @ApiModelProperty(value = "营业执照号")
    private String businessLicenseNo;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String phone;

    /**
     * 公司地址
     */
    @ApiModelProperty(value = "公司地址")
    private String address;

    /**
     * 区域code
     */
    @ApiModelProperty(value = "区域code")
    private String regionCode;

    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称")
    private String regionName;

    /**
     * 营业执照照片路径
     */
    @ApiModelProperty(value = "营业执照照片路径")
    private List<String> businessLicensePicPath;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;

    /**
     * 低空飞行相关资质照片路径列表，多张照片路径用英文逗号分隔
     */
    @ApiModelProperty(value = "低空飞行相关资质照片路径列表")
    private List<String> qualificationPicPaths;

    /**
     * 联系人信息列表
     */
    @ApiModelProperty(value = "联系人信息列表")
    private List<SupplierContactInfoDTO> contactInfoList;

    /**
     * 服务类型列表
     */
    @ApiModelProperty(value = "服务类型列表")
    private List<SupplierServiceDTO> serviceList;

    /**
     * 服务类型列表
     */
    @ApiModelProperty(value = "是否同意合作协议")
    private Boolean cooperationAgreement;

    /**
     * 审核状态：-1-初始状态（未认证），0-审核中，1-已认证，2-不通过
     */
    @ApiModelProperty(value = "审核状态：-1-初始状态（未认证），0-审核中，1-已认证，2-不通过")
    private Integer approvalStatus;

    /**
     * 审核状态描述
     */
    @ApiModelProperty(value = "审核状态描述")
    private String approvalStatusDesc;

    /**
     * 判断是否存在某个服务，传入服务类型的枚举
     *
     * @param serviceType 服务类型
     * @return
     */
    public Boolean containsTargetService(FlightServiceTypeEnum serviceType) {
        return serviceList.stream()
                .anyMatch(service -> StrUtil.equals(serviceType.getCode(), service.getServiceCode()));
    }
} 