package com.deepinnet.infra.api.enums;


import lombok.Getter;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * C<PERSON> zengju<PERSON>ui
 * Date 2024-01-15
 **/

@Getter
public enum SmsSceneTemplateEnum {

    VERIFICATION_CODE("短信验证码", "908e94ccf08b4476ba6c876d13f084ad"),

    ;

    private final String scene;


    private final String templateId;

    private static final Map<String, SmsSceneTemplateEnum> TEMPLATE_UNIQ_MAP = Arrays.stream(SmsSceneTemplateEnum.values())
            .collect(Collectors.toMap(SmsSceneTemplateEnum::getTemplateId, Function.identity()));

    SmsSceneTemplateEnum(String scene, String templateId) {
        this.scene = scene;
        this.templateId = templateId;
    }

    public static SmsSceneTemplateEnum getByTemplateUniqNo(String uniqNo) {
        return TEMPLATE_UNIQ_MAP.get(uniqNo);
    }

}
