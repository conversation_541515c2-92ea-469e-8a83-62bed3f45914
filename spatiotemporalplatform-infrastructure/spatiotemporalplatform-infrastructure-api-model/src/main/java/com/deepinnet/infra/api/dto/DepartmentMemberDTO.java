package com.deepinnet.infra.api.dto;

import com.deepinnet.infra.api.enums.MemberTypeEnums;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> wong
 * @create 2024/11/25 14:32
 * @Description
 */
@Data
public class DepartmentMemberDTO {

    private Long id;

    /**
     * 人员姓名
     */
    private String name;

    /**
     * 类型
     */
    @ApiModelProperty(value = "成员类型：responsible_person-负责人，normal_person-普通成员")
    private String type;

    /**
     * 占用状态
     */
    @ApiModelProperty(value = "占用状态：available-未占用，occupied-已占用")
    private String occupyStatus;

    /**
     * 是否关联了账号
     */
    private Boolean isRelatedAccount;
    /**
     * 关联的账号
     */
    private String relatedUserNo;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 职务
     */
    private String position;


    /**
     * 有账号的普通成员
     * */
    @JsonIgnore
    public boolean isNormalAndRelatedAccount() {
        return MemberTypeEnums.NORMAL_PERSON.getCode().equals(type) && isRelatedAccount;
    }

    /**
     * 有账号的负责人
     * */
    @JsonIgnore
    public boolean isResponsibleAndRelatedAccount() {
        return MemberTypeEnums.RESPONSIBLE_PERSON.getCode().equals(type) && isRelatedAccount;
    }
}
