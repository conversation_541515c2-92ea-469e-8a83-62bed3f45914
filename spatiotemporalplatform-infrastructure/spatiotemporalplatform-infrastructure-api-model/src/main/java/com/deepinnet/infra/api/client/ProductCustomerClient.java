package com.deepinnet.infra.api.client;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.api.vo.CustomerInfoVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 客户管理接口
 *
 * <AUTHOR> wong
 * @create 2025/2/11 10:05
 * @Description
 */
@FeignClient(name = "productCustomerClient", url = "${stpf.service.url}")
public interface ProductCustomerClient {

    /**
     * 保存客户信息
     *
     * @param customerInfoDTO 客户信息
     * @return 处理结果
     */
    @PostMapping("/user/customer/save")
    Result<String> saveCustomerInfo(@Valid @RequestBody CustomerInfoDTO customerInfoDTO);

    /**
     * 获取客户详情
     *
     * @return 客户详情
     */
    @PostMapping("/user/customer/detail")
    Result<CustomerDetailDTO> getCustomerDetail(@RequestBody CustomerQueryDetailDTO queryDetailDTO);

    /**
     * 分页查询客户列表
     * 支持按照以下条件查询：
     * 1. 登录手机号码（phone）
     * 2. 单位名称（organizationName）
     * 3. 联系人姓名（contactName）
     * 4. 联系人电话（contactPhone）
     *
     * @param queryDTO 查询条件
     * @return 客户列表分页数据
     */
    @PostMapping("/user/customer/page")
    Result<CommonPage<CustomerInfoVO>> pageQueryCustomers(@Valid @RequestBody CustomerQueryDTO queryDTO);
}
