package com.deepinnet.infra.api.enums;

import cn.hutool.core.util.EnumUtil;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

/**
 * 成员占用状态枚举
 *
 * <AUTHOR> wong
 * @create 2025/01/01 
 * @Description 标识成员是否被账号占用
 */
@Getter
@AllArgsConstructor
public enum MemberOccupyStatusEnum {

    /**
     * 未占用 - 成员可以被绑定到账号
     */
    AVAILABLE("available", "未占用"),

    /**
     * 已占用 - 成员已经绑定到账号
     */
    OCCUPIED("occupied", "已占用"),
    ;
    
    private final String code;

    private final String desc;

    public static MemberOccupyStatusEnum getByCode(String code) {
        return EnumUtil.getBy(MemberOccupyStatusEnum.class, c -> StringUtils.equals(c.getCode(), code));
    }
} 