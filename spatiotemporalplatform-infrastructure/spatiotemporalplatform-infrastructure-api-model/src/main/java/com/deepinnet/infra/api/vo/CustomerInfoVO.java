package com.deepinnet.infra.api.vo;

import com.deepinnet.infra.api.dto.ContactInfoDTO;
import io.swagger.annotations.*;
import lombok.Data;

import java.util.List;

/**
 * 客户信息VO
 *
 * <AUTHOR> wong
 * @create 2024/08/09
 */
@Data
@ApiModel(description = "客户信息")
public class CustomerInfoVO {

    /**
     * 客户ID
     */
    @ApiModelProperty(value = "客户ID", example = "12331231231")
    private String customerId;

    /**
     * 登录手机号码
     */
    @ApiModelProperty(value = "登录手机号码", example = "18273888921")
    private String phone;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称", example = "深圳市龙岗区交警支队")
    private String customerName;

    @ApiModelProperty(value = "联系人信息", required = true, example = "张上虞")
    private List<ContactInfoDTO> contactInfoDTOs;

    /**
     * 注册时间
     */
    @ApiModelProperty(value = "注册时间", example = "2025/03/02 15:28")
    private Long registerTime;
    
    /**
     * 身份类型：1-工商企业，2-政府/事业单位
     */
    @ApiModelProperty(value = "身份类型：1-工商企业，2-政府/事业单位", example = "1")
    private Integer identityType;
    
    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码", example = "91110000100000XXXX")
    private String socialCreditCode;

    /**
     * 审核状态：null-未认证，0-审核中，1-已认证，2-不通过
     */
    @ApiModelProperty(value = "审核状态：null-未认证，0-审核中，1-已认证，2-不通过")
    private Integer approvalStatus;

    /**
     * 审核状态描述
     */
    @ApiModelProperty(value = "审核状态描述")
    private String approvalStatusDesc;
} 