package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> wong
 * @create 2024/11/25 09:41
 * @Description
 */
@Data
public class DepartmentUpdateDTO {

    @ApiModelProperty(value = "新的部门名称")
    private String name;

    @ApiModelProperty(value = "当前的部门id")
    private Long departmentId;

    @ApiModelProperty("是否需要根据数据权限校验")
    private Boolean requiresDataPermission = false;
}
