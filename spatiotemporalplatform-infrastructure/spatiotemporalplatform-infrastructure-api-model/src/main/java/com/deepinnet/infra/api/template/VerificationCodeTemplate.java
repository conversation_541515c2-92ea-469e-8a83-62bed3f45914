package com.deepinnet.infra.api.template;

import lombok.Data;

/**
 * 验证码短信模板
 *
 * <AUTHOR> wong
 * @create 2025/04/18
 */
@Data
public class VerificationCodeTemplate extends SmsBaseTemplate {

    /**
     * 构造函数，设置短信模板ID
     */
    public VerificationCodeTemplate() {
        // 需要替换为实际的短信模板ID
        super.templateId = "908e94ccf08b4476ba6c876d13f084ad";
    }

    /**
     * 验证码
     */
    private String code;

    /**
     * 有效期（分钟）
     */
    private String minute;
} 