package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR> wong
 * @create 2024/8/8 16:05
 * @Description
 */
@Data
public class UserLoginDTO implements Serializable {

    @NotBlank(message = "登录账户不能为空")
    private String account;

    private String password;

    /**
     * 验证码
     */
    @ApiModelProperty(value = "验证码")
    private String verifyCode;

    /**
     * 登录场景
     */
    @ApiModelProperty(value = "matching_platform-撮合平台，supplier_matching_platform-撮合服务商工作台，customer_matching_platform-撮合客户工作台，operation_center-撮合运营中心,traffic_police-交警")
    private String scene;
}
