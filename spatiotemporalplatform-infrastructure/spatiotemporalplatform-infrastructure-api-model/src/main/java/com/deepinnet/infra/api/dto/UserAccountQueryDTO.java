package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2024/11/27 09:57
 * @Description
 */
@Data
public class UserAccountQueryDTO {

    @NotNull(message = "页码不能为空")
    private Integer pageNum;

    @NotNull(message = "页大小不能为空")
    private Integer pageSize;

    private String username;

    @ApiModelProperty(value = "用户类型：supplier-服务商，customer-客户，operation-运营，other-其他")
    private String userType;

    private String phone;

    private List<Long> departmentIds;

    @ApiModelProperty("是否需要根据数据权限校验")
    private Boolean requiresDataPermission = false;
}
