package com.deepinnet.infra.api.dto;

import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2022/12/12
 */

@Data
@Builder
public class OssObject implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * key
     */
    private String key;

    /**
     * bucketName
     */
    private String bucketName;

    /**
     * content-type
     */
    private String contentType;

    /**
     * content-length
     */
    private Long contentLength;

    /**
     * content-MD5
     */
    private String contentMd5;

    /**
     * etag
     */
    private String eTag;

}
