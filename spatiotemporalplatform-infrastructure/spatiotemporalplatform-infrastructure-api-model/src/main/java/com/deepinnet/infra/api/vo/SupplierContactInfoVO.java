package com.deepinnet.infra.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 服务商联系人VO
 *
 * <AUTHOR> wong
 * @create 2025/04/21
 */
@Data
@ApiModel("服务商联系人VO")
public class SupplierContactInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String phone;

    /**
     * 职务
     */
    @ApiModelProperty(value = "职务")
    private String position;
} 