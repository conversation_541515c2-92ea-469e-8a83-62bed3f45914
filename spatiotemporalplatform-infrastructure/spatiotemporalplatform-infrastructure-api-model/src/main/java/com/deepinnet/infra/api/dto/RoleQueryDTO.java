package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> wong
 * @create 2024/11/26 10:59
 * @Description
 */
@Data
public class RoleQueryDTO {
    @NotNull(message = "页码不为空")
    private Integer pageNum;

    @NotNull(message = "页大小不能为空")
    private Integer pageSize;

    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "角色描述")
    private String description;
}
