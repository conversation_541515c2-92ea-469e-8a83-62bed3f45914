package com.deepinnet.infra.api.dto;

import io.swagger.annotations.*;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 账号冻结/解冻DTO
 */
@Data
@ApiModel(description = "账号冻结/解冻DTO")
public class AccountFreezeDTO {

    /**
     * 账号编号列表
     */
    @ApiModelProperty(value = "账号编号列表")
    @NotBlank(message = "要冻结的账号不能为空")
    private String accountNo;

    /**
     * 操作原因
     */
    @ApiModelProperty(value = "操作原因")
    private String reason;
} 