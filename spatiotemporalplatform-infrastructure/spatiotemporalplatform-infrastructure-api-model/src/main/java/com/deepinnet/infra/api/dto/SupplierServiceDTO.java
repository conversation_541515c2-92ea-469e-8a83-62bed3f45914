package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 服务商服务DTO
 *
 * <AUTHOR> wong
 * @create 2025/04/21
 */
@Data
@ApiModel("服务商服务DTO")
public class SupplierServiceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 服务名称
     */
    @ApiModelProperty(value = "服务名称")
    private String serviceName;

    /**
     * 服务编码
     */
    @ApiModelProperty(value = "服务编码")
    private String serviceCode;
} 