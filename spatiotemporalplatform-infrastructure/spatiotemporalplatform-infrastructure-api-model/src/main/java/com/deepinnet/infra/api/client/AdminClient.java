package com.deepinnet.infra.api.client;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2025/2/11 10:04
 * @Description
 */
@FeignClient(name = "adminClient", url = "${stpf.service.url}")
public interface AdminClient {

    /**
     * 保存账号
     *
     * @param saveDTO 账号保存 DTO
     */
    @PostMapping("/domain/admin/account/save")
    Result<Boolean> saveOrUpdateAccount(@RequestBody UserAccountSaveDTO saveDTO);

    /**
     * 删除账号
     *
     * @param accountNo 账号编号
     */
    @GetMapping("/domain/admin/account/delete")
    Result<Boolean> delete(@RequestParam("accountNo") String accountNo);

    /**
     * 分页查询账号
     *
     * @param queryDTO 账号查询 DTO
     * @return 账号分页数据
     */
    @PostMapping("/domain/admin/account/page")
    Result<CommonPage<UserInfoDTO>> pageQueryUserAccount(@RequestBody UserAccountQueryDTO queryDTO);

    /**
     * 查询账号详情
     *
     * @param accountNo 账号编号
     * @return 账号详情 DTO
     */
    @GetMapping("/domain/admin/account/detail")
    Result<AccountDTO> getAccountDetail(@RequestParam("accountNo") String accountNo);

    /**
     * 分页查询角色
     *
     * @param roleQueryDTO 角色查询 DTO
     * @return 角色分页数据
     */
    @PostMapping("/domain/admin/role/page")
    Result<CommonPage<RoleDTO>> pageQueryRoleDTO(@RequestBody RoleQueryDTO roleQueryDTO);

    /**
     * 保存或更新角色
     *
     * @param saveDTO 角色保存 DTO
     */
    @PostMapping("/domain/admin/role/save")
    Result<Boolean> saveOrUpdateRole(@RequestBody RoleSaveDTO saveDTO);

    /**
     * 获取角色详情
     *
     * @param queryDTO 角色详情查询 DTO
     * @return 角色详情 DTO
     */
    @PostMapping("/domain/admin/role/detail")
    Result<RoleDTO> getRoleDetail(@RequestBody RoleDetailQueryDTO queryDTO);

    /**
     * 删除角色
     *
     * @param id 角色 ID
     */
    @GetMapping("/domain/admin/role/delete")
    Result<Boolean> deleteRole(@RequestParam("id") Long id);

    /**
     * 获取权限树
     *
     * @return 权限树列表
     */
    @PostMapping("/domain/admin/permission/tree")
    Result<List<PermissionDTO>> getPermissionTree();

    @PostMapping("/domain/admin/account/freeze")
    Result<Boolean> freezeAccounts(@RequestBody AccountFreezeDTO accountFreezeDTO);

    @PostMapping("/domain/admin/account/unfreeze")
    Result<Boolean> unfreezeAccounts(@RequestBody AccountFreezeDTO accountFreezeDTO);
}
