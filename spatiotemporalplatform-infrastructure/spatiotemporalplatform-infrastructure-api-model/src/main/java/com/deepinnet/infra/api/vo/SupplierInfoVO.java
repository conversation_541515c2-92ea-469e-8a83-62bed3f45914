package com.deepinnet.infra.api.vo;

import com.deepinnet.infra.api.dto.ContactInfoDTO;
import io.swagger.annotations.*;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 服务商信息VO
 *
 * <AUTHOR> wong
 * @create 2024/08/10
 */
@Data
@ApiModel(description = "服务商信息")
public class SupplierInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 服务商ID
     */
    @ApiModelProperty(value = "服务商ID", example = "12331231231")
    private String supplierId;

    /**
     * 登录手机号码
     */
    @ApiModelProperty(value = "登录手机号码", example = "18273888921")
    private String phone;

    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称", example = "深圳市高度创新科技有限公司")
    private String companyName;


    @ApiModelProperty(value = "联系人信息", example = "深圳市高度创新科技有限公司")
    private List<ContactInfoDTO> contactInfoDTOList;

    /**
     * 注册时间
     */
    @ApiModelProperty(value = "注册时间", example = "2025/03/02 15:28")
    private Long registerTime;

    /**
     * 营业执照号
     */
    @ApiModelProperty(value = "营业执照号")
    private String businessLicenseNo;

    /**
     * 公司地址
     */
    @ApiModelProperty(value = "公司地址")
    private String companyAddress;

    /**
     * 营业执照照片路径
     */
    @ApiModelProperty(value = "营业执照照片路径")
    private String businessLicensePicPath;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    private String socialCreditCode;

    /**
     * 低空飞行相关资质照片路径列表，多张照片路径用英文逗号分隔
     */
    @ApiModelProperty(value = "低空飞行相关资质照片路径列表，多张照片路径用英文逗号分隔")
    private String qualificationPicPaths;

    /**
     * 联系人信息列表
     */
    @ApiModelProperty(value = "联系人信息列表")
    private List<SupplierContactInfoVO> contactInfoList;

    /**
     * 服务类型列表
     */
    @ApiModelProperty(value = "服务类型列表")
    private List<SupplierServiceVO> serviceList;

    /**
     * 审核状态：null-未认证，0-审核中，1-已认证，2-不通过
     */
    @ApiModelProperty(value = "审核状态：null-未认证，0-审核中，1-已认证，2-不通过")
    private Integer approvalStatus;

    /**
     * 审核状态描述
     */
    @ApiModelProperty(value = "审核状态描述")
    private String approvalStatusDesc;
} 