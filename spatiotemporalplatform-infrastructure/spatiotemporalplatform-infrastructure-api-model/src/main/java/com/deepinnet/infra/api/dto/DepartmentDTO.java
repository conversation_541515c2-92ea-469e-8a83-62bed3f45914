package com.deepinnet.infra.api.dto;

import com.deepinnet.infra.api.enums.MemberTypeEnums;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 组织表
 *
 * @TableName department
 */
@Data
@NoArgsConstructor
public class DepartmentDTO implements Serializable {

    @ApiModelProperty(value = "部门id")
    private Long id;

    @ApiModelProperty(value = "部门名称")
    private String name;

    @ApiModelProperty(value = "部门层级")
    private Integer level;

    @ApiModelProperty(value = "父级的id")
    private Long parentId;

    @ApiModelProperty(value = "部门成员")
    private List<DepartmentMemberDTO> members;

    @ApiModelProperty(value = "子节点")
    private List<DepartmentDTO> children;

    @ApiModelProperty(value = "当前部门节点的路径")
    private List<DepartmentFlatDTO> path;

    private transient boolean canViewMembers;

    public DepartmentDTO(Long id, String name, Integer level, Long parentId, List<DepartmentMemberDTO> members, List<DepartmentDTO> children) {
        this.id = id;
        this.name = name;
        this.level = level;
        this.parentId = parentId;
        this.members = members;
        this.children = children;
    }

    /**
     * 有关联了账号的负责人
     * */
    @JsonIgnore
    public boolean hasRelatedAccountResponsible() {
        return CollectionUtils.emptyIfNull(getMembers()).stream()
                .anyMatch(m -> MemberTypeEnums.RESPONSIBLE_PERSON.getCode().equals(m.getType()) && m.getIsRelatedAccount());
    }
}