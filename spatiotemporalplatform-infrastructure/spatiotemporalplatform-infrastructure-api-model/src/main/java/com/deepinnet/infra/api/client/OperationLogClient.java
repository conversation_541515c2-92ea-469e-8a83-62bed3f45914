package com.deepinnet.infra.api.client;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR> wong
 * @create 2025/2/11 10:05
 * @Description
 */
@FeignClient(name = "operationLogClient", url = "${stpf.service.url}")
public interface OperationLogClient {

    @PostMapping("/domain/operation/log/page")
    Result<CommonPage<OperationLogDTO>> pageQueryOperationLog(@Valid @RequestBody OperationLogQueryDTO queryDTO);
}
