package com.deepinnet.infra.api.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2025/2/14 10:49
 * @Description
 */
@Data
public class UserLoginSuccessDTO {

    private String token;

    private String name;

    private String accountNo;

    private String account;

    private String userNo;

    private String userType;

    private List<Long> departmentIds;

    private Boolean isSuperAdmin;
    
    /**
     * 账号状态：0-正常，1-冻结
     * @see com.deepinnet.infra.api.enums.AccountStatusEnum
     */
    private Integer status;
}
