package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2024/11/27 11:26
 * @Description
 */
@Data
public class AccountDTO {

    private Long id;

    @ApiModelProperty(value = "账号编号")
    private String accountNo;

    @ApiModelProperty(value = "账户")
    private String account;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "部门id")
    private List<Long> departmentIds;

    @ApiModelProperty(value = "账号角色列表")
    private List<RoleDTO> roleList;

    @ApiModelProperty(value = "账号绑定的部门信息")
    private List<DepartmentFlatDTO> departmentFlatDTOs;

    @ApiModelProperty(value = "绑定的成员信息")
    private UserMemberBindDTO userMemberBindDTO;
    
    @ApiModelProperty(value = "是否已完成初始化（修改密码或完善信息）")
    private Boolean isInitialized;
}
