package com.deepinnet.infra.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 账号状态枚举
 */
@Getter
@AllArgsConstructor
public enum AccountStatusEnum {
    /**
     * 正常
     */
    NORMAL(0, "正常"),
    
    /**
     * 冻结
     */
    FROZEN(1, "冻结");
    
    private final Integer code;
    private final String desc;
    
    /**
     * 根据状态码获取枚举
     */
    public static AccountStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (AccountStatusEnum status : AccountStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 