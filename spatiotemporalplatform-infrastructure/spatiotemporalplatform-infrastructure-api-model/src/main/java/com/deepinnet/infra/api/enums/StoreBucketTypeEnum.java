package com.deepinnet.infra.api.enums;

/**
 * bucket类型枚举
 *
 * <AUTHOR>
 * @version 2022/11/10 10:38
 */
public enum StoreBucketTypeEnum {

    PUBLIC_BUCKET(0, "公共存储"),

    PRIVATE_BUCKET(1, "私有存储"),

    ;

    /**
     * bucket 类型
     */
    private Integer bucketType;

    /**
     * bucket 说明
     */
    private String bucketDesc;

    /**
     * 构造方法
     * @param bucketType bucket类型
     * @param bucketDesc 类型说明
     */
    StoreBucketTypeEnum(Integer bucketType, String bucketDesc) {
        this.bucketType = bucketType;
        this.bucketDesc = bucketDesc;
    }

    /**
     * 根据bucketType查询枚举
     * @param bucketType
     * @return 枚举
     */
    public static StoreBucketTypeEnum getTpStoreBucketTypeEnumByBucketType(Integer bucketType) {
        if (bucketType == null) {
            return null;
        }
        for (StoreBucketTypeEnum typeEnum : StoreBucketTypeEnum.values()) {
            if (typeEnum.getBucketType().equals(bucketType)){
                return typeEnum;
            }
        }
        return null;
    }

    public Integer getBucketType() {
        return bucketType;
    }

    public String getBucketDesc() {
        return bucketDesc;
    }

}
