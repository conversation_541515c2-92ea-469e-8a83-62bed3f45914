package com.deepinnet.infra.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description: 组织审批人层级关系
 * Date: 2025/8/8
 * Author: lijunheng
 */
@Data
public class AuthorizedUserDepartmentTree implements Serializable {

    /**
     * 组织编码
     */
    private Long orgCode;

    /**
     * 组织名称
     */
    private String orgName;

    /**
     * 组织审批人列表
     */
    private List<AuthorizedUserDTO> approverList;

    /**
     * 子组织列表
     */
    private List<AuthorizedUserDepartmentTree> children;
}
