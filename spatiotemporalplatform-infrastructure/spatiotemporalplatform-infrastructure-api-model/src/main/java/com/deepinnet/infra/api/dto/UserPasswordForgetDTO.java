package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> wong
 * @create 2024/11/26 16:37
 * @Description
 */
@Data
public class UserPasswordForgetDTO {

    @ApiModelProperty(value = "新密码")
    @NotBlank(message = "新密码不能为空")
    private String newPassword;

    @ApiModelProperty(value = "重置token")
    @NotBlank(message = "重置token不能为空")
    private String resetToken;
}
