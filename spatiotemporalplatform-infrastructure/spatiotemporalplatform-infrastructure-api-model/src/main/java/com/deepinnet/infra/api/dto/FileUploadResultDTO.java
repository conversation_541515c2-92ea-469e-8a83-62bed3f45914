package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件上传结果DTO
 *
 * <AUTHOR> wong
 * @create 2025/04/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("文件上传结果DTO")
public class FileUploadResultDTO {

    @ApiModelProperty("文件id [bizNo 未传时为空]")
    private Long id;
    /**
     * 文件原始名称
     */
    @ApiModelProperty("文件原始名称")
    private String originalFileName;

    /**
     * 文件保存路径
     */
    @ApiModelProperty("文件保存路径")
    private String filePath;

    /**
     * 文件大小(字节)
     */
    @ApiModelProperty("文件大小(字节)")
    private Long fileSize;

    /**
     * 文件类型
     */
    @ApiModelProperty("文件类型")
    private String fileType;
} 