package com.deepinnet.infra.api.dto;

import lombok.Data;

import javax.validation.constraints.*;
import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2025/8/13 16:10
 * @Description
 *
 */
@Data
public class AuthorizedUserQueryDTO {

    @NotNull(message = "根组织id不能为空")
    private Long rootId;

    /**
     * 权限code
     *
     * @see com.deepinnet.infra.api.constants.PermissionCodeConstants
     */
    @NotBlank(message = "权限code不能为空")
    private String permissionCode;

    /**
     * 权限作用域
     *
     * @see com.deepinnet.infra.api.enums.DataScopeEnum
     */
    @NotEmpty(message = "权限作用域不能为空")
    private List<Integer> dataScopeList;
}
