package com.deepinnet.infra.api.dto;

import io.swagger.annotations.*;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 验证码请求参数
 *
 * <AUTHOR> wong
 * @create 2025/04/18
 */
@Data
@ApiModel("验证码请求参数")
public class VerifyCodeRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号码不能为空")
    @ApiModelProperty(value = "手机号", required = true, example = "13800138000")
    private String phone;

    @NotBlank(message = "业务类型不能为空")
    @ApiModelProperty(value = "业务类型", required = true, example = "register-注册，login-登录，update_password-修改密码，forget_password-忘记密码")
    private String bizType;

    @NotBlank(message = "业务场景不能为空")
    @ApiModelProperty(value = "matching_platform-撮合平台，operation_center-撮合运营中心,traffic_police-交警,spatiotemporal_openprod-开放平台")
    private String scene;

    /**
     * 验证码
     */
    @ApiModelProperty(value = "验证码", example = "123456")
    private String code;
} 