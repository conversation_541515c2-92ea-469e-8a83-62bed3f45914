package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;
import java.io.Serializable;

/**
 * 服务商联系人DTO
 *
 * <AUTHOR> wong
 * @create 2025/04/21
 */
@Data
@ApiModel("服务商联系人DTO")
public class SupplierContactInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 姓名
     */
    @NotBlank(message = "联系人姓名不能为空")
    @ApiModelProperty(value = "姓名", required = true, example = "张三")
    @Length(max = 20, message = "联系人姓名不能超过20个字")
    private String name;

    /**
     * 电话
     */
    @NotBlank(message = "联系人电话不能为空")
    @ApiModelProperty(value = "电话", required = true, example = "13800138000")
    private String phone;

    /**
     * 职务
     */
    @NotBlank(message = "联系人职务不能为空")
    @ApiModelProperty(value = "职务", required = true, example = "业务部经理")
    @Length(max = 20, message = "联系人职务不能超过20个字")
    private String position;
} 