package com.deepinnet.infra.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 身份类型枚举
 *
 * <AUTHOR> wong
 * @create 2025/06/09
 */
@Getter
@AllArgsConstructor
public enum IdentityTypeEnum {

    /**
     * 工商企业
     */
    BUSINESS_ENTERPRISE(1, "工商企业"),

    /**
     * 政府/事业单位
     */
    GOVERNMENT_INSTITUTION(2, "政府/事业单位");

    /**
     * 代码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static IdentityTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (IdentityTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 描述
     */
    public static String getDescByCode(Integer code) {
        IdentityTypeEnum typeEnum = getByCode(code);
        return typeEnum != null ? typeEnum.getDesc() : "";
    }
} 