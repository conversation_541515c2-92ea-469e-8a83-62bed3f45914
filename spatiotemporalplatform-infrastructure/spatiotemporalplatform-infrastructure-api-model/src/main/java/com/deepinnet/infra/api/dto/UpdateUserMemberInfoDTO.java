package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * <AUTHOR> wong
 * @create 2024/12/2 10:33
 * @Description
 */
@Data
public class UpdateUserMemberInfoDTO {

    @ApiModelProperty(value = "成员id")
    @NotNull(message = "成员id不能为空")
    private Long id;

    @ApiModelProperty(value = "部门id")
    @NotNull(message = "部门id不能为空")
    private Long departmentId;

    @ApiModelProperty(value = "成员名称")
    @NotBlank(message = "成员名称不能为空")
    private String name;

    @ApiModelProperty(value = "手机号")
    @NotBlank(message = "手机号不能为空")
    private String phone;

    @ApiModelProperty(value = "电子邮箱")
    @NotNull(message = "电子邮箱不能为空")
    private String email;

    @ApiModelProperty(value = "职务")
    @NotNull(message = "职务不能为空")
    private String position;
}
