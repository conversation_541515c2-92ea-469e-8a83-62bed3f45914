package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;

/**
 * 联系人信息DTO
 *
 * <AUTHOR> wong
 * @create 2025/04/22
 */
@Data
@ApiModel("联系人信息DTO")
public class ContactInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 联系人姓名
     */
    @NotBlank(message = "联系人姓名不能为空")
    @ApiModelProperty(value = "联系人姓名", required = true, example = "张上虞")
    @Size(max = 20, message = "联系人姓名不能超过20个字")
    private String name;

    /**
     * 联系人电话
     */
    @NotBlank(message = "联系人电话不能为空")
    @ApiModelProperty(value = "联系人电话", required = true, example = "15292837283")
    private String phone;

    /**
     * 联系人职务
     */
    @NotBlank(message = "联系人职务不能为空")
    @ApiModelProperty(value = "联系人职务", required = true, example = "交警支队队长")
    @Size(max = 20, message = "联系人职务不能超过20个字")
    private String position;

    @ApiModelProperty(value = "用户编号")
    private String userNo;
} 