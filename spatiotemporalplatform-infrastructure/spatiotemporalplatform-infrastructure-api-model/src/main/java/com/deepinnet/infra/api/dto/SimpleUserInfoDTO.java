package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> wong
 * @create 2025/7/21 14:35
 * @Description
 */
@Data
public class SimpleUserInfoDTO {
    @ApiModelProperty(value = "用户编号")
    private String userNo;

    @ApiModelProperty(value = "账号名称")
    private String name;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "租户id")
    private String tenantId;
}
