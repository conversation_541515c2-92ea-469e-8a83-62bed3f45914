package com.deepinnet.infra.api.dto;

import io.swagger.annotations.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 审核记录详情DTO
 *
 * <AUTHOR> wong
 * @create 2025/04/25
 */
@Data
@ApiModel("审核记录详情DTO")
public class ApprovalDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 申请ID
     */
    @ApiModelProperty(value = "申请ID", example = "************")
    private String approvalId;

    /**
     * 审核类型：1-客户信息认证，2-服务商信息认证
     */
    @ApiModelProperty(value = "审核类型：1-客户信息认证，2-服务商信息认证", example = "1")
    private Integer approvalType;

    /**
     * 单位/企业名称
     */
    @ApiModelProperty(value = "单位/企业名称", example = "深圳市龙岗区交警支队")
    private String organizationName;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码", example = "91440300XXXXXXXXXX")
    private String socialCreditCode;

    /**
     * 审核状态：0-待审核，1-审核通过，2-审核驳回
     */
    @ApiModelProperty(value = "审核状态：0-待审核，1-审核通过，2-审核驳回", example = "0")
    private Integer status;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    private LocalDateTime applyTime;

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime approvalTime;

    /**
     * 审核人
     */
    @ApiModelProperty(value = "审核人")
    private String approver;

    /**
     * 审核意见
     */
    @ApiModelProperty(value = "审核意见")
    private String approvalComment;

    /**
     * 业务关联ID，客户ID或服务商ID
     */
    @ApiModelProperty(value = "业务关联ID，客户ID或服务商ID")
    private String bizId;

    /**
     * 客户信息详情(当审核类型为客户信息认证时有值)
     */
    @ApiModelProperty(value = "客户信息详情(当审核类型为客户信息认证时有值)")
    private CustomerDetailDTO customerDetail;

    /**
     * 服务商信息详情(当审核类型为服务商信息认证时有值)
     */
    @ApiModelProperty(value = "服务商信息详情(当审核类型为服务商信息认证时有值)")
    private SupplierDetailDTO supplierDetail;

    /**
     * 历史审核记录列表(当前用户的所有审核记录，按时间倒序)
     */
    @ApiModelProperty(value = "历史审核记录列表(当前用户的所有审核记录，按时间倒序)")
    private List<ApprovalHistoryDTO> approvalHistoryList;
} 