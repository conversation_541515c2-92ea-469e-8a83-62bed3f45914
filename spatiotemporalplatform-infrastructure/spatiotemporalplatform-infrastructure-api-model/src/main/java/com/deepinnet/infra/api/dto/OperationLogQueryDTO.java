package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 操作日志查询DTO
 * 
 * <AUTHOR> wong
 * @create 2025/7/31
 */
@Data
@ApiModel("操作日志查询")
public class OperationLogQueryDTO {

    @NotNull(message = "页码不能为空")
    @ApiModelProperty("页码")
    private Integer pageNum;

    @NotNull(message = "页大小不能为空")
    @ApiModelProperty("页大小")
    private Integer pageSize;

    @ApiModelProperty("操作人姓名")
    private String userName;

    @ApiModelProperty("操作模块")
    private String operationModule;

    @ApiModelProperty("操作类型")
    private String operationType;

    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("操作结果：SUCCESS-成功，FAILURE-失败")
    private String operationResult;

    @ApiModelProperty("IP地址")
    private String ipAddress;
}
