package com.deepinnet.infra.api.dto;

import io.swagger.annotations.*;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 操作日志查询传输对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "操作日志查询条件")
public class OperationLogQueryDTO {

    /**
     * 操作用户编号
     */
    @ApiModelProperty("操作用户编号")
    private String userNo;

    /**
     * 操作用户名称
     */
    @ApiModelProperty("操作用户名称")
    private String userName;

    /**
     * 操作模块
     */
    @ApiModelProperty("操作模块")
    private String module;

    /**
     * 页码
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码必须大于等于1")
    @ApiModelProperty(value = "页码", required = true, example = "1")
    private Integer pageNum = 1;

    /**
     * 每页数量
     */
    @NotNull(message = "每页数量不能为空")
    @Min(value = 1, message = "每页数量必须大于等于1")
    @ApiModelProperty(value = "每页数量", required = true, example = "10")
    private Integer pageSize = 10;
}
