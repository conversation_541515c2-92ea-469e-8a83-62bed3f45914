package com.deepinnet.infra.api.dto;

import io.swagger.annotations.*;
import lombok.Data;

import java.io.Serializable;

/**
 * 审核记录查询DTO
 *
 * <AUTHOR> wong
 * @create 2025/04/25
 */
@Data
@ApiModel("审核记录查询DTO")
public class ApprovalQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码", required = true, example = "1")
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @ApiModelProperty(value = "每页大小", required = true, example = "10")
    private Integer pageSize = 10;

    /**
     * 审核类型：1-客户信息认证，2-服务商信息认证
     */
    @ApiModelProperty(value = "审核类型：1-客户信息认证，2-服务商信息认证", example = "1")
    private Integer approvalType;

    /**
     * 单位/企业名称
     */
    @ApiModelProperty(value = "单位/企业名称", example = "深圳市龙岗区交警支队")
    private String organizationName;

    /**
     * 审核状态：0-待审核，1-审核通过，2-审核驳回
     */
    @ApiModelProperty(value = "审核状态：0-待审核，1-审核通过，2-审核驳回", example = "0")
    private Integer status;
} 