package com.deepinnet.infra.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户注册请求DTO
 *
 * <AUTHOR> wong
 * @create 2025/04/18
 */
@Data
@ApiModel("用户注册请求DTO")
public class UserRegisterDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @ApiModelProperty(value = "手机号", required = true, example = "13800138000")
    private String phone;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    @ApiModelProperty(value = "密码", required = true, example = "Password123!")
    private String password;

    /**
     * 验证码
     */
    @NotBlank(message = "验证码不能为空")
    @ApiModelProperty(value = "验证码", required = true, example = "123456")
    private String code;

    /**
     * 用户类型
     */
    @NotNull(message = "用户类型不能为空")
    @ApiModelProperty(value = "用户类型：supplier-服务商，customer-客户，operation-运营，traffic_police-交警")
    private String userType;
    
    /**
     * 用户名称
     */
    @NotBlank(message = "用户名称不能为空")
    @ApiModelProperty(value = "用户名称", required = true, example = "张三")
    private String userName;
} 