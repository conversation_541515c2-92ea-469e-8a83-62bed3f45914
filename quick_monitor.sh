#!/bin/bash

# 快速监控脚本 - 简化版
# 每10秒调用一次登录接口，记录异常

URL="http://192.168.3.200:8056/api/user/login"
INTERVAL=10
LOG_FILE="api_monitor_$(date +%Y%m%d_%H%M%S).log"

# 计数器
SUCCESS=0
ERROR=0

echo "开始监控登录接口..."
echo "日志文件: $LOG_FILE"
echo "按 Ctrl+C 停止"

# 信号处理
trap 'echo -e "\n监控停止\n成功: $SUCCESS, 失败: $ERROR"; exit 0' SIGINT

while true; do
    TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
    
    # 发送请求并获取HTTP状态码
    RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code};TIME:%{time_total}" \
        -H 'Content-Type: application/json' \
        -H 'Tenant-Id: sz_unifly' \
        --data-raw '{"account":"***********","password":"6b86b273ff34fce19d6b804eff5a3f5747ada4eaa22f1d49c01e52ddb7875b4b","scene":"matching_platform"}' \
        --max-time 30 \
        --insecure \
        "$URL" 2>&1)
    
    # 提取HTTP状态码和响应时间
    HTTP_CODE=$(echo "$RESPONSE" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    TIME_TOTAL=$(echo "$RESPONSE" | grep -o "TIME:[0-9.]*" | cut -d: -f2)
    BODY=$(echo "$RESPONSE" | sed 's/HTTPSTATUS:[0-9]*;TIME:[0-9.]*$//')
    
    # 判断结果
    if [ "$HTTP_CODE" = "200" ] && ! echo "$BODY" | grep -q '"success":false\|"error"\|"exception"'; then
        ((SUCCESS++))
        echo "[$TIMESTAMP] ✅ 成功 (${TIME_TOTAL}s) - 总计: 成功$SUCCESS 失败$ERROR"
        echo "[$TIMESTAMP] SUCCESS - HTTP:$HTTP_CODE, Time:${TIME_TOTAL}s" >> "$LOG_FILE"
    else
        ((ERROR++))
        echo "[$TIMESTAMP] ❌ 失败 - HTTP:$HTTP_CODE - 总计: 成功$SUCCESS 失败$ERROR"
        echo "[$TIMESTAMP] ERROR - HTTP:$HTTP_CODE, Time:${TIME_TOTAL}s" >> "$LOG_FILE"
        echo "[$TIMESTAMP] Response: $BODY" >> "$LOG_FILE"
        echo "---" >> "$LOG_FILE"
    fi
    
    sleep $INTERVAL
done
