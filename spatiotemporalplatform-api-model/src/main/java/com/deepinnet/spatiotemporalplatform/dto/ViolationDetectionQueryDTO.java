package com.deepinnet.spatiotemporalplatform.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 违规检测查询DTO
 *
 * <AUTHOR>
 */
@Data
public class ViolationDetectionQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 当前页码
     */
    @NotNull(message = "当前页码不能为空")
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @NotNull(message = "每页大小不能为空")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "运营主体")
    private String operatingEntity;

    /**
     * 时间范围开始
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    /**
     * 时间范围结束
     */
    @ApiModelProperty(value = "结束时间时间")
    private LocalDateTime endTime;
}
