package com.deepinnet.spatiotemporalplatform.dto;

import io.swagger.annotations.*;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> wong
 * @create 2025/3/6 14:11
 * @Description
 */
@Data
@ApiModel("飞行探测数据传输对象")
public class FlightDetectionDTO implements Serializable {
    @ApiModelProperty("数据ID")
    private Long id;

    @ApiModelProperty("基站编号")
    private String devId;

    @ApiModelProperty("无人机型号（通过 osid 匹配类型映射库，未匹配则为 unknown）")
    private String model;

    @ApiModelProperty("无人机编号")
    private String osid;

    @ApiModelProperty("无人机类型")
    private Integer uaType;

    @ApiModelProperty("起飞状态: active-黑飞中，ended-黑飞技术")
    private String status;

    @ApiModelProperty("航迹角 (0-360, 361 表示未知)")
    private Integer direction;

    @ApiModelProperty("水平速度 (m/s)")
    private String speedHorizontal;

    @ApiModelProperty("垂直速度 (m/s)")
    private String speedVertical;

    @ApiModelProperty("大类，1代表无人机，5代表飞鸟")
    private String bigType;

    /**
     * 无人机经度
     */
    @ApiModelProperty("无人机经度")
    private Double longitude;

    /**
     * 无人机纬度
     */
    @ApiModelProperty("无人机纬度")
    private Double latitude;

    /**
     * 海拔
     */
    @ApiModelProperty("海拔")
    private Double altitude;

    @ApiModelProperty("气压高度 (Ref 29.92 inHg, 1013.24 mb), -1000 表示无效值")
    private Integer altitudeBaro;

    @ApiModelProperty("真实高度 (meter), -1000 表示无效值")
    private Double height;

    @ApiModelProperty("飞手经度")
    private Double operatorLongitude;

    @ApiModelProperty("飞手纬度")
    private Double operatorLatitude;

    @ApiModelProperty("飞手海拔高度")
    private Integer operatorAltitudeGeo;

    @ApiModelProperty("无线信号强度 (取两根天线的最大值)")
    private Integer rssi;

    @ApiModelProperty("右上角天线信号强度")
    private Integer rssi0;

    @ApiModelProperty("左上角天线信号强度")
    private Integer rssi1;

    @ApiModelProperty("设备温度 (摄氏度)")
    private Double temperature;

    @ApiModelProperty("基站设备 MAC 地址")
    private String mac;

    @ApiModelProperty("无人机中心频点")
    private String frequency;

    @ApiModelProperty("无人机带宽")
    private String bandwidth;

    @ApiModelProperty("上报 Unix 时间戳 (秒)")
    private Long time;
}

