package com.deepinnet.spatiotemporalplatform.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/19
 */

@Data
public class BatchQueryPlanDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 需求业务编号
     */
    private List<String> bizNo;

    /**
     * 租户ID
     */
    private String tenantId;

}
