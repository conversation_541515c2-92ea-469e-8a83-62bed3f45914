package com.deepinnet.spatiotemporalplatform.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/12
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlightStatisticsOverviewQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "查询开始时间")
    private Long startTime;

    @ApiModelProperty(value = "查询结束时间")
    private Long endTime;

}
