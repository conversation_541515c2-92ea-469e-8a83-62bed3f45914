package com.deepinnet.spatiotemporalplatform.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/13
 */

@Data
@ApiModel
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class POIPolygonQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 区域范围
     */
    @ApiModelProperty(value = "多边形坐标")
    private String region;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private List<String> types;

    /**
     * 分页页码 1-100
     */
    @ApiModelProperty(value = "分页页码 1-100")
    private Integer pageNum = 1;

    /**
     * 分页大小 1-25
     */
    @ApiModelProperty(value = "分页大小 1-25")
    private Integer pageSize = 25;

}
