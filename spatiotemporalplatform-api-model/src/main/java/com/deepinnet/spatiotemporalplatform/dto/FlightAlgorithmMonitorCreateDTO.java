package com.deepinnet.spatiotemporalplatform.dto;

import com.deepinnet.spatiotemporalplatform.enums.FlightEventTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description:
 * Date: 2025/5/7
 * Author: lijunheng
 */
@Data
public class FlightAlgorithmMonitorCreateDTO implements Serializable {

    /**
     * 飞行任务id===飞行计划id
     */
    private String flightTaskId;

    /**
     * 检测事件类型列表
     */
    private List<FlightEventTypeEnum> eventTypeList;

    /**
     * 视频流URL
     */
    private String videoUrl;

    /**
     * 飞行架次 id
     * */
    private String flightId;

    private String tenantId;
}
