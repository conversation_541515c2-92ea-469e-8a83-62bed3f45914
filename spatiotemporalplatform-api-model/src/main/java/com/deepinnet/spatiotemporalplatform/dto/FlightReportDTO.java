package com.deepinnet.spatiotemporalplatform.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/19
 */

@Data
public class FlightReportDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 计划编号
     */
    private String planId;

    /**
     * 需要更新的飞行报告
     */
    private String flightReport;

}
