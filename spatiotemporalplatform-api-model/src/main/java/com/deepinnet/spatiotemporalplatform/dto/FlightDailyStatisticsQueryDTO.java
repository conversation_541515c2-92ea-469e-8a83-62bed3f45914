package com.deepinnet.spatiotemporalplatform.dto;

import com.deepinnet.spatiotemporalplatform.enums.FlightQueryFiledEnum;
import com.deepinnet.spatiotemporalplatform.enums.FlightQuerySortTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/12
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlightDailyStatisticsQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "查询开始时间")
    private Long startTime;

    @ApiModelProperty(value = "查询结束时间")
    private Long endTime;

    @ApiModelProperty(value = "排序字段: FLIGHT_COUNT-飞行架次; FLIGHT_DURATION-飞行时长; FLIGHT_MILES-飞行里程;")
    private String sortField = FlightQueryFiledEnum.FLIGHT_COUNT.getCode();

    @ApiModelProperty(value = "排序方式: ASC-升序; DESC-降序")
    private String sortType = FlightQuerySortTypeEnum.DESC.getCode();

}
