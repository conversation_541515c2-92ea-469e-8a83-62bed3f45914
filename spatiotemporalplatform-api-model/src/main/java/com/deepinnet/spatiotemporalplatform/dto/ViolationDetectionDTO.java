package com.deepinnet.spatiotemporalplatform.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 违规检测表数据对象
 * 存储飞行器违规行为的检测记录
 *
 * <AUTHOR>
 */
@Data
public class ViolationDetectionDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID，自1开始自增
     */
    private Long id;

    /**
     * 违规时间：违规行为发生的时间点
     */
    @ApiModelProperty(value = "违规时间")
    private LocalDateTime violationTime;

    /**
     * 违规类型：违规行为的分类，如超高飞行、禁飞区飞行等
     */
    @ApiModelProperty(value = "违规类型")
    private String violationType;

    /**
     * 运营主体：违规飞行器的运营方或责任单位
     */
    @ApiModelProperty(value = "运营主体")
    private String operatingEntity;

    /**
     * 飞行器SN码：违规飞行器的序列号或唯一标识
     */
    @ApiModelProperty(value = "飞行器SN码")
    private String aircraftSn;

    /**
     * 违规详情：违规行为的详细描述
     */
    @ApiModelProperty(value = "违规详情")
    private String violationDetails;

    /**
     * 坐标：违规行为发生位置的地理坐标
     */
    @ApiModelProperty(value = "坐标")
    private String coordinates;

    /**
     * 高度：违规行为发生的高度，单位可为米
     */
    @ApiModelProperty(value = "高度")
    private String altitude;
} 