package com.deepinnet.spatiotemporalplatform.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 禁飞区/管制区数据传输对象
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@Data
public class FenceSpaceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 管制区ID
     */
    private Integer id;

    /**
     * 地区代码
     */
    private String addressCode;

    /**
     * 管制区名称
     */
    private String name;

    /**
     * 名称拼音
     */
    private String namePinyin;

    /**
     * 管制区编号
     */
    private String fenceNum;

    /**
     * 删除标识
     */
    private String isDeleted;

    /**
     * 类型 1.净空区 2.禁飞区 3.适飞区 4.超低空航路
     */
    private Integer fenceType;

    /**
     * 航路宽度
     */
    private Integer bufferWidth;

    /**
     * 管制高度
     */
    private Integer height;

    /**
     * 基准海拔高
     */
    private Integer alt;

    /**
     * 开始时间
     */
    private Integer beginTime;

    /**
     * 结束时间
     */
    private Integer endTime;

    /**
     * 地址
     */
    private String address;

    /**
     * 详细坐标
     */
    private String spatialDetailPoints;

    /**
     * 豁免机型
     */
    private String freeUavType;

    /**
     * 划分类型 1-行政区域划分 2-手动划分
     */
    private String zoneType;

    /**
     * 生效类型 0-永久 1临时
     */
    private String limitMode;

    /**
     * 状态 1.停用 0.启用
     */
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
} 