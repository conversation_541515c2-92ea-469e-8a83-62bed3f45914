package com.deepinnet.spatiotemporalplatform.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 违规分组统计DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ViolationGroupStatisticsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 运营主体
     */
    @ApiModelProperty(value = "运营主体")
    private String operatingEntity;

    /**
     * 违规类型
     */
    @ApiModelProperty(value = "违规类型：deviate_route-偏离航道、enter_no_fly_zone侵入禁飞区、exceed_altitude-超高飞行")
    private String violationType;

    /**
     * 违规数量
     */
    @ApiModelProperty(value = "违规数量")
    private Long count;
} 