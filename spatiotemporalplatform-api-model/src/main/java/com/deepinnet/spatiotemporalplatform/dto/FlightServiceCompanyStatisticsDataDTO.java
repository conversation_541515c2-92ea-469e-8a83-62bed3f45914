package com.deepinnet.spatiotemporalplatform.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 飞行服务公司统计数据DTO
 *
 * <AUTHOR>
 * @create 2025/6/30
 */
@ApiModel("飞行服务公司统计数据")
public class FlightServiceCompanyStatisticsDataDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称", example = "大疆创新")
    private String companyName;

    /**
     * 飞行架次
     */
    @ApiModelProperty(value = "飞行架次", example = "3300")
    private Integer flightCount;

    // 构造器
    public FlightServiceCompanyStatisticsDataDTO() {}

    public FlightServiceCompanyStatisticsDataDTO(String companyName, Integer flightCount) {
        this.companyName = companyName;
        this.flightCount = flightCount;
    }

    // Getters and Setters
    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Integer getFlightCount() {
        return flightCount;
    }

    public void setFlightCount(Integer flightCount) {
        this.flightCount = flightCount;
    }
} 