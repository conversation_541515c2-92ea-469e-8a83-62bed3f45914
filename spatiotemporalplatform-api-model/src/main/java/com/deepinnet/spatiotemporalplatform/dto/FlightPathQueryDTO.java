package com.deepinnet.spatiotemporalplatform.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2025/3/17 18:03
 * @Description
 */
@Data
public class FlightPathQueryDTO {

    @ApiModelProperty(value = "业务编号")
    private List<Long> unplannedFlightIds;

    @ApiModelProperty(value = "目标状态：active-预警中，ended-预警结束")
    private List<String> targetStatusList;
}
