package com.deepinnet.spatiotemporalplatform.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> wong
 * @create 2025/3/12 17:09
 * @Description
 */
@Data
public class IntelligenceDTO {

    @ApiModelProperty(value = "业务编号")
    private String bizNo;

    /**
     * 空域编号
     */
    @ApiModelProperty(value = "空域编号")
    private String airspaceCode;

    /**
     * 空域名称
     */
    @ApiModelProperty(value = "空域名称")
    private String airspaceName;

    @ApiModelProperty(value = "空域最低高度")
    private Integer minHeight;

    @ApiModelProperty(value = "空域最高高度")
    private Integer maxHeight;

    @ApiModelProperty(value = "空域的二维wkt")
    private String wkt2;

    @ApiModelProperty(value = "空域的三维wkt")
    private String wkt3;

    /**
     * 预警类型
     */
    @ApiModelProperty(value = "temperature-温度,wind_speed-风速,rainfall-降雨量,control-管控情报")
    private String type;

    /**
     * 预警时间
     */
    @ApiModelProperty(value = "预警时间")
    private Long warningTime;

    /**
     * 预警状态
     */
    @ApiModelProperty(value = "预警状态：warning-预警中，ended-预警结束")
    private String status;

    /**
     * 预警内容
     */
    @ApiModelProperty(value = "预警内容")
    private String content;

    @ApiModelProperty(value = "天气网格数据url")
    private String weatherDataUrl;

    /**
     * 预警标题
     */
    @ApiModelProperty(value = "预警标题")
    private String title;
}
