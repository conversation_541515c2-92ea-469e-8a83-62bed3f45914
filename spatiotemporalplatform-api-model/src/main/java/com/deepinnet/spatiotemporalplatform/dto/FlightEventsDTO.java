package com.deepinnet.spatiotemporalplatform.dto;

import com.deepinnet.digitaltwin.common.util.PointCoordinate;
import com.deepinnet.spatiotemporalplatform.enums.FlightEventStatusEnum;
import com.deepinnet.spatiotemporalplatform.enums.FlightEventTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 飞行事件记录DTO
 *
 * <AUTHOR>
 */
@Data
public class FlightEventsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 算法侧事件唯一标识
     */
    private String algorithmEventId;

    /**
     * 事件类型，例如违停、闯入禁区等
     */
    private FlightEventTypeEnum eventType;

    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 事件描述，详细说明事件内容
     */
    private String description;

    /**
     * 事件发生地点的经纬度坐标，WGS84坐标系
     */
    private PointCoordinate eventPoint;

    /**
     * 事件发生地点的文字描述（如：某某街口）
     */
    private String eventLocation;

    /**
     * 事件持续时长，单位：秒
     */
    private Long duration;

    /**
     * 事件状态，例如持续中、已结束
     */
    private FlightEventStatusEnum status;

    /**
     * 涉事车辆的车牌号
     */
    private String licensePlate;

    /**
     * 取证图片的 JSON 数组，包含图片地址和描述
     */
    private String evidenceImages;

    /**
     * 取证视频的 JSON 数组，包含视频地址和描述
     */
    private String evidenceVideos;

    /**
     * 飞行任务id
     */
    private String flightTaskCode;

    /**
     * 事件开始时间
     */
    private LocalDateTime eventStartTime;

    /**
     * 事件结束时间
     */
    private LocalDateTime eventEndTime;

    /**
     * 算法侧事件更新时间
     */
    private LocalDateTime algorithmEventGmtModified;

    /**
     * 租户id
     */
    private String tenantId;
} 