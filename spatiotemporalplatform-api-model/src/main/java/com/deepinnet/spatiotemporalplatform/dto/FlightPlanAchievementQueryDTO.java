package com.deepinnet.spatiotemporalplatform.dto;

import com.deepinnet.spatiotemporalplatform.enums.FlightAchievementTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/8/11
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlightPlanAchievementQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("计划ID")
    private String planId;

    @ApiModelProperty("对比日期")
    private LocalDate compareDate;

    @ApiModelProperty("需对比的成果类型")
    private FlightAchievementTypeEnum achievementTypeEnum = FlightAchievementTypeEnum.IMAGE;

}
