package com.deepinnet.spatiotemporalplatform.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * 导航设备查询DTO
 *
 * <AUTHOR>
 * @create 2025/6/30
 */
@Data
@ApiModel("导航设备查询")
public class NavigateEquipmentQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 厂家
     */
    private String manufacturer;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 品牌型号
     */
    private String brandModel;

    /**
     * 序列号
     */
    private String serialNo;

    /**
     * 基站类型
     */
    private String stationType;

    /**
     * 设备用途
     */
    private String equipUsage;

    /**
     * 安装地址
     */
    private String installAdress;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 部门ID
     */
    private String departId;

    // Getter and Setter methods
    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getBrandModel() {
        return brandModel;
    }

    public void setBrandModel(String brandModel) {
        this.brandModel = brandModel;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public String getStationType() {
        return stationType;
    }

    public void setStationType(String stationType) {
        this.stationType = stationType;
    }

    public String getEquipUsage() {
        return equipUsage;
    }

    public void setEquipUsage(String equipUsage) {
        this.equipUsage = equipUsage;
    }

    public String getInstallAdress() {
        return installAdress;
    }

    public void setInstallAdress(String installAdress) {
        this.installAdress = installAdress;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getDepartId() {
        return departId;
    }

    public void setDepartId(String departId) {
        this.departId = departId;
    }

    @Override
    public String toString() {
        return "NavigateEquipmentQueryDTO{" +
                "pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                ", manufacturer='" + manufacturer + '\'' +
                ", deviceName='" + deviceName + '\'' +
                ", brandModel='" + brandModel + '\'' +
                ", serialNo='" + serialNo + '\'' +
                ", stationType='" + stationType + '\'' +
                ", equipUsage='" + equipUsage + '\'' +
                ", installAdress='" + installAdress + '\'' +
                ", tenantId='" + tenantId + '\'' +
                ", departId='" + departId + '\'' +
                '}';
    }
} 