package com.deepinnet.spatiotemporalplatform.dto;

import com.deepinnet.spatiotemporalplatform.vo.RealTimeUavFlightVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2025/3/17 18:03
 * @Description
 */
@Data
public class RiskWarningPathDTO {

    @ApiModelProperty(value = "预警编号")
    private String warningNo;

    @ApiModelProperty(value = "飞行路径")
    private List<RealTimeUavFlightVO> flightPath;
}
