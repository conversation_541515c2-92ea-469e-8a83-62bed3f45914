package com.deepinnet.spatiotemporalplatform.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 飞行服务公司统计响应DTO
 *
 * <AUTHOR>
 * @create 2025/6/30
 */
@ApiModel("飞行服务公司统计响应")
public class FlightServiceCompanyStatisticsResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    @ApiModelProperty(value = "状态码", example = "200")
    private Integer status;

    /**
     * 统计数据列表
     */
    @ApiModelProperty(value = "统计数据列表")
    private List<FlightServiceCompanyStatisticsDataDTO> data;

    // 构造器
    public FlightServiceCompanyStatisticsResponseDTO() {}

    public FlightServiceCompanyStatisticsResponseDTO(Integer status, List<FlightServiceCompanyStatisticsDataDTO> data) {
        this.status = status;
        this.data = data;
    }

    // Getters and Setters
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<FlightServiceCompanyStatisticsDataDTO> getData() {
        return data;
    }

    public void setData(List<FlightServiceCompanyStatisticsDataDTO> data) {
        this.data = data;
    }
} 