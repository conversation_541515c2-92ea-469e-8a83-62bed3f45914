package com.deepinnet.spatiotemporalplatform.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> wong
 * @create 2025/3/18 12:55
 * @Description
 */
@Data
public class FlightDetectionLatestPathDTO {

    @ApiModelProperty(value = "业务编号")
    private Long unplannedFlightId;

    @ApiModelProperty(value = "飞行器状态，active-黑飞中，ended-黑飞结束")
    private String status;

    @ApiModelProperty(value = "最新的飞行路径")
    private FlightDetectionDTO latestFlightPath;
}
