package com.deepinnet.spatiotemporalplatform.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 字典项批量操作传输对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "字典项批量操作传输对象")
public class DictionaryItemBatchOperationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "要删除的字典项ID列表")
    private List<Long> deleteIds;

    @ApiModelProperty(value = "要新增的字典项列表")
    private List<DictionaryItemDTO> createItems;

    @NotNull(message = "字典ID不能为空")
    @ApiModelProperty(value = "字典ID", required = true)
    private Long dictionaryId;
} 