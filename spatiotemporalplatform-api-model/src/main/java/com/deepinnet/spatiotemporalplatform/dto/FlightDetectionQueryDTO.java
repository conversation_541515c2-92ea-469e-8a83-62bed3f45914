package com.deepinnet.spatiotemporalplatform.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR> wong
 * @create 2025/3/6 14:11
 * @Description
 */
@Data
public class FlightDetectionQueryDTO implements Serializable {

    /**
     * 页码
     */
    @NotNull(message = "页码不能为空")
    private Integer pageNum = 1;

    /**
     * 每页数量
     */
    @NotNull(message = "页大小不能为空")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "开始时间，不传则查询当天的")
    private Long startTime;

    @ApiModelProperty(value = "结束时间，不传则查询当天的")
    private Long endTime;
}
