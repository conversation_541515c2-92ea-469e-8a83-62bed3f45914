package com.deepinnet.spatiotemporalplatform.dto;

import lombok.Data;

/**
 * <AUTHOR> wong
 * @create 2025/1/13 09:44
 * @Description
 */
@Data
public class GroundFeaturesQueryDTO {
    /**
     * 七级二维北斗网格位置码集合
     * 例如：{"N49FB520740010", "N49FB520740012"}
     */
    private String bd2DGridLocationCode;

    /**
     * 坐标信息（纬度、经度、高度）
     */
    private Coordinates coordinates;

    /**
     * 区域范围，GeoJSON 格式，定义二维空间的空域
     */
    private String region;

}
