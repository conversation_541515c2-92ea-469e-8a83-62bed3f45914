package com.deepinnet.spatiotemporalplatform.dto;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.*;

/**
 * <AUTHOR> wong
 * @create 2025/3/4 16:06
 * @Description
 */
@Data
public class RiskWarningSummaryDTO {

    @ApiModelProperty(value = "风险预警数量: super_scope-超域，conflict-冲突，impact-障碍物碰撞，no_fly-闯禁飞区，around-周边安全隐患")
    private Map<String, Integer> riskWarningCount;

    @ApiModelProperty(value = "风险预警列表")
    private CommonPage<RiskWarningDTO> riskWarningPage;
}
