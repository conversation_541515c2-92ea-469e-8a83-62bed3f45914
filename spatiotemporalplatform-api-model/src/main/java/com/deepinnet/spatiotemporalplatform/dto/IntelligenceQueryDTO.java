package com.deepinnet.spatiotemporalplatform.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2025/3/12 17:09
 * @Description
 */
@Data
public class IntelligenceQueryDTO {

    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    @ApiModelProperty(value = "页大小")
    private Integer pageSize;

    @ApiModelProperty(value = "开始时间")
    private Long startTime;

    @ApiModelProperty(value = "结束时间")
    private Long endTime;

    @ApiModelProperty(value = "是否是历史数据查询请求，查询历史传true")
    private Boolean isHistorical;
}
