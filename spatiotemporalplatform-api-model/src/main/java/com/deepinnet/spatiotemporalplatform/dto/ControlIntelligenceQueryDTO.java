package com.deepinnet.spatiotemporalplatform.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 管控情报查询DTO
 *
 * <AUTHOR>
 */
@Data
public class ControlIntelligenceQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页码
     */
    @NotNull(message = "页码不能为空")
    private Integer pageNum = 1;

    /**
     * 每页数量
     */
    @NotNull(message = "页大小不能为空")
    private Integer pageSize = 10;

    /**
     * 发布时间起始
     */
    @NotNull(message = "发布开始时间不能为空")
    private LocalDateTime publishTimeStart;

    /**
     * 发布时间截止
     */
    @NotNull(message = "发布结束时间不能为空")
    private LocalDateTime publishTimeEnd;
} 