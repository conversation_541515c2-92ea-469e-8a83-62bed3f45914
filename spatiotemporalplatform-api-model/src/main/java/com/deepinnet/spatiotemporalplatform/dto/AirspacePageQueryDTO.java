package com.deepinnet.spatiotemporalplatform.dto;

import lombok.Data;

/**
 * 空域分页查询DTO
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@Data
public class AirspacePageQueryDTO {
    
    /**
     * 页码
     */
    private Integer pageNum;
    
    /**
     * 每页大小
     */
    private Integer pageSize;
    
    /**
     * 空域名称
     */
    private String name;
    
    /**
     * 空域状态
     */
    private String status;

    /**
     * 空域范围类型,传 0 1 2 3
     *      CIRCLE("圆形", "0"),
     *     LINE("航线", "1"),
     *     POLYGON("多边形","2"),
     *     SECTOR("扇形","3"),;
     * @see com.deepinnet.spatiotemporalplatform.skyflow.enums.YuanFeiAirSpaceRangeTypeEnum
     */
    private String rangeType;
    
    /**
     * 空域类型
     */
    private String privateType;

    /**
     * 最小高度
     * 比如 10
     */
    private Integer minHeight;

    /**
     * 最大高度
     * 比如 1000
     */
    private Integer maxHeight;

    /**
     * 来源，不指定就查默认的
     * */
    private String source = "DEFAULT";
} 