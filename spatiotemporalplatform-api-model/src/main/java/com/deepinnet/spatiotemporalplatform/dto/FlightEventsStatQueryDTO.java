package com.deepinnet.spatiotemporalplatform.dto;

import com.deepinnet.spatiotemporalplatform.enums.FlightEventsStatStartTimeQueryType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 飞行事件统计查询DTO
 *
 * <AUTHOR>
 */
@Data
public class FlightEventsStatQueryDTO implements Serializable {

    @ApiModelProperty("事件发生开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "需求发布者编号")
    private String userNo;

    @ApiModelProperty(value = "需求类型")
    private String demandType;

    private String tenantId;

    private String account;

    private List<String> userNoList;

    private FlightEventsStatStartTimeQueryType startTimeType = FlightEventsStatStartTimeQueryType.DEMAND_PUBLISH_TIME;

    @ApiModelProperty(value = "需求来源")
    private String demandScene;
} 