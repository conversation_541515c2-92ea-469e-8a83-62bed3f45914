package com.deepinnet.spatiotemporalplatform.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字典查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "字典查询对象")
public class DictionaryQueryDTO extends PageQueryDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "字典ID")
    private Long dictionaryId;

    @ApiModelProperty(value = "字典名称")
    private String name;

    @ApiModelProperty(value = "字典类型")
    private String type;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;
} 