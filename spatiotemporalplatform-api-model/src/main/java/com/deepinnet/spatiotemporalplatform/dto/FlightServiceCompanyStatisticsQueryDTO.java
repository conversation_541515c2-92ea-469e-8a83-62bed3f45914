package com.deepinnet.spatiotemporalplatform.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 飞行服务公司统计查询DTO
 *
 * <AUTHOR>
 * @create 2025/6/30
 */
@Data
@ApiModel("飞行服务公司统计查询")
public class FlightServiceCompanyStatisticsQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始时间，格式：2025-01-01，不传则默认近一个月
     */
    @ApiModelProperty(value = "开始时间", example = "2025-01-01")
    private String startTime;

    /**
     * 结束时间，格式：2025-12-31，不传则默认近一个月
     */
    @ApiModelProperty(value = "结束时间", example = "2025-12-31")
    private String endTime;

    // 构造器
    public FlightServiceCompanyStatisticsQueryDTO() {}

    public FlightServiceCompanyStatisticsQueryDTO(String startTime, String endTime) {
        this.startTime = startTime;
        this.endTime = endTime;
    }

    // Getters and Setters
    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
} 