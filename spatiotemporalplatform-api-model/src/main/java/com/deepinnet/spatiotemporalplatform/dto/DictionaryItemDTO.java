package com.deepinnet.spatiotemporalplatform.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 字典项数据传输对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "字典项数据传输对象")
public class DictionaryItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "字典ID", required = true)
    @NotNull(message = "字典ID不能为空")
    private Long dictionaryId;

    @ApiModelProperty(value = "字典项编码", required = true)
    @NotBlank(message = "字典项编码不能为空")
    private String code;

    @ApiModelProperty(value = "字典项名称", required = true)
    @NotBlank(message = "字典项名称不能为空")
    private String name;

    @ApiModelProperty(value = "状态")
    private String state;

    @ApiModelProperty(value = "父编码")
    private String parentCode;

    @ApiModelProperty(value = "层级")
    private Integer level;

    @ApiModelProperty(value = "层级名称")
    private String levelName;

    @ApiModelProperty(value = "排序", notes = "数字越小排序越靠前，默认为1")
    private Integer sort = 1;

    @ApiModelProperty(value = "租户ID", required = true)
    @NotBlank(message = "租户ID不能为空")
    private String tenantId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreated;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "是否删除")
    private Integer isDeleted;
} 