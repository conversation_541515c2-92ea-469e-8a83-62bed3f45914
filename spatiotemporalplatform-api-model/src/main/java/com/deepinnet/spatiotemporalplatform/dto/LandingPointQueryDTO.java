package com.deepinnet.spatiotemporalplatform.dto;

import java.io.Serializable;

/**
 * 起降点查询DTO
 *
 * <AUTHOR>
 */
public class LandingPointQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 编号
     */
    private String code;

    /**
     * 类型
     */
    private String type;

    /**
     * 名称
     */
    private String name;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 团队ID
     */
    private String teamId;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 部门ID
     */
    private String departId;

    /**
     * 所有人
     */
    private String owner;

    /**
     * 运营人
     */
    private String operator;

    /**
     * 业务类型
     */
    private String busiType;

    /**
     * 起降场类型
     */

    private String airType;

    /**
     * 所属街道
     */
    private String street;

    // Getter and Setter methods

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getTeamId() {
        return teamId;
    }

    public void setTeamId(String teamId) {
        this.teamId = teamId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getDepartId() {
        return departId;
    }

    public void setDepartId(String departId) {
        this.departId = departId;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getBusiType() {
        return busiType;
    }

    public void setBusiType(String busiType) {
        this.busiType = busiType;
    }

    public String getAirType() {
        return airType;
    }

    public void setAirType(String airType) {
        this.airType = airType;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    @Override
    public String toString() {
        return "LandingPointQueryDTO{" +
                ", code='" + code + '\'' +
                ", type='" + type + '\'' +
                ", name='" + name + '\'' +
                ", address='" + address + '\'' +
                ", teamId='" + teamId + '\'' +
                ", tenantId='" + tenantId + '\'' +
                ", departId='" + departId + '\'' +
                ", owner='" + owner + '\'' +
                ", operator='" + operator + '\'' +
                ", busiType='" + busiType + '\'' +
                ", airType='" + airType + '\'' +
                ", street='" + street + '\'' +
                '}';
    }
} 