package com.deepinnet.spatiotemporalplatform.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 通用ID传输对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "通用ID传输对象")
public class IdDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID", required = true, example = "1")
    @NotNull(message = "ID不能为空")
    private Integer id;

} 