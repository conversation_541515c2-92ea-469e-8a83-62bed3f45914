package com.deepinnet.spatiotemporalplatform.dto;

import lombok.Data;
import org.locationtech.jts.geom.Point;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 空域数据传输对象
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@Data
public class AirspaceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * 空域代码
     */
    private String code;

    /**
     * 空域id
     */
    private String businessCode;

    /**
     * 名称
     */
    private String name;

    /**
     * 使用状态，开放、关闭、限制
     */
    private String status;

    /**
     * 空间三维WKT字符串
     */
    private String wkt3;

    /**
     * 二维WKT字符串
     */
    private String wkt2;

    /**
     * 原始航道 wkt
     */
    private String wkt1;

    /**
     * 航线长度
     */
    private Integer length;

    /**
     * 起飞地坐标
     */
    private Point beginAddr;

    /**
     * 降落地坐标
     */
    private Point endAddr;

    /**
     * 航线宽度
     */
    private Integer width;

    /**
     * 航线高度
     */
    private Integer height;

    /**
     * 半径
     */
    private Integer radius;
    /**
     * 最小高度
     */
    private Integer minHeight;

    /**
     * 最大高度
     */
    private Integer maxHeight;

    /**
     * 形状、长方体、圆柱体、不规则体
     */
    private Integer shapeType;

    /**
     * 1.公有空域2.私有空域
     */
    private String privateType;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
} 