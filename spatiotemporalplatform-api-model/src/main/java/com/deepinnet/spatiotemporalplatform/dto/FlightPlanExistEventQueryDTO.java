package com.deepinnet.spatiotemporalplatform.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description:
 * Date: 2025/8/14
 * Author: lijunheng
 */
@ApiModel("查询计划存在指定算法事件参数")
@Data
public class FlightPlanExistEventQueryDTO implements Serializable {

    @ApiModelProperty(value = "计划ID列表")
    private List<String> planIdList;

    @ApiModelProperty(value = "算法编码列表")
    private List<String> algorithmCodeList;
}
