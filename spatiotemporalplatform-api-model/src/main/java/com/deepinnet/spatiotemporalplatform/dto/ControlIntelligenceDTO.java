package com.deepinnet.spatiotemporalplatform.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 管控情报DTO
 *
 * <AUTHOR>
 */
@Data
public class ControlIntelligenceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 情报来源
     */
    private String source;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 管控内容
     */
    private String content;

    /**
     * 管控情报编号
     */
    private String controlIntelligenceNo;

} 