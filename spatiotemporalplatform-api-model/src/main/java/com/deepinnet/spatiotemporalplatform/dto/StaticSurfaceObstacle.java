package com.deepinnet.spatiotemporalplatform.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *     静态障碍物
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/16
 */
@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class StaticSurfaceObstacle implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 七级二维北斗网格位置码:例如N49FB520740010
     */
    @ApiModelProperty(value = "七级二维北斗网格位置码:例如N49FB520740010")
    private String bd2DGridLocationCode;

    /**
     * 竖直障碍物标记:例如[0,1]在0&1层空间对应的竖直位置处有静态障碍物
     */
    @ApiModelProperty(value = "竖直障碍物标记:例如[0,1]在0&1层空间对应的竖直位置处有静态障碍物")
    private List<Integer> verticalObstacleMarker;

    /**
     * 当前区域范围
     */
    @ApiModelProperty(value = "当前区域范围")
    private String region;

}
