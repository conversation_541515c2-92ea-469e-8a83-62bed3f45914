package com.deepinnet.spatiotemporalplatform.dto;

import lombok.Data;

/**
 * 禁飞区/管制区分页查询DTO
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@Data
public class FenceSpacePageQueryDTO {
    
    /**
     * 页码
     */
    private Integer pageNum;
    
    /**
     * 每页大小
     */
    private Integer pageSize;
    
    /**
     * 禁飞区/管制区名称
     */
    private String name;
    
    /**
     * 禁飞区/管制区类型
     */
    private Integer fenceType;
    
    /**
     * 禁飞区/管制区状态
     */
    private String status;
} 