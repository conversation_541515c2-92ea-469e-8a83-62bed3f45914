package com.deepinnet.spatiotemporalplatform.dto;

import lombok.Data;

/**
 * Description: 飞行业务分析统计信息查询请求DTO
 * Date: 2025/6/26
 * Author: amos
 */
@Data
public class FlightBusinessAnalysisSpaQueryDTO {
    /**
     * 开始时间，格式：2025-01-01，不传则默认近一个月
     */
    private String startTime;
    
    /**
     * 结束时间，格式：2025-12-31，不传则默认近一个月
     */
    private String endTime;
    
    /**
     * 执行单位ID，深度固定传：99fae838557b4c2e952468932b1db4a2
     */
    private String teamId;
} 