package com.deepinnet.spatiotemporalplatform.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2025/3/17 18:03
 * @Description
 */
@Data
public class FlightPathDTO {

    @ApiModelProperty(value = "业务编号")
    private Long unplannedFlightId;

    @ApiModelProperty(value = "黑飞的状态：active-在途，ended-黑飞结束")
    private String status;

    @ApiModelProperty(value = "飞行路径")
    private List<FlightDetectionDTO> flightPath;
}
