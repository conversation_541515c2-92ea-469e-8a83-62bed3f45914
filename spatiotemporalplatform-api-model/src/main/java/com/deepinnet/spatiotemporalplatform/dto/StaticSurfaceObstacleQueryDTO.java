package com.deepinnet.spatiotemporalplatform.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 *     静态地表障碍物查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/13
 */
@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class StaticSurfaceObstacleQueryDTO implements Serializable {

    /**
     * 七级二维北斗网格位置码
     * 例如：N49FB520740010
     */
    @ApiModelProperty(value = "七级二维北斗网格位置码")
    private String bd2DGridLocationCode;

    /**
     * 坐标
     */
    @ApiModelProperty(value = "坐标")
    private Coordinates coordinates;

    /**
     * 区域范围，GeoJSON 格式，定义二维空间的空域 范围
     */
    @ApiModelProperty(value = "区域范围，GeoJSON 格式，定义二维空间的空域 范围")
    private String region;

}
