package com.deepinnet.spatiotemporalplatform.dto;

import com.deepinnet.spatiotemporalplatform.enums.FlightEventStatusEnum;
import com.deepinnet.spatiotemporalplatform.enums.FlightEventTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 飞行事件查询DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FlightEventsQueryDTO extends PageQueryDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 事件id
     */
    private String id;

    /**
     * 事件类型，例如违停、闯入禁区等
     */
    private FlightEventTypeEnum eventType;

    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 事件状态
     */
    private FlightEventStatusEnum status;

    /**
     * 事件发生地点
     */
    private String eventLocation;

    /**
     * 飞行任务id
     */
    private String flightTaskCode;

    /**
     * 事件发生开始时间
     */
    private LocalDateTime startTime;

    /**
     * 事件发生结束时间
     */
    private LocalDateTime endTime;

    @ApiModelProperty(value = "需求发布者编号")
    private String userNo;

    @ApiModelProperty("需求类型")
    private String demandType;

    private LocalDateTime demandStartTime;

    private LocalDateTime demandEndTime;

    private List<String> accountList;

    private List<String> demandNoList;

    private List<String> userNoList;

    private LocalDateTime planedTakeoffStartTime;

    private LocalDateTime planedTakeoffEndTime;

    private String demandScene;
}