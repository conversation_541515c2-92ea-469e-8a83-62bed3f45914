package com.deepinnet.spatiotemporalplatform.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *    点位信息查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/28
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PositionQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer status;

    private String planId;

    private List<String> planIds;

}
