package com.deepinnet.spatiotemporalplatform.dto;

import lombok.Data;

/**
 * Creator z<PERSON>ui
 * Date 2025-04-19
 **/

@Data
public class BaseGeographicInfoDTO {

    /**
     * 云底高（米）
     */
    private double cloudBaseHeight;

    /**
     * 能见度（米）
     */
    private double visibility;

    /**
     * 地面降雨强度
     */
    private double precipitationIntensity;

    /**
     * 地面大气气压
     */
    private double airpressure;

    /**
     * 大气湿度
     */
    private double humidity;

    /**
     * 地面温度（摄氏度）
     */
    private double temperature;
}
