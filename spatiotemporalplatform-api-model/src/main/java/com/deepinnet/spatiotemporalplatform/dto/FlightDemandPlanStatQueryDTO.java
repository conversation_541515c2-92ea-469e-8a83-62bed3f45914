package com.deepinnet.spatiotemporalplatform.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 飞行事件统计查询DTO
 *
 * <AUTHOR>
 */
@Data
public class FlightDemandPlanStatQueryDTO implements Serializable {

    @ApiModelProperty(value = "需求创建的时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "需求来源场景", example = "REGIONAL_TRAFFIC_SUPPORT_PLATFORM、SKY_FLOW_ECONOMY_MATCHING_PLATFORM、ONE_NET_UNIFIED_FLY")
    private String scene;

    @ApiModelProperty(value = "类目类型")
    private String categoryType;

    @ApiModelProperty(value = "需求类型", example = "ROUTINE_INSPECTION、LOGISTICS_TRANSPORTATION、EMERGENCY_RESPONSE")
    private String demandType;

    @ApiModelProperty(value = "需求发布者编号")
    private String userNo;

    private String tenantId;

    @ApiModelProperty(value = "需求发布者账号")
    private String account;
} 