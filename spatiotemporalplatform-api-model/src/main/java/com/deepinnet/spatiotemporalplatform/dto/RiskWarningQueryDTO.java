package com.deepinnet.spatiotemporalplatform.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2025/3/4 15:57
 * @Description
 */
@Data
public class RiskWarningQueryDTO {
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "需要的预警类型：super_scope-超域,weather-天气,conflict-冲突,impact-障碍物碰撞,no_fly-闯禁飞区,around-周边安全隐患,black-黑飞")
    private List<String> needWarningTypeList;

    /**
     * 关键字：飞行器id或者飞行单位
     */
    @ApiModelProperty(value = "关键字：飞行器id或者飞行单位")
    private String keyword;

    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    @ApiModelProperty(value = "页大小")
    private Integer pageSize;

    private String planId;
}
