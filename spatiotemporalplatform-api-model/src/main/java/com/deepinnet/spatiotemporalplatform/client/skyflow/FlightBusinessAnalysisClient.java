package com.deepinnet.spatiotemporalplatform.client.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.FlightBusinessAnalysisSpaQueryDTO;
import com.deepinnet.spatiotemporalplatform.vo.FlightBusinessAnalysisVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 飞行业务分析服务客户端
 *
 * <AUTHOR> wong
 * @create 2025/01/14
 */
@FeignClient(name = "flightBusinessAnalysisClient", url = "${stpf.service.url}")
public interface FlightBusinessAnalysisClient {

    /**
     * 查询所有飞行业务分析数据
     *
     * @return 飞行业务分析数据列表
     */
    @GetMapping("/stpf/flight/business/analysis/list")
    @ApiOperation(value = "[飞行业务分析接口] => 查询所有飞行业务分析数据")
    Result<List<FlightBusinessAnalysisVO>> listAllFlightBusinessAnalysis();


    /**
     * 查询指定时间飞行业务分析数据
     * @param queryDTO
     * @return
     */
    @GetMapping("/stpf/flight/business/analysis/query")
    @ApiOperation(value = "[飞行业务分析接口] => 查询指定时间飞行业务分析数据")
    Result<List<FlightBusinessAnalysisVO>> listSpecificFlightBusinessAnalysis(@RequestBody FlightBusinessAnalysisSpaQueryDTO queryDTO);

}