package com.deepinnet.spatiotemporalplatform.client;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 区域人流接口客户端
 *
 * <AUTHOR>
 * @version 2024-08-02
 */
@FeignClient(name = "peopleFlowServiceClient", url = "${stpf.service.url}")
public interface PeopleFlowServiceClient {
    /**
     * 查询实时人流数据
     *
     * @param realTimePeopleFlowParams
     * @return
     */
    @PostMapping("/stpf/area/people/flow/realtime")
    Result<RealTimePeopleFlowData> getRealTimePeopleFlow(@RequestBody RealTimePeopleFlowParams realTimePeopleFlowParams);

    /**
     * 查询历史人流数据
     *
     * @param historyPeopleFlowParams
     * @return
     */
    @PostMapping("/stpf/area/people/flow/history")
    Result<List<HistoryPeopleFlowData>> getHistoryPeopleFlow(@RequestBody HistoryPeopleFlowParams historyPeopleFlowParams);

    /**
     * 查询网格实时人流&活力指数数据
     *
     * @param realTimePeopleFlowParams
     * @return
     */
    @PostMapping("/stpf/area/people/flow/grid/realtime")
    Result<GridRealTimePeopleFlowData> getGridRealTimePeopleFlow(@RequestBody GridRealTimePeopleFlowParams realTimePeopleFlowParams);

    /**
     * 查询网格历史人流数据&活力数据
     *
     * @param historyPeopleFlowParams
     * @return
     */
    @PostMapping("/stpf/area/people/flow/grid/history")
    Result<List<GridHistoryPeopleFlowData>> getGridHistoryPeopleFlow(@RequestBody GridHistoryPeopleFlowParams historyPeopleFlowParams);

    /**
     * 人流来源地
     * @param regionPeopleSourceFlowParams 区域人流来源地参数
     * @return 区域人流来源地数据
     */
    @PostMapping("/stpf/area/people/flow/source")
    Result<RegionPeopleSourceFlowData> getSourcePeopleFlow(@RequestBody RegionPeopleSourceFlowParams regionPeopleSourceFlowParams);

}
