package com.deepinnet.spatiotemporalplatform.client.skyflow;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * 飞行事件接口客户端
 *
 * <AUTHOR>
 * @version 2024-07-31
 */
@FeignClient(name = "flightEventClient", url = "${stpf.service.url}")
public interface FlightEventClient {

    /**
     * 保存飞行事件
     *
     * @param flightEventsDTO 飞行事件DTO
     * @return 事件ID
     */
    @ApiOperation(value = "[飞行事件接口] => 保存飞行事件")
    @PostMapping("/stpf/event/save")
    Result<String> saveEvent(@RequestBody @Valid FlightEventsDTO flightEventsDTO);

    /**
     * 批量保存飞行事件
     *
     * @param flightEventsDTOList 飞行事件DTO列表
     * @return 是否保存成功
     */
    @ApiOperation(value = "[飞行事件接口] => 批量保存飞行事件")
    @PostMapping("/stpf/event/batch/save")
    Result<Boolean> saveEventBatch(@RequestBody @Valid List<FlightEventsDTO> flightEventsDTOList);

    /**
     * 根据ID查询飞行事件
     *
     * @param eventId 事件ID
     * @return 飞行事件DTO
     */
    @ApiOperation(value = "[飞行事件接口] => 根据ID查询飞行事件")
    @PostMapping("/stpf/event/detail")
    Result<FlightEventsDTO> getEventById(@RequestParam("eventId") String eventId);

    /**
     * 分页查询飞行事件
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @ApiOperation(value = "[飞行事件接口] => 分页查询飞行事件")
    @PostMapping("/stpf/event/page")
    Result<CommonPage<FlightEventsDTO>> pageQuery(@RequestBody @Valid FlightEventsQueryDTO queryDTO);

    /**
     * 根据飞行任务ID查询飞行事件列表
     *
     * @param flightTaskCode 飞行任务ID
     * @return 飞行事件DTO列表
     */
    @ApiOperation(value = "[飞行事件接口] => 根据飞行任务ID查询飞行事件列表")
    @PostMapping("/stpf/event/list/by/task")
    Result<List<FlightEventsDTO>> queryListByFlightTaskCode(@RequestBody FlightEventsQueryDTO queryDTO);

    /**
     * 查询飞行事件统计
     *
     * @param queryDTO 查询条件
     * @return 飞行事件统计数据
     */
    @ApiOperation(value = "[飞行事件接口] => 查询飞行事件统计")
    @PostMapping("/stpf/event/stat")
    Result<List<FlightEventsStatDTO>> queryFlightEventsStat(@RequestBody @Valid FlightEventsStatQueryDTO queryDTO);

    @ApiOperation(value = "[飞行事件接口] => 查询计划存在指定算法事件")
    @PostMapping("/stpf/event/query/planExistEvent")
    Result<List<FlightPlanExistEventDTO>> queryPlanExistEvent(@RequestBody FlightPlanExistEventQueryDTO queryDTO);
}
