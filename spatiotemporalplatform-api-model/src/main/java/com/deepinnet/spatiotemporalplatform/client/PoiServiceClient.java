package com.deepinnet.spatiotemporalplatform.client;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.StaticSurfaceObstacle;
import com.deepinnet.spatiotemporalplatform.dto.StaticSurfaceObstacleQueryDTO;
import com.deepinnet.spatiotemporalplatform.model.tour.openapi.PoiParam;
import com.deepinnet.spatiotemporalplatform.model.tour.openapi.PoiResponse;
import com.deepinnet.spatiotemporalplatform.model.tour.openapi.PolygonPoiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "poiServiceClient", url = "${stpf.service.url}")
public interface PoiServiceClient {
    /**
     * poi查询
     * @param poiParam poi查询参数
     * @return poi查询结果
     */
    @PostMapping("/stpf/poi/query")
    Result<PoiResponse> getPoi(@RequestBody PoiParam poiParam);

    /**
     * polygon poi查询
     * @param polygonPoiParam poi查询参数
     * @return poi查询结果
     */
    @PostMapping("/stpf/poi/polygon")
    Result<PoiResponse> getPolygonPoi(@RequestBody PolygonPoiParam polygonPoiParam);

    /**
     * 查询静态障碍物
     * @param dto
     * @return
     */
    @PostMapping("/static/obstacle/query")
    Result<List<StaticSurfaceObstacle>> queryStaticObstacle(@RequestBody StaticSurfaceObstacleQueryDTO dto);
}
