package com.deepinnet.spatiotemporalplatform.client.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 点位信息 Feign Client
 *
 * <AUTHOR>
 * @since 2025/3/13
 */
@FeignClient(name = "pointOfInterestClient", url = "${stpf.service.url}")
public interface PointOfInterestClient {

    /**
     * 查询点位信息
     *
     * @param dto 查询参数
     * @return 点位信息列表
     */
    @PostMapping("/poi/around")
    Result<List<Poi>> queryPoi(@RequestBody POIQueryDTO dto);

    /**
     * 查询多边形点位信息
     *
     * @param dto 查询参数
     * @return 多边形点位信息列表
     */
    @PostMapping("/poi/polygon")
    Result<List<Poi>> queryPolygonPoi(@RequestBody POIPolygonQueryDTO dto);

    /**
     * 查询静态障碍物
     *
     * @param dto 查询参数
     * @return 静态障碍物列表
     */
    @PostMapping("/poi/static/obstacle/query")
    Result<List<StaticSurfaceObstacle>> queryStaticObstacle(@RequestBody StaticSurfaceObstacleQueryDTO dto);
}