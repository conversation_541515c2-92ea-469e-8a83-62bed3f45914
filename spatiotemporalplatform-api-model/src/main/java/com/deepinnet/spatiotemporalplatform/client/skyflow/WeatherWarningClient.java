package com.deepinnet.spatiotemporalplatform.client.skyflow;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> wong
 * @create 2025/3/13 10:14
 * @Description
 */
@FeignClient(name = "weatherWarningClient", url = "${stpf.service.url}")
public interface WeatherWarningClient {

    @PostMapping("/stpf/weather/intelligence/page")
    Result<CommonPage<IntelligenceDTO>> pageQueryIntelligenceList(@RequestBody IntelligenceQueryDTO queryDTO);

}
