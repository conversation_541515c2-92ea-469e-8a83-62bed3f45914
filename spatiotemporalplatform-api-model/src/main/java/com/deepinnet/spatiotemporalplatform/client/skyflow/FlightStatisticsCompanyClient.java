package com.deepinnet.spatiotemporalplatform.client.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.FlightServiceCompanyStatisticsQueryDTO;
import com.deepinnet.spatiotemporalplatform.vo.FlightServiceCompanyRankingVO;
import com.deepinnet.spatiotemporalplatform.vo.FlightTaskNatureStatisticsDataVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * 飞行服务公司统计客户端
 *
 * <AUTHOR>
 * @create 2025/6/30
 */
@FeignClient(name = "flightStatisticsCompanyClient", url = "${stpf.service.url}")
public interface FlightStatisticsCompanyClient {

    /**
     * 获取飞行服务公司统计数据
     *
     * @param queryDTO 查询参数
     * @return 飞行服务公司排行榜数据
     */
    @PostMapping("/stpf/flight/statistics/company/query")
    @ApiOperation(value = "[飞行统计接口] => 获取飞行服务公司统计数据")
    Result<List<FlightServiceCompanyRankingVO>> getFlightServiceCompanyStatistics(@RequestBody @Valid FlightServiceCompanyStatisticsQueryDTO queryDTO);

    /**
     * 获取飞行任务性质统计信息
     * @return
     */
    @PostMapping("/stpf/flight/statistics/company/nature")
    @ApiOperation(value = "[飞行统计接口] => 获取飞行任务性质统计信息")
    Result<List<FlightTaskNatureStatisticsDataVO>> getFlightTaskNatureStatistics();

}