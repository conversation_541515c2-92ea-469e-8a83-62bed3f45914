package com.deepinnet.spatiotemporalplatform.client.tour;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.road.RoadLinkFlowUrlParams;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**

 *
 * <AUTHOR>
 * @since 2024-09-19 星期四
 **/
@FeignClient(name = "roadLinkFlowClient", url = "${stpf.service.url}")
public interface RoadLinkFlowClient {

    @PostMapping("/stpf/tour/road/link/flow")
    Result<String> roadLinkFlow(@RequestBody RoadLinkFlowUrlParams roadLinkFlowUrlParams);
}
