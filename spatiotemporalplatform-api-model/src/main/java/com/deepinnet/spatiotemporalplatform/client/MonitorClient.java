package com.deepinnet.spatiotemporalplatform.client;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.monitor.ErrorMonitorUrlParams;
import com.deepinnet.spatiotemporalplatform.model.monitor.HeartMonitorUrlParams;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**

 *
 * <AUTHOR>
 * @since 2024-12-06 星期五
 **/
@FeignClient(name = "monitorClient", url = "${stpf.service.url}")
public interface MonitorClient {

    @PostMapping("/stpf/monitor/reportHeartbeat")
    Result<Boolean> reportHeartBeat(@RequestBody HeartMonitorUrlParams params);

    @PostMapping("/stpf/monitor/reportError")
    Result<Boolean> reportError(@RequestBody ErrorMonitorUrlParams params);

}
