package com.deepinnet.spatiotemporalplatform.client.parkinglot;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.parkinglot.AreaParkingLotSummaryDTO;
import com.deepinnet.spatiotemporalplatform.model.parkinglot.ParkingLotDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "parking-lot-service", url = "${stpf.service.url}")
@Api(tags = "停车场 Feign 客户端")
public interface ParkingLotClient {

    @PostMapping("/domain/parking/lots/getParkingLotsByType")
    @ApiOperation(value = "根据区域编码和类型获取停车场列表")
    Result<AreaParkingLotSummaryDTO> getParkingLotsByType(
            @RequestParam("areaCode") String areaCode,
            @RequestParam(value = "type", required = false) String type);

    @PostMapping("/domain/parking/lots/create")
    @ApiOperation(value = "新增停车场")
    Result<ParkingLotDTO> addParkingLot(@RequestBody ParkingLotDTO parkingLotDTO);
}