package com.deepinnet.spatiotemporalplatform.client.skyflow;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2025/3/13 10:14
 * @Description
 */
@FeignClient(name = "flightDetectionClient", url = "${stpf.service.url}")
public interface FlightDetectionClient {

    /**
     * 分页查询飞行监测数据
     *
     * @param queryDTO 查询条件
     * @return 分页查询结果
     */
    @PostMapping("/stpf/flight/detection/page")
    Result<CommonPage<FlightDetectionDTO>> pageFlightDetection(@Valid @RequestBody FlightDetectionQueryDTO queryDTO);

    /**
     * 获取飞行数据完整路基
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/stpf/flight/detection/path")
    Result<List<FlightPathDTO>> getFlightDetectionPath(@RequestBody FlightPathQueryDTO queryDTO);

    /**
     * 获取飞行器最新位置
     *
     * @param
     * @return
     */
    @PostMapping("/stpf/flight/detection/position")
    Result<List<FlightDetectionLatestPathDTO>> getLatestFlightPosition(@RequestBody FlightDetectionLatestPositionDTO queryDTO);
}
