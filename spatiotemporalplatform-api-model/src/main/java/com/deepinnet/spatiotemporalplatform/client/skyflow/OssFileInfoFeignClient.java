package com.deepinnet.spatiotemporalplatform.client.skyflow;

import com.deepinnet.spatiotemporalplatform.model.skyflow.OssFileInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "oss-file-info-service", url = "${stpf.service.url}")
public interface OssFileInfoFeignClient {

    @PostMapping("/get-all")
    List<OssFileInfo> getAllOssFileInfos();

    @PostMapping("/get-by-id")
    OssFileInfo getOssFileInfoById(@RequestBody OssFileInfo ossFileInfo);

    @PostMapping("/add")
    OssFileInfo addOssFileInfo(@RequestBody OssFileInfo ossFileInfo);

    @PostMapping("/update")
    OssFileInfo updateOssFileInfo(@RequestBody OssFileInfo ossFileInfo);

    @PostMapping("/delete")
    void deleteOssFileInfo(@RequestBody OssFileInfo ossFileInfo);
}