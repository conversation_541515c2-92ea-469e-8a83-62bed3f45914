package com.deepinnet.spatiotemporalplatform.client.tour;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.tour.group.GroupFlowDetect;
import com.deepinnet.spatiotemporalplatform.model.tour.group.GroupFlowDetectUrlParams;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**

 *
 * <AUTHOR>
 * @since 2024-08-27 星期二
 **/
@FeignClient(name = "groupFlowDetectClient",url = "${stpf.service.url}")
public interface GroupFlowDetectClient {

    @PostMapping("/stpf/tour/group/flow/detect")
    Result<GroupFlowDetect> detect(@RequestBody GroupFlowDetectUrlParams groupFlowDetectUrlParams);


}
