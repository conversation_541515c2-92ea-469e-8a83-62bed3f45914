package com.deepinnet.spatiotemporalplatform.client.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.DetectedDeviceQueryDTO;
import com.deepinnet.spatiotemporalplatform.vo.DetectedDeviceVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * 侦测设备服务客户端
 *
 * <AUTHOR>
 * @create 2025/06/30
 */
@FeignClient(name = "detectedDeviceClient", url = "${stpf.service.url}")
public interface DetectedDeviceClient {

    /**
     * 查询所有侦测设备列表
     *
     * @return 侦测设备列表
     */
    @PostMapping("/stpf/detected/device/list")
    @ApiOperation(value = "[侦测设备管理接口] => 查询所有侦测设备列表")
    Result<List<DetectedDeviceVO>> listAllDetectedDevices();
} 