package com.deepinnet.spatiotemporalplatform.client.skyflow;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.AerodromeQueryDTO;
import com.deepinnet.spatiotemporalplatform.model.skyflow.LandingPoint;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025-03-20
 */
@FeignClient(name = "landingPointFeignClient", url = "${stpf.service.url}")
public interface LandingPointFeignClient {

    /**
     * 分页查询起降点
     * @param queryDTO
     * @return
     */
    @PostMapping("/stpf/skyflow/landing/points/page")
    Result<CommonPage<LandingPoint>> pageLandingPoint(@RequestBody AerodromeQueryDTO queryDTO);

    /**
     * 查询所有起降点
     */
    @GetMapping("/stpf/skyflow/landing/points")
    Result<List<LandingPoint>> queryAllLandingPoints();
}
