package com.deepinnet.spatiotemporalplatform.client.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.NavigateEquipmentQueryDTO;
import com.deepinnet.spatiotemporalplatform.vo.NavigateEquipmentVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * 导航设备服务客户端
 *
 * <AUTHOR>
 * @create 2025/06/30
 */
@FeignClient(name = "navigateEquipmentClient", url = "${stpf.service.url}")
public interface NavigateEquipmentClient {

    /**
     * 查询所有导航设备列表
     *
     * @return 导航设备列表
     */
    @PostMapping("/stpf/navigate/equipment/list")
    @ApiOperation(value = "[导航设备管理接口] => 查询所有导航设备列表")
    Result<List<NavigateEquipmentVO>> listAllNavigateEquipments();
} 