package com.deepinnet.spatiotemporalplatform.client;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.road.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**

 *
 * <AUTHOR>
 * @since 2024-09-02 星期一
 **/
@FeignClient(name = "roadClient", url = "${stpf.service.url}")
public interface RoadClient {

    @PostMapping("/stpf/road/realRoadIndex")
    Result<List<RealRoadIndex>> realRoadIndex(@RequestBody RealRoadIndexUrlParams realRoadIndexUrlParams);

    @PostMapping("/stpf/road/historyRanking")
    Result<List<RoadHistoryRanking>> historyRanking(@RequestBody RoadHistoryRankingUrlParams roadHistoryRankingUrlParams);

    @PostMapping("/stpf/road/search")
    Result<List<RoadSearch>> search(@RequestBody RoadSearchUrlParams roadSearchUrlParams);

    @PostMapping("/stpf/road/shape")
    Result<List<RoadShape>> shape(@RequestBody RoadShapeUrlParams roadShapeUrlParams);
}
