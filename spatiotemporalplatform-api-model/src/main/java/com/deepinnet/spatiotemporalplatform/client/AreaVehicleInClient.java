package com.deepinnet.spatiotemporalplatform.client;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.vehicle.AreaVehicleInLink;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.vehicle.AreaVehicleInUrlParams;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**

 *
 * <AUTHOR>
 * @since 2024-08-03 星期六
 **/
@FeignClient(name = "areaVehicleInClient", url = "${stpf.service.url}")
public interface AreaVehicleInClient {

    @PostMapping("/stpf/area/channel/inflow/heat")
    Result<List<AreaVehicleInLink>> inflowHeat(@RequestBody AreaVehicleInUrlParams areaVehicleInUrlParams);


}
