package com.deepinnet.spatiotemporalplatform.client.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.FlightStatisticsOverviewQueryDTO;
import com.deepinnet.spatiotemporalplatform.vo.FlightStatisticsOverviewVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 飞行统计服务客户端
 *
 * <AUTHOR> wong
 * @create 2025/06/30
 */
@FeignClient(name = "flightStatisticsClient", url = "${stpf.service.url}")
public interface FlightStatisticsClient {

    /**
     * 获取飞行统计概览数据
     *
     * @param queryDTO 查询参数
     * @return 飞行统计概览数据
     */
    @PostMapping("/stpf/flight/statistics/overview")
    @ApiOperation(value = "[飞行统计接口] => 获取飞行统计概览数据")
    Result<FlightStatisticsOverviewVO> getFlightStatisticsOverview(@RequestBody FlightStatisticsOverviewQueryDTO queryDTO);
} 