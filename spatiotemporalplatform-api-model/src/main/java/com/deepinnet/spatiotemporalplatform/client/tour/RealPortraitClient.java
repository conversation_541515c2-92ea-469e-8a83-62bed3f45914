package com.deepinnet.spatiotemporalplatform.client.tour;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.tour.portrait.RealPortrait;
import com.deepinnet.spatiotemporalplatform.model.tour.portrait.RealPortraitUrlParams;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**

 *
 * <AUTHOR>
 * @since 2024-08-27 星期二
 **/
@FeignClient(name = "realPortraitClient", url = "${stpf.service.url}")
public interface RealPortraitClient {

    @PostMapping("/stpf/tour/real/portrait")
    Result<RealPortrait> realPortrait(@RequestBody RealPortraitUrlParams realPortraitUrlParams);
}
