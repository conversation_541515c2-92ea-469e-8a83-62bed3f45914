package com.deepinnet.spatiotemporalplatform.client.tour;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.tour.heat.RealHeat;
import com.deepinnet.spatiotemporalplatform.model.tour.heat.RealHeatUrlParams;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**

 *
 * <AUTHOR>
 * @since 2024-08-27 星期二
 **/
@FeignClient(name = "realHeatClient", url = "${stpf.service.url}")
public interface RealHeatClient {

    @PostMapping("/stpf/tour/real/heat")
    Result<RealHeat> heat(@RequestBody RealHeatUrlParams realHeatUrlParams);
}
