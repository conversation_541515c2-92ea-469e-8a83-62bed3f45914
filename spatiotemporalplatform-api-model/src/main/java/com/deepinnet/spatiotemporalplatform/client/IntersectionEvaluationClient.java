package com.deepinnet.spatiotemporalplatform.client;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis.RealTimeIntersectionEvaluationData;
import com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis.RealTimeIntersectionEvaluationUrlParams;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**

 *
 * <AUTHOR>
 * @since 2024-08-03 星期六
 **/
@FeignClient(name = "intersectionEvaluationClient", url = "${stpf.service.url}")
public interface IntersectionEvaluationClient {

    @PostMapping("/stpf/area/intersection/realtime/evaluation")
    Result<RealTimeIntersectionEvaluationData> realtimeEvaluation(@RequestBody RealTimeIntersectionEvaluationUrlParams realTimeIntersectionEvaluationUrlParams);

}
