package com.deepinnet.spatiotemporalplatform.client.skyflow;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.staticpoi.StaticPoi;
import com.deepinnet.spatiotemporalplatform.model.staticpoi.StaticPoiQueryDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * 静态POI客户端
 *
 * <AUTHOR>
 * @since 2025/3/12
 */
@FeignClient(name = "staticPoiClient", url = "${stpf.service.url}")
public interface StaticPoiClient {

    /**
     * 分页查询静态POI
     *
     * @param queryDTO 查询条件
     * @return 分页查询结果
     */
    @PostMapping("/stpf/poi/static/page")
    Result<CommonPage<StaticPoi>> pageQuery(@RequestBody StaticPoiQueryDTO queryDTO);

    /**
     * 查询静态POI详情
     *
     * @param poiId poiId
     * @return 飞行计划详情
     */
    @GetMapping("/stpf/poi/static/detail")
    Result<StaticPoi> queryPoiDetail(@RequestParam("poiId") String poiId);

}
