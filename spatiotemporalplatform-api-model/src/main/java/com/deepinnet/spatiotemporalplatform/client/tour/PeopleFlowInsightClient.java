package com.deepinnet.spatiotemporalplatform.client.tour;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.tour.insight.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-08-27 星期二
 **/
@FeignClient(name = "peopleFlowInsightClient", url = "${stpf.service.url}")
public interface PeopleFlowInsightClient {


    @PostMapping("/stpf/tour/people/flow/insight/realtime")
    Result<List<PeopleFlowInsightDTO>> getRealTimePeopleFlowInsight(@RequestBody PeopleFlowInsightUrlParams urlParams);

}
