package com.deepinnet.spatiotemporalplatform.client.camera;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.localdata.integration.model.input.MonitorPlaybackQueryDTO;
import com.deepinnet.localdata.integration.model.input.MonitorRecordQueryDTO;
import com.deepinnet.localdata.integration.model.input.RealMonitorQueryDTO;
import com.deepinnet.localdata.integration.model.output.MonitorRecordResponseDTO;
import com.deepinnet.spatiotemporalplatform.model.camera.KeyMonitorQueryDTO;
import com.deepinnet.spatiotemporalplatform.model.camera.MarkKeyMonitorDTO;
import com.deepinnet.spatiotemporalplatform.model.camera.SurveillanceCamera;
import com.deepinnet.spatiotemporalplatform.model.camera.SurveillanceCameraCondition;

@FeignClient(name = "surveillance-camera-service", url = "${stpf.service.url}")
public interface SurveillanceCameraFeignClient {

    @PostMapping("/domain/camera/page")
    Result<CommonPage<SurveillanceCamera>> listPage(@RequestBody SurveillanceCameraCondition condition);

    @PostMapping("/domain/camera/{code}")
    Result<SurveillanceCamera> getByCodeAndType(@PathVariable("code") String code, @RequestParam("type") String type);

    @PostMapping("/domain/camera/area")
    Result<List<SurveillanceCamera>> listByAreaCodeAndType(@RequestParam("areaCode") String areaCode, @RequestParam("type") String type);

    @PostMapping("/domain/camera/area/near")
    Result<List<SurveillanceCamera>> queryAreaNearCamera(@RequestBody SurveillanceCameraCondition condition);

    @PostMapping("/domain/camera/mark-key-monitor")
    Result<Boolean> markKeyMonitor(@RequestBody MarkKeyMonitorDTO markKeyMonitorDTO);

    @PostMapping("/domain/camera/key-monitor")
    Result<List<SurveillanceCamera>> getKeyMonitorInfo(@RequestBody KeyMonitorQueryDTO keyMonitorQueryDTO);

    @PutMapping("/domain/camera/key-monitor")
    Result<Boolean> updateKeyMonitor(@RequestBody MarkKeyMonitorDTO markKeyMonitorDTO);

    @DeleteMapping("/domain/camera/key-monitor")
    Result<Boolean> deleteKeyMonitor(@RequestBody MarkKeyMonitorDTO markKeyMonitorDTO);

    @PostMapping("/domain/camera/real-monitor-url")
    Result<String> getRealMonitorUrlByCode(@RequestBody RealMonitorQueryDTO queryDTO);

    @PostMapping("/domain/camera/playback-url")
    Result<String> getPlaybackUrl(@RequestBody MonitorPlaybackQueryDTO queryDTO);

    @PostMapping("/domain/camera/record")
    Result<MonitorRecordResponseDTO> getRecord(@RequestBody MonitorRecordQueryDTO queryDTO);

    @PostMapping("/domain/camera/import")
    Result<Boolean> importCamera(@RequestParam("file") MultipartFile file);

    @PostMapping("/domain/camera/save-batch")
    Result<Boolean> saveBatch(@RequestBody List<SurveillanceCamera> cameraList);
}