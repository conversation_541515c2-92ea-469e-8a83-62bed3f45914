package com.deepinnet.spatiotemporalplatform.client.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.skyflow.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR> wong
 * @create 2025/3/11 09:56
 * @Description
 */
@FeignClient(name = "gdFlightDetectionClient", url = "${stpf.service.url}")
public interface GdFlightDetectionClient {

    @PostMapping("/stpf/gd/flight/detection")
    Result<GdFlightDetectionResponse> getGdFlightDetection(@RequestBody GdFlightDetectionParam param);

}
