package com.deepinnet.spatiotemporalplatform.client.intercom;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.intercom.IntercomGeomWkt;
import com.deepinnet.spatiotemporalplatform.model.intercom.IntercomPointRecord;

/**
 * 对讲机服务客户端
 */
@FeignClient(name = "intercomClient",path = "",url = "${stpf.service.url}")
public interface IntercomClient {

    @PostMapping("/domain/intercom/points")
    Result<List<IntercomPointRecord>> getIntercomPointList(
            @RequestParam("areaCode") String areaCode,
            @RequestParam("interval") Integer interval);

    @PostMapping("/domain/intercom/points/nearby")
    Result<List<IntercomPointRecord>> queryNearIntercomPoint(
            @RequestBody IntercomGeomWkt pointWkt,
            @RequestParam("distance") Double distance);

    @PostMapping("/domain/intercom/points/polygon")
    Result<List<IntercomPointRecord>> queryPolygonContainsWithIn5Min(
            @RequestBody IntercomGeomWkt polygonWkt);
}