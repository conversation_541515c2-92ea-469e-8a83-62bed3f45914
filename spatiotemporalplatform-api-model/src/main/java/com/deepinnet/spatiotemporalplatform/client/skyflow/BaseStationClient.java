package com.deepinnet.spatiotemporalplatform.client.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.BaseStationQueryDTO;
import com.deepinnet.spatiotemporalplatform.vo.BaseStationVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * 基站服务客户端
 *
 * <AUTHOR>
 * @create 2025/06/30
 */
@FeignClient(name = "baseStationClient", url = "${stpf.service.url}")
public interface BaseStationClient {

    /**
     * 查询所有基站列表
     *
     * @return 基站列表
     */
    @PostMapping("/stpf/base/station/list")
    @ApiOperation(value = "[基站管理接口] => 查询所有基站列表")
    Result<List<BaseStationVO>> listAllBaseStations();
} 