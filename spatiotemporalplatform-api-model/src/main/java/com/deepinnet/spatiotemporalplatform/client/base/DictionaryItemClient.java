package com.deepinnet.spatiotemporalplatform.client.base;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.*;
import com.deepinnet.spatiotemporalplatform.vo.DictionaryItemVO;
import com.deepinnet.spatiotemporalplatform.vo.DictionaryTreeVO;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2024-08-03 星期六
 **/
@FeignClient(name = "dictionaryItemClient", url = "${stpf.service.url}")
public interface DictionaryItemClient {

    @PostMapping("/dictionary/item/create")
    @ApiOperation("创建字典项")
    Result<Long> createDictionaryItem(@RequestBody @Valid DictionaryItemDTO dictionaryItemDTO);

    @PostMapping("/dictionary/item/batch-create")
    @ApiOperation("批量创建字典项")
    Result<Boolean> batchCreateDictionaryItems(@RequestBody @Valid List<DictionaryItemDTO> dictionaryItemDTOList);

    @PostMapping("/dictionary/item/update")
    @ApiOperation("更新字典项")
    Result<Boolean> updateDictionaryItem(@RequestBody @Valid DictionaryItemDTO dictionaryItemDTO);

    @PostMapping("/dictionary/item/get")
    @ApiOperation("根据ID获取字典项")
    Result<DictionaryItemVO> getDictionaryItemById(@RequestBody @Valid @NotNull(message = "ID不能为空")
                                                          @ApiParam(value = "字典项ID DTO", required = true) IdDTO idDTO);

    @GetMapping("/dictionary/item/getByCode")
    @ApiOperation("根据字典ID和编码获取字典项")
    Result<DictionaryItemVO> getDictionaryItemByCode(
            @RequestParam("dictionaryId") @Valid @NotNull(message = "字典ID不能为空")
            @ApiParam(value = "字典ID", required = true) Long dictionaryId,
            @RequestParam("code") @Valid @NotNull(message = "字典项编码不能为空")
            @ApiParam(value = "字典项编码", required = true) String code);

    @GetMapping("/dictionary/item/listByDictionaryId")
    @ApiOperation("根据字典ID获取字典项列表")
    Result<List<DictionaryItemVO>> listDictionaryItemsByDictionaryId(
            @RequestParam("dictionaryId") @Valid @NotNull(message = "字典ID不能为空")
            @ApiParam(value = "字典ID", required = true) Long dictionaryId);

    @PostMapping("/dictionary/item/page")
    @ApiOperation("分页查询字典项")
    Result<CommonPage<DictionaryItemVO>> pageQueryDictionaryItems(@RequestBody @Valid DictionaryItemQueryDTO queryDTO);

    @PostMapping("/dictionary/item/delete")
    @ApiOperation("删除字典项")
    Result<Boolean> deleteDictionaryItem(@RequestBody @Valid @NotNull(message = "ID不能为空")
                                                @ApiParam(value = "字典项ID DTO", required = true) IdDTO idDTO);

    @GetMapping("/dictionary/item/tree")
    @ApiOperation("根据字典类型和租户ID获取字典树")
    Result<List<DictionaryTreeVO>> getDictionaryTreeByTypeAndTenantId(
            @RequestParam("type") @Valid @NotBlank(message = "字典类型不能为空")
            @ApiParam(value = "字典类型", required = true) String type,
            @RequestParam("tenantId") @Valid @NotBlank(message = "租户ID不能为空")
            @ApiParam(value = "租户ID", required = true) String tenantId);

    @GetMapping("/dictionary/item/listByTypeAndParentCode")
    @ApiOperation("根据租户ID、字典类型和父编码查询字典项列表")
    Result<List<DictionaryItemVO>> listDictionaryItemsByTypeAndParentCode(
            @RequestParam("type") @Valid @NotBlank(message = "字典类型不能为空")
            @ApiParam(value = "字典类型", required = true) String type,
            @RequestParam(required = false, value = "parentCode")
            @ApiParam(value = "父编码", required = true) String parentCode,
            @RequestParam("tenantId") @Valid @NotBlank(message = "租户ID不能为空")
            @ApiParam(value = "租户ID", required = true) String tenantId);

    @PostMapping("/dictionary/item/batch-operation")
    @ApiOperation("批量操作字典项（删除和新增）")
    Result<Boolean> batchOperateDictionaryItems(@RequestBody @Valid DictionaryItemBatchOperationDTO batchOperationDTO);
}
