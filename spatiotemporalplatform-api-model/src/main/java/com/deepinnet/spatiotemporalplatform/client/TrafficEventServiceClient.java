package com.deepinnet.spatiotemporalplatform.client;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.traffic.event.TrafficEvent;
import com.deepinnet.spatiotemporalplatform.model.traffic.event.TrafficEventUrlParams;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 交通事件接口客户端
 *
 * <AUTHOR>
 * @version 2024-07-31
 */
@FeignClient(name = "trafficEventServiceClient", url = "${stpf.service.url}")
public interface TrafficEventServiceClient {
    /**
     * 查询交通事件
     *
     * @param trafficEventUrlParams
     * @return
     */
    @PostMapping("/stpf/traffic/event/realtime")
    Result<List<TrafficEvent>> getTrafficEvents(@RequestBody TrafficEventUrlParams trafficEventUrlParams);

}
