package com.deepinnet.spatiotemporalplatform.client;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.reggov.create.CustomAreaCreatePostBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 自定义区域客户端
 *
 * <AUTHOR>
 * @version 2024-07-31
 */
@FeignClient(name = "customAreaServiceClient", url = "${stpf.service.url}")
public interface CustomAreaServiceClient {

    /**
     * @param customAreaCreatePostBody
     * @return
     */
    @PostMapping("/stpf/area/custom/create")
    Result<String> createCustomArea(@RequestBody CustomAreaCreatePostBody customAreaCreatePostBody);
}
