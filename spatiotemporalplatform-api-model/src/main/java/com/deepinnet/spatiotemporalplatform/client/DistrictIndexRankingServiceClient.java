package com.deepinnet.spatiotemporalplatform.client;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis.HistoryDistrictIndexRanking;
import com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis.HistoryDistrictIndexRankingUrlParams;
import com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis.RealTimeDistrictIndexRanking;
import com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis.RealTimeDistrictIndexRankingUrlParams;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2024-07-31
 */
@FeignClient(name = "districtIndexRankingServiceClient", url = "${stpf.service.url}")
public interface DistrictIndexRankingServiceClient {
    @PostMapping("/stpf/traffic/diagnosis/realtime")
    Result<List<RealTimeDistrictIndexRanking>> getRealTimeDistrictIndexRanking(@RequestBody RealTimeDistrictIndexRankingUrlParams districtIndexRankingUrlParams);

    @PostMapping("/stpf/traffic/diagnosis/history")
    Result<List<HistoryDistrictIndexRanking>> getHistoryDistrictIndexRanking(@RequestBody HistoryDistrictIndexRankingUrlParams districtIndexRankingUrlParams);
}
