package com.deepinnet.spatiotemporalplatform.client.skyflow;

import javax.validation.Valid;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.RadarPageQueryDTO;
import com.deepinnet.spatiotemporalplatform.model.skyflow.Radar;

/**
 * 雷达服务Feign客户端
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@FeignClient(name = "radarFeignClient", url = "${stpf.service.url}")
public interface RadarFeignClient {

    /**
     * 创建雷达
     *
     * @param radar 雷达信息
     * @return 雷达编码
     */
    @PostMapping("/stpf/skyflow/radar/create")
    Result<String> createRadar(@Valid @RequestBody Radar radar);

    /**
     * 分页查询雷达
     *
     * @param queryDTO 查询条件
     * @return 分页查询结果
     */
    @PostMapping("/stpf/skyflow/radar/page")
    Result<CommonPage<Radar>> pageQueryRadar(@Valid @RequestBody RadarPageQueryDTO queryDTO);

    /**
     * 根据编码查询雷达详情
     *
     * @param code 雷达编码
     * @return 雷达详情
     */
    @GetMapping("/stpf/skyflow/radar/detail")
    Result<Radar> getRadarByCode(@RequestParam("code") String code);
} 