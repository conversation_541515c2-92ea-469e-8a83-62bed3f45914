package com.deepinnet.spatiotemporalplatform.client.skyflow;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.spatiotemporalplatform.dto.PoiNameMappingPageQueryDTO;
import com.deepinnet.spatiotemporalplatform.model.skyflow.PoiNameMapping;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * PoiNameFeignClient
 * Author: chenkaiyang
 * Date: 2025/3/21
 */
@FeignClient(name = "poiNameFeignClient", url = "${stpf.service.url}")
public interface PoiNameFeignClient {

    @PostMapping("/api/skyflow/poiName/pageQuery")
    CommonPage<PoiNameMapping> pageQueryPoiNameMapping(@RequestBody PoiNameMappingPageQueryDTO queryDTO);
}