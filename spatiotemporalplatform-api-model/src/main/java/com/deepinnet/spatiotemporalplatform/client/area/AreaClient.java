package com.deepinnet.spatiotemporalplatform.client.area;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.area.AreaDTO;
import com.deepinnet.spatiotemporalplatform.model.area.AreaCondition;
import com.deepinnet.spatiotemporalplatform.model.area.AreaQueryDTO;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * Feign client for AreaAdmin service
 */
@FeignClient(name = "areaClient", url = "${stpf.service.url}")
public interface AreaClient {

    @Headers("X-Auth-Mode: bypass")
    @PostMapping("/domain/area/page")
    Result<CommonPage<AreaDTO>> pageArea(@RequestBody AreaCondition condition);

    @Headers("X-Auth-Mode: bypass")
    @PostMapping("/domain/area/get")
    Result<AreaDTO> getAreaByCode(@RequestBody AreaQueryDTO queryDTO);

    @Headers("X-Auth-Mode: bypass")
    @PostMapping("/domain/area/create")
    Result<AreaDTO> createArea(@RequestBody AreaDTO area);

    @Headers("X-Auth-Mode: bypass")
    @PostMapping("/domain/area/update")
    Result<AreaDTO> updateArea(@RequestBody AreaDTO area);

    @Headers("X-Auth-Mode: bypass")
    @PostMapping("/domain/area/delete")
    Result<Boolean> deleteArea(@RequestBody AreaDTO areaDTO);

    @Headers("X-Auth-Mode: bypass")
    @PostMapping("/domain/area/getAreaByNameList")
    Result<List<AreaDTO>> getAreaByNameList(@RequestBody List<String> nameList);
}