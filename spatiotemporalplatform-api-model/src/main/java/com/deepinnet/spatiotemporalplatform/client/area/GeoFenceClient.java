package com.deepinnet.spatiotemporalplatform.client.area;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.area.StreetPolygonQueryParams;
import com.deepinnet.spatiotemporalplatform.model.area.StreetPolygonRespDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Description:
 * Date: 2025/4/30
 * Author: lijunheng
 */
@FeignClient(name = "GeoFenceClient", url = "${stpf.service.url}")
public interface GeoFenceClient {

    @PostMapping("/stpf/fence/street")
    Result<CommonPage<StreetPolygonRespDTO>> getStreetPolygon(@RequestBody StreetPolygonQueryParams streetPolygonQueryParams);
}
