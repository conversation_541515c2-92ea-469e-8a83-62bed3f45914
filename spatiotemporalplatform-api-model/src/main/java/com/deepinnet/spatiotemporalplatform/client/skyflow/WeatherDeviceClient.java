package com.deepinnet.spatiotemporalplatform.client.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.vo.WeatherDeviceVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * 天气设备服务客户端
 *
 * <AUTHOR> wong
 * @create 2025/01/14
 */
@FeignClient(name = "weatherDeviceClient", url = "${stpf.service.url}")
public interface WeatherDeviceClient {

    /**
     * 查询所有天气设备
     *
     * @return 天气设备列表
     */
    @GetMapping("/stpf/weather/device/list")
    @ApiOperation(value = "[天气设备接口] => 查询所有天气设备")
    Result<List<WeatherDeviceVO>> listAllWeatherDevices();

    /**
     * 统计当前租户的天气设备总数
     *
     * @return 天气设备总数
     */
    @PostMapping("/stpf/weather/device/count")
    @ApiOperation(value = "[天气设备接口] => 统计当前租户天气设备总数")
    Result<Long> countWeatherDevices();
} 