package com.deepinnet.spatiotemporalplatform.client.tour;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.tour.flow.PassengerFlow;
import com.deepinnet.spatiotemporalplatform.model.tour.flow.PassengerFlowUrlParams;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**

 *
 * <AUTHOR>
 * @since 2024-08-27 星期二
 **/
@FeignClient(name = "passengerFlowClient", url = "${stpf.service.url}")
public interface PassengerFlowClient {

    @PostMapping("/stpf/tour/passenger/flow")
    Result<PassengerFlow> flow(@RequestBody PassengerFlowUrlParams passengerFlowUrlParams);

}
