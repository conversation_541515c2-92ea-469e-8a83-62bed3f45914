package com.deepinnet.spatiotemporalplatform.client.area;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.area.CircleLayerBatchDTO;
import com.deepinnet.spatiotemporalplatform.model.area.CircleLayerDTO;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Feign client for CircleLayerAdmin service
 */
@FeignClient(name = "circleLayerAdminFeignClient", url = "${stpf.service.url}")
public interface CircleLayerClient {

    @Headers("X-Auth-Mode: bypass")
    @PostMapping("/domain/circle-layer/all")
    Result<List<CircleLayerDTO>> getAllCircleLayers(
            @RequestParam("page") int page,
            @RequestParam("size") int size
    );

    @Headers("X-Auth-Mode: bypass")
    @PostMapping("/domain/circle-layer/detail")
    Result<CircleLayerDTO> getCircleLayerByLayerId(
            @RequestParam("layerId") String layerId
    );

    @Headers("X-Auth-Mode: bypass")
    @PostMapping("/domain/circle-layer/batchCreate")
    Result<List<CircleLayerDTO>> batchCreateOrUpdateCircleLayer(
            @RequestBody CircleLayerBatchDTO circleLayerBatchDTO
    );

    @Headers("X-Auth-Mode: bypass")
    @PostMapping("/domain/circle-layer/update")
    Result<CircleLayerDTO> updateCircleLayer(
            @RequestBody CircleLayerDTO circleLayerDTO
    );

    @Headers("X-Auth-Mode: bypass")
    @DeleteMapping("/domain/circle-layer/delete")
    Result<Boolean> deleteCircleLayer(
            @RequestParam("layerId") String layerId
    );
}