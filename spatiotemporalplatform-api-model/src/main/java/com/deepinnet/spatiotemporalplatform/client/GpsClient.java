package com.deepinnet.spatiotemporalplatform.client;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.gps.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * gps接口客户端
 *
 * <AUTHOR>
 * @version 2024-11-22
 */
@FeignClient(name = "gpsClient", url = "${stpf.service.url}")
public interface GpsClient {

    @GetMapping("/stpf/gps/queryFullData")
    Result<List<RemoteGps>> queryFullData();
}
