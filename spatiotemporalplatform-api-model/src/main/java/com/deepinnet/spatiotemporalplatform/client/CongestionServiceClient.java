package com.deepinnet.spatiotemporalplatform.client;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.congestion.CongestionData;
import com.deepinnet.spatiotemporalplatform.model.congestion.HistoryCongestionPostBody;
import com.deepinnet.spatiotemporalplatform.model.congestion.RealTimeCongestionPostBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 拥堵预警接口客户端
 *
 * <AUTHOR>
 * @version 2024-07-31
 */
@FeignClient(name = "congestionServiceClient", url = "${stpf.service.url}")
public interface CongestionServiceClient {
    @PostMapping("/stpf/congestion/realtime")
    Result<CongestionData> getRealTimeCongestion(@RequestBody RealTimeCongestionPostBody congestionPostBody);

    @PostMapping("/stpf/congestion/history")
    Result<CongestionData> getHistoryCongestion(@RequestBody HistoryCongestionPostBody congestionPostBody);

}
