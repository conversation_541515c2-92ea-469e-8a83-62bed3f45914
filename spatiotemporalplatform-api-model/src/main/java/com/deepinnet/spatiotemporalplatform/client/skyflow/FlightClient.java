package com.deepinnet.spatiotemporalplatform.client.skyflow;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.*;
import com.deepinnet.spatiotemporalplatform.model.skyflow.Airspace;
import com.deepinnet.spatiotemporalplatform.vo.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * 飞行服务客户端
 * <AUTHOR>
 * @since  2025/3/12
 */
@FeignClient(name = "flightClient", url = "${stpf.service.url}")
public interface FlightClient {

    /**
     * 分页查询飞行计划
     *
     * @param queryDTO 查询条件
     * @return 分页查询结果
     */
    @PostMapping("/stpf/flight/page/plan")
    Result<CommonPage<FlightPlanVO>> pageQueryPlan(@RequestBody FlightQueryDTO queryDTO);

    /**
     * 查询飞行计划详情
     *
     * @param planId 计划id
     * @return 飞行计划详情
     */
    @GetMapping("/stpf/flight/plan/detail")
    Result<FlightPlanVO> queryPlanDetail(@RequestParam("planId") String planId);


    /**
     * 查询飞行计划架次列表
     * @param planId
     * @return
     */
    @GetMapping("/stpf/flight/plan/flight/list")
    Result<List<RealTimeUavFlightVO>> queryPlanFlight(@RequestParam("planId") String planId);

    /**
     * 查询飞行计划架次详情
     * @param planId
     * @param flightId
     * @return
     */
    @GetMapping("/stpf/flight/plan/flight/detail")
    Result<FlightPlanVO> queryPlanFlightDetail(@RequestParam("planId") String planId, @RequestParam("flightId") String flightId);

    /**
     * 查询飞行计划航道
     *
     * @param planId 计划id
     * @return 查询飞行计划航道
     */
    @GetMapping("/stpf/flight/plan/airway")
    Result<Airspace> queryPlanAirway(@RequestParam("planId") String planId);

    /**
     * 今日飞行概览
     *
     * @return 今日飞行概览
     */
    @GetMapping("/stpf/flight/overview")
    Result<FlightTodayOverviewVO> overview();

    /**
     * 今日概览趋势图
     *
     * @param type 趋势类型
     * @return 趋势图数据
     */
    @GetMapping("/stpf/flight/overview/trend")
    Result<List> queryTrend(@RequestParam("type") String type);

    @GetMapping("/stpf/flight/overview/height/trend")
    Result<List> queryHeightTrend(@RequestParam(value = "startTime", required = false) Long startTime, @RequestParam(value = "endTime", required = false) Long endTime);

    @PostMapping("/stpf/flight/realtime/uav/position")
    Result<List<RealTimeUavFlightVO>> getUavPosition(@RequestBody PositionQueryDTO queryDTO);

    @PostMapping("/stpf/flight/realtime/uav/trajectory")
    Result<List<UavFlightTrackVO>> getUavTrajectory(@RequestBody PositionQueryDTO queryDTO);

    @GetMapping("/stpf/flight/realtime/uav/live")
    Result<String> getUavLiveUrl(@RequestParam("sn") String sn, @RequestParam(value = "httpProtocol", defaultValue = "http") String httpProtocol);

    @PostMapping("/stpf/flight/enterprise/statistics")
    Result<List<FlightDailyStatisticsVO>> flightDailyStatistics(@RequestBody FlightDailyStatisticsQueryDTO queryDTO);

    @PostMapping("/stpf/flight/statistics/overview")
    Result<FlightStatisticsOverviewVO> flightStatisticsOverview(@RequestBody FlightStatisticsOverviewQueryDTO queryDTO);

    @PostMapping("/stpf/flight/planCountByBizNo")
    Result<List<FlightCountVO>> planCountByBizNo(@RequestBody BatchQueryPlanDTO queryDTO);

    @PostMapping("/stpf/flight/planListByBizNo")
    Result<List<FlightPlanVO>> planListByBizNo(@RequestBody BatchQueryPlanDTO queryDTO);

    @PostMapping("/stpf/flight/aerodromeList")
    Result<List<AerodromeVO>> queryAerodromeList(@RequestBody BatchQueryAerodromeDTO queryAerodromeDTO);

    @PostMapping("/stpf/flight/update/report")
    Result<Boolean> updateFlightReport(@RequestBody FlightReportDTO dto);

    @PostMapping("/stpf/flight/demand/plan/stat")
    Result<List<FlightDemandPlanStatDTO>> queryFlightDemandPlanStat(@RequestBody FlightDemandPlanStatQueryDTO queryDTO);

    @ApiOperation("提供根据计划id返回算法视频流地址")
    @GetMapping("/stpf/flight/algorithm/monitor/url")
    Result<String> queryAlgorithmMonitorUrl(@RequestParam("planId") String planId);
}
