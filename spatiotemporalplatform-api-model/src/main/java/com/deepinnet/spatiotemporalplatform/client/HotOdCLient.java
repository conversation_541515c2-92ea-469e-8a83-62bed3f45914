package com.deepinnet.spatiotemporalplatform.client;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.hotod.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**

 *
 * <AUTHOR>
 * @since 2024-11-22 星期五
 **/
@FeignClient(name = "hotOdClient", url = "${stpf.service.url}")
public interface HotOdCLient {

    /**
     * 10.1 区域到达车辆数预测
     */
    @PostMapping("/stpf/hotod/areaArriveCarPredict")
    Result<List<AreaArriveCarPredict>> areaArriveCarPredict(@RequestBody AreaArriveCarPredictUrlParams params);

    /**
     * 10.2 热门驾车⽬的地排⾏
     */
    @PostMapping("/stpf/hotod/driverDestinationRanking")
    Result<DriverDestinationRanking> driverDestinationRanking(@RequestBody DriverDestinationRankingUrlParams params);

    /**
     * 10.3 区域来源地/⽬的地明细
     */
    @PostMapping("/stpf/hotod/sourceDestinationDetail")
    Result<SourceDestinationDetail> sourceDestinationDetail(@RequestBody SourceDestinationDetailUrlParams params);

}
