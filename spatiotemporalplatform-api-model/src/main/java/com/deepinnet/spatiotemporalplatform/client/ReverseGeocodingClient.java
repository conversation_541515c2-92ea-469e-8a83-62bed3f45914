package com.deepinnet.spatiotemporalplatform.client;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.tour.openapi.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 拥堵预警接口客户端
 *
 * <AUTHOR>
 * @version 2024-07-31
 */
@FeignClient(name = "reverseGeocodingClient", url = "${stpf.service.url}")
public interface ReverseGeocodingClient {
    @PostMapping("/stpf/openapi/reverse/geocoding")
    Result<ReverseGeocoding> getReverseGeocoding(@RequestBody ReverseGeocodingParam param);

    @PostMapping("/stpf/openapi/geocode/geocoding")
    Result<Geocoding> getGeocoding(@RequestBody GeocodingParam param);

    @PostMapping("/stpf/openapi/assistant/inputtips")
    Result<InputTips> getTips(@RequestBody InputTipsParam param);
}
