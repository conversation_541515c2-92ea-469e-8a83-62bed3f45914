package com.deepinnet.spatiotemporalplatform.client.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.skyflow.WeatherGridPostBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR> wong
 * @create 2025/3/11 09:56
 * @Description
 */
@FeignClient(name = "weatherGridClient", url = "${stpf.service.url}")
public interface WeatherGridClient {

    @PostMapping("/stpf/weather/grid")
    Result<List<String>> queryWeatherGridData(@RequestBody WeatherGridPostBody weatherGridPostBody);

}
