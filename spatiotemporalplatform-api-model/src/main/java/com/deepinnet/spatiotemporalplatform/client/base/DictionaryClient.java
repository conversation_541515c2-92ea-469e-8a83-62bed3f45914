package com.deepinnet.spatiotemporalplatform.client.base;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.DictionaryDTO;
import com.deepinnet.spatiotemporalplatform.dto.DictionaryQueryDTO;
import com.deepinnet.spatiotemporalplatform.vo.DictionaryVO;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 *
 * <AUTHOR>
 * @since 2024-08-03 星期六
 **/
@FeignClient(name = "dictionaryClient", url = "${stpf.service.url}")
public interface DictionaryClient {

    @PostMapping("/dictionary/create")
    @ApiOperation("创建字典")
    Result<Long> createDictionary(@RequestBody @Valid DictionaryDTO dictionaryDTO);

    @PostMapping("/dictionary/update")
    @ApiOperation("更新字典")
    Result<Boolean> updateDictionary(@RequestBody @Valid DictionaryDTO dictionaryDTO);

    @PostMapping("/dictionary/get")
    @ApiOperation("根据ID获取字典")
    Result<DictionaryVO> getDictionaryById(@RequestBody @Valid @NotNull(message = "ID不能为空")
                                                  @ApiParam(value = "字典ID", required = true) Long id);

    @PostMapping("/dictionary/page")
    @ApiOperation("分页查询字典")
    Result<CommonPage<DictionaryVO>> pageQueryDictionary(@RequestBody @Valid DictionaryQueryDTO queryDTO);

    @PostMapping("/dictionary/delete")
    @ApiOperation("删除字典")
    Result<Boolean> deleteDictionary(@RequestBody @Valid @NotNull(message = "ID不能为空")
                                            @ApiParam(value = "字典ID", required = true) Long id);

}
