package com.deepinnet.spatiotemporalplatform.client.police;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.police.PoliceDeployment;
import com.deepinnet.spatiotemporalplatform.model.police.PoliceDeploymentBatchOperation;
import com.deepinnet.spatiotemporalplatform.model.police.PoliceDeploymentDTO;
import com.deepinnet.spatiotemporalplatform.model.police.PoliceDeploymentQueryDTO;

@FeignClient(name = "policeDeploymentAdminClient", url = "${stpf.service.url}")
public interface PoliceDeploymentClient {
    @PostMapping("/domain/police/deployment/listByAreaCodeAndPlanCodeList")
    Result<List<PoliceDeployment>> listByAreaCodeAndPlanCodeList(@RequestParam("areaCode") String areaCode, @RequestBody List<String> contingencyPlanCodeList);

    @PostMapping("/domain/police/deployment/list")
    Result<List<PoliceDeployment>> listByAreaCodeAndObjectCode(@RequestBody PoliceDeploymentQueryDTO queryDTO);

    @PostMapping("/domain/police/deployment/listByAreaCode")
    Result<List<PoliceDeployment>> listByAreaCode(@RequestBody PoliceDeploymentQueryDTO queryDTO);

    @PostMapping("/domain/police/deployment/create")
    Result<PoliceDeploymentDTO> add(@RequestBody PoliceDeploymentDTO policeDeployment);

    @PostMapping("/domain/police/deployment/delete")
    Result<Boolean> delete(@RequestParam("id") Integer id);

    @PostMapping("/domain/police/deployment/batch")
    Result<Boolean> batch(@RequestBody PoliceDeploymentBatchOperation batchOperation);

    @PostMapping("/domain/police/deployment/realtime/list")
    Result<List<PoliceDeployment>> listRealTimeByAreaCodeAndObjectCode(@RequestBody PoliceDeploymentQueryDTO queryDTO);

    @PostMapping("/domain/police/deployment/listByObjCodeList")
    Result<List<PoliceDeployment>> listByObjCodeList(@RequestBody List<String> objCodeList);

    @PostMapping("/domain/police/deployment/deleteByObjCode")
    Result<Boolean> deleteByObjCode(@RequestParam("objCode") String objCode);

    @PostMapping("/domain/police/deployment/deleteByObjCodeAndType")
    Result<Boolean> deleteByObjCodeAndType(@RequestParam("planCode") String planCode, @RequestParam("type") String type);

    @PostMapping("/domain/police/deployment/assignToObject")
    Result<Boolean> assignToObject(@RequestBody List<PoliceDeployment> policeDeploymentList, @RequestParam("objCode") String objCode);

}
