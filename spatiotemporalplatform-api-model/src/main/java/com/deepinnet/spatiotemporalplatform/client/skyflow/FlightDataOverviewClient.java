package com.deepinnet.spatiotemporalplatform.client.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.vo.FlightDataOverviewVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * 飞行数据概览服务客户端
 *
 * <AUTHOR> wong
 * @create 2025/01/14
 */
@FeignClient(name = "flightDataOverviewClient", url = "${stpf.service.url}")
public interface FlightDataOverviewClient {

    /**
     * 获取最新的飞行数据概览
     *
     * @return 最新的飞行数据概览
     */
    @GetMapping("/stpf/flight/data/overview/latest")
    @ApiOperation(value = "[飞行数据概览接口] => 获取最新的飞行数据概览")
    Result<FlightDataOverviewVO> getLatestFlightDataOverview();
} 