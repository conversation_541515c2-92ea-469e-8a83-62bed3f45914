package com.deepinnet.spatiotemporalplatform.client.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.FlightRestrictionsAOI;
import com.deepinnet.spatiotemporalplatform.dto.GroundFeatures;
import com.deepinnet.spatiotemporalplatform.dto.GroundFeaturesQueryDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 区域信息 Feign Client
 *
 * <AUTHOR>
 * @since 2025/3/13
 */
@FeignClient(name = "areaOfInterestClient", url = "${stpf.service.url}")
public interface AreaOfInterestClient {

    /**
     * 获取区域信息
     *
     * @param queryDTO 查询参数
     * @return 区域信息列表
     */
    @PostMapping("/aoi/list")
    Result<List<FlightRestrictionsAOI>> getAoiList(@RequestBody GroundFeaturesQueryDTO queryDTO);

    /**
     * 获取地面特征信息
     *
     * @param queryDTO 查询参数
     * @return 地面特征信息列表
     */
    @PostMapping("/aoi/groundFeatures")
    Result<List<GroundFeatures>> getGroundFeatures(@RequestBody GroundFeaturesQueryDTO queryDTO);
}