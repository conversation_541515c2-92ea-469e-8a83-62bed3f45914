package com.deepinnet.spatiotemporalplatform.client.skyflow;

import javax.validation.Valid;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.FenceSpacePageQueryDTO;
import com.deepinnet.spatiotemporalplatform.model.skyflow.FenceSpace;

/**
 * 禁飞区/管制区服务Feign客户端
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@FeignClient(name = "fenceSpaceFeignClient", url = "${stpf.service.url}")
public interface FenceSpaceFeignClient {

    /**
     * 创建禁飞区/管制区
     *
     * @param fenceSpace 禁飞区/管制区信息
     * @return 禁飞区/管制区编号
     */
    @PostMapping("/stpf/skyflow/fencespace/create")
    Result<String> createFenceSpace(@Valid @RequestBody FenceSpace fenceSpace);

    /**
     * 分页查询禁飞区/管制区
     *
     * @param queryDTO 查询条件
     * @return 分页查询结果
     */
    @PostMapping("/stpf/skyflow/fencespace/page")
    Result<CommonPage<FenceSpace>> pageQueryFenceSpace(@Valid @RequestBody FenceSpacePageQueryDTO queryDTO);

    /**
     * 根据编号查询禁飞区/管制区详情
     *
     * @param fenceNum 禁飞区/管制区编号
     * @return 禁飞区/管制区详情
     */
    @GetMapping("/stpf/skyflow/fencespace/detail")
    Result<FenceSpace> getFenceSpaceByFenceNum(@RequestParam("fenceNum") String fenceNum);
} 