package com.deepinnet.spatiotemporalplatform.client.tour;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.model.tour.customer.CustomerFlowDetect;
import com.deepinnet.spatiotemporalplatform.model.tour.customer.CustomerFlowDetectGrid;
import com.deepinnet.spatiotemporalplatform.model.tour.customer.CustomerFlowDetectGridUrlParams;
import com.deepinnet.spatiotemporalplatform.model.tour.customer.CustomerFlowDetectUrlParams;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**

 *
 * <AUTHOR>
 * @since 2024-08-27 星期二
 **/
@FeignClient(name = "customerFlowClient",url = "${stpf.service.url}")
public interface CustomerFlowClient {

    @PostMapping("/stpf/tour/people/flow/detect")
    Result<CustomerFlowDetect> detect(@RequestBody CustomerFlowDetectUrlParams customerFlowDetectUrlParams);

    @PostMapping("/stpf/tour/people/flow/detect/grid")
    Result<CustomerFlowDetectGrid> detectGrid(@RequestBody CustomerFlowDetectGridUrlParams customerFlowDetectGridUrlParams);

}
