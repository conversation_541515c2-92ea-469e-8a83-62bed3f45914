package com.deepinnet.spatiotemporalplatform.client.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.RiskWarningPathQueryDTO;
import com.deepinnet.spatiotemporalplatform.dto.*;
import com.deepinnet.spatiotemporalplatform.vo.RealTimeUavFlightVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.*;


/**
 * <AUTHOR> wong
 * @create 2025/3/11 09:56
 * @Description
 */
@FeignClient(name = "riskWarningClient", url = "${stpf.service.url}")
public interface RiskWarningClient {

    @PostMapping("/stpf/warning/summary")
    Result<Map<String, Integer>> getRiskWarningSummary(@RequestBody RiskWarningQueryDTO queryDTO);

    @PostMapping("/stpf/warning/page")
    Result<RiskWarningSummaryDTO> getRiskWaringList(@RequestBody RiskWarningQueryDTO queryDTO);

    @PostMapping("/stpf/warning/path")
    Result<List<RiskWarningPathDTO>> getRiskWaringFlightPath(@RequestBody RiskWarningPathQueryDTO queryDTO);

    @PostMapping("/stpf/warning/position")
    Result<List<RealTimeUavFlightVO>> getNewestFlightPosition(@RequestBody RiskWarningPositionQueryDTO queryDTO);

    @GetMapping("/stpf/warning/listByPlanId")
    Result<List<RiskWarningDTO>> getRiskWarningListByPlanId(@RequestParam(value = "planId") String planId);

    @PostMapping("/stpf/warning/history/path")
    Result<List<RiskWarningPathDTO>> getHistoryRiskWaringFlightPath(@RequestBody RiskWarningPathQueryDTO queryDTO);
}
