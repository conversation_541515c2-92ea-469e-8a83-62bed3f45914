package com.deepinnet.spatiotemporalplatform.client.skyflow;

import javax.validation.Valid;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.AirspacePageQueryDTO;
import com.deepinnet.spatiotemporalplatform.model.skyflow.Airspace;

/**
 * 空域服务Feign客户端
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@FeignClient(name = "airspaceFeignClient", url = "${stpf.service.url}")
public interface AirspaceFeignClient {

    /**
     * 创建空域
     *
     * @param airspace 空域信息
     * @return 空域代码
     */
    @PostMapping("/stpf/skyflow/airspace/create")
    Result<String> createAirspace(@Valid @RequestBody Airspace airspace);

    /**
     * 分页查询空域
     *
     * @param queryDTO 查询条件
     * @return 分页查询结果
     */
    @PostMapping("/stpf/skyflow/airspace/page")
    Result<CommonPage<Airspace>> pageQueryAirspace(@Valid @RequestBody AirspacePageQueryDTO queryDTO);

    /**
     * 根据代码查询空域详情
     *
     * @param code 空域代码
     * @return 空域详情
     */
    @GetMapping("/stpf/skyflow/airspace/detail")
    Result<Airspace> getAirspaceByCode(@RequestParam("code") String code);
} 