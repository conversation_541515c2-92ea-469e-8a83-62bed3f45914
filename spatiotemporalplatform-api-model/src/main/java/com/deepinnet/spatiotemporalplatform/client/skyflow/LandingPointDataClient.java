package com.deepinnet.spatiotemporalplatform.client.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.LandingPointQueryDTO;
import com.deepinnet.spatiotemporalplatform.vo.LandingPointVO;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 起降点数据服务客户端
 *
 * <AUTHOR>
 */
@FeignClient(name = "landingPointDataClient", url = "${stpf.service.url}")
public interface LandingPointDataClient {

    /**
     * 查询起降点列表
     *
     * @param queryDTO 查询条件
     * @return 起降点列表
     */
    @PostMapping("/stpf/landing-point-data/list")
    @ApiOperation("查询起降点列表")
    Result<List<LandingPointVO>> listLandingPoint(@RequestBody LandingPointQueryDTO queryDTO);

} 