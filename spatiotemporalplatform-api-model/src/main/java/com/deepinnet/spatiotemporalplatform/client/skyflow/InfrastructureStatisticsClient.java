package com.deepinnet.spatiotemporalplatform.client.skyflow;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.dto.InfrastructureStatisticsQueryDTO;
import com.deepinnet.spatiotemporalplatform.vo.InfrastructureStatisticsVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * 基础设施统计服务客户端
 *
 * <AUTHOR>
 * @create 2025/06/30
 */
@FeignClient(name = "infrastructureStatisticsClient", url = "${stpf.service.url}")
public interface InfrastructureStatisticsClient {

    /**
     * 获取基础设施统计数据
     *
     * @return 基础设施统计数据
     */
    @PostMapping("/stpf/infrastructure/statistics")
    @ApiOperation(value = "[基础设施统计接口] => 获取基础设施统计数据")
    Result<InfrastructureStatisticsVO> getInfrastructureStatistics();
} 