package com.deepinnet.spatiotemporalplatform.common.request;

import com.deepinnet.spatiotemporalplatform.common.constants.GlobalConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**

 *
 * <AUTHOR>
 * @since 2024-08-27 星期二
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class DigestSignParams extends BaseSignParams {

    /**
     * clientKey
     */
    private String clientKey;

    /**
     * clientSecret
     */
    private String clientSecret;


    /**
     * userKey
     */
    private String userKey;

}
