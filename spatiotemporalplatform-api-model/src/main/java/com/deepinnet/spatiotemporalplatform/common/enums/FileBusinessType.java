package com.deepinnet.spatiotemporalplatform.common.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum FileBusinessType {
    WIND("wind", "风力"),
    TRAFFIC_PEOPLE("traffic_people", "地物客流"),
    TRAFFIC_VEHICLES("traffic_vehicles", "地物车流"),
    OBSTACLE("obstacle", "障碍物"),
    POI("poi", "POI");

    private final String code;
    private final String desc;

    FileBusinessType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static FileBusinessType getByCode(String code) {
        for (FileBusinessType type : FileBusinessType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

}