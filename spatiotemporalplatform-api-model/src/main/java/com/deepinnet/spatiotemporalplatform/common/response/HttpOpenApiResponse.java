package com.deepinnet.spatiotemporalplatform.common.response;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR> wong
 * @create 2024/9/25 16:43
 * @Description
 */
@Data
public class HttpOpenApiResponse {

    @JSONField(alternateNames = {"code"})
    private String code;

    private String message;

    private String status;

    private String info;

    private String infocode;
}
