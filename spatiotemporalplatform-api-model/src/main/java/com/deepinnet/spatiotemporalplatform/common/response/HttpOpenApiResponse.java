package com.deepinnet.spatiotemporalplatform.common.response;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> wong
 * @create 2024/9/25 16:43
 * @Description
 */
@Data
public class HttpOpenApiResponse implements Serializable {

    @JSONField(alternateNames = {"code"})
    private String code;

    private String message;

    private String status;

    private String info;

    private String infocode;
}
