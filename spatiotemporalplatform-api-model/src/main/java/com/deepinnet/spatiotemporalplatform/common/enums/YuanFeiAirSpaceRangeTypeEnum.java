package com.deepinnet.spatiotemporalplatform.common.enums;

import lombok.Getter;

/**
 * YuanFeiAirSpaceRangeTypeEnum
 * Author: chenkaiyang
 * Date: 2025/3/15
 */
@Getter
public enum YuanFeiAirSpaceRangeTypeEnum {
    CIRCLE("圆形", "0"),
    LINE("航线", "1"),
    POLYGON("多边形","2"),
    SECTOR("扇形","3"),;
    private final String name;
    private final String code;
    YuanFeiAirSpaceRangeTypeEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }
    public static YuanFeiAirSpaceRangeTypeEnum getByCode(String code) {
        for (YuanFeiAirSpaceRangeTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
