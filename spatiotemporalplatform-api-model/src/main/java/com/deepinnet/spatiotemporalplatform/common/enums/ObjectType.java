package com.deepinnet.spatiotemporalplatform.common.enums;

import cn.hutool.core.util.EnumUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <PERSON><PERSON> z<PERSON>
 * Date 2024-08-06
 **/

@Getter
@AllArgsConstructor
public enum ObjectType implements BaseEnum<String> {

    TRAFFIC_SECURITY_TASK("traffic_security_task", "交保任务"),
    TRAFFIC_SECURITY_TASK_ROUTE("traffic_security_task_route", "交保任务线路"),
    TRAFFIC_SECURITY_TASK_DISPATCH_INSTANCE("traffic_security_task_instance", "交保任务下发实例"),
    AREA("area", "区域"),
    CONTINGENCY_PLAN("contingency_plan", "预案"),

    ;

    public static ObjectType getByCode(String code) {
        return EnumUtil.getBy(ObjectType.class, c -> StringUtils.equals(c.getCode(), code));
    }

    private final String code;

    private final String desc;
}
