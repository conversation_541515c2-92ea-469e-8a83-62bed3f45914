package com.deepinnet.spatiotemporalplatform.common.request;

import com.deepinnet.spatiotemporalplatform.common.constants.GlobalConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**

 *
 * <AUTHOR>
 * @since 2024-08-27 星期二
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TokenSignParams extends BaseSignParams {

    /**
     * userKey
     */
    private String apiSecret;

    private String appname;

    private String key;

}
