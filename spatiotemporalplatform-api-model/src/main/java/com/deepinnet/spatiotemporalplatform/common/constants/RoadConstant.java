package com.deepinnet.spatiotemporalplatform.common.constants;

/**

 *
 * <AUTHOR>
 * @since 2024-08-28 星期三
 **/
public interface RoadConstant {

    String REAL_ROAD_INDEX_API_URL = "http://et-api.amap.com/index/roadRanking?adcode=%s&type=%s&roadLength=%s&roadClass=%s&district=%s&ids=%s&size=%s&clientKey=%s&timestamp=%s";

    String REAL_ROAD_INDEX_ALL_API_URL = "http://et-api.amap.com/index/roadRankingAll?adcode=%s&type=%s&roadLength=%s&roadClass=%s&district=%s&ids=%s&size=%s&clientKey=%s&timestamp=%s";

    String ROAD_HISTORY_RANKING_API_URL = "http://et-api.amap.com/index/historyRoadRanking?adcode=%s&customHour=%s&dateRangeList=%s&dayType=%s&district=%s&roadClass=%s&timeGrading=%s&timePeriodList=%s&timestamp=%s&type=%s&clientKey=%s&size=%s&ids=%s";

    String ROAD_FLOW_PREDICT_API_URL = "http://et-api.amap.com/predict/roadPredict?adcode=%s&clientKey=%s&timestamp=%s&startPoint=%s&extensions=%s&saturationFlag=%s";

    String ROAD_LINK_FLOW_API_URL = "http://et-api.amap.com/state/areaJSONPub?adcode=%s&clientKey=%s&timestamp=%s&pubname=%s";

    String ROAD_SEARCH_API_URL = "http://et-api.amap.com/index/queryLinksInfo?adcode=%s&clientKey=%s&timestamp=%s&types=%s&name=%s";

    String ROAD_FLOW_API_URL = "http://et-api.amap.com/support/city/flow/list?clientKey=%s&timestamp=%s&";

    String ROAD_SHAPE_API_URL = "http://et-api.amap.com/index/queryLinksShapeByIds?adcode=%s&clientKey=%s&timestamp=%s&type=%s&ids=%s&isRarefy=%s&rarefyMeter=%s&rarefyPointNum=%s&validTopology=%s";

}
