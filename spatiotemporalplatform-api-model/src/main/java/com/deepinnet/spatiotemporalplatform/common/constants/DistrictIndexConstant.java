package com.deepinnet.spatiotemporalplatform.common.constants;

/**
 * 区域指数相关常量
 *
 * <AUTHOR>
 * @version 2024-07-29
 */
public interface DistrictIndexConstant {
    /**
     *
     * 替换123.183.232.20 et-api.amap.com
     *
     * 替换106.11.226.133 restapi.amap.com
     */

    /**
     * 实时区域指数排⾏API地址-衢州
     */
    String REAL_TIME_DISTRICT_INDEX_RANKING_API_URL = "http://et-api.amap.com/index/districtRanking?clientKey=%s&timestamp=%s&adcode=%s&types=%s&district=%s&withArea=%s&ids=%s&size=%s";

    /**
     * 实时区域指数排⾏API地址-全量
     */
    String REAL_TIME_DISTRICT_INDEX_RANKING_ALL_API_URL = "http://et-api.amap.com/index/districtRankingAll?clientKey=%s&timestamp=%s&adcode=%s&types=%s&district=%s&withArea=%s&ids=%s&size=%s";


    /**
     * 历史区域指数排行API地址
     */
    String HISTORY_DISTRICT_INDEX_RANKING_API_URL = "http://et-api.amap.com/index/historyDistrictRanking?clientKey=%s&timestamp=%s&adcode=%s&type=%s&dateRangeList=%s&timePeriodList=%s&dayType=%s&timeGrading=%s&ids=%s&size=%s&aggregate=%s&customHour=%s";

    /**
     * 区域实时人流API地址
     */
    String REAL_TIME_DISTRICT_PEOPLE_FLOW_API_URL = "https://et-api.amap.com/support/survey/out/peopleflow/area/query?userKey=%s&clientKey=%s&timestamp=%s&customAreaId=%s";

    /**
     * 区域历史人流API地址
     */
    String HISTORY_DISTRICT_PEOPLE_FLOW_API_URL = "https://et-api.amap.com/support/survey/out/peopleflow/history/area/query?userKey=%s&clientKey=%s&flowType=%s&customAreaId=%s&startDate=%s&timeGrading=%s&endDate=%s&timestamp=%s";

    /**
     * ⽹格历史⼈流&⼈流活⼒指数API地址
     */
    String GRID_HISTORY_PEOPLE_FLOW_API_URL = "https://et-api.amap.com/support/survey/out/peopleflow/history/grid/query?userKey=%s&clientKey=%s&timestamp=%s&customAreaId=%s&flowType=%s&gridScale=%s&timeGrading=%s&startDate=%s&endDate=%s";

    /**
     * ⽹格实时⼈流&⼈流活⼒指数API地址
     */
    String GRID_REALTIME_PEOPLE_FLOW_API_URL = "https://et-api.amap.com/support/survey/out/peopleflow/grid/queryList?userKey=%s&clientKey=%s&timestamp=%s&customAreaId=%s&page=%s&size=%s";

    /**
     * 人流来源地
     */
    String PEOPLE_FLOW_SOURCE_AREA_URL = "https://et-api.amap.com/support/survey/out/routeflow/v2/customArea/fromOrToObj/list?userKey=%s&clientKey=%s&timestamp=%s&adcode=%s&fromOrTo=%s&customAreaId=%s&type=%s&pageNo=%s&pageSize=%s";
}
