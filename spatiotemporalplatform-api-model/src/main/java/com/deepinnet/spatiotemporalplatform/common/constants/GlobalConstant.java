package com.deepinnet.spatiotemporalplatform.common.constants;

/**
 * <AUTHOR>
 * @version 2024-07-29
 */
public interface GlobalConstant {

    String SMART_TRAFFIC_POLICE = "smarttrafficpolice";

    String SPATIOTEMPORAL_PLATFORM = "spatiotemporalplatform";

    String SPATIOTEMPORAL_GATEWAY = "spatiotemporal-gateway";

    /**
     * 高德分配给衢州的城市代码
     */
    String QZ_AD_CODE = "330800";

    String SHENZHEN_DP_AD_CODE = "440300";

    /**
     * 政府内网部署环境
     */
    String ENV_GOV_INNER_NETWORK = "gov-inner-network";

    /**
     * 交警平台 衢州部署环境
     */
    String ENV_QZ_GOV_INNER_NETWORK = "qz-gov-inner-network";

    /**
     * 衢州政务网环境
     */
    String ENV_QZ_GOV_CLOUD = "qz-gov-cloud";

    /**
     * 阿里云公网环境
     */
    String ENV_ALI_CLOUD = "ali-cloud";


    /**
     * 测试环境 local模拟的会最终在test执行
     */
    String TEST = "test";

    /**
     * 深圳内网
     */
    String SZ_INNER = "sz-gov-inner";

}
