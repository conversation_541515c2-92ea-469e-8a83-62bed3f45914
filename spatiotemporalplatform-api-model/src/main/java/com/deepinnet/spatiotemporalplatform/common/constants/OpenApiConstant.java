package com.deepinnet.spatiotemporalplatform.common.constants;

/**
 * <AUTHOR> wong
 * @create 2024/9/25 15:56
 * @Description
 */
public interface OpenApiConstant {

    String REVERSE_GEOCODING_URL = "https://restapi.amap.com/v3/geocode/regeo?output=json&location=%s&key=%s&radius=%s&extensions=%s";

    String GEOCODE_URL = "https://restapi.amap.com/v3/geocode/geo?address=%s&output=json&key=%s&city=%s";

    String POI_URL = "https://restapi.amap.com/v5/place/around?location=%s&radius=%s&types=%s&page_num=%s&page_size=%s&key=%s";

    String POLYGON_POI_URL = "https://restapi.amap.com/v5/place/polygon?polygon=%s&types=%s&page_num=%s&page_size=%s&key=%s";

    String USER_KEY = "68bd083e4be7259f87440d8ef12446a5";

    String GD_FLIGHT_DETECTION_URL = "https://els-yunjing.amap.com/data/mtkdrone?key=%s&center=%s&radius=%s";

    String WEATHER_GRID_QUERY_URL = "https://els-yunjing.amap.com/data/weather?key=%s";

    String STREET_POLYGON_QUERY_URL = "https://restapi.amap.com/rest/me/polygon/dataquery/street";

    String INPUT_TIPS_URL = "https://restapi.amap.com/v3/assistant/inputtips?output=json&city=%s&keywords=%s&location=%s&key=%s";

    String INPUT_TIPS_KEY = "0f7d1bfe627c1b7c7c096bc8c52a30a1";
}
