package com.deepinnet.spatiotemporalplatform.common.request;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.enums.SignTypeEnum;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;

/**
 * <AUTHOR>
 * @version 2024-07-29
 */
public interface HttpApiRequest {
    /**
     * 请求方式：GET / POST
     */
    default HttpMethod httpMethod() {
        return HttpMethod.GET;
    }

    /**
     * post请求的请求体, 默认为空
     */
    default PostBody postBody() {
        return null;
    }

    default HttpHeaders httpHeaders() {
        return new HttpHeaders();
    }

    /**
     * api返回的response body类型，有的api是结构体，有的是String
     */
    TypeReference responseBodyType();

    /**
     * 当response body类型为String时，此方法提供反序列化类型;
     */
    default TypeReference deserializeType() {
        return null;
    }

    /**
     * 请求url参数
     */
    UrlParams urlParams();

    /**
     * api请求地址，带请求参数
     */
    String apiUrl();

    /**
     * 签名类型
     */
    default SignTypeEnum signType() {
        return SignTypeEnum.DIGEST;
    }

    /**
     * 是否使用临时的apiKey
     */
    default boolean useTempApiKey() {
        return true;
    }

    /**
     * 整体采用简单的存储数据库模式，如果有特殊需求，可以自定义持久化逻辑
     * 是否自定义持久化
     * 默认为false
     */
    default Boolean isCustomPersistence() {
        return false;
    }

    /**
     * 自定义持久化数据
     * 当isCustomPersistence 返回true时，需要实现此方法，进行自定义处理
     * 如需 UrlParams 对象 直接 在方法里this.urlParams() 获取即可
     */
    default void customPersistenceData(Object responseData) {
    }

    /**
     * 持久化数据表名 类如：“real_road_index”
     */
    default String saveDataTableName() {
        return null;
    }

    /**
     * 自定义转换返回数据
     * 传入的为HttpApiResponse中的getData()
     * 当数据库中的元素与要存储的表有差异时候需要做转换
     * 注意⚠️：对象的属性需要跟数据表里的字段对应 实体类对象属性会自动转换数据库对应的下划线字段
     * 通用插入处理 对象属性与数据库字段必须一一对应 不能多 负责插入数据库会报错
     * 如需 UrlParams 对象 直接 在方法里this.urlParams() 获取即可
     */
    default Object transformData(Object responseData) {
        //默认不做任何处理
        return responseData;
    }
}
