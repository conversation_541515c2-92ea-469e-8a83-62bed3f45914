package com.deepinnet.spatiotemporalplatform.common.response;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 2024-07-30
 */
@Data
public abstract class HttpApiResponse {
    /**
     * 响应代码
     */
    @JSONField(alternateNames = {"code", "errcode", "errorcode"})
    private int code;

    /**
     * 响应信息
     */
    @JSONField(alternateNames = {"msg", "message", "errmsg", "errormsg"})
    private String msg;

    /**
     *
     */
    @JSONField(alternateNames = {"detail", "errdetail"})
    private String detail;

    /**
     * 响应数据
     *
     * @return
     */
    public abstract Object getData();
}
