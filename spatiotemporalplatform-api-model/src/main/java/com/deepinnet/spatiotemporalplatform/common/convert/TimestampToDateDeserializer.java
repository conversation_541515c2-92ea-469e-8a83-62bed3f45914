package com.deepinnet.spatiotemporalplatform.common.convert;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.util.Date;

public class TimestampToDateDeserializer extends JsonDeserializer<Date> {

    @Override
    public Date deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        // 从 JsonParser 中获取时间戳的数值
        long timestamp = p.getValueAsLong();
        // 将时间戳转换为 Date 对象
        return new Date(timestamp);
    }
}