package com.deepinnet.spatiotemporalplatform.common.convert;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;

import java.io.IOException;
import java.util.Date;

public class DateToTimestampSerializer extends StdSerializer<Date> {

    public DateToTimestampSerializer() {
        super(Date.class);
    }

    @Override
    public void serialize(Date value, JsonGenerator gen, SerializerProvider provider) throws IOException {
        // 将 Date 转换为时间戳
        long timestamp = value.getTime();
        gen.writeNumber(timestamp);
    }
}