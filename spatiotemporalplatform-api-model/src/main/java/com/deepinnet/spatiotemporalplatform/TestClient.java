package com.deepinnet.spatiotemporalplatform;

import com.deepinnet.digitaltwin.common.response.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * gps接口客户端
 *
 * <AUTHOR>
 * @version 2024-11-22
 */
@FeignClient(name = "testClient", url = "${stpf.service.url}")
public interface TestClient {

    @PostMapping("/stpf/test/weather/task")
    Result<Boolean> pageQueryIntelligenceList(@RequestParam(value = "weatherData") String weatherData);

    @PostMapping("/stpf/test/task")
    @ApiOperation(value = "测试定时任务")
    Result<Boolean> testTask();

    @PostMapping("/stpf/test/risk/task")
    @ApiOperation(value = "测试风险预警定时任务")
    Result<Boolean> testRiskWarningTask();

    @GetMapping("/stpf/test/live_test/start")
    @ApiOperation(value = "算法直播测试")
    Result<String> liveTest(@RequestParam(value = "planId") String planId,
                            @RequestParam(value = "url") String url,
                            @RequestParam(value = "flightId") String flightId);

    @GetMapping("/stpf/test/live_test/end")
    @ApiOperation(value = "算法直播测试")
    Result<String> liveTestEnd(@RequestParam(value = "planId") String planId, @RequestParam(value = "flightId") String flightId);

    @GetMapping("/stpf/test/report")
    @ApiOperation(value = "生成飞行报告")
    Result<String> generateFlightReport(@RequestParam(value = "planId") String planId);
}
