package com.deepinnet.spatiotemporalplatform.anno;

import com.deepinnet.digitaltwin.common.log.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.List;

/**
 * 工具类，用于根据网格位置码填充对象的字段。
 */
public class GridCodeProcessor {

    /**
     * 根据注解解析网格位置码并填充对象字段。
     *
     * @param target 需要填充的对象
     */
    public static void process(Object target, List<String> values) {
        if (target == null || CollectionUtils.isEmpty(values)) {
            throw new IllegalArgumentException("目标对象或数组不能为空");
        }

        Class<?> clazz = target.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            GridCodePart annotation = field.getAnnotation(GridCodePart.class);
            if (annotation != null) {
                int position = annotation.position();
                if (position + 1 > values.size()) {
                    LogUtil.warn("数组长度不足，无法获取第 {} 位的值", position);
                    return;
                }

                // 获取数组指定位置的值
                String value = values.get(position);

                // 将字段设置为可访问状态
                field.setAccessible(true);

                try {
                    // 根据字段类型自动设置值
                    if (field.getType() == int.class || field.getType() == Integer.class) {
                        field.set(target, Integer.parseInt(value));
                    } else if (field.getType() == String.class) {
                        field.set(target, String.valueOf(value));
                    } else if (field.getType() == boolean.class || field.getType() == Boolean.class) {
                        field.set(target, StringUtils.equals(value, "1"));
                    } else if (field.getType() == double.class || field.getType() == Double.class) {
                        field.set(target, Double.parseDouble(value));
                    } else {
                        throw new UnsupportedOperationException("不支持的字段类型：" + field.getType());
                    }
                } catch (IllegalAccessException e) {
                    throw new RuntimeException("无法设置字段值", e);
                }
            }
        }
    }
}