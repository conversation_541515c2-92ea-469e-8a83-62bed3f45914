package com.deepinnet.spatiotemporalplatform.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 *    趋势图坐标数据
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/11
 */
@Data
@Builder
@ApiModel(value = "趋势图坐标数据")
@NoArgsConstructor
@AllArgsConstructor
public class TrendVO<X,Y> implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "横坐标")
    private X x;

    @ApiModelProperty(value = "纵坐标-数量")
    private Y countY;

    @ApiModelProperty(value = "纵坐标-历史平均")
    private String historyAvgY;

    @ApiModelProperty(value = "纵坐标-增长率")
    private String rate;

}
