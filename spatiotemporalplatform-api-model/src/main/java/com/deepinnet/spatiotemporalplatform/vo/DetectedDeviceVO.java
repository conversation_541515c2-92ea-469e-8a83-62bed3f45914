package com.deepinnet.spatiotemporalplatform.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 侦测定位设备数据VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel("侦测定位设备信息")
public class DetectedDeviceVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("外部系统设备ID")
    private String externalId;

    @ApiModelProperty("厂家")
    private String manufacturer;

    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty("品牌型号")
    private String brandModel;

    @ApiModelProperty("序列号")
    private String serialNo;

    @ApiModelProperty("基站类型")
    private String stationType;

    @ApiModelProperty("技术参数")
    private String techParams;

    @ApiModelProperty("数据标准")
    private String dataStandard;

    @ApiModelProperty("设备用途")
    private String equipUsage;

    @ApiModelProperty("安装环境")
    private String installEnvir;

    @ApiModelProperty("安装地址")
    private String installAdress;

    @ApiModelProperty("天线高度")
    private String aerialHeight;

    @ApiModelProperty("工作频率或频段")
    private String operatingFrequency;

    @ApiModelProperty("安装日期")
    private String installDate;

    @ApiModelProperty("安装数量")
    private Integer installCount;

    @ApiModelProperty("安装情况")
    private String installSituation;

    @ApiModelProperty("所在物业单位")
    private String propCompany;

    @ApiModelProperty("物业联系人")
    private String propLinkUser;

    @ApiModelProperty("物业联系人电话")
    private String linkPhone;

    @ApiModelProperty("设备维护人")
    private String deviceManager;

    @ApiModelProperty("设备联系人电话")
    private String devicePhone;

    @ApiModelProperty("维护要求")
    private String manageRequire;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建人")
    private String crtUserName;

    @ApiModelProperty("创建人ID")
    private String crtUserId;

    @ApiModelProperty("创建时间")
    private String crtTime;

    @ApiModelProperty("是否删除")
    private String isDeleted;

    @ApiModelProperty("修改人")
    private String updUserName;

    @ApiModelProperty("修改人ID")
    private String updUserId;

    @ApiModelProperty("修改时间")
    private String updTime;

    @ApiModelProperty("部门id")
    private String departId;

    @ApiModelProperty("部门ids（20级）")
    private String departIds;

    @ApiModelProperty("租户id")
    private String tenantId;

    @ApiModelProperty("团队ID")
    private String teamId;

    @ApiModelProperty("接收增益")
    private String receiverGain;

    @ApiModelProperty("发射功率")
    private String transPower;

    @ApiModelProperty("脉冲波形")
    private String pulseWaveform;

    @ApiModelProperty("极化方式")
    private String polarizationMode;

    @ApiModelProperty("噪声系数")
    private String figure;

    @ApiModelProperty("通讯系统原始带宽")
    private String bandWidth;

    @ApiModelProperty("现场照片")
    private String sitePicUrl;

    @ApiModelProperty("经度")
    private Double lon;

    @ApiModelProperty("纬度")
    private Double lat;

    @ApiModelProperty("水平覆盖范围")
    private String gcoverRage;

    @ApiModelProperty("垂直覆盖范围")
    private String vcoverRage;

    @ApiModelProperty("数据批次号")
    private Long batchNo;
} 