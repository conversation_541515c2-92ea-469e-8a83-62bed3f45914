package com.deepinnet.spatiotemporalplatform.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 飞行统计概览VO
 *
 * <AUTHOR>
 * @since 2025/4/12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("飞行统计概览")
public class FlightStatisticsOverviewVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("飞行架次")
    private Integer flightCount;

    @ApiModelProperty("飞行里程")
    private String flightMiles;

    @ApiModelProperty("预警数量")
    private Long flightWarningCount;

    @ApiModelProperty("探测发现数量")
    private Long flightDetectionCount;
}
