package com.deepinnet.spatiotemporalplatform.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 飞行数据概览VO
 *
 * <AUTHOR> wong
 * @create 2025/01/14
 */
@Data
@ApiModel("飞行数据概览信息")
public class FlightDataOverviewVO {

    @ApiModelProperty("主键id")
    private Long id;

    /**
     * 近一月飞行架次
     */
    @ApiModelProperty("近一月飞行架次")
    private Integer lastMonthFlightCount;

    /**
     * 近一月飞行架次同比增长（百分比）
     */
    @ApiModelProperty("近一月飞行架次同比增长（百分比）")
    private Double lastMonthFlightCountGrowth;

    /**
     * 最近一周飞行架次
     */
    @ApiModelProperty("最近一周飞行架次")
    private Integer lastWeekFlightCount;

    /**
     * 最近一周飞行架次同比增长（百分比）
     */
    @ApiModelProperty("最近一周飞行架次同比增长（百分比）")
    private Double lastWeekFlightCountGrowth;

    /**
     * 近一年飞行架次
     */
    @ApiModelProperty("近一年飞行架次")
    private Integer lastYearFlightCount;

    /**
     * 近一年飞行架次同比
     */
    @ApiModelProperty("近一年飞行架次同比增长（百分比）")
    private Double lastYearFlightCountGrowth;

    /**
     * 近一年飞行里程（公里）
     */
    @ApiModelProperty("近一年飞行里程（公里）")
    private Double lastYearDistance;

    /**
     * 近一年飞行里程同比增长（百分比）
     */
    @ApiModelProperty("近一年飞行里程同比增长（百分比）")
    private Double lastYearDistanceGrowth;

    /**
     * 近一年飞行时长（小时）
     */
    @ApiModelProperty("近一年飞行时长（小时）")
    private Double lastYearFlightDuration;

    /**
     * 数据批次号（用于标识同一批数据）
     */
    @ApiModelProperty("数据批次号")
    private Long batchNo;


    /**
     * 近一年飞行时长同比增长（%）
     */
    @ApiModelProperty("近一年飞行时长同比增长")
    private Double lastYearFlightDurationGrowth;

    /**
     * 近一月日均架次（架次/天）
     */
    @ApiModelProperty("近一月日均架次")
    private Double lastMonthDailyAverageFlightCount;

    @ApiModelProperty("近一月日均架次同比")
    private Double lastMonthDailyAverageFlightCountGrowth;

} 