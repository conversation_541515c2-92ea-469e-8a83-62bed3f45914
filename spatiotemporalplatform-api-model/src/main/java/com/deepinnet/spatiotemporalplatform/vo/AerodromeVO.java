package com.deepinnet.spatiotemporalplatform.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 *  机场信息VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/3
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AerodromeVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 机场ID
     */
    private String uavPortId;

    /**
     * 机场名称
     */
    private String name;

    /**
     * 机场AOI
     */
    private String aoi;

    /**
     * 机场坐标
     */
    private String position;

    /**
     * 场地状态
     */
    private String status;

    /**
     * 机场编号
     */
    private String code;

    /**
     * 高度
     */
    private String height;

    /**
     * 海拔高度
     */
    private String alt;

} 