package com.deepinnet.spatiotemporalplatform.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 字典项树结构VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "字典项树结构VO")
public class DictionaryItemTreeVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "字典ID")
    private Long dictionaryId;

    @ApiModelProperty(value = "字典项编码")
    private String code;

    @ApiModelProperty(value = "字典项名称")
    private String name;

    @ApiModelProperty(value = "状态")
    private String state;

    @ApiModelProperty(value = "父编码")
    private String parentCode;

    @ApiModelProperty(value = "层级")
    private Integer level;

    @ApiModelProperty(value = "层级名称")
    private String levelName;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreated;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "子节点")
    private List<DictionaryItemTreeVO> children;
} 