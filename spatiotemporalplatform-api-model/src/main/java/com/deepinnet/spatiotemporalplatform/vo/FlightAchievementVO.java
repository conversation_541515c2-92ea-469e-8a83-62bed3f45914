package com.deepinnet.spatiotemporalplatform.vo;

import com.deepinnet.spatiotemporalplatform.enums.FlightAchievementTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.locationtech.jts.geom.Point;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/8/11
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlightAchievementVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("计划ID")
    private String planId;

    /**
     * 计划编号(flight_plan.plan_no)
     */
    @ApiModelProperty("计划编号")
    private String planNo;

    /**
     * 鸢飞成果ID
     */
    @ApiModelProperty("成果ID")
    private String achievementId;

    /**
     * 鸢飞成果名称
     */
    @ApiModelProperty("成果名称")
    private String achievementName;

    /**
     * 鸢飞成果类型(IMAGE-图片;VIDEO-视频;2d-二维图;3d-三维图;PANORAMA-全景; REPORT-其它报告)
     */
    @ApiModelProperty("成果类型")
    private FlightAchievementTypeEnum achievementType;

    /**
     * 鸢飞成果时间
     */
    @ApiModelProperty("成果时间")
    private Long achievementTime;

    /**
     * 鸢飞成果地址
     */
    @ApiModelProperty("成果url")
    private String achievementUrl;

    /**
     * 鸢飞成果省市区地址
     */
    @ApiModelProperty("成果地址")
    private String achievementAddress;

    /**
     * 鸢飞成果经纬度
     */
    @ApiModelProperty("成果经纬度")
    private String achievementLocation;

    /**
     * 需求编号
     */
    @ApiModelProperty("需求编号")
    private String demandNo;

    /**
     * 可以比对的日期
     */
    @ApiModelProperty("可比对的日期")
    private List<LocalDate> availableCompareDate;

}
