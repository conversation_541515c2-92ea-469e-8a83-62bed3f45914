package com.deepinnet.spatiotemporalplatform.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/19
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlightCountVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String bizNo;

    private Integer count;

}
