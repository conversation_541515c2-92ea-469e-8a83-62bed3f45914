package com.deepinnet.spatiotemporalplatform.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 飞行计划VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlightPlanVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 外部计划编号（服务商计划编号）
     */
    private String outPlanNo;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 需求名称
     */
    private String requirementName;

    /**
     * 场景编码
     */
    private String scenceCode;

    /**
     * 业务编码
     */
    private String bizNo;

    /**
     * 任务ID
     */
    private String missionId;

    /**
     * 飞行计划唯一标识
     */
    private String planId;

    /**
     * 无人机ID，通常为SN码
     */
    private String uavId;

    /**
     * 飞行器状态
     */
    private String uavStatus;

    /**
     * 无人机型号
     */
    private String uavModel;

    /**
     * 申请人名称
     */
    private String applyUserName;

    /**
     * 飞行计划状态
     */
    private Integer status;

    /**
     * 申请计划类型:1-长期;2-次日;3-当日
     */
    private Integer applyType;

    /**
     * 关联长期计划的ID
     */
    private String relatedId;

    /**
     * 计划起飞时间
     */
    private Long planedTakeoffTime;

    /**
     * 计划降落时间
     */
    private Long planedLandingTime;

    /**
     * 计划创建时间
     */
    private Long planCreateTime;

    /**
     * 飞行区域ID
     */
    private String airspaceId;

    /**
     * 计划飞行高度
     */
    private Long planedAltitude;

    /**
     * 是否实名
     */
    private Boolean realNameVerification;

    /**
     * 飞手（操作员）
     */
    private String operatorUser;

    /**
     * 飞手联系电话
     */
    private String operatorPhone;

    /**
     * 飞行单位
     */
    private String flightUnit;

    /**
     * 飞行单位ID
     */
    private String flightUnitId;

    /**
     * 创建人名称
     */
    private String crtUserName;

    /**
     * 创建人ID
     */
    private String crtUserId;

    /**
     * 飞行报告
     */
    private String flightReport;

    /**
     * 监控地址
     */
    private String monitorUrl;

    /**
     * 组织编号
     */
    private String organizationId;

    /**
     * 组织名称
     */
    private String organizationName;

    /**
     * 场景分类
     */
    private String type;

    /**
     * 飞行频次
     */
    private String flyingFrequency;

    /**
     * 飞行次数
     */
    private Integer flyingNums;

    /**
     * 周期类型
     */
    private String cycleType;

    /**
     * 实时飞行信息
     */
    private List<RealTimeUavFlightVO> realtimeUavInfoList;

    /**
     * 无人机信息
     */
    private UavVO uavInfo;

    /**
     * 起飞机场ID
     */
    private String landingAerodromeId;

    /**
     * 起降场
     */
    private List<AerodromeVO> landingAerodrome;

    /**
     * 飞行记录
     */
    private FlightRecordVO flightRecord;

    /**
     * 是否异常
     */
    private Boolean existException = false;
} 