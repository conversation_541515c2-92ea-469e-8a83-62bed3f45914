package com.deepinnet.spatiotemporalplatform.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 基础设施建设统计VO
 *
 * <AUTHOR>
 */
@Data
@Builder
@ApiModel("基础设施建设统计")
@AllArgsConstructor
@NoArgsConstructor
public class InfrastructureStatisticsVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("通信设备数量")
    private Integer communicationDeviceCount;

    @ApiModelProperty("导航设备数量")
    private Integer navigationDeviceCount;

    @ApiModelProperty("监视设备数量")
    private Integer detectedDeviceCount;


    @ApiModelProperty("起降场总数量")
    private Integer totalLandingPointCount;

    @ApiModelProperty("机场数量")
    private Integer airportCount;

    @ApiModelProperty("固定起降点数量")
    private Integer fixedLandingPointCount;

    @ApiModelProperty("临时起降点数量")
    private Integer temporaryLandingPointCount;


    @ApiModelProperty("气象设备数量")
    private Long weatherDevicesCount;
} 