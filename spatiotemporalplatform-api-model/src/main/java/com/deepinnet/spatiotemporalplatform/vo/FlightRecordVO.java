package com.deepinnet.spatiotemporalplatform.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/8
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlightRecordVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "计划ID", example = "PLAN_ID_001")
    private String planId;

    @ApiModelProperty(value = "起飞时间", example = "1000000000000")
    private Long takeoffTime;

    @ApiModelProperty(value = "降落时间", example = "9999999999999")
    private Long landingTime;

}
