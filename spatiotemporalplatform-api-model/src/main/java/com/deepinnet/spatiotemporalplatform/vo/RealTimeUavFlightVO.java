package com.deepinnet.spatiotemporalplatform.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * <p>
 * 实时飞行数据VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RealTimeUavFlightVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("飞行计划ID")
    private String planId;

    @ApiModelProperty("飞行计划名称")
    private String planName;

    @ApiModelProperty("飞行单位")
    private String flightUnit;

    @ApiModelProperty("无人机ID")
    private String uavId;

    @ApiModelProperty("架次id")
    private String flightId;

    @ApiModelProperty("无人机状态")
    private String status;

    @ApiModelProperty("飞手经纬度位置")
    private String operatorPosition;

    @ApiModelProperty("飞手高度")
    private String operatorAltitude;

    @ApiModelProperty("无人机飞行速度")
    private String flightSpeed;

    @ApiModelProperty("无人机位置")
    private String uavPosition;

    @ApiModelProperty("无人机高度")
    private String uavAltitude;

    @ApiModelProperty("海拔高度")
    private String elevation;

    @ApiModelProperty("飞行时长")
    private String flightDuration;

    @ApiModelProperty("电池电压:伏")
    private String voltage;

    @ApiModelProperty("电量百分比")
    private Integer soc;

    @ApiModelProperty("上报时间")
    private Long reportTime;
} 