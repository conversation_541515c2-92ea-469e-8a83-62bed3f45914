package com.deepinnet.spatiotemporalplatform.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlightTodayOverviewVO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 飞行中数量 及其 增长率
     */
    private FlightStat<Integer> inFlightCount;
    
    /**
     * 累计飞行里程 及其 增长率
     */
    private FlightStat<String> totalDistance;
    
    /**
     * 累计飞行架次 及其 增长率
     */
    private FlightStat<Integer> totalFlights;
    
    /**
     * 累计预警数量 及其 增长率
     */
    private FlightStat<Integer> totalWarnings;


    /**
     * 今日飞行计划 及其 增长率
     */
    @ApiModelProperty(value = "今日飞行计划 及其 增长率")
    private FlightStat<Integer> todayFlightPlan;

    /**
     * 今日累计黑飞探测 及其 增长率
     */
    @ApiModelProperty(value = "今日累计黑飞探测 及其 增长率")
    private FlightStat<Integer> todayFlightDetect;

} 