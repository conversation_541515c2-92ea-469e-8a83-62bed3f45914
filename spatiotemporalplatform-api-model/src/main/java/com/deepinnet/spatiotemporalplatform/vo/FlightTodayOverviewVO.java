package com.deepinnet.spatiotemporalplatform.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlightTodayOverviewVO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 飞行中数量 及其 增长率
     */
    private FlightStat<Integer> inFlightCount;
    
    /**
     * 累计飞行里程 及其 增长率
     */
    private FlightStat<String> totalDistance;
    
    /**
     * 累计飞行架次 及其 增长率
     */
    private FlightStat<Integer> totalFlights;
    
    /**
     * 累计预警数量 及其 增长率
     */
    private FlightStat<Integer> totalWarnings;
} 