package com.deepinnet.spatiotemporalplatform.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/12
 */

@Getter
@AllArgsConstructor
public enum FlightQueryFiledEnum {

    /**
     * 飞行架次
     */
    FLIGHT_COUNT("FLIGHT_COUNT", "飞行架次"),

    /**
     * 飞行时长
     */
    FLIGHT_DURATION("FLIGHT_DURATION", "飞行时长"),

    /**
     * 飞行里程
     */
    FLIGHT_MILES("FLIGHT_MILES", "飞行里程"),

    ;

    private final String code;

    private final String desc;

    public static FlightQueryFiledEnum getEnumByCode(String code) {
        for (FlightQueryFiledEnum flightQueryFiledEnum : FlightQueryFiledEnum.values()) {
            if (StrUtil.equals(flightQueryFiledEnum.getCode(), code)) {
                return flightQueryFiledEnum;
            }
        }

        // 默认 飞行架次 排序
        return FLIGHT_COUNT;
    }
}
