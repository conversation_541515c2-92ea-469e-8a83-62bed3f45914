package com.deepinnet.spatiotemporalplatform.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description:
 * Date: 2025/5/9
 * Author: lijunheng
 */
@AllArgsConstructor
@Getter
public enum FlightEventStatusEnum {

    // 初始状态
    INIT("init", "初始状态"),
    // 进行中
    ONGOING("ongoing", "进行中"),
    // 已结束（实际发生了违章）
    END("end", "已结束"),
    // 已取消（违停中止，未达到违章阈值）
    CANCELLED("cancelled", "已取消");

    private final String code;
    private final String desc;

    public static FlightEventStatusEnum getByCode(String eventStatus) {
        for (FlightEventStatusEnum value : values()) {
            if (value.getCode().equals(eventStatus)) {
                return value;
            }
        }
        throw new IllegalArgumentException("Invalid event status: " + eventStatus);
    }
}
