package com.deepinnet.spatiotemporalplatform.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/12
 */

@Getter
@AllArgsConstructor
public enum FlightQuerySortTypeEnum {

    /**
     * 倒序
     */
    DESC("DESC", "倒序"),

    /**
     * 正序
     */
    ASC("ASC", "正序");

    private final String code;

    private final String desc;

    public static FlightQuerySortTypeEnum getByCode(String code) {
        for (FlightQuerySortTypeEnum flightQuerySortTypeEnum : values()) {
            if (StrUtil.equals(flightQuerySortTypeEnum.getCode(), code)) {
                return flightQuerySortTypeEnum;
            }
        }

        // 默认倒序排序
        return DESC;
    }
}
