package com.deepinnet.spatiotemporalplatform.enums;

import com.deepinnet.spatiotemporalplatform.common.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 起降场类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AirType implements BaseEnum<String> {

    /**
     * 机场
     */
    AIRPORT("1", "机场"),

    /**
     * 固定起降场
     */
    FIXED_LANDING_SITE("2", "固定起降场"),

    /**
     * 临时起降场
     */
    TEMPORARY_LANDING_SITE("3", "临时起降场");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static AirType getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (AirType value : AirType.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据code获取描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getDescByCode(Integer code) {
        AirType airType = getByCode(code);
        return airType == null ? null : airType.getDesc();
    }
} 