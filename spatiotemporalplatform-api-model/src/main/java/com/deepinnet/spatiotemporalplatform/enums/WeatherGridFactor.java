package com.deepinnet.spatiotemporalplatform.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * C<PERSON> zeng<PERSON><PERSON>
 * Date 2025-04-18
 **/

@Getter
@AllArgsConstructor
public enum WeatherGridFactor {

    TEMPERATURE("TEMPERATURE", "温度"),
    PRECIPITATION("PRECIPITATION", "降雨量"),
    WIND_FORCE("WIND_FORCE", "风力")

    ;

    private final String code;

    private final String desc;
}
