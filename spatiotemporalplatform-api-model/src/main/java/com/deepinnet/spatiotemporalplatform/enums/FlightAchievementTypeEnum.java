package com.deepinnet.spatiotemporalplatform.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/8/8
 */

@Getter
@AllArgsConstructor
public enum FlightAchievementTypeEnum {

    IMAGE("image", "IMAGE", "图片"),

    VIDEO("VIDEO", "VIDEO", "视频"),

    TWO_D("2d", "2D", "二维图"),

    THREE_3D("3d", "3D", "三维图"),

    PANORAMA("panorama", "PANORAMA", "全景"),

    REPORT("report", "REPORT", "其它报告"),

    OTHER("other", "OTHER", "其它, 不存在于以上枚举使用OTHER")

    ;

    private final String yfCode;

    private final String deepCode;

    private final String desc;

    public static FlightAchievementTypeEnum getEnumByYfCode(String yfCode) {

        if (StrUtil.isEmpty(yfCode)) {
            return null;
        }

        for (FlightAchievementTypeEnum typeEnum : FlightAchievementTypeEnum.values()) {
            if (StrUtil.equals(yfCode, typeEnum.getYfCode())) {
                return typeEnum;
            }
        }

        return null;
    }

    public static FlightAchievementTypeEnum getEnumByDeepCode(String deepCode) {

        if (StrUtil.isEmpty(deepCode)) {
            return null;
        }

        for (FlightAchievementTypeEnum typeEnum : FlightAchievementTypeEnum.values()) {
            if (StrUtil.equals(deepCode, typeEnum.getDeepCode())) {
                return typeEnum;
            }
        }

        return null;
    }


}
