package com.deepinnet.spatiotemporalplatform.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description:
 * Date: 2025/5/9
 * Author: lijunheng
 */
@AllArgsConstructor
@Getter
public enum FlightEventTypeEnum {

    // 违章停车
    PARKING_VIOLATION("parking_violation", "车辆违停"),
    // 压实线行驶
    LINE_VIOLATION("line_violation", "压实线行驶"),
    // 违法掉头
    TURN_VIOLATION("turn_violation", "违法掉头"),
    // 逆行
    REVERSE_VIOLATION("reverse_violation", "逆行");

    private final String code;

    private final String name;

    public static FlightEventTypeEnum getByCode(String eventType) {
        for (FlightEventTypeEnum flightEventTypeEnum : values()) {
            if (flightEventTypeEnum.getCode().equals(eventType)) {
                return flightEventTypeEnum;
            }
        }
        throw new IllegalArgumentException("Invalid eventType: " + eventType);
    }
}
