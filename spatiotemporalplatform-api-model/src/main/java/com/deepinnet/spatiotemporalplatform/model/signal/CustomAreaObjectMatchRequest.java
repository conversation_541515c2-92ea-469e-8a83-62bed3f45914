package com.deepinnet.spatiotemporalplatform.model.signal;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.enums.SignTypeEnum;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.PostBody;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonArrayResponse;
import lombok.Data;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;

/**
 * 评诊治-自定义区域-对象匹配Request类
 * 
 * <AUTHOR>
 * @version 2025-06-09
 */
@Data
public class CustomAreaObjectMatchRequest implements HttpApiRequest {

    /**
     * URL参数
     */
    private CustomAreaObjectMatchUrlParams urlParams;

    /**
     * POST请求体
     */
    private CustomAreaObjectMatchPostBody postBody;

    @Override
    public UrlParams urlParams() {
        return urlParams;
    }

    @Override
    public String apiUrl() {
        return "https://et-api.amap.com/diagnosis/statics/graspObj?clientKey=%s&timestamp=%s&adcode=%s&objType=%s&district=%s&ds=%s";
    }

    @Override
    public HttpMethod httpMethod() {
        return HttpMethod.POST;
    }

    @Override
    public HttpHeaders httpHeaders() {
        return null;
    }

    @Override
    public PostBody postBody() {
        return postBody;
    }

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {};
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<HttpApiJsonArrayResponse<CustomAreaObjectMatch>>() {};
    }

    @Override
    public SignTypeEnum signType() {
        return SignTypeEnum.DIGEST;
    }

    @Override
    public boolean useTempApiKey() {
        return true;
    }
} 