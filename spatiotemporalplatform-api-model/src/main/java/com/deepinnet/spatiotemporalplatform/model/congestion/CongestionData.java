package com.deepinnet.spatiotemporalplatform.model.congestion;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2024-07-29
 */
@Data
public class CongestionData {
    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码")
    private int currentPage;

    /**
     * 页码总数
     */
    @ApiModelProperty(value = "页码总数")
    private int totalPage;

    /**
     * 总数据量
     */
    @ApiModelProperty(value = "总数据量")
    private int totalSize;

    /**
     * 数据列表
     */
    @ApiModelProperty(value = "数据列表")
    private List<Congestion> list;
}