package com.deepinnet.spatiotemporalplatform.model.tour.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.stream.Collectors;

/**

 *
 * <AUTHOR>
 * @since 2024-08-28 星期三
 **/
@Getter
@AllArgsConstructor
public enum ProfileEnum {

    SEX_DIST("sex_dist"),

    UV("uv"),

    PMNT_PROV_DIST("pmnt_prov_dist"),
    PMNT_CITY_DIST("pmnt_city_dist"),
    PROPLEVEL_DIST("proplevel_dist"),
    AGE_DIST("age_dist"),
    OCCUPATION_DIST("occupation_dist"),

    ;

    private final String value;

    public static String getValues(ProfileEnum... profileEnum) {
        return Arrays.stream(profileEnum).map(ProfileEnum::getValue).collect(Collectors.joining(","));
    }

}
