package com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.transform;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 *   实时人流存储数据
 * </p>
 *
 * <AUTHOR>
 * @since 2024/11/6
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransformRealTimePeopleFlowData {

    /**
     * 自定义区域ID
     */
    private String customAreaId;

    /**
     * 时间（yyyy-MM-dd HH:mm:ss）
     */
    private String time;

    /**
     * 人流活力指数（数值越大，人流越活跃）
     */
    private Double activityIndex;

    /**
     * 人流量（人次）
     */
    private Integer flow;


    private Integer preFlow;

    /**
     * 今日累计人流量（从今日凌晨到当前批次累计去重后的人流量）（仅升级
     * 版）
     */
    private Integer todayCumulativeFlow;

    /**
     * 历史平均人流量（仅升级版）
     */
    private Double avgFlow;

    /**
     * 历史最大人流量（仅升级版）
     */
    private Integer maxFlow;

    /**
     * md5
     */
    private String md5;

}
