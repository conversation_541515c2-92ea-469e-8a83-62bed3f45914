package com.deepinnet.spatiotemporalplatform.model.signal;

import lombok.Data;

/**
 * 查询指数路或者通勤走廊的rid及顺序数据对象
 * 
 * <AUTHOR>
 * @version 2025-06-09
 */
@Data
public class QueryRoadRid {

    /**
     * 城市编码
     */
    private String adcode;

    /**
     * 区县编码
     */
    private String district;

    /**
     * 查询ID，拼接规则：adcode_1/2_走廊Id
     */
    private String roadQueryId;

    /**
     * rid
     */
    private String rid;

    /**
     * rid在路段中的顺序
     */
    private Integer ridNo;

    /**
     * 数据生产月份
     */
    private String ds;
} 