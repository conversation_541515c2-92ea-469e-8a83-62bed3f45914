package com.deepinnet.spatiotemporalplatform.model.dify;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * <PERSON><PERSON> zeng<PERSON>
 * Date 2025-01-22
 **/

@Data
public class DifyRunWorkflowResult {

    private String id;

    @JSONField(name = "workflow_id")
    private String workflowId;

    private String status;

    private Outputs outputs;

    private String error;

    @JSONField(name = "elapsed_time")
    private double elapsedTime;

    @JSONField(name = "total_tokens")
    private int totalTokens;

    @JSONField(name = "total_steps")
    private int totalSteps;

    @JSONField(name = "created_at")
    private long createdAt;

    @JSONField(name = "finished_at")
    private long finishedAt;

    @Data
    public static class Outputs {
        private String output;
    }
}
