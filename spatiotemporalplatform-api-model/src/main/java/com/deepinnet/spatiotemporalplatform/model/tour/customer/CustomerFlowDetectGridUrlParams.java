package com.deepinnet.spatiotemporalplatform.model.tour.customer;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**

 *
 * <AUTHOR>
 * @since 2024-08-26 星期一
 * #483
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class CustomerFlowDetectGridUrlParams extends UrlParams {

    /**
     * 区域ID
     */
    private String areaId;

    /**
     * 区块尺寸areaid支持：10，100，500，
     * （热力制式）1000，4000（默认100）
     * adcode支持：1000，4000（默认1000）
     */
    private Integer geoLength;

    /**
     * 查询开始时间 如20200405
     */
    private Integer startDate;

    /**
     * 查询结束时间 如20200410
     */
    private Integer endDate;

    /**
     * total_flow或visit_flow
     * 类型
     *
     * @see com.deepinnet.spatiotemporalplatform.model.tour.enums.FlowTypeEnum
     */
    private String flowType;

    /**
     * hour|day
     *
     * @see com.deepinnet.spatiotemporalplatform.model.tour.enums.TimeTypeEnum
     */
    private String timeType;
}
