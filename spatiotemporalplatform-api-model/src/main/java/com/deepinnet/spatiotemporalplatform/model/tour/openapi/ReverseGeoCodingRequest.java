package com.deepinnet.spatiotemporalplatform.model.tour.openapi;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.OpenApiConstant;
import com.deepinnet.spatiotemporalplatform.common.request.*;
import lombok.Data;

/**
 * 高德开放平台逆地理编码：https://lbs.amap.com/api/webservice/guide/api/georegeo
 */
@Data
public class ReverseGeoCodingRequest implements HttpApiRequest {

    private ReverseGeocodingParam urlParams;

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {
        };
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<ReverseGeocoding>() {
        };
    }

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public String apiUrl() {
        return OpenApiConstant.REVERSE_GEOCODING_URL;
    }
}
