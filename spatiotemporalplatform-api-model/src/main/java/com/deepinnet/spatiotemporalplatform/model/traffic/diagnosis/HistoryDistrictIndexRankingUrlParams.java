package com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 历史区域交通指数查询URL参数
 *
 * <AUTHOR>
 * @version 2024-07-29
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class HistoryDistrictIndexRankingUrlParams extends UrlParams {

    /**
     * 授权城市ADCODE.
     */
    private String adcode;

    /**
     * 区域类型，多个','隔开.
     * 0 - 城市
     * 1 - 高德中心城区行政区域
     * 3 - 高德商圈
     * 4 - 国家行政区域
     */
    private int type;

    /**
     * 时间范围，格式为startdate@enddate。⽇期格
     * 式为"yyyy-MM-dd"，例如"2021-01-
     * 01@2021-02-01"。当粒度为⽉或季度时，
     * date是该⽉或季度的⾸⽇
     */
    private String dateRangeList;

    /**
     * 时间段列表，多个⽤','隔开，
     * all 表示所有时段
     * mpeak 表示早⾼峰(7-9)
     * epeak 表示晚⾼峰(17-19)
     * non-peak 表示⾼峰除外(6-21)
     * evening 表示(22-23)
     * night 表示(0-5)
     */
    private String timePeriodList;
    /**
     * ⽇期类型，多个⽤','隔开
     * workday 表示⼯作⽇
     * weekend 表示周末
     */
    private String dayType;

    /**
     * 时间粒度。
     * quarter-季度
     * month-⽉
     * day-天
     * hour-⼩时
     * dayofweek-星期⼏
     */
    private String timeGrading;

    /**
     * ⾃定义⼩时，多个⽤','隔开。
     * 当timeGrading取值为day或者dayofweek时，
     * ⽀持此参数。
     */
    private String customHour;

    /**
     * 是否聚合。取值：true-是、false-否。
     */
    private boolean aggregate;

    /**
     * 区域ID列表，多个⽤','隔开
     */
    private String ids;

    /**
     * 返回数据条数
     */
    private int size;
}
