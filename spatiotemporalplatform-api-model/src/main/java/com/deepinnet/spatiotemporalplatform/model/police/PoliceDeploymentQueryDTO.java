package com.deepinnet.spatiotemporalplatform.model.police;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * policeDeploymentQueryDTO
 * Author: chenkaiyang
 * Date: 2024/8/5
 */
@Data
public class PoliceDeploymentQueryDTO {

    @ApiModelProperty(value = "区域code")
    private String areaCode;

    @ApiModelProperty(value = "关联对象类型")
    private String objectType;

    @ApiModelProperty(value = "关联对象code")
    private String objectCode;
}
