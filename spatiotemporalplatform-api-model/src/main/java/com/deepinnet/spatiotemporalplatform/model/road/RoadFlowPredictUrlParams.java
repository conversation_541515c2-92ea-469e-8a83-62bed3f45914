package com.deepinnet.spatiotemporalplatform.model.road;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**

 *
 * <AUTHOR>
 * @since 2024-08-28 星期三
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class RoadFlowPredictUrlParams extends UrlParams {

    /**
     * ⾏政区代码
     */
    private String adcode;

    /**
     * 道路坐标（流量位置）
     */
    private String startPoint;

    /**
     * 传⼊all获取真实流量推演 不传⼊获取基于过去10分钟为基准 的未来流量变化百分⽐
     */
    private String extensions;

    /**
     * 传⼊1获取即时速度+饱和度
     */
    private Integer saturationFlag;
}
