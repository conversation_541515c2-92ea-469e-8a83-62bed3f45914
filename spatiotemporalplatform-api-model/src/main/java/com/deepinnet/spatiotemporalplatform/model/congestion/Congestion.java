package com.deepinnet.spatiotemporalplatform.model.congestion;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 拥堵数据
 *
 * <AUTHOR>
 * @version 2024-07-29
 */
@Data
public class Congestion {

    @ApiModelProperty(value = "拥堵类型")
    private String congestionType;

    @ApiModelProperty(value = "拥堵开始时间")
    private String abnormalStartTime;

    @ApiModelProperty(value = "拥堵⽅向平⾯⻆度")
    private String angle;

    @ApiModelProperty(value = "批次时间，拥堵数据计算时间")
    private String batchTime;

    @ApiModelProperty(value = "拥堵排队⽅向起点")
    private String centerPointWKT;

    @ApiModelProperty(value = "城市代码")
    private int citycode;

    @ApiModelProperty(value = "拥堵率")
    private String congestRate;

    @ApiModelProperty(value = "拥堵信息id")
    private String congestionId;

    @ApiModelProperty(value = "拥堵分级 1-极度拥堵 2-严重拥堵 3-普通拥堵 4-轻微拥堵")
    private int congestionLevel;

    @ApiModelProperty(value = "数据类型")
    private int dataType;

    @ApiModelProperty(value = "方向")
    private String direction;

    @ApiModelProperty(value = "距离")
    private int distance;

    @ApiModelProperty(value = "持续时间（分钟）")
    private int durationMin;

    @ApiModelProperty(value = "结束地址")
    private String endAddress;

    @ApiModelProperty(value = "结束点")
    private String endPoint;

    @ApiModelProperty(value = "结束原始点")
    private String endRawPoint;

    @ApiModelProperty(value = "头索引")
    private int headIndex;

    @ApiModelProperty(value = "初始链路ID")
    private String initLinkId;

    @ApiModelProperty(value = "初始起点地址")
    private String initStartAddr;

    @ApiModelProperty(value = "初始起点")
    private String initStartPoint;

    @ApiModelProperty(value = "初始时间")
    private String initTime;

    @ApiModelProperty(value = "链路信息")
    private String links;

    @ApiModelProperty(value = "链路状态信息列表")
    private List<LinkInfoState> listLinkInfoState;

    @ApiModelProperty(value = "拥堵曾经达到的最⻓距离")
    private int maxDistance;

    @ApiModelProperty(value = "拥堵data的包裹矩形坐标")
    private Rect rect;

    @ApiModelProperty(value = "拥堵整体可信度，越⾼越好 <80为低可信度")
    private int reliability;

    @ApiModelProperty(value = "道路ID")
    private String roadId;

    @ApiModelProperty(value = "道路名称")
    private String roadName;

    @ApiModelProperty(value = "道路类型 0：普通道路")
    private int roadType;

    @ApiModelProperty(value = "道路平均速度")
    private int speed;

    @ApiModelProperty(value = "拥堵趋势 1.拥堵 2.趋向严重 3.趋向疏通")
    private int trend;

    @ApiModelProperty(value = "道路拥堵状态 3：拥堵 ，4：严重拥堵")
    private int state;

    @ApiModelProperty(value = "拥堵起点⽂字描述")
    private String startAddress;

    @ApiModelProperty(value = "拥堵起点坐标")
    private String startPoint;

    @ApiModelProperty(value = "拥堵起点坐标")
    private String startRawPoint;

    @ApiModelProperty(value = "拥堵开始时间(时间戳)")
    private String startTime;

    @Data
    public static class Rect {
        @ApiModelProperty(value = "最小经度")
        private String minLon;

        @ApiModelProperty(value = "最大经度")
        private String maxLon;

        @ApiModelProperty(value = "最小纬度")
        private String minLat;

        @ApiModelProperty(value = "最大纬度")
        private String maxLat;
    }

    @Data
    public static class LinkInfoState {
        @ApiModelProperty(value = "Link起点")
        private String firstPoint;

        @ApiModelProperty(value = "Link终点")
        private String lastPoint;

        @ApiModelProperty(value = "Link⻓度")
        private int length;

        @ApiModelProperty(value = "道路id")
        private String linkId;

        @ApiModelProperty(value = "道路类型：0 ⾼速公路, 1 国道, 2 省道, 3 县道, 4 乡公路, 5 县乡村内部道路, 6 主要⼤街、城市快速道, 7 主要道路, 8 次要道路, 9 普通道路, 10 ⾮导航道路")
        private int linkType;

        @ApiModelProperty(value = "Link路形坐标串")
        private String lngLats;

        @ApiModelProperty(value = "要描述的section距离参考点的距离")
        private int offset;

        @ApiModelProperty(value = "Link包裹矩形")
        private String rect;

        @ApiModelProperty(value = "道路id")
        private String roadId;

        @ApiModelProperty(value = "道路名称")
        private String roadName;

        @ApiModelProperty(value = "0：普通道路，1：轮渡航线 ，2：隧道， 3：桥")
        private int roadType;

        @ApiModelProperty(value = "拥堵整体可信度，越⾼越好 <80为低可信度")
        private int reliability;

        @ApiModelProperty(value = "速度，当前拥堵Link的平均速度")
        private int speed;

        @ApiModelProperty(value = "Link状态 2：缓⾏，3：拥堵 ，4：严重拥堵")
        private int state;

        @ApiModelProperty(value = "Link通过时间，单位0.1秒")
        private String travelTime;

        @ApiModelProperty(value = "路段信息")
        private List<SectionStatus> listSectionStatus;
    }

    @Data
    public static class SectionStatus {
        @ApiModelProperty(value = "道路平均速度")
        private int speed;

        @ApiModelProperty(value = "Link状态 3：拥堵 ，4：严重拥堵")
        private int state;

        @ApiModelProperty(value = "Link通过时间，单位0.1秒")
        private int travelTime;

        @ApiModelProperty(value = "要描述的section距离参考点的距离")
        private int offset;

        @ApiModelProperty(value = "拥堵整体可信度，越⾼越好 <80为低可信度")
        private int reliability;
    }
}

