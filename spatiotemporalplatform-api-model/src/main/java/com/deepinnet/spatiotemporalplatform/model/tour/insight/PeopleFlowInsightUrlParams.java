package com.deepinnet.spatiotemporalplatform.model.tour.insight;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.*;

/**
 * <AUTHOR>
 * @since 2024-08-26 星期一
 * #490
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class PeopleFlowInsightUrlParams extends UrlParams {

    /**
     * 自定义区域id
     */
    private String customAreaId;

    /**
     * 洞察维度
     *
     * @see com.deepinnet.spatiotemporalplatform.model.tour.enums.PeopleFlowInsightEnum
     */
    private String profile;

}
