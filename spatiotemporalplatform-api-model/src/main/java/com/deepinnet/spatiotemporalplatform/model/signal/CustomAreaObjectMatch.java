package com.deepinnet.spatiotemporalplatform.model.signal;

import lombok.Data;

/**
 * 评诊治-自定义区域-对象匹配数据对象
 * 
 * <AUTHOR>
 * @version 2025-06-09
 */
@Data
public class CustomAreaObjectMatch {

    /**
     * 对象类型
     * INTER：路口、RID：路段、POI：POI
     */
    private String objType;

    /**
     * 城市编码
     */
    private String adcode;

    /**
     * 对象id
     */
    private String objId;

    /**
     * 对象名称
     * 仅POI或INTER时有值
     */
    private String objName;

    /**
     * 点坐标
     * 仅路口和poi有点，其余对象需要单独调静态信息接口
     */
    private String lngLat;

    /**
     * poi类型
     * 01:加油站，02:充电站，06:商场，08:运动场馆，09:医院，10:产业园，
     * 11:景区，12:写字楼，13:住宅小区，14:学校，15:飞机场，16:火车站，
     * 17:公交站，18:地铁站，19:停车场
     */
    private String poiType;

    /**
     * 指数类型
     * 10：收费站、11：机场、12：火车站、34：医院、37：大学、
     * 38：中学、41：景区、49：小学
     */
    private String linksType;

    /**
     * 是否信控路口
     * 1-信控灯路口；0-非信控灯路口，仅INTER有值
     */
    private Integer isSignlight;

    /**
     * 行政区代码
     */
    private Integer district;

    /**
     * 数据时间戳
     */
    private Integer ds;
} 