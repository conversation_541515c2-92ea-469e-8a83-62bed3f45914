package com.deepinnet.spatiotemporalplatform.model.monitor;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.deepinnet.spatiotemporalplatform.model.monitor.error.ErrorType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**

 *
 * <AUTHOR>
 * @since 2025-01-02 星期四
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class ErrorMonitorUrlParams extends MonitorUrlParams {
    /**
     * 监测类型 默认心跳
     */
    private String monitorType = MonitorType.ERROR.name();

    private String errorType = ErrorType.SYSTEM.name();

    private String className;

    private String methodName;

    private Integer lineNumber;

    private Exception exception;

    public String getException() {
        return ExceptionUtil.getMessage(exception);
    }
}

