package com.deepinnet.spatiotemporalplatform.model.road;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.RoadConstant;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonArrayResponse;
import lombok.Data;

/**

 *
 * <AUTHOR>
 * @since 2024-08-28 星期三
 **/
@Data
public class RoadShapeRequest implements HttpApiRequest {

    private RoadShapeUrlParams urlParams;

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {
        };
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<HttpApiJsonArrayResponse<RoadShape>>() {
        };
    }

    @Override
    public boolean useTempApiKey() {
        return false;
    }

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public String apiUrl() {
        return RoadConstant.ROAD_SHAPE_API_URL;
    }


}
