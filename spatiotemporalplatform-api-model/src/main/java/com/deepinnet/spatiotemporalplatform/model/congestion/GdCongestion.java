package com.deepinnet.spatiotemporalplatform.model.congestion;

import lombok.Data;

/**
 * <AUTHOR> wong
 * @create 2024/11/6 11:29
 * @Description
 */
@Data
public class GdCongestion {

    private String congestionType;

    private String abnormalStartTime;

    private String angle;

    private String batchTime;

    private String centerPointWKT;

    private int citycode;

    private String congestRate;

    private String congestionId;

    private int congestionLevel;

    private int dataType;

    private String direction;

    private int distance;

    private int durationMin;

    private String endAddress;

    private String endPoint;

    private String endRawPoint;

    private int headIndex;

    private String initLinkId;

    private String initStartAddr;

    private String initStartPoint;

    private String initTime;

    private String links;

    private String listLinkInfoState;

    private int maxDistance;

    private String rect;

    private int reliability;

    private String roadId;

    private String roadName;

    private int roadType;

    private int speed;

    private int trend;

    private int state;

    private String startAddress;

    private String startPoint;

    private String startRawPoint;

    private String startTime;

    private String uniqKey;

}
