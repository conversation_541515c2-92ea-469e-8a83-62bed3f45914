package com.deepinnet.spatiotemporalplatform.model.reggov.flow.people;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 网格历史人流和活力指数请求参数
 *
 * <AUTHOR>
 * @version 2024-08-05
 */
@Data
@ApiModel(description = "网格历史人流和活力指数请求URL参数")
public class GridHistoryPeopleFlowParams extends HistoryPeopleFlowParams {

    /**
     * 区块尺⼨，⽀持10*10,100*100,500*500,1000*1000
     * SCALE_10 -(10,"1010⽹格")
     * SCALE_100 -(100,"100100⽹格")
     * SCALE_500 -(500,"500500⽹格")
     * SCALE_1000 -(1000,"10001000⽹格")
     */
    @ApiModelProperty(value = "区块尺⼨", required = true)
    private String gridScale;

}

