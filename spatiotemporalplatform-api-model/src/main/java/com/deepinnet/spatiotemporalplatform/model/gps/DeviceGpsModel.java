package com.deepinnet.spatiotemporalplatform.model.gps;

import lombok.Data;

import java.io.Serializable;

/**
 * 领航车gps消息
 */
@Data
public class DeviceGpsModel implements Serializable {

    /**
     * 定位状态，1表示定位成功
     */
    private Integer orientationStatus;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 速度，单位：米/秒，如：0.0
     */
    private String speed;

    /**
     * userDeviceCode是国标编码
     */
    private String userDeviceCode;

    /**
     * 用户ID，如：1
     */
    private String uid;

    /**
     * 卫星数量，如：4
     */
    private Integer starCount;

    /**
     * 天线状态，如：1
     */
    private Integer antennaStatus;

    /**
     * 角度，单位：度，如：246.0
     */
    private String angle;

    /**
     * 事件，例如 "gps"
     */
    private String event;

    /**
     * 时间戳，表示事件发生的时间，单位：毫秒，如：1724920752008
     */
    private String capTime;

    /**
     * 经度
     */
    private String gpsX;

    /**
     * 纬度
     */
    private String gpsY;

    /**
     * 高度，单位：米
     */
    private String height;

}