package com.deepinnet.spatiotemporalplatform.model.signal;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.enums.SignTypeEnum;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.PostBody;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonArrayResponse;
import lombok.Data;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;

/**
 * 评诊治-路口-通勤路径月维度Request类
 * 
 * <AUTHOR>
 * @version 2025-06-09
 */
@Data
public class CommuterPathMonthlyRequest implements HttpApiRequest {

    /**
     * URL参数
     */
    private CommuterPathMonthlyUrlParams urlParams;

    /**
     * 设置URL参数
     */
    public void setUrlParams(CommuterPathMonthlyUrlParams urlParams) {
        this.urlParams = urlParams;
    }

    @Override
    public UrlParams urlParams() {
        return urlParams;
    }

    @Override
    public String apiUrl() {
        return "https://et-api.amap.com/diagnosis/inter/odInterIndexListM?clientKey=%s&timestamp=%s&adcode=%s&ds=%s&particle=%s&id=%s&hh=%s&timespan=%s&idxState=%s&losType=%s&confidence=%s&interName=%s";
    }

    @Override
    public HttpMethod httpMethod() {
        return HttpMethod.GET;
    }

    @Override
    public HttpHeaders httpHeaders() {
        return null;
    }

    @Override
    public PostBody postBody() {
        return null;
    }

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {};
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<HttpApiJsonArrayResponse<CommuterPathMonthly>>() {};
    }

    @Override
    public SignTypeEnum signType() {
        return SignTypeEnum.DIGEST;
    }

    @Override
    public boolean useTempApiKey() {
        return true;
    }

    public String saveDataTableName() {
        return "commuter_path_monthly";
    }
} 