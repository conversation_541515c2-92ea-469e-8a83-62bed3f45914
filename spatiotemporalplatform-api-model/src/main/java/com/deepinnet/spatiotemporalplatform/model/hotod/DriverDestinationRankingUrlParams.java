package com.deepinnet.spatiotemporalplatform.model.hotod;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 2024-07-29
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DriverDestinationRankingUrlParams extends UrlParams {

    /**
     * ⽬的地区域类型 0.⾃定义区域 1.poi-枢纽 2.poi-景区 3.poi-场馆 4.poi-商圈
     */
    private String types;

    /**
     * poi或⾃定义区域名称
     */
    private String name;

    /**
     * ⽬的地区域ID：⾃定义区域id/poi id
     */
    private String ids;

    /**
     * 默认1
     */
    private Integer pageNum;

    /**
     * 默认值为10，最⼤为200
     */
    private Integer pageSize;

}
