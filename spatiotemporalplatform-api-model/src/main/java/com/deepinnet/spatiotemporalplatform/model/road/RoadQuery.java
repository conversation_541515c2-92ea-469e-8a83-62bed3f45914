package com.deepinnet.spatiotemporalplatform.model.road;

import lombok.Data;

import java.util.List;

/**

 *
 * <AUTHOR>
 * @since 2024-08-29 星期四
 **/
@Data
public class RoadQuery {

    private String name;

    private Integer utcSeconds;

    private List<LinkCoord> linkCoordList;

    @Data
    static class LinkCoord {

        private Double[] coordList;

        private Integer formway;

        private Integer length;

        private Long linkId;

        private String roadName;

        private String roadclass;
    }
}
