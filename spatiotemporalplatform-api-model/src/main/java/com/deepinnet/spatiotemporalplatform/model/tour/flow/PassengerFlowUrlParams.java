package com.deepinnet.spatiotemporalplatform.model.tour.flow;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**

 *
 * <AUTHOR>
 * @since 2024-08-26 星期一
 * #490
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class PassengerFlowUrlParams extends UrlParams {

    /**
     * 定位id
     */
    private String areaId;

    /**
     * 热力total_flow或visit_flow
     * 类型
     *
     * @see com.deepinnet.spatiotemporalplatform.model.tour.enums.FlowTypeEnum
     */
    private String flowType;

    /**
     * 15m：15分钟级数据
     * 60m：60分钟级数据
     */
    private String timeType;

    /**
     * <p>
     *      格式yyyyMMddHHmm
     *      ds15分钟级mm取值为00/15/30/45，
     *      60分钟级mm取值00。
     *      此时间为热力图统计开始时间，如
     *      果传入参数为
     *      timeTpye=60&ds=202004201200，
     *      则表示查询从2020年4月20日
     *      12点00分-2020年4月20日13
     *      点00分的热力数据，注意要在
     *      2020年4月20日13点00分后查
     *      询，否则数据可能因为时间问题导
     *      致不准确
     * </p>
     * <p>
     *      区间查询：yyyyMMddHHmm_yyyyMMddHHmm
     *      total 区 间 查 询 时 指 定 起 止 区
     *      县 。 最 大 支 持 24h 内 的 数 据
     *      示 例 ：
     *      202308311800_202308312100（ 输
     *      出 点 位 2023 年 8 月 31 日 18 时
     *      00 分 到 21 时 00 分 的 去 重 客 流 数
     *      据 ）
     * </p>
     */
    private String ds;
}
