package com.deepinnet.spatiotemporalplatform.model.road.transfer;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @TableName gd_real_road_index
 */
@Data
public class GdRealRoadIndex {
    /**
     *
     */
    private Long id;

    /**
     * 城市code
     */
    private Integer adcode;

    /**
     * 旅⾏时间(单位：秒)
     */
    private Double commuting;

    /**
     * 拥堵⻓度(单位：⽶)
     */
    private Double congestLength;

    /**
     * 道路或路段描述
     */
    private String description;

    /**
     * 自由流速度(单位：⽶/秒)
     */
    private Double freeFlowSpeed;

    /**
     * 高德的道路id
     */
    private String roadId;

    /**
     * 拥堵延时指数值 定义为实际出⾏耗时/⾃由流条件下的出⾏耗时
     */
    private Double idx;

    /**
     * 道路等级道路等级，取值 41000 表示⾼速路(Free Way) 43000 表示城市快速路(Main Street、City Speed way) 42000 表示国道(National Road) 44000 表示主要道路(Main road) 51000 表示省道(Province Road) 45000 表示次要道路(Secondary road) 47000 表示普通道路(Common road) 52000 表示县道(County Road) 53000 表示乡公路(Rural Road) 54000 表示县乡村内部道路(In County Road)
     */
    private Integer majorRoadclass;

    /**
     * 道路名称
     */
    private String name;

    /**
     * 实时速度值(公⾥/⼩时)
     */
    private Double realSpeed;

    /**
     * 道路⻓度(⽶)
     */
    private Integer totalLength;

    /**
     * 道路类型，取值 2 表示道路 5 表示隧道 6 表示桥梁 8 表示⾼速区间分段 29 ⾃定义道路)
     */
    private Integer type;

    /**
     * 时间
     */
    private LocalDateTime timestamp;

    /**
     * 唯一约束
     */
    private String md5;

}