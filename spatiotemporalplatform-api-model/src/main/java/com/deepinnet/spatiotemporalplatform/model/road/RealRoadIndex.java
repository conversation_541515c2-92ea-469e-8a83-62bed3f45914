package com.deepinnet.spatiotemporalplatform.model.road;

import lombok.Data;

/**

 *
 * <AUTHOR>
 * @since 2024-08-28 星期三
 **/
@Data
public class RealRoadIndex {
    /**
     * ⾏政区代码
     */
    private Integer adcode;
    /**
     * 旅⾏时间(单位：秒)
     */
    private Double commuting;
    /**
     * 拥堵⻓度(单位：⽶)
     */
    private Double congestLength;
    /**
     * 道路或路段描述
     */
    private String description;
    /**
     * ⾃由流速度(公⾥/⼩时)
     */
    private Double freeFlowSpeed;
    /**
     * 道路对象id
     */
    private String id;
    /**
     * 拥堵延时指数值 定义为实际出⾏耗时/⾃由流条件下的出⾏耗时
     */
    private Double idx;
    /**
     * 道路等级，取值 41000 表示⾼速路(Free Way)
     * 43000 表示城市快速路(Main Street、City Speed way)
     * 42000 表示国道(National Road) 44000 表示主要道路(Main road)
     * 51000 表示省道(Province Road) 45000 表示次要道路(Secondary road)
     * 47000 表示普通道路(Common road) 52000 表示县道(County Road)
     * 53000 表示乡公路(Rural Road) 54000 表示县乡村内部道路(In County Road)
     */
    private Integer majorRoadclass;
    /**
     * 道路名称
     */
    private String name;
    /**
     * 实时速度值(公⾥/⼩时)
     */
    private Double realSpeed;
    /**
     * 道路⻓度(⽶)
     */
    private Integer totalLength;
    /**
     * 道路类型，取值 2 表示道路 5 表示隧道 6 表示桥梁 8 表示⾼速区间分段 29 ⾃定义道路
     */
    private Integer type;
}
