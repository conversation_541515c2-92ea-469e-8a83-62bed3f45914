package com.deepinnet.spatiotemporalplatform.model.tour.customer;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**

 *
 * <AUTHOR>
 * @since 2024-08-27 星期二
 **/
@Data
public class CustomerFlowDetect implements Serializable {

    private static final long serialVersionUID = -4944861185374843825L;

    @JSONField(name = "charge_cnt")
    private Integer chargeCnt;

    @JSONField(name = "index_data")
    private IndexData indexData;

    @Data
    public static class IndexData {
        private List<Query> query;
    }

    @Data
    public static class Query {
        private String ds;
        private Integer hour;
        private Integer index;
    }
}
