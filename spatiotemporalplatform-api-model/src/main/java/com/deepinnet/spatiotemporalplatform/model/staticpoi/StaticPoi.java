package com.deepinnet.spatiotemporalplatform.model.staticpoi;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 静态核心POI领域模型
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-10
 */
@Data
public class StaticPoi implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * POI ID
     */
    private String poiId;

    /**
     * POI名称
     */
    private String poiName;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 坐标系
     */
    private String coordinateSystem;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 更新时间
     */
    private Date gmtModified;
} 