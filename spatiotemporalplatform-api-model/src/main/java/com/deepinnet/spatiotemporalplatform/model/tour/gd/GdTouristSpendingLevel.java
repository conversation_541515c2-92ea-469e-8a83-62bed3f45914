package com.deepinnet.spatiotemporalplatform.model.tour.gd;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 旅客消费水平
 * @TableName tourist_spending_level
 */
@Data
public class GdTouristSpendingLevel implements Serializable {

    private Long id;

    /**
     * 区域code
     */
    private String areaCode;

    /**
     * 城市code
     */
    private String adCode;

    /**
     * 游客数量
     */
    private Integer count;

    /**
     * 消费水平
     */
    private String level;

    private Date gmtCreated;

    private Date gmtModified;


    private static final long serialVersionUID = 1L;
}