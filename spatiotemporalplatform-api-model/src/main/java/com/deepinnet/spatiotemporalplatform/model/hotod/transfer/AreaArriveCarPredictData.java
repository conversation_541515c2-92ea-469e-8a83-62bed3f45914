package com.deepinnet.spatiotemporalplatform.model.hotod.transfer;

import lombok.Data;

import java.time.LocalDateTime;

/**

 *
 * <AUTHOR>
 * @since 2024-11-18 星期一
 **/
@Data
public class AreaArriveCarPredictData {

    /**
     * ⽬的地区域ID：⾃定义区域id/poi id
     */
    private String objId;

    /**
     * ⽬的地区域名称
     */
    private String name;

    /**
     * 预测时间分布
     */
    private String remainTimeItemList;

    /**
     * 预测距离分布
     */
    private String remainDistanceItemList;

    /**
     * 城市code
     */
    private String adcode;

    /**
     * 批次号
     */
    private String batchId;

    /**
     * 时间戳
     */
    private LocalDateTime timestamp;

    /**
     * 唯一约束
     */
    private String md5;

}
