package com.deepinnet.spatiotemporalplatform.model.reggov.flow.vehicle;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 2024-07-29
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AreaVehicleInUrlParams extends UrlParams {
    /**
     * 自定义区域Id
     */
    private String customAreaId;

//    /**
//     * 用户Key
//     */
//    private String userKey;
}
