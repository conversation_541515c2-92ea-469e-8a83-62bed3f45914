package com.deepinnet.spatiotemporalplatform.model.signal;

import lombok.Data;

import java.io.Serializable;

/**
 * 路口自适应历史数据实体类
 */
@Data
public class IntersectionAdaptiveHistory implements Serializable {
    /** 数据计算批次，单位毫秒 */
    private Long batchTime;
    /** 路口ID */
    private String interId;
    /** 路口名称 */
    private String interName;
    /** 路口主体坐标经度 */
    private Double lng;
    /** 路口主体坐标纬度 */
    private Double lat;
    /** 路口类型，0-主路口 */
    private Integer turnDirNo;
    /** 最大排队长度，单位米 */
    private Double queueLenMax;
    /** 平均排队长度，单位米 */
    private Double queueLenAvg;
    /** 通过路口的全部流量，单位车辆数 */
    private Integer passFlow;
    /** 车均灯前停车时间，单位秒 */
    private Double stopTime;
    /** 车均停车次数，只记录灯前最后一次停车 */
    private Double stopTimes;
    /** 车均通过速度，出上游信控路口到此路口 */
    private Double passSpeed;
    /** 车均不停车通过速度，出上游信控路口到此路口 */
    private Double noStopPassSpeed;
    /** 延误指数，综合考虑停车时间和停车次数 */
    private Double delayIndex;
    /** 路口状态：0-正常、1-失衡、2-溢出、3-失衡溢出 */
    private Integer idxState;
    /** 评级：A-F，A为最好，F为最差 */
    private String los;
    /** 置信度 1：低，2：中，3：高 */
    private Integer confidence;
    /** 行政区ADCODE */
    private Integer district;
    /** 城市ADCODE */
    private Integer adcode;
    /** 指定日期 yyyymmdd */
    private String ds;
    /** 聚合颗粒度：minute5/minute10/hour */
    private String timeGrading;
    /** 路口分方向详情 */
    private String directionInterIndex;

    private static final long serialVersionUID = 1L;
} 