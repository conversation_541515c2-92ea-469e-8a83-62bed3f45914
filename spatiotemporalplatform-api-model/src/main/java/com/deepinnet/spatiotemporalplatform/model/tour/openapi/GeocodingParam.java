package com.deepinnet.spatiotemporalplatform.model.tour.openapi;

import com.deepinnet.spatiotemporalplatform.common.constants.OpenApiConstant;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * C<PERSON> zeng<PERSON>
 * Date 2024-12-03
 **/

@EqualsAndHashCode(callSuper = true)
@Data
public class GeocodingParam extends UrlParams {

    /**
     * 用户key
     */
    private String key = OpenApiConstant.USER_KEY;
    /**
     * 结构化地址信息:省份＋城市＋区县＋城镇＋乡村＋街道＋门牌号码
     */
    private String address;
    /**
     * 查询城市，可选：城市中文、中文全拼、citycode、adcode
     * */
    private String city;
}
