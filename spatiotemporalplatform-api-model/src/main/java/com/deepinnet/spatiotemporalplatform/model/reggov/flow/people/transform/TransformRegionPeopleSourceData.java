package com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.transform;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 人流来源地数据
 *
 * <AUTHOR>
 * @version 2024-08-05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransformRegionPeopleSourceData {

    /**
     * id（城市、区县、乡镇的编码或百⽶/千⽶⽹格的id）
     */
    private String objId;

    /**
     * obj类型 1:城市；2:区县；3:乡镇或街道；4:千⽶⽹
     * 格；5:百⽶⽹格
     */
    private Integer objType;

    /**
     * 名称
     */
    private String objName;

    /**
     * 历史人流量均值
     */
    private String objGeo;

    /**
     * 流入意愿(0-1)
     */
    private String wish;

    /**
     * 流⼊意愿环⽐(0-1)
     */
    private String withRatio;

    /**
     * 流量等级（1:0~1w; 2:1w~3w; 3:3w+）
     */
    private Integer routeLevel;

    /**
     * 时间戳 yyyyMMddHHmm
     */
    private String dt;

    /**
     * md5
     */
    private String md5;
}
