package com.deepinnet.spatiotemporalplatform.model.road;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.GlobalConstant;
import com.deepinnet.spatiotemporalplatform.common.constants.RoadConstant;
import com.deepinnet.spatiotemporalplatform.common.request.BaseSignParams;
import com.deepinnet.spatiotemporalplatform.common.request.DigestSignParams;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;

/**

 *
 * <AUTHOR>
 * @since 2024-08-28 星期三
 * 返回的是文件地址 需调用方自行处理！！！
 **/
@Data
public class RoadLinkFlowRequest implements HttpApiRequest {

    private RoadLinkFlowUrlParams urlParams;

    @Override
    public TypeReference responseBodyType() {
        return null;
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<String>() {};
    }

    @Override
    public boolean useTempApiKey() {
        return false;
    }

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public String apiUrl() {
        return RoadConstant.ROAD_LINK_FLOW_API_URL;
    }

}
