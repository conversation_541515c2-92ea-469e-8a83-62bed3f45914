package com.deepinnet.spatiotemporalplatform.model.skyflow;

import lombok.Data;
import org.locationtech.jts.geom.LineString;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 空域领域模型
 * 航道和空域现在都在 airspace 表
 * rangeType 1-航道，2-空域。暂时只有 1,2 的数据
 * width 宽度，单位千米
 * wkt1 线，rangeType 为1时有值，业务理解为航道的线
 * wkt2 面，rangeType 为 1,2 时都有值，业务理解为航道或空域的面，为 1 时是通过 wkt1 加上 width 缓冲区获得
 * origin_wkt 存原始点数据，业务上不用这个字段
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@Data
public class Airspace implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 空域id
     */
    private String airspaceId;

    /**
     * 空域代码
     */
    private String code;

    /**
     * 空域id
     */
    private String businessCode;

    /**
     * 名称
     */
    private String name;

    /**
     * 使用状态，开放、关闭、限制
     */
    private String status;

    /**
     * 空间三维
     */
    private LineString wkt3;

    /**
     * 二维WKT
     */
    private String wkt2;

    /**
     * 最小高度
     */
    private Integer minHeight;

    /**
     * 最大高度
     */
    private Integer maxHeight;

    /**
     * @see com.deepinnet.spatiotemporalplatform.skyflow.enums.YuanFeiAirSpaceRangeTypeEnum
     */
    private String rangeType;

    /**
     * 原始航道线
     */
    private String wkt1;

    /**
     * 航线长度
     */
    private Integer length;

    /**
     * 起飞地坐标
     */
    private Point beginAddr;

    /**
     * 降落地坐标
     */
    private Point endAddr;

    /**
     * 航线宽度 单位：千米
     */
    private Integer width;

    /**
     * 航线高度
     */
    private Integer height;

    /**
     * 半径
     */
    private Integer radius;


    /**
     * 形状、长方体、圆柱体、不规则体
     */
    private Integer shapeType;

    /**
     * 1.公有空域2.私有空域
     */
    private String privateType;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 来源
     * */
    private String source;

    /**
     * 面积，平方千米
     * */
    private Double area;
} 