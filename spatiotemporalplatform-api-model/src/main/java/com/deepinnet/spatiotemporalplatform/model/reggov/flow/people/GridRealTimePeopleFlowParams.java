package com.deepinnet.spatiotemporalplatform.model.reggov.flow.people;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 网格实时人流和活力指数请求参数
 *
 * <AUTHOR>
 * @version 2024-08-05
 */
@Data
@ApiModel(description = "网格实时人流和活力指数请求URL参数")
public class GridRealTimePeopleFlowParams extends RealTimePeopleFlowParams {

    @ApiModelProperty(value = "页码")
    private int page = 1;

    @ApiModelProperty(value = "每页返回数据数量")
    private int size = 100;

}

