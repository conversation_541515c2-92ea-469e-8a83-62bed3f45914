package com.deepinnet.spatiotemporalplatform.model.park;

import com.deepinnet.spatiotemporalplatform.common.request.PostBody;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 停车场数据
 *
 * <AUTHOR>
 * @version 2024-08-19
 */
@Data
@ApiModel(description = "第三方停车场数据")
public class ThirdPartyParkingData extends PostBody {

    @ApiModelProperty(value = "停车场ID", example = "PARK12345", required = true)
    private String parkId;

    @ApiModelProperty(value = "停车场名称", example = "Central Park", required = true)
    private String parkName;

    @ApiModelProperty(value = "停车场类型（例如接驳停车场、普通停车场）", example = "normal")
    private String parkType;

    @ApiModelProperty(value = "停车场地址", example = "衢州市柯城区国金中心", required = true)
    private String parkAddress;

    @ApiModelProperty(value = "停车场所在区域", example = "衢州市柯城区")
    private String parkArea;

    @ApiModelProperty(value = "停车场状态（例如开放、关闭）", example = "Open", required = true)
    private String parkStatus;

    @ApiModelProperty(value = "停车场经度", example = "123.456789", required = true)
    private String parkLongitude;

    @ApiModelProperty(value = "停车场纬度", example = "98.7654321", required = true)
    private String parkLatitude;

    @ApiModelProperty(value = "开放时间", example = "08:00", required = true)
    private String openTime;

    @ApiModelProperty(value = "关闭时间", example = "22:00", required = true)
    private String closeTime;

    @ApiModelProperty(value = "停车场总车位数", example = "100", required = true)
    private int totalSpaces;

    @ApiModelProperty(value = "空余车位数", example = "25", required = true)
    private int AvailableSpaces;

    @ApiModelProperty(value = "车位信息", example = "", required = true)
    private List<BerthStatus> berthStatusList;

}
