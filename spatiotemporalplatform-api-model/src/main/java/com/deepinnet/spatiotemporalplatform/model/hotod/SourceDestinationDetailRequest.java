package com.deepinnet.spatiotemporalplatform.model.hotod;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.HotOdConstant;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonResponse;
import com.deepinnet.spatiotemporalplatform.model.hotod.transfer.SourceDestinationDetailData;
import com.deepinnet.spatiotemporalplatform.model.util.MD5Util;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.stream.Collectors;

/**

 *
 * <AUTHOR>
 * @since 2024-11-18 星期一
 **/
@Data
public class SourceDestinationDetailRequest implements HttpApiRequest {

    private SourceDestinationDetailUrlParams urlParams;

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {
        };
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<HttpApiJsonResponse<SourceDestinationDetail>>() {
        };
    }

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public boolean useTempApiKey() {
        return true;
    }

    @Override
    public String apiUrl() {
        return HotOdConstant.SOURCE_DESTINATION_DETAIL_URL;
    }

    @Override
    public String saveDataTableName() {
        return "gd_source_destination_detail_data";
    }

    @Override
    public Object transformData(Object responseData) {
        SourceDestinationDetail sourceDestinationDetail = (SourceDestinationDetail) responseData;
        if (ObjectUtil.isEmpty(sourceDestinationDetail) || CollectionUtil.isEmpty(sourceDestinationDetail.getList())) {
            return null;
        }
        String adcode = urlParams.getAdcode();
        LocalDateTime now = LocalDateTime.now().withNano(0);
        String batchId = IdUtil.getSnowflakeNextIdStr();
        return sourceDestinationDetail.getList().stream().map(sourceDestinationDetailData -> {
            SourceDestinationDetailData destinationDetailData = new SourceDestinationDetailData();
            destinationDetailData.setAdcode(adcode);
            destinationDetailData.setSourceId(sourceDestinationDetailData.getSourceId());
            destinationDetailData.setSourceName(sourceDestinationDetailData.getSourceName());
            destinationDetailData.setSourceCenter(sourceDestinationDetailData.getSourceCenter());
            destinationDetailData.setFlow(sourceDestinationDetailData.getFlow());
            destinationDetailData.setBatchId(batchId);
            destinationDetailData.setTimestamp(now);
            destinationDetailData.setMd5(MD5Util.getObjectMD5(destinationDetailData));
            return destinationDetailData;
        }).collect(Collectors.toList());
    }
}
