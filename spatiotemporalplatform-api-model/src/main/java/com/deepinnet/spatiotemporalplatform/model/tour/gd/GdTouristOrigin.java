package com.deepinnet.spatiotemporalplatform.model.tour.gd;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 游客来源地
 * @TableName tourist_origin
 */
@Data
public class GdTouristOrigin implements Serializable {

    private Long id;

    /**
     * 区域code
     */
    private String areaCode;

    /**
     * 城市code
     */
    private String adCode;

    /**
     * 行政区划名称
     */
    private String regionName;

    /**
     * 行政区划code
     */
    private String regionCode;

    /**
     * 游客数量
     */
    private Integer count;

    /**
     * 区域类型
     */
    private String type;

    /**
     * 数据范围
     */
    private String scope;

    private Date gmtCreated;

    private Date gmtModified;


    private static final long serialVersionUID = 1L;
}