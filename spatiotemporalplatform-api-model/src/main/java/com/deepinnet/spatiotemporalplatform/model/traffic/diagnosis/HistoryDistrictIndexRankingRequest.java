package com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.DistrictIndexConstant;
import com.deepinnet.spatiotemporalplatform.common.constants.GlobalConstant;
import com.deepinnet.spatiotemporalplatform.common.request.BaseSignParams;
import com.deepinnet.spatiotemporalplatform.common.request.DigestSignParams;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonArrayResponse;
import lombok.Data;

/**
 * 历史区域交通指数排行API请求
 *
 * <AUTHOR>
 * @version 2024-07-29
 */
@Data
public class HistoryDistrictIndexRankingRequest implements HttpApiRequest {
    /**
     * URL参数
     */
    private HistoryDistrictIndexRankingUrlParams urlParams;

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public boolean useTempApiKey() {
        return false;
    }

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {
        };
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<HttpApiJsonArrayResponse<HistoryDistrictIndexRanking>>() {
        };
    }

    @Override
    public String apiUrl() {
        return DistrictIndexConstant.HISTORY_DISTRICT_INDEX_RANKING_API_URL;
    }
}
