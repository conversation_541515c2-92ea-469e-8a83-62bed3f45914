package com.deepinnet.spatiotemporalplatform.model.tour.group;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**

 *
 * <AUTHOR>
 * @since 2024-08-26 星期一
 * #504
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class GroupFlowDetectUrlParams extends UrlParams {

    /**
     * 定位id
     */
    private String areaId;

    /**
     * 查询开始时间 如20200405
     */
    private Integer startDate;

    /**
     * 查询结束时间 如20200410
     */
    private Integer endDate;

    /**
     * total_flow或visit_flow
     * 类型
     *
     * @see com.deepinnet.spatiotemporalplatform.model.tour.enums.FlowTypeEnum
     */
    private String flowType;

    /**
     * hour|day|week|month
     *
     * @see com.deepinnet.spatiotemporalplatform.model.tour.enums.TimeTypeEnum
     */
    private String timeType;

    /**
     * 洞察维度 sex_dist,uv
     * 同时入参多个维度，以英
     * 文逗号分隔
     *
     * @see com.deepinnet.spatiotemporalplatform.model.tour.enums.ProfileEnum
     */
    private String profile;
}
