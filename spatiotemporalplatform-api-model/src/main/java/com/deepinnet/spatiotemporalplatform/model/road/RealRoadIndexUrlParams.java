package com.deepinnet.spatiotemporalplatform.model.road;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**

 *
 * <AUTHOR>
 * @since 2024-08-28 星期三
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class RealRoadIndexUrlParams extends UrlParams {

    /**
     * ⾏政区代码
     */
    private String adcode;

    /**
     * 道路类型 取值2
     */
    private Integer type;

    /**
     * 道路长度
     */
    private String roadLength;

    /**
     * 道路等级，取值 41000 表示⾼速路(Free Way)
     * 43000 表示城市快速路(Main Street、City Speed way)
     * 42000 表示国道(National Road) 44000 表示主要道路(Main road)
     * 51000 表示省道(Province Road) 45000 表示次要道路(Secondary road)
     * 47000 表示普通道路(Common road) 52000 表示县道(County Road)
     * 53000 表示乡公路(Rural Road) 54000 表示县乡村内部道路(In County Road)
     */
    private Integer roadClass;

    /**
     * 区县级行政规划代码
     */
    private String district;

    /**
     * 道路ID 多个用“,”分割
     */
    private String ids;

    /**
     * 返回数据条数 最大200
     */
    private Integer size;

}
