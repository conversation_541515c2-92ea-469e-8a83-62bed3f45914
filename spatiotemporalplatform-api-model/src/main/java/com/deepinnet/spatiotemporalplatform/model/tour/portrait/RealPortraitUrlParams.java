package com.deepinnet.spatiotemporalplatform.model.tour.portrait;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**

 *
 * <AUTHOR>
 * @since 2024-08-26 星期一
 * #507
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class RealPortraitUrlParams extends UrlParams {

    /**
     * 定位id
     */
    private String areaId;

    /**
     * 热力total_flow或visit_flow
     * 类型
     *
     * @see com.deepinnet.spatiotemporalplatform.model.tour.enums.FlowTypeEnum
     */
    private String flowType;

    /**
     * 15m：15分钟级数据
     * 60m：60分钟级数据
     */
    private String timeType;

    /**
     * 格式yyyyMMddHHmm
     * ds15分钟级mm取值为00/15/30/45，
     * 60分钟级mm取值00。
     * 此时间为热力图统计开始时间，如
     * 果传入参数为
     * timeTpye=60&ds=202004201200，
     * 则表示查询从2020年4月20日
     * 12点00分-2020年4月20日13
     * 点00分的热力数据，注意要在
     * 2020年4月20日13点00分后查
     * 询，否则数据可能因为时间问题导
     * 致不准确
     */
    private Long ds;

    /**
     * 画像维度
     * u_sex|u_occupation|u_age|u_constellation|property_level|adcode|u_permanent_adcode
     */
    private String profile;
}
