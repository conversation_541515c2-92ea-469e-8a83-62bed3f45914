package com.deepinnet.spatiotemporalplatform.model.util;

import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * <AUTHOR> wong
 * @create 2024/9/11 13:59
 * @Description
 */
public class GaoDeUtil {

    private static final String COLL_SPLIT_SYMBOL = "\u0002";

    private static final String KV_SPLIT_SYMBOL = "\u0003";

    private static final String BLANK = " ";

    /**
     * 高德部分接口返回数据处理方法
     * 高德多个集合之间根据 \u0002来分割，每个集合里面的kv根据\u0003来分割
     * 举例：25-34\u000313\u000245-54\u00039\u000235-44\u00039\u0002>=55\u00038\u000219-24\u00036\u0002-1\u0003119
     *
     * @param data
     * @return
     */
    public static Map<String, String> parseGaoDeData2MapWithEscape(String data) {
        if (StringUtils.isBlank(data)) {
            return null;
        }

        Map<String, String> dataMap = new HashMap<>();
        String[] splitList = data.split(COLL_SPLIT_SYMBOL);
        for (String list : splitList) {
            if (StringUtils.isBlank(list)) {
                continue;
            }

            String[] item = list.split(KV_SPLIT_SYMBOL);
            dataMap.put(item[0], item[1]);
        }

        return dataMap;
    }
}
