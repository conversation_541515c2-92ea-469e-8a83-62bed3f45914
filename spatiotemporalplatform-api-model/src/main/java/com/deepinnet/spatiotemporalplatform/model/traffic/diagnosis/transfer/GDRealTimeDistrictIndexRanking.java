package com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis.transfer;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 实时区域交通指数排行
 *
 * <AUTHOR>
 * @version 2024-07-29
 */
@Data
public class GDRealTimeDistrictIndexRanking {
    /**
     * 行政区代码
     */
    private int adcode;

    /**
     * 区域描述
     */
    private String description;

    /**
     * 自由流速度（公里/小时）
     */
    private Double freeFlowSpeed;

    /**
     * 区域ID
     */
    private String areaId;

    /**
     * 拥堵延时指数
     * 定义为实际出行耗时/自由流条件下的出行耗时
     */
    private Double idx;

    /**
     * 区域名称
     */
    private String name;

    /**
     * 实时速度（公里/小时）
     */
    private Double realSpeed;

    /**
     * 区域类型
     *
     * @value 0 城市
     * @value 1 高德中心城区行政区域
     * @value 3 高德商圈
     * @value 4 国家行政区域
     */
    private int type;

    private LocalDateTime timestamp;

    private String uniqueKey;

}
