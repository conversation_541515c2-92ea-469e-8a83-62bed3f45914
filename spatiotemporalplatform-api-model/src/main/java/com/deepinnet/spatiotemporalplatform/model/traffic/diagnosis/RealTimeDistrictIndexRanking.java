package com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 实时区域交通指数排行
 *
 * <AUTHOR>
 * @version 2024-07-29
 */
@Data
public class RealTimeDistrictIndexRanking {
    /**
     * 行政区代码
     */
    @ApiModelProperty(value = "行政区划代码")
    private int adcode;

    /**
     * 区域描述
     */
    @ApiModelProperty(value = "区域描述")
    private String description;

    /**
     * 自由流速度（公里/小时）
     */
    @ApiModelProperty(value = "自由流速度（公里/小时），即在无交通拥堵情况下车辆可以达到的最高速度")
    private String freeFlowSpeed;

    /**
     * 区域ID
     */
    @ApiModelProperty(value = "区域ID")
    private String id;

    /**
     * 拥堵延时指数
     * 定义为实际出行耗时/自由流条件下的出行耗时
     */
    @ApiModelProperty(value = "拥堵延时指数，定义为实际出行耗时/自由流条件下的出行耗时，数值越大，拥堵越严重")
    private String idx;

    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称")
    private String name;

    /**
     * 实时速度（公里/小时）
     */
    @ApiModelProperty(value = "实时速度（公里/小时），即当前车辆的平均行驶速度")
    private String realSpeed;

    /**
     * 区域类型
     * @value 0 城市
     * @value 1 高德中心城区行政区域
     * @value 3 高德商圈
     * @value 4 国家行政区域
     */
    @ApiModelProperty(value = "区域类型", allowableValues = "0, 1, 3, 4")
    private int type;
}
