package com.deepinnet.spatiotemporalplatform.model.intelligence;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 管控情报查询DTO
 *
 * <AUTHOR>
 */
@Data
public class ControlIntelligenceQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页数量
     */
    private Integer pageSize = 10;

    /**
     * 发布时间起始
     */
    private LocalDateTime publishTimeStart;

    /**
     * 发布时间截止
     */
    private LocalDateTime publishTimeEnd;
} 