package com.deepinnet.spatiotemporalplatform.model.area;

import com.deepinnet.spatiotemporalplatform.common.response.HttpOpenApiResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * Description:
 * Date: 2025/5/24
 * Author: lijunheng
 */
@Data
public class PoiKeywordResponse extends HttpOpenApiResponse {

    /**
     * 搜索方案数目
     */
    private String count;
    /**
     * 返回状态说明，status为0时，info返回错误原因，否则返回“OK”。详情参阅info状态表
     */
    private String info;
    private String infocode;
    /**
     * 搜索POI信息列表
     */
    private List<Pois> pois;
    /**
     * 结果状态值，值为0或1，0：请求失败；1：请求成功
     */
    private String status;
//    /**
//     * 城市建议列表，当搜索的文本关键字在限定城市中没有返回时会返回建议城市列表；
//     */
//    private Suggestion suggestion;


    @Data
    public static class Pois implements Serializable {
//        /**
//         * 区域编码， extensions=all时返回
//         */
//        private String adcode;
//        /**
//         * 地址，东四环中路189号百盛北门
//         */
//        private String address;
//        /**
//         * 区域名称，区县级别的返回，例如朝阳区
//         */
//        private String adname;
//        /**
//         * 别名， extensions=all时返回
//         */
//        private List<String> alias;
//        /**
//         * 深度信息， extensions=all时返回
//         */
////        private BizExt biz_ext;
//        /**
//         * 行业类型
//         */
//        private List<String> biz_type;
//        /**
//         * 所属商圈， extensions=all时返回
//         */
//        private List<String> business_area;
//        private List<Child> children;
////        private Childtype childtype;
//        /**
//         * 城市编码， extensions=all时返回
//         */
//        private String citycode;
//        /**
//         * 城市名， 若是直辖市的时候，此处直接显示市名，例如北京市
//         */
//        private String cityname;
//        /**
//         * 优惠信息数目，此字段逐渐废弃
//         */
//        private String discount_num;
//        /**
//         * 离中心点距离
//         */
//        private List<String> distance;
//        /**
//         * POI的电子邮箱， extensions=all时返回
//         */
//        private List<String> email;
//        /**
//         * POI的入口经纬度， extensions=all时返回，也可用作于POI的到达点；
//         */
////        private Childtype entr_location;
//        private List<String> event;
//        /**
//         * POI的出口经纬度，目前不会返回内容；
//         */
//        private List<String> exit_location;
//        /**
//         * 地理格ID， extensions=all时返回
//         */
//        private String gridcode;
//        /**
//         * 团购数据，此字段逐渐废弃
//         */
//        private String groupbuy_num;
//        /**
//         * 唯一ID
//         */
//        private String id;
//        private List<String> importance;
//        /**
//         * 室内地图相关数据， 当indoor_map=0时，字段为空   extensions=all时返回
//         */
//        private IndoorData indoor_data;
//        /**
//         * 是否有室内地图标志，1，表示有室内相关数据  0，代表没有室内相关数据   extensions=all时返回
//         */
//        private String indoor_map;
        /**
         * 经纬度，格式：X,Y
         */
        private String location;
//        private String match;
        /**
         * 名称
         */
        private String name;
//        /**
//         * POI导航id， extensions=all时返回
//         */
////        private Childtype navi_poiid;
//        /**
//         * 父POI的ID，当前POI如果有父POI，则返回父POI的ID。可能为空
//         */
////        private Childtype parent;
//        /**
//         * 停车场类型， 仅在停车场类型POI的时候显示该字段  展示停车场类型，包括：地下、地面、路边   extensions=all的时候显示
//         */
//        private String parking_type;
//        /**
//         * POI所在省份编码， extensions=all时返回
//         */
//        private String pcode;
//        /**
//         * 照片相关信息，extensions=all时返回
//         */
//        private List<Photo> photos;
//        /**
//         * POI所在省份名称，若是直辖市的时候，此处直接显示市名，例如北京市
//         */
//        private String pname;
//        private List<String> poiweight;
//        /**
//         * 邮编， extensions=all时返回
//         */
//        private List<String> postcode;
//        private String recommend;
//        private List<String> shopid;
//        private String shopinfo;
//        /**
//         * 该POI的特色内容，主要出现在美食类POI中，代表特色菜  例如“烤鱼,麻辣香锅,老干妈回锅肉”  extensions=all时返回
//         */
//        private List<String> tag;
//        /**
//         * POI的电话
//         */
////        private Childtype tel;
//        private String timestamp;
//        /**
//         * 兴趣点类型， 顺序为大类、中类、小类  例如：餐饮服务;中餐厅;特色/地方风味餐厅
//         */
//        private String type;
//        /**
//         * 兴趣点类型编码，例如：050118
//         */
//        private String typecode;
//        /**
//         * POI的网址， extensions=all时返回
//         */
//        private List<String> website;
    }


    /**
     * 深度信息， extensions=all时返回
     */
//    @Data
//    public static class BizExt {
//        /**
//         * 人均消费，仅存在于餐饮、酒店、景点、影院类POI之下
//         */
//        private List<String> cost;
//        /**
//         * 评分，仅存在于餐饮、酒店、景点、影院类POI之下
//         */
//        private List<String> rating;
//    }


    @Data
    public static class Child implements Serializable {
        private String address;
        private String distance;
        private String id;
        private String location;
        private String name;
        private String sname;
        private String subtype;
        private String typecode;
    }

    /**
     * POI的入口经纬度， extensions=all时返回，也可用作于POI的到达点；
     * <p>
     * POI导航id， extensions=all时返回
     * <p>
     * 父POI的ID，当前POI如果有父POI，则返回父POI的ID。可能为空
     * <p>
     * POI的电话
     */
//    @Data
//    public static class Childtype implements Serializable {
//        public String stringValue;
//        public List<String> anythingArrayValue;
//    }


    /**
     * 室内地图相关数据， 当indoor_map=0时，字段为空   extensions=all时返回
     */
    @Data
    public static class IndoorData implements Serializable {
        private List<String> cmsid;
        /**
         * 当前POI的父级POI，如果当前POI为建筑物类POI，则cpid为自身POI ID；如果当前POI为商铺类POI，则cpid为其所在建筑物的POI ID
         */
        private List<String> cpid;
        /**
         * 楼层索引，一般会用数字表示，例如8
         */
        private List<String> floor;
        /**
         * 所在楼层，一般会带有字母，例如F8
         */
        private List<String> truefloor;
    }


    @Data
    public static class Photo implements Serializable {
        /**
         * 图片介绍
         */
        private List<String> title;
        /**
         * 具体链接
         */
        private String url;
    }


    /**
     * 城市建议列表，当搜索的文本关键字在限定城市中没有返回时会返回建议城市列表；
     */
    @Data
    public static class Suggestion implements Serializable {
        /**
         * 城市列表
         */
        private List<City> cities;
        /**
         * 关键字
         */
        private List<String> keywords;
    }


    @Data
    public static class City implements Serializable {
        /**
         * 该城市的adcode
         */
        private String adcode;
        /**
         * 该城市的citycode
         */
        private String citycode;
        /**
         * 名称
         */
        private String name;
        /**
         * 该城市包含此关键字的个数
         */
        private String num;
    }
}
