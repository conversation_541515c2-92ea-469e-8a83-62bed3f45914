package com.deepinnet.spatiotemporalplatform.model.skyflow;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 雷达领域模型
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@Data
public class Radar implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 代码
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 海拔位置
     */
    private Integer alt;

    /**
     * 探测高度
     */
    private Integer height;

    /**
     * 探测半径
     */
    private Integer radius;

    /**
     * 坐标
     */
    private String point;

    /**
     * 地址
     */
    private String address;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否停用
     */
    private String isDisabled;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 部门IDs
     */
    private String departIds;
} 