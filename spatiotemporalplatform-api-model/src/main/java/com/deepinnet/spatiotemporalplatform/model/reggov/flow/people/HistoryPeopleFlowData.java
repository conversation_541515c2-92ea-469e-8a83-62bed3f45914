package com.deepinnet.spatiotemporalplatform.model.reggov.flow.people;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 历史人流
 *
 * <AUTHOR>
 * @version 2024-07-30
 */
@Data
@ApiModel(value = "历史人流")
public class HistoryPeopleFlowData {

    /**
     * 自定义区域ID
     */
    @ApiModelProperty(value = "自定义区域ID")
    private String customAreaId;

    /**
     * 时间，格式根据时间粒度决定
     * <p>
     *     小时：yyyy-MM-dd HH
     * </p>
     * <p>
     *     天：yyyy-MM-dd
     * </p>
     * <p>
     *     周：yyyy-MM-dd 取周一为一周的第一天
     * </p>
     * <p>
     *     月: yyyy-MM
     * </p>
     * 总和, time为null
     */
    @ApiModelProperty(value = "时间，格式根据时间粒度决定")
    private String time;

    /**
     * 人流量
     */
    @ApiModelProperty(value = "人流量")
    private Integer peopleFlow;

    /**
     * 人流活力指数
     */
    @ApiModelProperty(value = "人流活力指数")
    private String activityIndex;

    /**
     * 人流饱和指数
     */
    @ApiModelProperty(value = "人流饱和指数")
    private String saturationIndex;
}
