package com.deepinnet.spatiotemporalplatform.model.skyflow;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName oss_file_info
 */
@Data
public class OssFileInfo implements Serializable {

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private Long fileVersion;

    /**
     * 上一个版本
     */
    @ApiModelProperty(value = "上一个版本")
    private Long fileLastVersion;

    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名")
    private String fileName;

    /**
     * 文件地址
     */
    @ApiModelProperty(value = "文件地址")
    private String fileUrl;


    private static final long serialVersionUID = 1L;

}