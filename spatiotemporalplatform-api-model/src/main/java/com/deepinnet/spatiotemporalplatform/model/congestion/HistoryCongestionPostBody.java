package com.deepinnet.spatiotemporalplatform.model.congestion;

import com.deepinnet.spatiotemporalplatform.common.request.PostBody;
import lombok.Data;

import java.util.List;

/**
 * 历史拥堵情况查询post请求body
 *
 * <AUTHOR>
 * @version 2024-07-29
 */
@Data
public class HistoryCongestionPostBody extends PostBody {
    /**
     * 行政区划代码列表
     */
    private List<String> adCodes;
    /**
     * 起始时间戳
     */
    private Long startTime;
    /**
     * 结束时间戳
     */
    private Long endTime;
    /**
     * WKT ⾃定义区域
     */
    private List<String> areas;
    /**
     * 要查询的⽇⼦：
     * 0-星期⼀
     * 1-星期⼆
     * 2-星期三
     * 3-星期四
     * 4-星期五
     * 5-星期六
     * 6-星期⽇
     */
    private List<String> weekDays;

    /**
     * 排序规则：
     * 1.最新
     * 2.时⻓
     * 3.距离
     */
    private int order;

    /**
     * 查询页码
     */
    private int pageNum = 1;

    /**
     * 每页条数
     */
    private int pageSize = 30;

    /**
     * 查询列表
     */
    private List<HistoryCongestionQuery> queryList;

    /**
     * 查询项
     */
    @Data
    public static class HistoryCongestionQuery {
        /**
         * 获取指定时⻓的拥堵信息，
         * 单位：分钟
         */
        private int duration;

        /**
         * 获取指定等级道路的预警
         * 信息参数：
         * 0 ⾼速公路
         * 1 国道
         * 3
         * 4. 返回参数/Return parameter
         * 1 国道
         * 2 省道
         * 3 县道
         * 4 乡公路
         * 5 县乡村内部道路
         * 6 主要⼤街、城市快速道
         * 7 主要道路
         * 8 次要道路
         * 9 普通道路
         * 10 ⾮导航道路
         */
        private int roadType;

        /**
         * 单位“⽶”
         * 拥堵预警距离定义值
         */
        private int distance;

        /**
         * 道路拥堵概率值，⼩于
         * 0,02定义为异常拥堵
         */
        private String congestRate;

        /**
         * 拥堵信息类型
         * 0.常规拥堵
         * 1.异常拥堵
         */
        private int congestType;
    }
}
