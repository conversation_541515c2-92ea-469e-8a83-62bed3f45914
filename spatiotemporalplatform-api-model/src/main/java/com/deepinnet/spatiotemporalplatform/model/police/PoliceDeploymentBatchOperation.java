package com.deepinnet.spatiotemporalplatform.model.police;

import com.deepinnet.spatiotemporalplatform.model.police.PoliceDeploymentDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 警力部署表
 * @TableName police_deployment
 */
@Data
public class PoliceDeploymentBatchOperation implements Serializable {

    /**
     * 批量新增
     */
    @ApiModelProperty(value = "批量新增列表")
    private List<PoliceDeploymentDTO> batchSaveList;

    /**
     * 批量删除
     */
    @ApiModelProperty(value = "批量删除的id列表")
    private List<Integer> batchDeleteList;

    /**
     * 批量更新
     */
    @ApiModelProperty(value = "批量更新列表")
    private List<PoliceDeploymentDTO> batchUpdateList;

    private static final long serialVersionUID = 1L;

}