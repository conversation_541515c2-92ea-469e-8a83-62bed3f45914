package com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.transform;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 2024-08-05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransformGridRealTimePeopleFlow {

    /**
     * 网格中心点坐标
     */
    private String gridCenter;

    /**
     * 网格名称
     */
    private String gridName;

    /**
     * 自定义区域id
     */
    private String customAreaId;

    /**
     * 人流量
     */
    private int peopleFlow;

    /**
     * 网格人流密度（每平方米人流），小数
     */
    private String density;

    /**
     * 活力指数，小数
     */
    private String activityIndex;

    /**
     * 历史人流量均值
     */
    private String historyAvgFlow;

    /**
     * 历史人流量最大值
     */
    private String maxFlow;

    /**
     * ⽇期,格式:yyyy-MM-dd HH:mm:ss
     */
    private String ds;

    /**
     * md5
     */
    private String md5;

}
