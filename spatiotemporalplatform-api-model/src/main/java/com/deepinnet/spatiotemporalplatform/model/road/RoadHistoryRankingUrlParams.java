package com.deepinnet.spatiotemporalplatform.model.road;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**

 *
 * <AUTHOR>
 * @since 2024-12-20 星期五
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class RoadHistoryRankingUrlParams extends UrlParams {

    /**
     * 时间范围，格式为startdate@enddate。⽇期
     * 格式为"yyyy-MM-dd"，例如"2021-01-01@2021-02-01"。当粒度为⽉或季度时，date是该⽉或季度的⾸⽇
     */
    private String dateRangeList;

    /**
     * 时间段列表，多个⽤','隔开，all 表示所有时段
     * mpeak 表示早⾼峰(7-9)epeak 表示晚⾼峰(17-19)
     * non-peak 表示⾼峰除外(6-21)
     * evening 表示(22-23)
     * night  表示(0-5)
     */
    private String timePeriodList;

    /**
     * ⽇期类型，多个⽤','隔开
     * workday 表示⼯作⽇weekend 表示周末
     */
    private String dayType;

    /**
     * 时间粒度。
     * quarter-季度month-⽉
     * day-天
     * hour-⼩时
     * dayofweek-星期⼏
     */
    private String timeGrading;

    /**
     * ⾃定义⼩时，多个⽤','隔开。
     * 当timeGrading取值为day或者dayofweek，⽀持此参数。
     */
    private String customHour;

    /**
     * 道路类型，取值2
     */
    private String type = "2";

    /**
     * 道路⻓度(单位：⽶)
     */
    private String roadLength;

    /**
     * 道路等级，多个⽤","分隔，取值
     * 41000 表示⾼速路(Free Way)
     * 43000 表示城市快速路(Main Street、CitySpeed way)
     * 42000 表示国道(National Road)44000 表示主要道路(Main road)51000 表示省道(Province Road)
     * 45000 表示次要道路(Secondary road)47000 表示普通道路(Common road)52000 表示县道(County Road)
     * 53000 表示乡公路(Rural Road)
     * 54000 表示县乡村内部道路(In CountyRoad)
     */
    private String roadClass;

    /**
     * 区县级⾏政区划代码（三级⾏政区划）
     */
    private String district;

    /**
     * 道路id，多个⽤","分隔 。
     */
    private String ids;

    /**
     * 返回数据条数
     */
    private Integer size;

}
