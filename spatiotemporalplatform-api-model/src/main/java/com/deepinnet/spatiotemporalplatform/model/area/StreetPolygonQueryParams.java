package com.deepinnet.spatiotemporalplatform.model.area;

import com.deepinnet.spatiotemporalplatform.common.constants.OpenApiConstant;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Description:
 * Date: 2025/4/29
 * Author: lijunheng
 */
@Data
public class StreetPolygonQueryParams extends UrlParams implements Serializable {

    private String key = OpenApiConstant.USER_KEY;

    /**
     * 区县代码，6位，仅支持区县
     * 与 streetid 二选一必填，同时传入则取交集
     */
    @ApiModelProperty("区县代码，6位，仅支持区县")
    private String adcode;

    /**
     * 乡镇区划代码，12位
     * 与 adcode 二选一必填，同时传入则取交集
     */
    @ApiModelProperty("乡镇区划代码，12位")
    private Long streetid;

    /**
     * 关键词，根据关键词模糊查询
     * 与 adcode 或 streetid 求交集
     */
    @ApiModelProperty("关键词，根据关键词模糊查询")
    private Long keywords;

    /**
     * 是否返回形状
     * 0 为不返回，1 为返回
     * 默认 0
     */
    @ApiModelProperty("是否返回形状, 0 为不返回，1 为返回, 默认 0")
    private Integer showPolygon = 0;

    /**
     * 页码数，最小为 1
     * 默认 1
     */
    @ApiModelProperty("页码数，最小为 1, 默认 1")
    private Integer pageNum = 1;

    /**
     * 每页个数，最小 1，最大 20
     * 默认 20
     */
    @ApiModelProperty("每页个数，最小 1，最大 20, 默认 20")
    private Integer pageSize = 20;

    /**
     * 数字签名
     * 选择数字签名认证的用户必填
     */
    @ApiModelProperty("数字签名")
    private String sig;
}
