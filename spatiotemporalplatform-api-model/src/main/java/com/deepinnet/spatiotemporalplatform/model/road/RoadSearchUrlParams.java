package com.deepinnet.spatiotemporalplatform.model.road;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**

 *
 * <AUTHOR>
 * @since 2024-08-28 星期三
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class RoadSearchUrlParams extends UrlParams {

    /**
     * ⾏政区代码
     */
    private String adcode;

    private String types;

    private String name;
}
