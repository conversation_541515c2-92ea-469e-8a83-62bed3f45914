package com.deepinnet.spatiotemporalplatform.model.monitor;

import com.alibaba.fastjson2.annotation.JSONField;
import com.deepinnet.spatiotemporalplatform.common.constants.GlobalConstant;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**

 *
 * <AUTHOR>
 * @since 2024-12-03 星期二
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class MonitorUrlParams extends UrlParams {

    /**
     * 应用名称
     */
    private String applicationName = GlobalConstant.SPATIOTEMPORAL_PLATFORM;

    /**
     * 请求环境
     */
    private String env;

    /**
     * 时间
     */
    private Long monitorTime = System.currentTimeMillis();

    // 监控类型 子类实现
    public String getMonitorType() {
        return "";
    }

    @Override
    @JSONField(serialize = false)
    public Boolean getIsPersistence() {
        return false;
    }
}
