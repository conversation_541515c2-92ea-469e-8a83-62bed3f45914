package com.deepinnet.spatiotemporalplatform.model.reggov.flow.people;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.DistrictIndexConstant;
import com.deepinnet.spatiotemporalplatform.common.constants.GlobalConstant;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonResponse;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.transform.TransformRegionPeopleSourceData;
import com.deepinnet.spatiotemporalplatform.model.util.MD5Util;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 来源地请求参数
 *
 * <AUTHOR>
 * @version 2024-07-29
 */
@Data
public class RegionPeopleSourceFlowRequest implements HttpApiRequest {
    /**
     * URL参数
     */
    private RegionPeopleSourceFlowParams urlParams;

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<HttpApiJsonResponse<RegionPeopleSourceFlowData>>() {
        };
    }

    @Override
    public String apiUrl() {
        return DistrictIndexConstant.PEOPLE_FLOW_SOURCE_AREA_URL;
    }

    @Override
    public String saveDataTableName() {
        return "gd_region_people_source";
    }

    @Override
    public Object transformData(Object responseData) {

        if (ObjectUtil.isNull(responseData)) {
            return null;
        }

        RegionPeopleSourceFlowData sourceFlow = (RegionPeopleSourceFlowData) responseData;

        List<RegionPeopleSourceData> sourceList = sourceFlow.getList();

        if (CollUtil.isEmpty(sourceList)) {
            return null;
        }

        return sourceList.stream().map(source -> TransformRegionPeopleSourceData.builder()
                .objId(source.getObjId())
                .objType(source.getObjType())
                .objName(source.getObjName())
                .objGeo(source.getObjGeo())
                .wish(source.getWish())
                .withRatio(source.getWithRatio())
                .routeLevel(source.getRouteLevel())
                .dt(source.getDt())
                .md5(MD5Util.getObjectMD5(source)).build()).collect(Collectors.toList());
    }
}
