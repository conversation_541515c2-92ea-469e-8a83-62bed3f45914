package com.deepinnet.spatiotemporalplatform.model.dify;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * <PERSON><PERSON> zeng<PERSON>ui
 * Date 2025-01-22
 **/

@Data
public class CompleteFireAlarmAddressesResult {

    private String district;

    private String location;

    @JSONField(alternateNames = {"model_return_address", "modelReturnAddress"})
    private String modelReturnAddress;

    @JSONField(alternateNames = {"matched_address", "matchedAddress"})
    private String matchedAddress;

    @JSONField(alternateNames = {"model_similarity_result", "modelSimilarityResult"})
    private double modelSimilarityResult;

    @JSONField(alternateNames = {"user_input_similarity_result", "userInputSimilarityResult"})
    private double userInputSimilarityResult;
}
