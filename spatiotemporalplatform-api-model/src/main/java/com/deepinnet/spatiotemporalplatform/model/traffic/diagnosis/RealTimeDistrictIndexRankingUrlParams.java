package com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;

/**
 * 实时区域交通指数查询URL参数
 *
 * <AUTHOR>
 * @version 2024-07-29
 */
@Data
public class RealTimeDistrictIndexRankingUrlParams extends UrlParams {

    /**
     * 授权城市ADCODE.
     */
    private String adcode;

    /**
     * 区域类型，多个','隔开.
     * 0 - 城市
     * 1 - 高德中心城区行政区域
     * 3 - 高德商圈
     * 4 - 国家行政区域
     */
    private String types;

    /**
     * 是否返回区域，默认false.
     */
    private boolean withArea;

    /**
     * 区级⾏政区划代码
     */
    private String district;

    /**
     * 区域对象id，多个用','分割.
     */
    private String ids;

    /**
     * 一次查询返回的数据条数
     */
    private int size = 200;

}
