package com.deepinnet.spatiotemporalplatform.model.tour.group;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**

 *
 * <AUTHOR>
 * @since 2024-08-27 星期二
 **/
@Data
public class GroupFlowDetect implements Serializable {

    private static final long serialVersionUID = -8691061077096043226L;

    @JSONField(name = "charge_cnt")
    private int chargeCnt;
    @JSONField(name = "index_data")
    private IndexData indexData;

    @Data
    public static class IndexData {
        private List<Query> query;
    }

    @Data
    public static class Query {
        @JSONField(name = "loc_day")
        private String locDay;
        private Integer hour;
        @JSONField(name = "sex_dist")
        private String sexDist;
        private String uv;
        @JSONField(name = "pmnt_prov_dist")
        private String pmntProvDist;
        @JSONField(name = "pmnt_city_dist")
        private String pmntCityDist;

        @JSONField(name = "proplevel_dist")
        private String proplevelDist;

        @JSONField(name = "age_dist")
        private String ageDist;

        @JSONField(name = "occupation_dist")
        private String occupationDist;
    }
}
