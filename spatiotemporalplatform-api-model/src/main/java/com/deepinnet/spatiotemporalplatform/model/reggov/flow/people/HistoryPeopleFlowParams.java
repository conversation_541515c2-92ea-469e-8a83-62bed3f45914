package com.deepinnet.spatiotemporalplatform.model.reggov.flow.people;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 历史区域人流请求参数
 *
 * <AUTHOR>
 * @version 2024-07-30
 */
@Data
@ApiModel(value = "区域历史人数查询参数")
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class HistoryPeopleFlowParams extends UrlParams {

    /**
     * 自定义区域ID
     */
    @ApiModelProperty(value = "自定义区域ID")
    private String customAreaId;

    /**
     * 时间粒度，可选值：HOUR, DAY, WEEK, MONTH, TOTAL
     */
    @ApiModelProperty(value = "时间粒度，可选值：HOUR, DAY, WEEK, MONTH, TOTAL")
    private String timeGrading;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始⽇期，yyyy-MM-dd")
    private String startDate;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束⽇期，yyyy-MM-dd")
    private String endDate;

    /**
     * 客流类型, TOTAL_FLOW-全部; VISIT_FLOW-访客;
     */
    @ApiModelProperty(value = "客流类型, TOTAL_FLOW-全部; VISIT_FLOW-访客;")
    private String flowType;
}
