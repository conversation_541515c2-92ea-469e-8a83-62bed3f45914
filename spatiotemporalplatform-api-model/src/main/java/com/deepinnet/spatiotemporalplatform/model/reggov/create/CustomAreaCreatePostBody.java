package com.deepinnet.spatiotemporalplatform.model.reggov.create;

import com.deepinnet.spatiotemporalplatform.common.request.PostBody;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 2024-07-31
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "自定义区域新建请求Body参数")
public class CustomAreaCreatePostBody extends PostBody {
    /**
     * 用户key
     */
    @ApiModelProperty(value = "用户key")
    private String userKey;

    /**
     * 城市编码
     */
    @ApiModelProperty(value = "城市编码", required = true)
    private String adcode;

    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称", required = true)
    private String name;

    /**
     * 人流区域wkt
     */
    @ApiModelProperty(value = "人流区域wkt", required = true)
    private String geo;

    /**
     * 车流区域wkt，如果不传，默认使用人流区域
     */
    @ApiModelProperty(value = "车流区域wkt，如果不传，默认使用人流区域")
    private String geoBig;

    /**
     * 动态计算指标类型，多个进行组合加和
     * 例如：1-实时人流, 2-实时车流流入通道, 8-历史人流, 16-人流流入通道
     */
    @ApiModelProperty(value = "动态计算指标类型，多个进行组合加和", required = true)
    private Integer dynamicCalType;

    /**
     * 历史人流计算开始时间，格式为yyyy-MM-dd，只有计算历史人流时才需要
     */
    @ApiModelProperty(value = "历史人流计算开始时间，格式为yyyy-MM-dd")
    private String peopleFlowHistoryStartDate;

    /**
     * 历史人流计算结束时间，格式为yyyy-MM-dd，只有计算历史人流时才需要
     */
    @ApiModelProperty(value = "历史人流计算结束时间，格式为yyyy-MM-dd")
    private String peopleFlowHistoryEndDate;
}
