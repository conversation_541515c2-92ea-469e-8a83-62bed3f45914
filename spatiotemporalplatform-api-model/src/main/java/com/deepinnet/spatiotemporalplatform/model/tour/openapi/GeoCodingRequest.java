package com.deepinnet.spatiotemporalplatform.model.tour.openapi;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.OpenApiConstant;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;

/**
 * Creator zengjuerui
 * Date 2024-12-03
 **/

@Data
public class GeoCodingRequest implements HttpApiRequest {

    private GeocodingParam urlParams;

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {
        };
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<Geocoding>() {
        };
    }

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public String apiUrl() {
        return OpenApiConstant.GEOCODE_URL;
    }
}
