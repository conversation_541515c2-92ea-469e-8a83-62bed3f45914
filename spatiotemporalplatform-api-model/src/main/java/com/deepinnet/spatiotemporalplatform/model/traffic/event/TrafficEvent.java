package com.deepinnet.spatiotemporalplatform.model.traffic.event;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 交通事件数据对象
 *
 * <AUTHOR>
 * @version 2024-07-30
 */
@Data
public class TrafficEvent {
    /**
     * 事件标题
     */
    @ApiModelProperty(value = "事件标题")
    private String brief;

    /**
     * 事件预计结束时间
     */
    @ApiModelProperty(value = "事件预计结束时间")
    private String endTime;

    /**
     * 事件描述
     */
    @ApiModelProperty(value = "事件描述")
    private String eventDesc;

    /**
     * 事件id
     */
    @ApiModelProperty(value = "事件id")
    private String eventID;

    /**
     * 事件类型码
     */
    @ApiModelProperty(value = "事件类型码")
    private String eventType;

    /**
     * 是否高速，1-是, 0-否
     */
    @ApiModelProperty(value = "是否高速，1-是, 0-否")
    private int expressway;

    /**
     * 线路坐标
     */
    @ApiModelProperty(value = "线路坐标")
    private String lines;

    /**
     * 发布方名称
     */
    @ApiModelProperty(value = "发布方名称")
    private String nickName;

    /**
     * 是否权威发布，0-官方, 1-权威, 2-UGC
     */
    @ApiModelProperty(value = "是否权威发布，0-官方, 1-权威, 2-UGC")
    private int offcial;

    /**
     * 事件图片链接
     */
    @ApiModelProperty(value = "事件图片链接")
    private String picture;

    /**
     * 道路名称
     */
    @ApiModelProperty(value = "道路名称")
    private String roadName;

    /**
     * 数据源编号
     */
    @ApiModelProperty(value = "数据源编号")
    private int source;

    /**
     * 事件开始时间
     */
    @ApiModelProperty(value = "事件开始时间")
    private String startTime;

    /**
     * 事件最后更新时间
     */
    @ApiModelProperty(value = "事件最后更新时间")
    private String updateTime;

    /**
     * 经度坐标
     */
    @ApiModelProperty(value = "经度坐标")
    private String x;

    /**
     * 纬度坐标
     */
    @ApiModelProperty(value = "纬度坐标")
    private String y;

    /**
     * 城市代码
     */
    private String adcode;
}