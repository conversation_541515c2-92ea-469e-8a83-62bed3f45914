package com.deepinnet.spatiotemporalplatform.model.road;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.RoadConstant;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonArrayResponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-08-28 星期三
 **/
@Data
public class RealRoadIndexAllRequest extends RealRoadIndexRequest{

    private RealRoadIndexUrlParams urlParams;

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {
        };
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<HttpApiJsonArrayResponse<RealRoadIndex>>() {
        };
    }

    @Override
    public boolean useTempApiKey() {
        return false;
    }

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public String apiUrl() {
        return RoadConstant.REAL_ROAD_INDEX_ALL_API_URL;
    }
}
