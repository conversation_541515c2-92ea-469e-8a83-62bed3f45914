package com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;

/**
 * 实时路⼝评价请求参数
 *
 * <AUTHOR>
 * @version 2024-07-30
 */
@Data
public class RealTimeIntersectionEvaluationUrlParams extends UrlParams {
    /**
     * 授权城市ADCODE
     */
    private String adcode;

    /**
     * 指定路口ID，支持多个，逗号分割
     */
    private String interIds;

    /**
     * 数据时间错 yyyyMMddHHmm
     */
    private String ds;

    /**
     * 页大小，最大100
     */
    private int pageSize = 100;

    /**
     * 页号，默认1
     */
    private int pageNum = 1;
}
