package com.deepinnet.spatiotemporalplatform.model.tour.openapi;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * C<PERSON> zengjuerui
 * Date 2024-12-03
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GeoCode {

    private String formatted_address;

    private String location;

    private String country;

    private String province;

    private String citycode;

    private String city;

    private String district;

    private String adcode;

    private String street;

    private String number;

    private String level;
}
