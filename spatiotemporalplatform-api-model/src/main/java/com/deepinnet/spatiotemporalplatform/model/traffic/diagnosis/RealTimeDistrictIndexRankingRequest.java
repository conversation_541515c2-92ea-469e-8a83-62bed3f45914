package com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.DistrictIndexConstant;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonArrayResponse;
import com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis.transfer.GDRealTimeDistrictIndexRanking;
import com.deepinnet.spatiotemporalplatform.model.util.MD5Util;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 实时区域交通指数TOP排行API请求
 *
 * <AUTHOR>
 * @version 2024-07-29
 */
@Data
public class RealTimeDistrictIndexRankingRequest implements HttpApiRequest {
    /**
     * URL参数
     */
    private RealTimeDistrictIndexRankingUrlParams urlParams;

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {
        };
    }

    @Override
    public boolean useTempApiKey() {
        return false;
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<HttpApiJsonArrayResponse<RealTimeDistrictIndexRanking>>() {
        };
    }

    @Override
    public String apiUrl() {
        return DistrictIndexConstant.REAL_TIME_DISTRICT_INDEX_RANKING_API_URL;
    }

    @Override
    public String saveDataTableName() {
        return "gd_real_time_district_index_ranking";
    }

    @Override
    @SuppressWarnings("all")
    public Object transformData(Object responseData) {
        List<RealTimeDistrictIndexRanking> realTimeDistrictIndexRankings = (List<RealTimeDistrictIndexRanking>) responseData;
        LocalDateTime now = LocalDateTime.now().withNano(0);
        return realTimeDistrictIndexRankings.stream().map(realTimeDistrictIndexRanking -> {
            GDRealTimeDistrictIndexRanking gdRealTimeDistrictIndexRanking = BeanUtil.copyProperties(realTimeDistrictIndexRanking, GDRealTimeDistrictIndexRanking.class);
            gdRealTimeDistrictIndexRanking.setAreaId(realTimeDistrictIndexRanking.getId());
            gdRealTimeDistrictIndexRanking.setTimestamp(now);
            gdRealTimeDistrictIndexRanking.setUniqueKey(MD5Util.getObjectMD5(gdRealTimeDistrictIndexRanking));
            return gdRealTimeDistrictIndexRanking;
        }).collect(Collectors.toList());
    }
}
