package com.deepinnet.spatiotemporalplatform.model.link;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;

/**
 * 交通路况查询
 *
 * <AUTHOR>
 * @version 2025-07-09
 */
@Data
public class AreaJsonPubQueryRequest implements HttpApiRequest {

    private AreaPubUrlParams urlParams;

    @Override
    public TypeReference responseBodyType() {
        return null;
    }


    /**
     * 是否使用临时的apiKey
     */
    public boolean useTempApiKey() {
        return false;
    }

    @Override
    public UrlParams urlParams() {
        return urlParams;
    }

    @Override
    public String apiUrl() {
        return "http://et-api.amap.com/state/areaJSONPub?clientKey=%s&timestamp=%s&adcode=%s&pubname=%s";
    }
}
