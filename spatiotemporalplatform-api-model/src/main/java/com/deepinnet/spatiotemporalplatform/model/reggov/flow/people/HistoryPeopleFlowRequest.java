package com.deepinnet.spatiotemporalplatform.model.reggov.flow.people;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.DistrictIndexConstant;
import com.deepinnet.spatiotemporalplatform.common.constants.GlobalConstant;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonArrayResponse;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.transform.TransformHistoryPeopleFlowData;
import com.deepinnet.spatiotemporalplatform.model.util.MD5Util;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 历史人流数据请求
 *
 * <AUTHOR>
 * @version 2024-07-29
 */
@Data
public class HistoryPeopleFlowRequest implements HttpApiRequest {
    /**
     * URL参数
     */
    private HistoryPeopleFlowParams urlParams;

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<HttpApiJsonArrayResponse<HistoryPeopleFlowData>>() {
        };
    }

    @Override
    public String apiUrl() {
        return DistrictIndexConstant.HISTORY_DISTRICT_PEOPLE_FLOW_API_URL;
    }

    @Override
    public String saveDataTableName() {
        return "gd_history_people_flow";
    }

    @Override
    public Object transformData(Object responseData) {

        if (ObjectUtil.isNull(responseData)) {
            return null;
        }

        List<HistoryPeopleFlowData> historyPeopleFlowDataList = (List<HistoryPeopleFlowData>) responseData;

        if (CollUtil.isEmpty(historyPeopleFlowDataList)) {
            return null;
        }

        return historyPeopleFlowDataList.stream().map(flow -> TransformHistoryPeopleFlowData.builder()
                .customAreaId(flow.getCustomAreaId())
                .time(flow.getTime())
                .peopleFlow(flow.getPeopleFlow())
                .activityIndex(flow.getActivityIndex())
                .saturationIndex(flow.getSaturationIndex())
                .md5(MD5Util.getObjectMD5(flow)).build()).collect(Collectors.toList());
    }
}
