package com.deepinnet.spatiotemporalplatform.model.tour.portrait;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**

 *
 * <AUTHOR>
 * @since 2024-08-27 星期二
 **/
@Data
public class RealPortrait {

    @JSONField(name = "charge_cnt")
    private Integer chargeCnt;
    @JSONField(name = "index_data")
    private IndexData indexData;

    @Data
    public static class IndexData {
        private List<Query> query;
    }

    @Data
    public static class Query {
        private int val;
        private int index;
    }
}
