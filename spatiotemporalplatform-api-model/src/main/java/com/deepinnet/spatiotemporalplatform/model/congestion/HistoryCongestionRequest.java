package com.deepinnet.spatiotemporalplatform.model.congestion;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.CongestionConstant;
import com.deepinnet.spatiotemporalplatform.common.constants.GlobalConstant;
import com.deepinnet.spatiotemporalplatform.common.request.*;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonResponse;
import lombok.Data;
import org.springframework.http.HttpMethod;

/**
 * 历史拥堵信息API请求
 *
 * <AUTHOR>
 * @version 2024-07-29
 */
@Data
public class HistoryCongestionRequest implements HttpApiRequest {
    /**
     * URL参数
     */
    private CongestionUrlParams urlParams;

    /**
     * post请求body
     */
    private HistoryCongestionPostBody postBody;

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public PostBody postBody() {
        return this.postBody;
    }

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<HttpApiJsonResponse<CongestionData>>() {
        };
    }

    @Override
    public boolean useTempApiKey() {
        return false;
    }

    /**
     * 请求方式：POST
     *
     * @return
     */
    public HttpMethod httpMethod() {
        return HttpMethod.POST;
    }

    @Override
    public String apiUrl() {
        return CongestionConstant.HISTORY_CONGESTION_API_URL;
    }

}
