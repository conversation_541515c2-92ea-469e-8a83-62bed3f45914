package com.deepinnet.spatiotemporalplatform.model.camera;

import com.deepinnet.spatiotemporalplatform.model.common.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Creator zengjuerui
 * Date 2024-07-31
 **/

@EqualsAndHashCode(callSuper = true)
@Data
public class SurveillanceCameraCondition extends PageQuery {

    private String wkt;

    private Integer distance;

    private List<String> typeList;

    private String keyword;
}
