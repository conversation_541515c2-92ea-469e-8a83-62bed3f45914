package com.deepinnet.spatiotemporalplatform.model.signal;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 评诊治-图形-路段历史路况数据接口URL参数类
 * 
 * <AUTHOR>
 * @version 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class HistoryRidIndex10minUrlParams extends UrlParams {
    /**
     * 行政区
     */
    private String district;

    /**
     * 进口道id（必填）
     */
    private String rid;

    /**
     * 开始日期（必填）
     * 格式：yyyyMMdd
     */
    private String startDs;

    /**
     * 结束日期（必填）
     * 格式：yyyyMMdd，间隔最长31天
     */
    private String endDs;
} 