package com.deepinnet.spatiotemporalplatform.model.tour.openapi;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;

import static com.deepinnet.spatiotemporalplatform.common.constants.OpenApiConstant.INPUT_TIPS_URL;

/**
 * <PERSON><PERSON> zengjuerui
 * Date 2025-07-28
 **/

@Data
public class InputTipsRequest implements HttpApiRequest {

    private InputTipsParam param;

    @Override
    public TypeReference<String> responseBodyType() {
        return new TypeReference<>() {};
    }

    @Override
    public TypeReference<InputTips> deserializeType() {
        return new TypeReference<>() {};
    }

    @Override
    public UrlParams urlParams() {
        return this.param;
    }

    @Override
    public String apiUrl() {
        return INPUT_TIPS_URL;
    }
}
