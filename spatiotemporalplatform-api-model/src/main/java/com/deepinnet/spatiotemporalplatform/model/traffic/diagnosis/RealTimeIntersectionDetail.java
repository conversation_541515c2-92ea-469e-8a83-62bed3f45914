package com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis;

import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2024-07-30
 */
@Data
public class RealTimeIntersectionDetail {

    /**
     * 路口整体信息
     */
    @ApiModelProperty(value = "路口整体信息")
    private MainIntersectionIndex mainInterIndex;

    /**
     * 路口分方向详情
     */
    @ApiModelProperty(value = "路口分方向详情")
    private List<DirectionIntersectionIndex> directionInterIndex;

    @Data
    public static class MainIntersectionIndex {
        /**
         * 数据计算批次时间（秒）
         */
        @ApiModelProperty(value = "数据计算批次时间（秒）")
        private String batchTime;
        /**
         * 数据时间戳 yyyyMMddHHmm
         */
        private String ds;

        /**
         * 路口ID
         */
        @ApiModelProperty(value = "路口ID")
        private String interId;

        /**
         * 路口名称
         */
        @ApiModelProperty(value = "路口名称")
        private String interName;

        /**
         * 路段名称
         */
        @ApiModelProperty(value = "路段名称")
        private String ridName;

        /**
         * 路口纬度
         */
        @ApiModelProperty(value = "路口纬度")
        private String lat;

        /**
         * 路口经度
         */
        @ApiModelProperty(value = "路口经度")
        private String lng;

        /**
         * 路口类型（0-主路口）
         */
        @ApiModelProperty(value = "路口类型（0-主路口）", allowableValues = "0")
        private int turnDirNo;

        /**
         * 排队长度（米）
         */
        @ApiModelProperty(value = "排队长度（米）")
        private String queueLen;

        @ApiModelProperty(value = "平均排队长度（米）")
        private String queueLenAvg;

        /**
         * 平均停车时间
         */
        @ApiModelProperty(value = "平均停车时间")
        private String stopTime;

        @ApiModelProperty(value = "平均⻋辆通过路口时间，单位秒")
        private String passTime;

        @ApiModelProperty(value = "分方向道路等级")
        private String level;

        /**
         * 平均停车次数/次
         */
        @ApiModelProperty(value = "平均停车次数/次")
        private String stopTimes;

        /**
         * 延误指数（综合考虑路口延误和停车次数）
         */
        @ApiModelProperty(value = "延误指数（综合考虑路口延误和停车次数）")
        private String delayIndex;

        /**
         * 路口状态（0-正常、1-失衡、2-溢出、3-失衡溢出）
         */
        @ApiModelProperty(value = "路口状态（0-正常、1-失衡、2-溢出、3-失衡溢出）", allowableValues = "0, 1, 2, 3")
        private int idxState;

        /**
         * 服务水平（A-F，A为最好，F为最差）
         */
        @ApiModelProperty(value = "服务水平（A-F，A为最好，F为最差）", allowableValues = "A, B, C, D, E, F")
        private String los;

        /**
         * 置信度（0-1）
         */
        @ApiModelProperty(value = "置信度（0-1）")
        private String confidence;

        /**
         * 城市行政区划代码
         */
        @ApiModelProperty(value = "城市行政区划代码")
        private String adcode;

        /**
         * 分方向⻋道信息
         */
        private LaneInfoVO laneInfoVO;

    }

    @Data
    public static class DirectionIntersectionIndex {

        /**
         * 数据计算批次时间（秒）
         */
        @ApiModelProperty(value = "数据计算批次时间（秒）")
        private String batchTime;

        /**
         * 路口ID
         */
        @ApiModelProperty(value = "路口ID")
        private String interId;

        /**
         * 路口名称
         */
        @ApiModelProperty(value = "路口名称")
        private String interName;

        /**
         * 路段名称
         */
        @ApiModelProperty(value = "路段名称")
        private String ridName;

        /**
         * 路口状态（0-正常、1-失衡、2-溢出、3-失衡溢出）
         */
        @ApiModelProperty(value = "路口状态（0-正常、1-失衡、2-溢出、3-失衡溢出）", allowableValues = "0, 1, 2, 3")
        private int idxState;

        /**
         * 分方向路段坐标序列（经纬度对，逗号分隔）
         */
        @ApiModelProperty(value = "分方向路段坐标序列（经纬度对，逗号分隔）")
        private String lngLatSeq;

        @ApiModelProperty(value = "平均排队长度（米）")
        private String queueLenAvg;

        /**
         * 平均停车时间
         */
        @ApiModelProperty(value = "平均停车时间")
        private String stopTime;

        @ApiModelProperty(value = "平均⻋辆通过路口时间，单位秒")
        private String passTime;

        @ApiModelProperty(value = "分方向道路等级")
        private String level;

        /**
         * 分方向类型（1-左转、2-直行、3-右转、4-调头）
         */
        @ApiModelProperty(value = "分方向类型（1-左转、2-直行、3-右转、4-调头）", allowableValues = "1, 2, 3, 4")
        private int turnDirNo;

        /**
         * 分方向排队长度（米）
         */
        @ApiModelProperty(value = "分方向排队长度（米）")
        private String queueLen;

        /**
         * 分方向平均停车次数/次
         */
        @ApiModelProperty(value = "分方向平均停车次数/次")
        private String stopTimes;

        /**
         * 分方向延误指数（综合考虑路口延误和停车次数）
         */
        @ApiModelProperty(value = "分方向延误指数（综合考虑路口延误和停车次数）")
        private String delayIndex;

        /**
         * 分方向服务水平（A-F，A为最好，F为最差）
         */
        @ApiModelProperty(value = "分方向服务水平（A-F，A为最好，F为最差）", allowableValues = "A, B, C, D, E, F")
        private String los;

        /**
         * 分方向置信度（0-1）
         */
        @ApiModelProperty(value = "分方向置信度（0-1）")
        private String confidence;

        /**
         * 城市行政区划代码
         */
        @ApiModelProperty(value = "城市行政区划代码")
        private String adcode;

        /**
         * 分方向⻋道信息
         */
        private LaneInfoVO laneInfoVO;
    }


    /**
     * 车道信息VO
     */
    @Data
    public static class LaneInfoVO {
        /**
         * 车道数量
         */
        private Integer laneNum;

        /**
         * 车道方向数组
         */
        private String[] laneDirs;
    }
}
