package com.deepinnet.spatiotemporalplatform.model.area;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 乡镇/街道界查询API响应结构
 * Date: 2025/4/29
 * Author: lijunheng
 */
@Data
public class StreetPolygonData implements Serializable {

    /**
     * 信息
     */
    private StreetPolygonInfo info;

    /**
     * 列表数据
     */
    private List<StreetPolygonRespDTO> list;

    @Data
    public static class StreetPolygonInfo implements Serializable {
        /**
         * 返回数量
         */
        private Integer count;
    }
} 