package com.deepinnet.spatiotemporalplatform.model.reggov.flow.vehicle;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 区域车辆流入通道数据
 *
 * <AUTHOR>
 * @version 2024-07-30
 */
@Data
public class AreaVehicleInLink {
    /**
     * 与区域相交的link
     */
    @ApiModelProperty(value = "与区域相交的link")
    private String crossLinkId;

    /**
     * 与区域相交的link坐标,wkt格式
     */
    @ApiModelProperty(value = "与区域相交的link坐标,wkt格式")
    private String crossLinkGeo;

    /**
     * 与区域相交的link方向描述。如:南向北
     */
    @ApiModelProperty(value = "与区域相交的link方向描述。如:南向北")
    private String crossLinkDirectionDesc;

    /**
     * 相交link所在道路名字
     */
    @ApiModelProperty(value = "相交link所在道路名字")
    private String crossLinkName;

    /**
     * 车流量
     */
    @ApiModelProperty(value = "车流量")
    private int flow;

    /**
     * 车流量占比
     */
    @ApiModelProperty(value = "车流量占比")
    private String flowRate;

    /**
     * 进入区域的links,多个逗号分割
     */
    @ApiModelProperty(value = "进入区域的links,多个逗号分割")
    private String preLinksId;

    /**
     * 进入区域的link坐标,wkt格式
     */
    @ApiModelProperty(value = "进入区域的link坐标,wkt格式")
    private String preLinksGeo;

    /**
     * 出区域的links,多个逗号分割
     */
    @ApiModelProperty(value = "出区域的links,多个逗号分割")
    private String nextLinksId;

    /**
     * 出区域的link坐标,wkt格式
     */
    @ApiModelProperty(value = "出区域的link坐标,wkt格式")
    private String nextLinksGeo;

}
