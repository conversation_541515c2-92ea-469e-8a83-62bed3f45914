package com.deepinnet.spatiotemporalplatform.model.skyflow;

import lombok.Data;

@Data
public class GdFlightDetection {

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    private String id;

    /**
     * 真高
     */
    private Double height;

    private String bigtype;

    private String middletype;

    private String direction;

    /**
     * 海拔
     */
    private Double altitude;

    /**
     * 航迹角（0 ≤ dir < 360, -1 表示未知）
     */
    private Integer dir;

    /**
     * 无人机编号
     */
    private String osid;

    /**
     * 无人机类型
     */
    private Integer uatype;

    /**
     * 秒级 Unix 时间戳
     */
    private Integer timestamp;

    /**
     * 水平速度 (m/s)
     */
    private Integer speedh;

    /**
     * 垂直速度 (m/s)
     */
    private Integer speedv;

    private String status;

    /**
     * 无人机型号
     */
    private String model;
}