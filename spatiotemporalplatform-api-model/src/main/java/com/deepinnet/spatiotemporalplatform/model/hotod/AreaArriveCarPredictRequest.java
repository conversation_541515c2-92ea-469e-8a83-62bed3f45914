package com.deepinnet.spatiotemporalplatform.model.hotod;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.HotOdConstant;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonArrayResponse;
import com.deepinnet.spatiotemporalplatform.model.hotod.transfer.AreaArriveCarPredictData;
import com.deepinnet.spatiotemporalplatform.model.util.MD5Util;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**

 *
 * <AUTHOR>
 * @since 2024-08-28 星期三
 **/
@Data
public class AreaArriveCarPredictRequest implements HttpApiRequest {

    private AreaArriveCarPredictUrlParams urlParams;

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {
        };
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<HttpApiJsonArrayResponse<AreaArriveCarPredict>>() {
        };
    }

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public boolean useTempApiKey() {
        return true;
    }

    @Override
    public String apiUrl() {
        return HotOdConstant.AREA_ARRIVE_CAR_PREDICT_URL;
    }

    @Override
    @SuppressWarnings("all")
    public Object transformData(Object responseData) {
        String adcode = urlParams().getAdcode();
        LocalDateTime now = LocalDateTime.now().withNano(0);
        String batchId = IdUtil.getSnowflakeNextIdStr();
        List<AreaArriveCarPredict> areaArriveCarPredicts = (List<AreaArriveCarPredict>) responseData;
        if (CollectionUtil.isEmpty(areaArriveCarPredicts)) {
            return null;
        }
        return areaArriveCarPredicts.stream().map(areaArriveCarPredict -> {
            AreaArriveCarPredictData areaArriveCarPredictData = new AreaArriveCarPredictData();
            areaArriveCarPredictData.setObjId(areaArriveCarPredict.getId());
            areaArriveCarPredictData.setName(areaArriveCarPredict.getName());
            areaArriveCarPredictData.setRemainTimeItemList(JSONUtil.toJsonStr(areaArriveCarPredict.getRemainTimeItemList()));
            areaArriveCarPredictData.setRemainDistanceItemList(JSONUtil.toJsonStr(areaArriveCarPredict.getRemainDistanceItemList()));
            areaArriveCarPredictData.setBatchId(batchId);
            areaArriveCarPredictData.setAdcode(adcode);
            areaArriveCarPredictData.setTimestamp(now);
            areaArriveCarPredictData.setMd5(MD5Util.getObjectMD5(areaArriveCarPredictData));
            return areaArriveCarPredictData;
        }).collect(Collectors.toList());
    }

    @Override
    public String saveDataTableName() {
        return "gd_area_arrive_car_predict_data";
    }
}
