package com.deepinnet.spatiotemporalplatform.model.signal;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 智慧交通-实时路口-自适应信控路口数据服务 URL参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IntersectionAdaptiveSignalUrlParams extends UrlParams {
    /**
     * 区县编码
     */
    private String district;
    /** 指定路口ID，多个英文逗号分隔 */
    private String interIds;
    /** 路口评级，A-F */
    private String losType;
    /** 置信度，1低2中3高，支持多个 */
    private String confidence;
    /** 异常状态，0正常1失衡2溢出，支持多个 */
    private String idxState;
    /** 排序指标 */
    private String orderIndex;
    /** 升序降序，1升序2降序 */
    private Integer orderType;
    /** 页大小，默认100，最大1000 */
    private Integer pageSize;
    /** 页号，默认1 */
    private Integer pageNum;
} 