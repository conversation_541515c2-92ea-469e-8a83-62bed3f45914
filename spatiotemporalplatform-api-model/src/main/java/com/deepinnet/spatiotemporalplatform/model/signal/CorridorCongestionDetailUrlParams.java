package com.deepinnet.spatiotemporalplatform.model.signal;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 评诊治-通勤-走廊拥堵详情接口URL参数类
 * 
 * <AUTHOR>
 * @version 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CorridorCongestionDetailUrlParams extends UrlParams {

    /**
     * 时段（必填）
     * 早高峰：M_PEAK
     * 晚高峰：E_PEAK
     */
    private String timespan;

    /**
     * 时间（必填）
     * yyyyMM
     */
    private String ds;

    /**
     * 走廊id列表（必填）
     * 多个逗号间隔，最多300
     */
    private String corridorIds;
} 