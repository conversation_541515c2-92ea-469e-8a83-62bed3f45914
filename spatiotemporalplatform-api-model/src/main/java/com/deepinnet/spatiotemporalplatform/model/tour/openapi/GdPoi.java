package com.deepinnet.spatiotemporalplatform.model.tour.openapi;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * C<PERSON> zengjuerui
 * Date 2024-12-03
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GdPoi {

    private String id;

    private String parent;

    private String address;

    private String distance;

    private String pcode;

    private String adcode;

    private String pname;

    private String cityname;

    private String type;

    private String typecode;

    private String adname;

    private String citycode;

    private String name;

    private String location;
}
