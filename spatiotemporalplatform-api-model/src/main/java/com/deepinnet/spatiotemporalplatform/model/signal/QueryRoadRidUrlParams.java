package com.deepinnet.spatiotemporalplatform.model.signal;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询指数路或者通勤走廊的rid及顺序接口URL参数类
 * 
 * <AUTHOR>
 * @version 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryRoadRidUrlParams extends UrlParams {

    /**
     * 区县编码（可选）
     */
    private String district;

    /**
     * 道路类型（必填）
     * ROAD - "指数路"
     * OD - "通勤走廊"
     */
    private String roadType;

    /**
     * 道路id（必填）
     */
    private String id;

    /**
     * 日期（必填）
     * 格式YYYYMM
     */
    private String ds;
} 