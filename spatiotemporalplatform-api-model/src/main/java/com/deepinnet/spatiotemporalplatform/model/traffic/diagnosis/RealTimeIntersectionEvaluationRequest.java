package com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.IntersectionEvaluationConstant;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonResponse;
import lombok.Data;

/**
 * 路口评价API请求
 *
 * <AUTHOR>
 * @version 2024-07-29
 */
@Data
public class RealTimeIntersectionEvaluationRequest implements HttpApiRequest {
    /**
     * URL参数
     */
    private RealTimeIntersectionEvaluationUrlParams urlParams;

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<HttpApiJsonResponse<RealTimeIntersectionEvaluationData>>() {
        };
    }

    @Override
    public String apiUrl() {
        return IntersectionEvaluationConstant.REAL_TIME_INTERSECTION_EVALUATION_API_URL;
    }

    @Override
    public boolean useTempApiKey() {
        return false;
    }

    public String saveDataTableName() {
        return "real_time_intersection_detail";
    }

}
