package com.deepinnet.spatiotemporalplatform.model.signal;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 评诊治-路口-通勤路径月维度接口URL参数类
 * 
 * <AUTHOR>
 * @version 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CommuterPathMonthlyUrlParams extends UrlParams {

    /**
     * 日期（必填）
     * 月维度：yyyyMM
     */
    private String ds;

    /**
     * 粒度（必填）
     * 小时级：HOUR
     * 时段级：TIME_SPAN
     */
    private String particle;

    /**
     * 对象id（必填）
     * 由adcode(城市编码)、时段(1-早高峰 2-晚高峰)、corridor_id(通勤廊道id)三
     * 个字段值通过"_"拼接而成的id，示例"610100_1_21028"
     */
    private String id;

    /**
     * 小时（小时级必填）
     * 0-23
     */
    private Integer hh;

    /**
     * 时段（时段级必填）
     * 早晚高峰：PEAK
     * 早高峰：M_PEAK
     * 晚高峰：E_PEAK
     * 平峰：F_PEAK
     * 凌晨：MORNING
     * 夜间：NIGHT
     * 全天：ALL_DAY
     */
    private String timespan;

    /**
     * 路口状态（可选）
     * 正常：NORMAL
     * 失衡：UNBALANCE
     * 溢出：OVERFLOW
     * 失衡溢出：UNBALANCE_OVERFLOW
     */
    private String idxState;

    /**
     * 评级（可选）
     * A、B、C、D、E、F
     */
    private String losType;

    /**
     * 置信度（可选）
     * 高：HIGH
     * 中：MID
     * 低：LOW
     */
    private String confidence;

    /**
     * 路口名（可选）
     */
    private String interName;
} 