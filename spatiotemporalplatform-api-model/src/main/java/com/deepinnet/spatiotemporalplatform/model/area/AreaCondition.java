package com.deepinnet.spatiotemporalplatform.model.area;

import com.deepinnet.spatiotemporalplatform.model.common.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * AreaCondition
 * Author: chenkaiyang
 * Date: 2024/8/5
 */
@Data
public class AreaCondition extends PageQuery {
    @ApiModelProperty("区域编码")
    private String areaCode;

    @ApiModelProperty("区域名称")
    private String areaName;
}
