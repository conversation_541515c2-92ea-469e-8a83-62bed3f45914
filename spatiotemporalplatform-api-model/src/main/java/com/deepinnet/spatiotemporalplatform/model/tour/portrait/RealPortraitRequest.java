package com.deepinnet.spatiotemporalplatform.model.tour.portrait;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.tour.RealPortraitConstant;
import com.deepinnet.spatiotemporalplatform.common.enums.SignTypeEnum;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonResponse;
import lombok.Data;

/**

 *
 * <AUTHOR>
 * @since 2024-08-26 星期一
 * #507
 **/
@Data
public class RealPortraitRequest implements HttpApiRequest {

    private RealPortraitUrlParams urlParams;

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {};
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<HttpApiJsonResponse<RealPortrait>>() {};
    }

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public String apiUrl() {
        return RealPortraitConstant.REAL_PORTRAIT_API_URL;
    }

    @Override
    public SignTypeEnum signType() {
        return SignTypeEnum.TOKEN;
    }
}
