package com.deepinnet.spatiotemporalplatform.model.monitor;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.AllArgsConstructor;
import lombok.Data;

/**

 *
 * <AUTHOR>
 * @since 2024-12-03 星期二
 **/
@Data
@AllArgsConstructor
public class MonitorRequest implements HttpApiRequest {

    private MonitorUrlParams urlParams;

    @Override
    public TypeReference responseBodyType() {
        return null;
    }

    @Override
    public UrlParams urlParams() {
        return urlParams;
    }

    @Override
    public String apiUrl() {
        return "";
    }
}
