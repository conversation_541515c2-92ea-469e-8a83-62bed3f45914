package com.deepinnet.spatiotemporalplatform.model.road;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.GlobalConstant;
import com.deepinnet.spatiotemporalplatform.common.constants.RoadConstant;
import com.deepinnet.spatiotemporalplatform.common.request.BaseSignParams;
import com.deepinnet.spatiotemporalplatform.common.request.DigestSignParams;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonArrayResponse;
import lombok.Data;

/**

 *
 * <AUTHOR>
 * @since 2024-08-30 星期五
 **/
@Data
public class RoadSearchRequest implements HttpApiRequest {

    private RoadSearchUrlParams urlParams;

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {
        };
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<HttpApiJsonArrayResponse<RoadSearch>>() {
        };
    }

    @Override
    public boolean useTempApiKey(){
        return false;
    }

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public String apiUrl() {
        return RoadConstant.ROAD_SEARCH_API_URL;
    }

}