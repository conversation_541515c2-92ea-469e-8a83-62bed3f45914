package com.deepinnet.spatiotemporalplatform.model.tour.customer;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.tour.PeopleFlowDetectConstant;
import com.deepinnet.spatiotemporalplatform.common.enums.SignTypeEnum;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonResponse;
import lombok.Data;

/**

 *
 * <AUTHOR>
 * @since 2024-08-26 星期一
 * #483
 **/
@Data
public class CustomerFlowDetectGridRequest implements HttpApiRequest {

    private CustomerFlowDetectGridUrlParams urlParams;

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {};
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<HttpApiJsonResponse<CustomerFlowDetectGrid>>() {};
    }

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public String apiUrl() {
        return PeopleFlowDetectConstant.PEOPLE_FLOW_DETECT_GRID_API_URL;
    }

    @Override
    public SignTypeEnum signType() {
        return SignTypeEnum.TOKEN;
    }
}
