package com.deepinnet.spatiotemporalplatform.model.area;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Description:
 * Date: 2025/4/29
 * Author: lijunheng
 */
@ApiModel
@Data
public class StreetPolygonRespDTO implements Serializable {

    @ApiModelProperty("乡镇/街道名称")
    private String name;

    @ApiModelProperty("乡镇/街道的边界WKT")
    private String polygon;

    @ApiModelProperty("乡镇/街道的中心点")
    private String center;

    @ApiModelProperty("乡镇等级")
    private String level;

    @ApiModelProperty("乡镇区划代码")
    private Long streetid;
}
