package com.deepinnet.spatiotemporalplatform.model.reggov.flow.people;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.DistrictIndexConstant;
import com.deepinnet.spatiotemporalplatform.common.constants.GlobalConstant;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonArrayResponse;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.transform.TransformGridHistoryPeopleFlowData;
import com.deepinnet.spatiotemporalplatform.model.util.MD5Util;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * 网格历史人流和活力指数请求参数
 *
 * <AUTHOR>
 * @version 2024-07-29
 */
@Data
public class GridHistoryPeopleFlowRequest implements HttpApiRequest {
    /**
     * URL参数
     */
    private GridHistoryPeopleFlowParams urlParams;

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<HttpApiJsonArrayResponse<GridHistoryPeopleFlowData>>() {
        };
    }

    @Override
    public String apiUrl() {
        return DistrictIndexConstant.GRID_HISTORY_PEOPLE_FLOW_API_URL;
    }

    @Override
    public String saveDataTableName() {
        return "gd_grid_history_people_flow";
    }

    @Override
    public Object transformData(Object responseData) {

        if (ObjectUtil.isNull(responseData)) {
            return null;
        }

        List<GridHistoryPeopleFlowData> historyGridPeopleFlowList = (List<GridHistoryPeopleFlowData>) responseData;

        if (CollUtil.isEmpty(historyGridPeopleFlowList)) {
            return null;
        }

        List<TransformGridHistoryPeopleFlowData> transformGridHistoryPeopleFlowDataList = Lists.newArrayList();

        for (GridHistoryPeopleFlowData gridData : historyGridPeopleFlowList) {

            if (CollUtil.isNotEmpty(gridData.getList())) {

                gridData.getList().forEach(flow -> {
                    TransformGridHistoryPeopleFlowData transformGridHistoryPeopleFlowData = TransformGridHistoryPeopleFlowData.builder()
                            .gridCenter(gridData.getGridCenter())
                            .gridName(gridData.getGridName()).build();

                    transformGridHistoryPeopleFlowData.setTime(flow.getTime());
                    transformGridHistoryPeopleFlowData.setPeopleFlow(flow.getPeopleFlow());
                    transformGridHistoryPeopleFlowData.setDensity(flow.getDensity());
                    transformGridHistoryPeopleFlowData.setHistoryAvgFlow(flow.getHistoryAvgFlow());
                    transformGridHistoryPeopleFlowData.setHistoryMaxFlow(flow.getHistoryMaxFlow());
                    transformGridHistoryPeopleFlowData.setActivityIndex(flow.getActivityIndex());
                    transformGridHistoryPeopleFlowData.setSaturationIndex(flow.getSaturationIndex());
                    transformGridHistoryPeopleFlowData.setMd5(MD5Util.getObjectMD5(transformGridHistoryPeopleFlowData));
                    transformGridHistoryPeopleFlowDataList.add(transformGridHistoryPeopleFlowData);
                });

            } else {
                TransformGridHistoryPeopleFlowData transformGridHistoryPeopleFlowData = TransformGridHistoryPeopleFlowData.builder()
                        .gridCenter(gridData.getGridCenter())
                        .gridName(gridData.getGridName()).build();

                transformGridHistoryPeopleFlowData.setMd5(MD5Util.getObjectMD5(transformGridHistoryPeopleFlowData));

                transformGridHistoryPeopleFlowDataList.add(transformGridHistoryPeopleFlowData);
            }
        }

        return transformGridHistoryPeopleFlowDataList;
    }
}
