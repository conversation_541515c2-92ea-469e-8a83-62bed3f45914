package com.deepinnet.spatiotemporalplatform.model.police;

import com.deepinnet.spatiotemporalplatform.common.enums.ObjectType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 警力部署表
 * @TableName police_deployment
 */
@Data
public class PoliceDeployment implements Serializable {

    /**
     * 自增ID
     */
    @ApiModelProperty(value = "自增ID", notes = "唯一标识")
    private Integer id;

    /**
     * 警员姓名
     */
    @ApiModelProperty(value = "警员姓名", notes = "警员的姓名")
    private String officerName;

    /**
     * 警员编号
     */
    @ApiModelProperty(value = "警员编号", notes = "警员的唯一编号")
    private String officerId;

    /**
     * 警力类型
     */
    @ApiModelProperty(value = "警力类型", notes = "警力的类型")
    private String policeType;

    /**
     * 经纬度
     */
    @ApiModelProperty(value = "经纬度", notes = "警力部署的地理位置坐标")
    private String coordinates;

    /**
     * 所属区域code
     */
    @ApiModelProperty(value = "所属区域code", notes = "警力部署所属的区域编码")
    private String areaCode;

    /**
     * 所属对象类型
     */
    @ApiModelProperty(value = "所属对象类型", notes = "警力部署所属的对象类型")
    private ObjectType objectType;

    /**
     * 所属对象code
     */
    @ApiModelProperty(value = "所属对象code", notes = "警力部署所属的对象编码")
    private String objectCode;

    /**
     * 组织名称
     */
    @ApiModelProperty(value = "组织名称", notes = "警力部署所属的组织名称")
    private String orgName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", notes = "关于警力部署的额外说明")
    private String remark;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间", notes = "警力部署的开始时间")
    private Long startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间", notes = "警力部署的结束时间")
    private Long endTime;

    /**
     * 设置人数
     */
    @ApiModelProperty(value = "设置人数", notes = "部署的警力人数")
    private Integer count;


    private Long orgId;

    private static final long serialVersionUID = 1L;

}