package com.deepinnet.spatiotemporalplatform.model.signal;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 路口信号配时接口URL参数类
 * 
 * <AUTHOR>
 * @version 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IntersectionSignalTimingUrlParams extends UrlParams {

    /**
     * 查询时间（必填）
     * 格式：yyyyMM或yyyyMMdd
     */
    private String ds;

    /**
     * 时间维度（可选）
     * DAY（天维度）或 MONTH（月维度），默认：DAY
     */
    private String dimension;

    /**
     * 路口集合（必填）
     * 最多传20个，逗号分隔
     */
    private String interIds;

    /**
     * 时段类型（可选）
     * PEAK:早晚高峰 M_PEAK:早高峰 E_PEAK:晚高峰 F_PEAK:平峰 
     * MORNING:凌晨 NIGHT:夜间 ALL_DAY:全天，默认：ALL_DAY
     */
    private String timespan;

    /**
     * 小时（可选）
     * 0-23
     */
    private Integer hh;
} 