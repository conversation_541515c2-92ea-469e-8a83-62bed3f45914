package com.deepinnet.spatiotemporalplatform.model.signal;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;

/**
 * 路口自适应历史数据URL参数
 */
@Data
public class IntersectionAdaptiveHistoryUrlParams extends UrlParams {
    /** 指定路口ID */
    private String interId;
    /** 指定日期 yyyymmdd */
    private String ds;
    /** 聚合颗粒度：minute5/minute10/hour */
    private String timeGrading;
} 