package com.deepinnet.spatiotemporalplatform.model.traffic.event;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;

/**
 * 交通事件查询url参数
 *
 * <AUTHOR>
 * @version 2024-07-30
 */
@Data
public class TrafficEventUrlParams extends UrlParams {
    /**
     * 授权城市ADCODE, 仅支持城市级ADCODE（必传）
     */
    private String adcode;

    /**
     * 事件类型，多个用','隔开，取值见《高德事件类型》
     * <p>
     * 非必传
     */
    private String eventType;

    /**
     * 来源，多个用','隔开。取值：0-官方, 1-权威, 2-UGC
     * <p>
     * 非必传
     */
    private String sourceType;

    /**
     * 是否高速。1-是, 0-否
     * <p>
     * 非必传
     */
    private Integer isExpressway;
}
