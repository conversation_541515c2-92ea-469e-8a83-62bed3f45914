package com.deepinnet.spatiotemporalplatform.model.road;

import lombok.Data;

import java.util.List;

/**

 *
 * <AUTHOR>
 * @since 2024-08-28 星期三
 **/
@Data
public class RoadFlowPredict {

    private int carTravelPrecent;
    /**
     * 流量坐标点
     */
    private String coords;
    /**
     * 路段长度 单位：米
     */
    private int linkLength;
    /**
     * 当前LINK⻋速
     */
    private int linkSpeed;
    /**
     * 当前LINKID
     */
    private String linkId;

    private List<Predict> predict;
    /**
     * 道路名称
     */
    private String roadName;
    /**
     * 当前LINK⾼德⾃由流速度
     */
    private int trafficKmph;
    private int truckTravelPrecent;

    @Data
    public static class Predict {
        /**
         * 备注，例如低流量⽆法推演时显示“低流量”
         */
        private String comment;
        /**
         * 置信度
         */
        private String confidence;
        /**
         * 5分钟真实流量推演，⻋辆数
         */
        private int count;
        /**
         * 最⼤通⾏能⼒占⽐推演，按⾼德监测历史最⼤值计算
         */
        private int flowRatio;
        /**
         * 饱和度推演，过饱为⼤于100%
         */
        private int saturation;
        /**
         * 未来时间，5分钟颗粒度
         */
        private int time;
    }
}
