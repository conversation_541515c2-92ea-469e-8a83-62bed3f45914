package com.deepinnet.spatiotemporalplatform.model.parkinglot;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Data
public class ParkingLotDTO {
    private Integer id;
    @ApiModelProperty(value = "停车场编码")
    private String parkingLotCode;
    @ApiModelProperty(value = "停车场名称")
    private String name;
    @ApiModelProperty(value = "停车场类型")
    private String type;
    @ApiModelProperty(value = "地址")
    private String address;
    @ApiModelProperty(value = "经度")
    private String longitude;

    @ApiModelProperty(value = "纬度")
    private String latitude;

    @ApiModelProperty(value = "总车位数")
    private Integer totalSpaces;
    @ApiModelProperty(value = "可用车位数")
    private Integer availableSpaces;
    private String openHours;

    @ApiModelProperty(value = "停车场状态")
    private String status;

    @ApiModelProperty(value = "车位饱和度")
    private Double saturation;

    @ApiModelProperty("可备用停车场名称")
    private String standbyParkingLotName;
    @ApiModelProperty(value = "已使用车位数")
    private Integer inUseSpaces;


    public Double calculateSaturation() {
        if (availableSpaces == null || totalSpaces == null) {
            return 0D;
        }
        return totalSpaces > 0 ? BigDecimal.valueOf((totalSpaces.doubleValue() - availableSpaces.doubleValue()) / totalSpaces.doubleValue()).setScale(4, RoundingMode.FLOOR).doubleValue() : 0;
    }
}