package com.deepinnet.spatiotemporalplatform.model.signal;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 评诊治-通勤-走廊排名接口URL参数类
 * 
 * <AUTHOR>
 * @version 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CorridorRankingUrlParams extends UrlParams {

    /**
     * 时段（必填）
     * 早高峰：M_PEAK
     * 晚高峰：E_PEAK
     */
    private String timespan;

    /**
     * 时间（必填）
     * 格式：yyyyMM
     */
    private String ds;

    /**
     * 行政区（可选）
     */
    private String district;

    /**
     * 自定义区域（可选）
     * 经纬度空格间隔，多个逗号间隔
     * 如：116.489678 39.979974,116.481696 39.98512,...
     * 面积需小于100平方公里
     */
    private String xys;

    /**
     * 区域类型（可选）
     * IN_IN：内内
     * IN_OUT：内外
     * OUT_IN：外内
     * ALL：全部
     * 仅在district或xys不空时生效
     */
    private String commuteType;

    /**
     * 最大长度（可选）
     */
    private Double maxLen;

    /**
     * 最小长度（可选）
     */
    private Double minLen;

    /**
     * 返回记录数（可选）
     * 最大300，默认300
     */
    private Integer size;
} 