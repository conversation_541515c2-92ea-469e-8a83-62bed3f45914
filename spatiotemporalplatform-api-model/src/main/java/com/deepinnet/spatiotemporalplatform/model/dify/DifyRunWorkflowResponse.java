package com.deepinnet.spatiotemporalplatform.model.dify;

import com.alibaba.fastjson2.annotation.JSONField;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Optional;

/**
 * C<PERSON> zengjuerui
 * Date 2025-01-22
 **/

@EqualsAndHashCode(callSuper = true)
@Data
public class DifyRunWorkflowResponse extends HttpApiJsonResponse<DifyRunWorkflowResult> {

    @JSONField(name = "task_id")
    private String taskId;

    @JSONField(name = "workflow_run_id")
    private String workflowRunId;

    private DifyRunWorkflowResult data;

    @Override
    public int getCode() {
        if(data != null && "succeeded".equals(data.getStatus())) return 0;

        return -1;
    }

    @Override
    public String getMsg() {
        return Optional.ofNullable(data)
                .map(DifyRunWorkflowResult::getError)
                .orElse("");
    }
}
