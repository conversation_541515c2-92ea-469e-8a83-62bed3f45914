package com.deepinnet.spatiotemporalplatform.model.signal;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.enums.SignTypeEnum;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.PostBody;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonArrayResponse;
import lombok.Data;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;

/**
 * 路口信号配时接口请求类
 * 
 * <AUTHOR>
 * @version 2025-06-09
 */
@Data
public class IntersectionSignalTimingRequest implements HttpApiRequest {

    /**
     * URL参数
     */
    private IntersectionSignalTimingUrlParams urlParams;

    /**
     * 设置URL参数
     */
    public void setUrlParams(IntersectionSignalTimingUrlParams urlParams) {
        this.urlParams = urlParams;
    }

    @Override
    public UrlParams urlParams() {
        return urlParams;
    }

    @Override
    public String apiUrl() {
        return "https://et-api.amap.com/diagnosis/inter/interSignLightTime?clientKey=%s&timestamp=%s&adcode=%s&ds=%s&dimension=%s&interIds=%s&timespan=%s&hh=%s";
    }

    @Override
    public HttpMethod httpMethod() {
        return HttpMethod.GET;
    }

    @Override
    public HttpHeaders httpHeaders() {
        return null;
    }

    @Override
    public PostBody postBody() {
        return null;
    }

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {};
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<HttpApiJsonArrayResponse<IntersectionSignalTiming>>() {};
    }

    @Override
    public SignTypeEnum signType() {
        return SignTypeEnum.DIGEST;
    }

    @Override
    public boolean useTempApiKey() {
        return true;
    }
} 