package com.deepinnet.spatiotemporalplatform.model.tour.gd;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 游客去往景区分布
 *
 * @TableName tourist_distribution
 */
@Data
public class GdTouristDistribution implements Serializable {
    /**
     *
     */
    private Long id;

    /**
     * 区域code
     */
    private String areaCode;

    /**
     * 城市code
     */
    private String adCode;

    /**
     * 游客数量
     */
    private String count;

    private Date gmtCreated;

    private Date gmtModified;

    private static final long serialVersionUID = 1L;
}