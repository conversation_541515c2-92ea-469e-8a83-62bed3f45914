package com.deepinnet.spatiotemporalplatform.model.hotod;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.HotOdConstant;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonResponse;
import com.deepinnet.spatiotemporalplatform.model.hotod.transfer.DriverDestinationRankingData;
import com.deepinnet.spatiotemporalplatform.model.util.MD5Util;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.stream.Collectors;

/**

 *
 * <AUTHOR>
 * @since 2024-08-28 星期三
 **/
@Data
public class DriverDestinationRankingRequest implements HttpApiRequest {

    private DriverDestinationRankingUrlParams urlParams;

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {
        };
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<HttpApiJsonResponse<DriverDestinationRanking>>() {
        };
    }

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public boolean useTempApiKey() {
        return true;
    }

    @Override
    public String apiUrl() {
        return HotOdConstant.DRIVER_DESTINATION_RANKING_URL;
    }


    @Override
    public String saveDataTableName() {
        return "gd_driver_destination_ranking_data";
    }

    @Override
    public Object transformData(Object responseData) {
        DriverDestinationRanking driverDestinationRankings = (DriverDestinationRanking) responseData;
        if (ObjectUtil.isEmpty(driverDestinationRankings) || CollectionUtil.isEmpty(driverDestinationRankings.getList())) {
            return null;
        }
        LocalDateTime now = LocalDateTime.now().withNano(0);
        String batchId = IdUtil.getSnowflakeNextIdStr();
        return driverDestinationRankings.getList().stream().map(driverDestinationRanking -> {
            DriverDestinationRankingData driverDestinationRankingData = new DriverDestinationRankingData();
            driverDestinationRankingData.setAdcode(driverDestinationRanking.getAdcode());
            driverDestinationRankingData.setObjId(driverDestinationRanking.getObjId());
            driverDestinationRankingData.setObjType(driverDestinationRanking.getObjType());
            driverDestinationRankingData.setObjName(driverDestinationRanking.getObjName());
            driverDestinationRankingData.setGeo(driverDestinationRanking.getGeo());
            driverDestinationRankingData.setFlow(driverDestinationRanking.getFlow());
            driverDestinationRankingData.setAvgFlow(driverDestinationRanking.getAvgFlow());
            driverDestinationRankingData.setBatchId(batchId);
            driverDestinationRankingData.setTimestamp(now);
            driverDestinationRankingData.setMd5(MD5Util.getObjectMD5(driverDestinationRankingData));
            return driverDestinationRankingData;
        }).collect(Collectors.toList());
    }
}
