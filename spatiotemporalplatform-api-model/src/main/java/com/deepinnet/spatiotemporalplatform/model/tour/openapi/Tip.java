package com.deepinnet.spatiotemporalplatform.model.tour.openapi;

import lombok.Data;

import java.util.List;

/**
 * Creator z<PERSON><PERSON><PERSON><PERSON>
 * Date 2025-07-28
 **/

@Data
public class Tip {

    private String id;             // POI的ID
    private String name;          // 名称
    private String district;      // 所属区域
    private String adcode;        // 区域编码
    private String location;     // 经纬度
    private String address;       // 地址
    private String typecode;      // 类型编码
    private String city;          // 城市
    private String citycode;      // 城市编码
    private List<String> cityname; // 城市名称列表(可能包含上级城市)
    private List<String> addressname; // 地址名称列表(可能包含上级地址)
}
