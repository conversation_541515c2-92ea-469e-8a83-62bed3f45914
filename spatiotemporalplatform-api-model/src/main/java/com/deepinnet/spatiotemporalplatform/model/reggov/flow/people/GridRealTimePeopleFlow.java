package com.deepinnet.spatiotemporalplatform.model.reggov.flow.people;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 2024-08-05
 */
@Data
public class GridRealTimePeopleFlow {

    /**
     * 网格中心点坐标
     */
    @ApiModelProperty(value = "网格中心点坐标")
    private String gridCenter;

    /**
     * 网格名称
     */
    @ApiModelProperty(value = "网格名称")
    private String gridName;

    /**
     * 自定义区域id
     */
    @ApiModelProperty(value = "⾃定义区域id")
    private String customAreaId;

    /**
     * 人流量
     */
    @ApiModelProperty(value = "人流量")
    private int peopleFlow;

    /**
     * 网格人流密度（每平方米人流），小数
     */
    @ApiModelProperty(value = "网格人流密度（每平方米人流），小数")
    private String density;

    /**
     * 活力指数，小数
     */
    @ApiModelProperty(value = "活力指数，小数")
    private String activityIndex;

    /**
     * 历史人流量均值
     */
    @ApiModelProperty(value = "历史人流量均值")
    private String historyAvgFlow;

    /**
     * 历史人流量最大值
     */
    @ApiModelProperty(value = "历史人流量最大值")
    private String maxFlow;

    /**
     * ⽇期,格式:yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "⽇期,格式:yyyy-MM-dd HH:mm:ss")
    private String ds;

}
