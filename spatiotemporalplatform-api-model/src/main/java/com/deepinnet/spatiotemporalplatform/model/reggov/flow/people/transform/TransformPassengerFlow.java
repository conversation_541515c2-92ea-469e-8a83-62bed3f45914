package com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.transform;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/11/12
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransformPassengerFlow {

    private Integer chargeCnt;

    private Integer index;

    private String md5;

}
