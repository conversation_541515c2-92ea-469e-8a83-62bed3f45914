package com.deepinnet.spatiotemporalplatform.model.tour.openapi;

import com.deepinnet.spatiotemporalplatform.common.response.HttpOpenApiResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <PERSON><PERSON>
 * Date 2025-07-28
 **/

@EqualsAndHashCode(callSuper = true)
@Data
public class InputTips extends HttpOpenApiResponse {

    private Integer count;     // 提示条数
    private List<Tip> tips;
}
