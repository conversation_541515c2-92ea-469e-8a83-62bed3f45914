package com.deepinnet.spatiotemporalplatform.model.skyflow;

import com.deepinnet.spatiotemporalplatform.common.constants.OpenApiConstant;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
public class GdFlightDetectionParam extends UrlParams {

    /**
     * 用户key
     */
    private String key = OpenApiConstant.USER_KEY;

    /**
     * 中心点
     */
    private String center;

    /**
     * 半径
     */
    private Integer radius;
}
