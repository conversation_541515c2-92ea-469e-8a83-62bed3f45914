package com.deepinnet.spatiotemporalplatform.model.signal;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 评诊治-图形-路段接口URL参数类
 * 
 * <AUTHOR>
 * @version 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoadSegmentGraphicsUrlParams extends UrlParams {

    /**
     * 路段id列表（必填）
     * 多个逗号间隔，上限20
     */
    private String rids;

    /**
     * 时间版本（必填）
     * 月版本，格式: yyyymm
     */
    private String ds;
} 