package com.deepinnet.spatiotemporalplatform.model.tour.insight;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.tour.PeopleFlowInsightConstants;
import com.deepinnet.spatiotemporalplatform.common.enums.SignTypeEnum;
import com.deepinnet.spatiotemporalplatform.common.request.*;
import com.deepinnet.spatiotemporalplatform.common.response.*;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-08-26 星期一
 * #490
 **/
@Data
public class PeopleFlowInsightRequest implements HttpApiRequest {

    private PeopleFlowInsightUrlParams urlParams;

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {
        };
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<HttpApiJsonArrayResponse<PeopleFlowInsightDTO>>() {
        };
    }

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public String apiUrl() {
        return PeopleFlowInsightConstants.PEOPLE_FLOW_INSIGHT_URL;
    }

    @Override
    public SignTypeEnum signType() {
        return SignTypeEnum.DIGEST;
    }
}
