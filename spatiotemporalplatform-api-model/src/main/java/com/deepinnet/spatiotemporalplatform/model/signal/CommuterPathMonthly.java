package com.deepinnet.spatiotemporalplatform.model.signal;

import lombok.Data;

/**
 * 评诊治-路口-通勤路径月维度数据对象
 * 
 * <AUTHOR>
 * @version 2025-06-09
 */
@Data
public class CommuterPathMonthly {

    /**
     * 对象Id，对应高德文档中的id字段
     */
    private String objectId;

    /**
     * 粒度
     */
    private String particle;

    /**
     * 路口id
     */
    private String interId;

    /**
     * 进口道id
     * -1表示主路口，其他为进口道id
     */
    private String fRid;

    /**
     * 转向
     * 0-主路口
     */
    private Integer turnDirNo;

    /**
     * 小时
     * 小时级粒度有意义：0-23
     */
    private Integer hh;

    /**
     * 时段
     * 0-早晚高峰、1-早高峰、2-晚高峰、3-平峰、4-凌晨、5-夜间、6-全天
     */
    private String timespan;

    /**
     * 排队长度
     */
    private Double queueLen;

    /**
     * 停车次数
     */
    private Integer stopTimes;

    /**
     * 延误指数
     */
    private Double delayIndex;

    /**
     * 平均速度
     */
    private Double avgSpeed;

    /**
     * 路口状态
     * 0-正常、1-失衡、2-溢出、3-失衡溢出
     */
    private Integer idxState;

    /**
     * 路口评级
     * A、B、C、D、E、F
     */
    private String los;

    /**
     * 置信度
     * 高、中、低
     */
    private String confidence;

    /**
     * 路口名称
     */
    private String interName;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 日期
     * 月维度：yyyyMM
     */
    private String ds;

    /**
     * 城市编码
     * 直辖市或地级市编码
     */
    private String adcode;

    /**
     * 道路名称
     */
    private String ridName;

    /**
     * 道路长度
     */
    private Double ridLen;

    /**
     * 角度
     */
    private Double angleF;

    /**
     * 是否灯控
     * 0-非灯控、1-灯控
     */
    private Integer isSignlight;

    /**
     * 车道信息VO
     */
    private Object laneInfoVO;
} 