package com.deepinnet.spatiotemporalplatform.model.tour.group;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.tour.GroupFlowDetectConstant;
import com.deepinnet.spatiotemporalplatform.common.enums.SignTypeEnum;
import com.deepinnet.spatiotemporalplatform.common.request.*;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonResponse;
import com.deepinnet.spatiotemporalplatform.model.tour.enums.ProfileEnum;
import com.deepinnet.spatiotemporalplatform.model.tour.gd.*;
import com.deepinnet.spatiotemporalplatform.model.util.GaoDeUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**

 *
 * <AUTHOR>
 * @since 2024-08-26 星期一
 * #504
 **/
@Data
public class GroupFlowDetectRequest implements HttpApiRequest {

    private GroupFlowDetectUrlParams urlParams;

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {
        };
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<HttpApiJsonResponse<GroupFlowDetect>>() {
        };
    }

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public String apiUrl() {
        return GroupFlowDetectConstant.GROUP_FLOW_DETECT_API_URL;
    }

    @Override
    public SignTypeEnum signType() {
        return SignTypeEnum.TOKEN;
    }

    public String saveDataTableName() {
        // 获取景区乘客来源地
        if (StringUtils.equals(urlParams.getProfile(), ProfileEnum.getValues(ProfileEnum.PMNT_PROV_DIST, ProfileEnum.PMNT_CITY_DIST))) {
            return "gd_tourist_origin";
            // 获取旅客消费水平
        } else if (StringUtils.equals(urlParams.getProfile(), ProfileEnum.PROPLEVEL_DIST.getValue())) {
            return "gd_tourist_spending_level";
            // 用户年龄/职业画像
        } else if (StringUtils.equals(urlParams.getProfile(), ProfileEnum.getValues(ProfileEnum.AGE_DIST, ProfileEnum.OCCUPATION_DIST))) {
            return "gd_tourist_portrait";
        }

        return null;
    }

    public Object transformData(Object responseData) {
        GroupFlowDetect groupFlowDetect = (GroupFlowDetect) responseData;
        GroupFlowDetect.IndexData indexData = groupFlowDetect.getIndexData();
        if (indexData == null) {
            return null;
        }
        List<GroupFlowDetect.Query> dataList = indexData.getQuery();

        // 获取景区乘客来源地
        if (StringUtils.equals(urlParams.getProfile(), ProfileEnum.getValues(ProfileEnum.PMNT_PROV_DIST, ProfileEnum.PMNT_CITY_DIST))) {
            return convertToGdTouristOrigins(dataList);
        } else if (StringUtils.equals(urlParams.getProfile(), ProfileEnum.PROPLEVEL_DIST.getValue())) {
            // 获取旅客消费水平
            return convertToGdTouristSpendingLevel(dataList);
        } else if (StringUtils.equals(urlParams.getProfile(), ProfileEnum.getValues(ProfileEnum.AGE_DIST, ProfileEnum.OCCUPATION_DIST))) {
            // 用户年龄/职业画像
            return convertToGdPortrait(dataList);
        }
        return null;
    }

    private List<GdTouristPortrait> convertToGdPortrait(List<GroupFlowDetect.Query> dataList) {
        List<GdTouristPortrait> touristPortraitDOList = Lists.newArrayList();
        dataList.forEach(res -> {
            String agePortrait = res.getAgeDist();
            String careerPortrait = res.getOccupationDist();

            if (StringUtils.isNotBlank(agePortrait)) {
                Map<String, String> agePortraitMap = GaoDeUtil.parseGaoDeData2MapWithEscape(agePortrait);
                assert agePortraitMap != null;
                agePortraitMap.forEach((k, v) -> {
                    GdTouristPortrait portraitDO = new GdTouristPortrait();
                    portraitDO.setAdCode(urlParams.getAdcode());
                    portraitDO.setAreaCode(urlParams.getAreaId());
                    portraitDO.setType("age");
                    portraitDO.setCode(k);
                    portraitDO.setCount(Integer.parseInt(v));
                    if (StringUtils.equals(k, "-1")) {
                        portraitDO.setName("未知");
                    } else {
                        portraitDO.setName(k);
                    }
                    touristPortraitDOList.add(portraitDO);
                });
            }

            if (StringUtils.isNotBlank(careerPortrait)) {
                Map<String, String> careerPortraitMap = GaoDeUtil.parseGaoDeData2MapWithEscape(careerPortrait);
                // 上面职业画像的职业是返回的两级的职业，系统要求只取第一级的职业，需要进行聚合处理
                assert careerPortraitMap != null;

                careerPortraitMap.forEach((k, v) -> {
                    GdTouristPortrait portraitDO = new GdTouristPortrait();
                    portraitDO.setAdCode(urlParams.getAdcode());
                    portraitDO.setAreaCode(urlParams.getAreaId());
                    portraitDO.setType("career");
                    portraitDO.setCode(k);
                    portraitDO.setCount(Integer.parseInt(v));
                    portraitDO.setName(k);
                    touristPortraitDOList.add(portraitDO);
                });
            }
        });

        return touristPortraitDOList;
    }

    private List<GdTouristSpendingLevel> convertToGdTouristSpendingLevel(List<GroupFlowDetect.Query> touristSpeedingLevelList) {
        List<GdTouristSpendingLevel> spendingLevelDOs = Lists.newArrayList();
        touristSpeedingLevelList.forEach(touristSpeedingLevel -> {
            Map<String, String> levelMap = GaoDeUtil.parseGaoDeData2MapWithEscape(touristSpeedingLevel.getProplevelDist());
            assert levelMap != null;
            levelMap.forEach((k, v) -> {
                GdTouristSpendingLevel spendingLevelDO = new GdTouristSpendingLevel();
                spendingLevelDO.setAreaCode(this.urlParams.getAreaId());
                spendingLevelDO.setAdCode(this.urlParams.getAdcode());
                spendingLevelDO.setLevel(k);
                spendingLevelDO.setCount(v == null ? null : Integer.parseInt(v));
                spendingLevelDOs.add(spendingLevelDO);
            });
        });

        return spendingLevelDOs;
    }

    private List<GdTouristOrigin> convertToGdTouristOrigins(List<GroupFlowDetect.Query> touristOriginList) {
        List<GdTouristOrigin> touristOriginDOs = Lists.newArrayList();
        touristOriginList.forEach(touristOrigin -> {
            // 返回值里面，如果是直辖市，省级来源地会返回 北京，而市级来源地会返回为：北京-北京
            Map<String, String> provinceMap = GaoDeUtil.parseGaoDeData2MapWithEscape(touristOrigin.getPmntProvDist());
            Map<String, String> cityMap = GaoDeUtil.parseGaoDeData2MapWithEscape(touristOrigin.getPmntCityDist());
            assert provinceMap != null;
            assert cityMap != null;
            provinceMap.forEach((k, v) -> {
                GdTouristOrigin gdTouristOrigin = new GdTouristOrigin();
                gdTouristOrigin.setAreaCode(urlParams.getAreaId());
                gdTouristOrigin.setAdCode(urlParams.getAdcode());
                gdTouristOrigin.setRegionCode(null);
                if (StringUtils.isBlank(v)) {
                    gdTouristOrigin.setCount(0);
                } else {
                    gdTouristOrigin.setCount(Integer.parseInt(v));
                }
                if (StringUtils.equals(k, "-1")) {
                    gdTouristOrigin.setRegionName("未知");
                } else {
                    gdTouristOrigin.setRegionName(k);
                }
                gdTouristOrigin.setType("province");
                touristOriginDOs.add(gdTouristOrigin);
            });

            cityMap.forEach((k, v) -> {
                GdTouristOrigin gdTouristOrigin = new GdTouristOrigin();
                gdTouristOrigin.setAreaCode(urlParams.getAreaId());
                gdTouristOrigin.setAdCode(urlParams.getAdcode());
                if (StringUtils.equals(k, "-1")) {
                    gdTouristOrigin.setRegionName("未知");
                } else {
                    gdTouristOrigin.setRegionName(k);
                }
                gdTouristOrigin.setRegionCode(null);
                if (StringUtils.isBlank(v)) {
                    gdTouristOrigin.setCount(0);
                } else {
                    gdTouristOrigin.setCount(Integer.parseInt(v));
                }
                gdTouristOrigin.setType("city");
                touristOriginDOs.add(gdTouristOrigin);
            });
        });

        return touristOriginDOs;
    }
}
