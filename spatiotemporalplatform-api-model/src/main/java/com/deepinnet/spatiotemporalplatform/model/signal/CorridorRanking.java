package com.deepinnet.spatiotemporalplatform.model.signal;

import lombok.Data;

/**
 * 评诊治-通勤-走廊排名数据对象
 * 
 * <AUTHOR>
 * @version 2025-06-09
 */
@Data
public class CorridorRanking {
    /** 城市编码 */
    private String adcode;
    /** 时段 1-早高峰、2-晚高峰 */
    private String timespan;
    /** 走廊id */
    private String corridorId;
    /** 走廊名称 */
    private String corridorName;
    /** 方向 0-西向东、1-北向南、2-东向西、3-南向北 */
    private Integer direction;
    /** 人数 */
    private Integer population;
    /** 平均通勤时长 */
    private Double avgTime;
    /** 平均通勤距离 */
    private Double avgLength;
    /** 日期 yyyyMM */
    private String ds;
}