package com.deepinnet.spatiotemporalplatform.model.area;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.OpenApiConstant;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;

/**
 * Description:
 * Date: 2025/4/29
 * Author: lijunheng
 */
@Data
public class StreetPolygonRequest implements HttpApiRequest {

    private StreetPolygonQueryParams streetPolygonQueryParams;

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<StreetPolygonResponse>() {
        };
    }

    @Override
    public UrlParams urlParams() {
        return streetPolygonQueryParams;
    }

    @Override
    public String apiUrl() {
        return OpenApiConstant.STREET_POLYGON_QUERY_URL;
    }
}
