package com.deepinnet.spatiotemporalplatform.model.road;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.RoadConstant;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonArrayResponse;
import com.deepinnet.spatiotemporalplatform.model.road.transfer.GdRealRoadIndex;
import com.deepinnet.spatiotemporalplatform.model.util.MD5Util;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**

 *
 * <AUTHOR>
 * @since 2024-08-28 星期三
 **/
@Data
public class RealRoadIndexRequest implements HttpApiRequest {

    private RealRoadIndexUrlParams urlParams;

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {};
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<HttpApiJsonArrayResponse<RealRoadIndex>>() {
        };
    }

    @Override
    public boolean useTempApiKey() {
        return false;
    }

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public String apiUrl() {
        return RoadConstant.REAL_ROAD_INDEX_API_URL;
    }

    @Override
    public String saveDataTableName() {
        return "gd_real_road_index";
    }

    @Override
    @SuppressWarnings("unchecked")
    public Object transformData(Object responseData) {
        List<RealRoadIndex> realRoadIndexList = (List<RealRoadIndex>) responseData;
        LocalDateTime now = LocalDateTime.now().withNano(0);
        return realRoadIndexList.stream().map(realRoadIndex -> {
            GdRealRoadIndex gdRealRoadIndex = BeanUtil.copyProperties(realRoadIndex, GdRealRoadIndex.class, "id");
            gdRealRoadIndex.setRoadId(realRoadIndex.getId());
            gdRealRoadIndex.setTimestamp(now);
            gdRealRoadIndex.setMd5(MD5Util.getObjectMD5(gdRealRoadIndex));
            return gdRealRoadIndex;
        }).collect(Collectors.toList());
    }
}
