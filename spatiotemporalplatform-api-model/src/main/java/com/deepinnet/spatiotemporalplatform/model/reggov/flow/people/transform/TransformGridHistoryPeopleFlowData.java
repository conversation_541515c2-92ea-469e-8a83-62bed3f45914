package com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.transform;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 网格历史人流和活力指数数据
 *
 * <AUTHOR>
 * @version 2024-08-05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransformGridHistoryPeopleFlowData {
    /**
     * 网格中心点坐标
     */
    private String gridCenter;

    /**
     * 网格名称
     */
    private String gridName;

    /**
     * 时间，格式根据时间粒度决定
     */
    private String time;

    /**
     * 人流量
     */
    private int peopleFlow;

    /**
     * 网格人流密度（每平方米人流），小数
     */
    private String density;

    /**
     * 历史人流量均值
     */
    private String historyAvgFlow;

    /**
     * 历史人流量最大值
     */
    private String historyMaxFlow;

    /**
     * 活力指数，小数
     */
    private String activityIndex;

    /**
     * 饱和度指数，小数
     */
    private String saturationIndex;

    /**
     * md5
     */
    private String md5;
}
