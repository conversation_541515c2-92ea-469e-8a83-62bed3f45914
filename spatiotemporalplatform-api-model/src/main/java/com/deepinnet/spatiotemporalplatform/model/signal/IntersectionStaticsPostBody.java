package com.deepinnet.spatiotemporalplatform.model.signal;

import com.deepinnet.spatiotemporalplatform.common.request.PostBody;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 智慧交通-实时路口-路口静态信息查询服务 POST请求体
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IntersectionStaticsPostBody extends PostBody {
    /**
     * 授权城市的ADCODE
     */
    private String adcode;
    /**
     * 授权城市区县的ADCODE
     */
    private String district;
    /**
     * 指定路口坐标，多个经纬度空格分隔，逗号分隔多个点
     */
    private String xys;
    /**
     * 指定路口ID，最多支持10个
     */
    private String[] interIds;
    /**
     * 页大小，默认100，最大1000
     */
    private Integer pageSize;
    /**
     * 页号，默认1
     */
    private Integer pageNum;
} 