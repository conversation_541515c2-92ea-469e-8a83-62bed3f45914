package com.deepinnet.spatiotemporalplatform.model.signal;

import com.deepinnet.spatiotemporalplatform.common.request.PostBody;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 评诊治-通勤-走廊拥堵详情POST请求体
 * 
 * <AUTHOR>
 * @version 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CorridorCongestionDetailPostBody extends PostBody {

    /**
     * 城市编码（必填）
     * 直辖市或地级市编码
     */
    private String adcode;

    /**
     * 时段（必填）
     * 早高峰：M_PEAK
     * 晚高峰：E_PEAK
     */
    private String timespan;

    /**
     * 时间（必填）
     * yyyyMM
     */
    private String ds;

    /**
     * 走廊id列表（必填）
     * 多个逗号间隔，最多300
     */
    private String corridorIds;
} 