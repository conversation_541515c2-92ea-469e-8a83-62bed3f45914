package com.deepinnet.spatiotemporalplatform.model.gps;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * GpsModel
 * Author: chenkaiyang
 * Date: 2024/11/22
 */
@Data
public class RemoteGps implements Serializable {
    private String userAccount;

    private String userName;

    private String longitude;

    private String latitude;

    private String lng;

    private String lat;

    private Date gpsArriveTime;

    private Date lastUpdateTime;

    private Integer online;

    private Date onlineTime;

    private Date offlineTime;

    private String enterpriseId;

    private String enterpriseName;
}
