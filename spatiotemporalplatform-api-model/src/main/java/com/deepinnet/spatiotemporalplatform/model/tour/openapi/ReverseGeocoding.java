package com.deepinnet.spatiotemporalplatform.model.tour.openapi;

import com.deepinnet.spatiotemporalplatform.common.response.HttpOpenApiResponse;
import lombok.*;

/**
 * <AUTHOR> wong
 * @create 2024/9/25 15:58
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReverseGeocoding extends HttpOpenApiResponse {

    /**
     * 逆地理编码列表
     */
    private RegeoCode regeocode;
}
