package com.deepinnet.spatiotemporalplatform.model.staticpoi;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 静态核心POI查询条件
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-10
 */
@Data
public class StaticPoiQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * POI ID
     */
    private String poiId;

    /**
     * POI名称
     */
    private String poiName;

    /**
     * 当前页码
     */
    private Integer pageNum = 1;

    /**
     * 每页数量
     */
    private Integer pageSize = 100;
} 