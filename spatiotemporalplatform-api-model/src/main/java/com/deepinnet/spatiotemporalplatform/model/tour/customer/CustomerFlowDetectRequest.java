package com.deepinnet.spatiotemporalplatform.model.tour.customer;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.tour.PeopleFlowDetectConstant;
import com.deepinnet.spatiotemporalplatform.common.enums.SignTypeEnum;
import com.deepinnet.spatiotemporalplatform.common.request.*;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonResponse;
import com.deepinnet.spatiotemporalplatform.model.tour.enums.*;
import com.deepinnet.spatiotemporalplatform.model.tour.gd.GdTouristDistribution;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**

 *
 * <AUTHOR>
 * @since 2024-08-26 星期一
 * #488
 **/
@Data
public class CustomerFlowDetectRequest implements HttpApiRequest {

    private CustomerFlowDetectUrlParams urlParams;

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {
        };
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<HttpApiJsonResponse<CustomerFlowDetect>>() {
        };
    }

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public String apiUrl() {
        return PeopleFlowDetectConstant.PEOPLE_FLOW_DETECT_API_URL;
    }

    @Override
    public SignTypeEnum signType() {
        return SignTypeEnum.TOKEN;
    }

    public String saveDataTableName() {
        // 获取上周旅客去往景区分布
        if (StringUtils.equals(urlParams.getFlowType(), FlowTypeEnum.TOTAL_FLOW.getValue()) && StringUtils.equals(urlParams.getTimeType(), TimeTypeEnum.WEEK.getValue())) {
            return "gd_tourist_distribution";
        }

        return null;
    }

    public Object transformData(Object responseData) {
        CustomerFlowDetect groupFlowDetect = (CustomerFlowDetect) responseData;
        if (groupFlowDetect == null) {
            return null;
        }

        CustomerFlowDetect.IndexData indexData = groupFlowDetect.getIndexData();
        if (indexData == null) {
            return null;
        }

        List<CustomerFlowDetect.Query> dataList = indexData.getQuery();

        // 获取上周旅客去往景区分布
        if (StringUtils.equals(urlParams.getFlowType(), FlowTypeEnum.TOTAL_FLOW.getValue()) && StringUtils.equals(urlParams.getTimeType(), TimeTypeEnum.WEEK.getValue())) {
            return convertToGdDistribution(dataList);
        }

        return null;
    }

    private List<GdTouristDistribution> convertToGdDistribution(List<CustomerFlowDetect.Query> touristDistributeList) {
        List<GdTouristDistribution> touristDistributionDOs = Lists.newArrayList();
        touristDistributeList.forEach(touristDistribute -> {
            GdTouristDistribution touristDistributionDO = new GdTouristDistribution();
            touristDistributionDO.setAreaCode(urlParams.getAreaId());
            touristDistributionDO.setAdCode(urlParams.getAdcode());
            touristDistributionDO.setCount(String.valueOf(touristDistribute.getIndex()));
            touristDistributionDOs.add(touristDistributionDO);
        });

        return touristDistributionDOs;
    }
}
