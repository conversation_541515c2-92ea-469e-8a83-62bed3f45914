package com.deepinnet.spatiotemporalplatform.model.reggov.flow.people;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.DistrictIndexConstant;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonResponse;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.transform.TransformGridRealTimePeopleFlow;
import com.deepinnet.spatiotemporalplatform.model.util.MD5Util;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 网格实时人流和活力指数请求参数
 *
 * <AUTHOR>
 * @version 2024-07-29
 */
@Data
public class GridRealTimePeopleFlowRequest implements HttpApiRequest {
    /**
     * URL参数
     */
    private GridRealTimePeopleFlowParams urlParams;

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<HttpApiJsonResponse<GridRealTimePeopleFlowData>>() {
        };
    }

    @Override
    public String apiUrl() {
        return DistrictIndexConstant.GRID_REALTIME_PEOPLE_FLOW_API_URL;
    }

    @Override
    public String saveDataTableName() {
        return "gd_grid_real_time_people_flow";
    }

    @Override
    public Object transformData(Object responseData) {
        if (ObjectUtil.isNull(responseData)) {
            return null;
        }

        GridRealTimePeopleFlowData gridRealTimePeopleFlow = (GridRealTimePeopleFlowData) responseData;

        if (CollUtil.isEmpty(gridRealTimePeopleFlow.getList())) {
            return null;
        }

        return gridRealTimePeopleFlow.getList().stream()
                .map(flow -> TransformGridRealTimePeopleFlow.builder()
                        .customAreaId(flow.getCustomAreaId())
                        .gridCenter(flow.getGridCenter())
                        .gridName(flow.getGridName())
                        .peopleFlow(flow.getPeopleFlow())
                        .density(flow.getDensity())
                        .activityIndex(flow.getActivityIndex())
                        .historyAvgFlow(flow.getHistoryAvgFlow())
                        .maxFlow(flow.getMaxFlow())
                        .ds(flow.getDs())
                        .md5(MD5Util.getObjectMD5(flow)).build()).collect(Collectors.toList());
    }
}
