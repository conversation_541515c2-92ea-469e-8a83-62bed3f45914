package com.deepinnet.spatiotemporalplatform.model.traffic.event;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.TrafficEventConstant;
import com.deepinnet.spatiotemporalplatform.common.request.*;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonArrayResponse;
import com.deepinnet.spatiotemporalplatform.model.util.MD5Util;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.*;

/**
 * 交通事件查询请求
 *
 * <AUTHOR>
 * @version 2024-07-30
 */
@Data
public class TrafficEventRequest implements HttpApiRequest {

    private TrafficEventUrlParams urlParams;

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<HttpApiJsonArrayResponse<TrafficEvent>>() {
        };
    }

    @Override
    public UrlParams urlParams() {
        return urlParams;
    }

    @Override
    public boolean useTempApiKey() {
        return false;
    }

    @Override
    public String apiUrl() {
        return TrafficEventConstant.TRAFFIC_EVENT_API_URL;
    }

    @Override
    public String saveDataTableName() {
        return "gd_traffic_event";
    }

    @Override
    public Object transformData(Object responseData) {
        List<TrafficEvent> trafficEvents = (List<TrafficEvent>) responseData;
        List<GdTrafficEvent> gdEvents = Lists.newArrayList();

        trafficEvents.forEach(trafficEvent -> {
            GdTrafficEvent event = new GdTrafficEvent();
            event.setBrief(trafficEvent.getBrief());
            event.setEndTime(trafficEvent.getEndTime());
            event.setEventDesc(trafficEvent.getEventDesc());
            event.setEventId(trafficEvent.getEventID());
            event.setEventType(trafficEvent.getEventType());
            event.setExpressway(trafficEvent.getExpressway());
            event.setLines(trafficEvent.getLines());
            event.setNickName(trafficEvent.getNickName());
            event.setOffcial(trafficEvent.getOffcial());
            event.setPicture(trafficEvent.getPicture());
            event.setRoadName(trafficEvent.getRoadName());
            event.setSource(trafficEvent.getSource());
            event.setStartTime(trafficEvent.getStartTime());
            event.setUpdateTime(trafficEvent.getUpdateTime());
            event.setX(trafficEvent.getX());
            event.setY(trafficEvent.getY());
            String uniqKey = MD5Util.getObjectMD5(trafficEvent);
            event.setUniqKey(uniqKey);
            gdEvents.add(event);
        });

        return gdEvents;
    }
}
