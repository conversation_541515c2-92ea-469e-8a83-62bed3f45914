package com.deepinnet.spatiotemporalplatform.model.tour.openapi;

import com.deepinnet.spatiotemporalplatform.common.response.HttpOpenApiResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2025/01/15
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)

public class PoiResponse extends HttpOpenApiResponse {

    private List<GdPoi> pois;
}
