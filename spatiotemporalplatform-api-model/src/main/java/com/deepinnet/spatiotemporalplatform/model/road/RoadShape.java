package com.deepinnet.spatiotemporalplatform.model.road;

import lombok.Data;

/**

 *
 * <AUTHOR>
 * @since 2024-11-13 星期三
 **/
@Data
public class RoadShape {

    /**
     * 道路ID
     */
    private String id;

    /**
     * 道路名称
     */
    private String name;

    /**
     * 道路描述
     */
    private String description;

    /**
     * WKT格式图形信息
     */
    private String geo;

    /**
     * 中点x坐标，⽤于label展示位置
     */
    private String centerX;

    /**
     * 中点y坐标，⽤于label展示位置
     */
    private String centerY;

    /**
     * 道路集合⻓度
     */
    private Double totalLength;

    /**
     * 城市code
     */
    private String adcode;

}
