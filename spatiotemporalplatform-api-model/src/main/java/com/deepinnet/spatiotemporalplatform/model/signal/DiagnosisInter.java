package com.deepinnet.spatiotemporalplatform.model.signal;

import lombok.Data;

/**
 * 评诊治-图形-路口静态信息数据对象
 * 
 * <AUTHOR>
 * @version 2025-06-09
 */
@Data
public class DiagnosisInter {

    /**
     * 路口id
     */
    private String interId;

    /**
     * 路口名称
     */
    private String interName;

    /**
     * 是否信控
     * 1-信控灯路口; 0-非信控灯路口
     */
    private Integer isSignlight;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 行政区编码
     */
    private String district;

    /**
     * 城市编码
     */
    private String adcode;

    /**
     * 时间版本
     * 月版本
     */
    private String ds;
} 