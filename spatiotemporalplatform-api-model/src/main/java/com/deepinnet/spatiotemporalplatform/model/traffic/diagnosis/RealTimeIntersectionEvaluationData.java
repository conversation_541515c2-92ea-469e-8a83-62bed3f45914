package com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis;

import lombok.Data;

import java.util.List;

/**
 * 实时路口评价详情
 *
 * <AUTHOR>
 * @version 2024-07-30
 */
@Data
public class RealTimeIntersectionEvaluationData {

    /**
     * 当前页数
     */
    private int currentPage;

    /**
     * 总页数
     */
    private int totalPage;

    /**
     * 具体数据列表
     */
    private List<RealTimeIntersectionDetail> list;
}
