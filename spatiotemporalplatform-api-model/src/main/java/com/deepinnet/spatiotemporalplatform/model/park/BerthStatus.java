package com.deepinnet.spatiotemporalplatform.model.park;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "车位信息")
public class BerthStatus {

    @ApiModelProperty(value = "车位编号", example = "不含楼层/物理区域的编号 001/002/003", required = true)
    private String berthCode;

    @ApiModelProperty(value = "车位空闲状态", example = "0：未知 1：空闲 2：占用", required = true)
    private String berthStatus;

    @ApiModelProperty(value = "车位所属楼层", example = "层/物理区域的编号 F1/F2/F3/B1/B2/B3", required = true)
    private String spaceFloor;

    @ApiModelProperty(value = "更新时间", example = "2025-05-09 10:10:10", required = true)
    private String updateTime;
}