package com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 历史区域交通指数
 *
 * <AUTHOR>
 * @version 2024-07-30
 */
@Data
public class HistoryDistrictIndexRanking {
    /**
     * 城市行政区划代码
     */
    @ApiModelProperty(value = "城市行政区划代码")
    private String adcode;

    /**
     * 计算总长度（米）
     */
    @ApiModelProperty(value = "计算总长度（米）")
    private int computeLength;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 自由流速度（公里/小时）
     */
    @ApiModelProperty(value = "自由流速度（公里/小时）")
    private String freeFlowSpeed;

    /**
     * 区域id编码
     */
    @ApiModelProperty(value = "区域id编码")
    private int id;

    /**
     * 拥堵延时指数值
     * 定义为实际出行耗时/自由流条件下的出行耗时
     */
    @ApiModelProperty(value = "拥堵延时指数值，定义为实际出行耗时/自由流条件下的出行耗时")
    private String idx;

    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称")
    private String name;

    /**
     * 平均速度（公里/小时）
     */
    @ApiModelProperty(value = "平均速度（公里/小时）")
    private String realSpeed;

    /**
     * 道路集合长度（米）
     */
    @ApiModelProperty(value = "道路集合长度（米）")
    private int totalLength;

    /**
     * 区域类型
     */
    @ApiModelProperty(value = "区域类型")
    private String type;
}
