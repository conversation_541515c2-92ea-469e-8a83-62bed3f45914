package com.deepinnet.spatiotemporalplatform.model.congestion;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.CongestionConstant;
import com.deepinnet.spatiotemporalplatform.common.request.*;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonResponse;
import com.deepinnet.spatiotemporalplatform.model.util.MD5Util;
import com.google.common.collect.Lists;
import lombok.Data;
import org.springframework.http.HttpMethod;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 实时拥堵预警API请求
 *
 * <AUTHOR>
 * @version 2024-07-29
 */
@Data
public class RealTimeCongestionRequest implements HttpApiRequest {
    /**
     * URL参数
     */
    private CongestionUrlParams urlParams;

    /**
     * post请求body
     */
    private RealTimeCongestionPostBody postBody;

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public PostBody postBody() {
        return this.postBody;
    }

    @Override
    public boolean useTempApiKey() {
        return false;
    }

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<HttpApiJsonResponse<CongestionData>>() {
        };
    }

    /**
     * 请求方式：POST
     *
     * @return
     */
    public HttpMethod httpMethod() {
        return HttpMethod.POST;
    }

    @Override
    public String apiUrl() {
        return CongestionConstant.REAL_TIME_CONGESTION_API_URL;
    }

    public String saveDataTableName() {
        return "gd_congestion";
    }


    public Object transformData(Object responseData) {
        if (responseData == null) {
            return null;
        }

        CongestionData congestionData = (CongestionData) responseData;
        if (CollectionUtils.isEmpty(congestionData.getList())) {
            return null;
        }

        List<GdCongestion> gdCongestionList = Lists.newArrayList();
        List<Congestion> congestionList = congestionData.getList();
        congestionList.forEach(congestion -> {
            GdCongestion gdCongestion = new GdCongestion();
            gdCongestion.setCongestionType(congestion.getCongestionType());
            gdCongestion.setAbnormalStartTime(congestion.getAbnormalStartTime());
            gdCongestion.setAngle(congestion.getAngle());
            gdCongestion.setBatchTime(congestion.getBatchTime());
            gdCongestion.setCenterPointWKT(congestion.getCenterPointWKT());
            gdCongestion.setCitycode(congestion.getCitycode());
            gdCongestion.setCongestRate(congestion.getCongestRate());
            gdCongestion.setCongestionId(congestion.getCongestionId());
            gdCongestion.setCongestionLevel(congestion.getCongestionLevel());
            gdCongestion.setDataType(congestion.getDataType());
            gdCongestion.setDirection(congestion.getDirection());
            gdCongestion.setDistance(congestion.getDistance());
            gdCongestion.setDurationMin(congestion.getDurationMin());
            gdCongestion.setEndAddress(congestion.getEndAddress());
            gdCongestion.setEndPoint(congestion.getEndPoint());
            gdCongestion.setEndRawPoint(congestion.getEndRawPoint());
            gdCongestion.setHeadIndex(congestion.getHeadIndex());
            gdCongestion.setInitLinkId(congestion.getInitLinkId());
            gdCongestion.setInitStartAddr(congestion.getInitStartAddr());
            gdCongestion.setInitStartPoint(congestion.getInitStartPoint());
            gdCongestion.setInitTime(congestion.getInitTime());
            gdCongestion.setLinks(congestion.getLinks());
            gdCongestion.setListLinkInfoState(JSONUtil.toJsonStr(congestion.getListLinkInfoState()));
            gdCongestion.setMaxDistance(congestion.getMaxDistance());
            gdCongestion.setRect(JSONUtil.toJsonStr(congestion.getRect()));
            gdCongestion.setReliability(congestion.getReliability());
            gdCongestion.setRoadId(congestion.getRoadId());
            gdCongestion.setRoadName(congestion.getRoadName());
            gdCongestion.setRoadType(congestion.getRoadType());
            gdCongestion.setSpeed(congestion.getSpeed());
            gdCongestion.setTrend(congestion.getTrend());
            gdCongestion.setState(congestion.getState());
            gdCongestion.setStartAddress(congestion.getStartAddress());
            gdCongestion.setStartPoint(congestion.getStartPoint());
            gdCongestion.setStartRawPoint(congestion.getStartRawPoint());
            gdCongestion.setStartTime(congestion.getStartTime());
            gdCongestion.setUniqKey(MD5Util.getObjectMD5(gdCongestion));
            gdCongestionList.add(gdCongestion);
        });

        return gdCongestionList;
    }
}
