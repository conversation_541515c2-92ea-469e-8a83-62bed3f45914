package com.deepinnet.spatiotemporalplatform.model.hotod;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 2024-07-29
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AreaArriveCarPredictUrlParams extends UrlParams {

    /**
     * ⽬的地区域类型 0.⾃定义区域 1.poi-枢纽 2.poi-景区 3.poi-场馆 4.poi-商圈
     */
    private String type;

    /**
     * ⽬的地区域ID：⾃定义区域id/poi id
     */
    private String ids;

    /**
     * 预测未来到达量时间最⼩值，单位：分钟 可查未来2⼩时内10分钟粒度数据，要求 为10的倍数，不⾜10分钟则舍去 minTime、maxTime不传默认未来2⼩时
     */
    private String minTime;

    /**
     * 预测未来到达量时间最大值，单位：分钟 可查未来2⼩时内10分钟粒度数据，要求 为10的倍数，不⾜10分钟则舍去 minTime、maxTime不传默认未来2⼩时
     */
    private String maxTime;

}
