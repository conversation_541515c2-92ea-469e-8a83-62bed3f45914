package com.deepinnet.spatiotemporalplatform.model.signal;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.enums.SignTypeEnum;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.PostBody;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonResponse;
import lombok.Data;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;

/**
 * 智慧交通-实时路口-路口静态信息查询服务 POST请求
 */
@Data
public class IntersectionStaticsVrcRequest implements HttpApiRequest {
    /**
     * URL参数（如需透传url参数，可用此字段）
     */
    private UrlParams urlParams;
    /**
     * POST请求体
     */
    private IntersectionStaticsPostBody postBody;

    /**
     * 设置URL参数
     */
    public void setUrlParams(UrlParams urlParams) {
        this.urlParams = urlParams;
    }
    /**
     * 设置POST请求体
     */
    public void setPostBody(IntersectionStaticsPostBody postBody) {
        this.postBody = postBody;
    }

    @Override
    public UrlParams urlParams() {
        return urlParams;
    }

    @Override
    public String apiUrl() {
        return "https://et-api.amap.com/vrc/inter/list?clientKey=%s&timestamp=%s&adcode=%s";
    }

    @Override
    public HttpMethod httpMethod() {
        return HttpMethod.POST;
    }

    @Override
    public HttpHeaders httpHeaders() {
        return null;
    }

    @Override
    public PostBody postBody() {
        return postBody;
    }

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {};
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<HttpApiJsonResponse<IntersectionStaticsResponse>>() {};
    }

    @Override
    public SignTypeEnum signType() {
        return SignTypeEnum.DIGEST;
    }

    @Override
    public boolean useTempApiKey() {
        return true;
    }


    public String saveDataTableName() {
        return "intersection_statics_vrc";
    }

} 