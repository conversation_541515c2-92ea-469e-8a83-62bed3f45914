package com.deepinnet.spatiotemporalplatform.model.signal;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.enums.SignTypeEnum;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.PostBody;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonArrayResponse;
import lombok.Data;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;

/**
 * 评诊治-通勤-走廊排名Request类
 * 
 * <AUTHOR>
 * @version 2025-06-09
 */
@Data
public class CorridorRankingRequest implements HttpApiRequest {

    /**
     * URL参数
     */
    private UrlParams urlParams;

    /**
     * POST请求体
     */
    private CorridorRankingPostBody postBody;

    /**
     * 设置URL参数
     */
    public void setUrlParams(UrlParams urlParams) {
        this.urlParams = urlParams;
    }

    /**
     * 设置POST请求体
     */
    public void setPostBody(CorridorRankingPostBody postBody) {
        this.postBody = postBody;
    }

    @Override
    public UrlParams urlParams() {
        return urlParams;
    }

    @Override
    public String apiUrl() {
        return "https://et-api.amap.com/diagnosis/od/corridor/rankCorridorTop?clientKey=%s&timestamp=%s&adcode=%s&timespan=%s&ds=%s&district=%s&size=%s";
    }

    @Override
    public HttpMethod httpMethod() {
        return HttpMethod.POST;
    }

    @Override
    public HttpHeaders httpHeaders() {
        return null;
    }

    @Override
    public PostBody postBody() {
        return postBody;
    }

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {};
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<HttpApiJsonArrayResponse<CorridorRanking>>() {};
    }

    @Override
    public SignTypeEnum signType() {
        return SignTypeEnum.DIGEST;
    }

    @Override
    public boolean useTempApiKey() {
        return true;
    }
} 