package com.deepinnet.spatiotemporalplatform.model.tour.heat;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.tour.RealHeatConstant;
import com.deepinnet.spatiotemporalplatform.common.enums.SignTypeEnum;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonResponse;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.transform.TransformRealHeat;
import com.deepinnet.spatiotemporalplatform.model.util.MD5Util;
import lombok.Data;

/**

 *
 * <AUTHOR>
 * @since 2024-08-26 星期一
 * #484
 **/
@Data
public class RealHeatRequest implements HttpApiRequest {

    private RealHeatUrlParams urlParams;

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {};
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<HttpApiJsonResponse<RealHeat>>() {};
    }

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public String apiUrl() {
        return RealHeatConstant.REAL_HEAT_API_URL;
    }

    @Override
    public SignTypeEnum signType() {
        return SignTypeEnum.TOKEN;
    }

    @Override
    public String saveDataTableName() {
        return "gd_real_heat";
    }

    @Override
    public Object transformData(Object responseData) {

        if (ObjectUtil.isNull(responseData)) {
            return null;
        }

        RealHeat realHeat = (RealHeat) responseData;

        return TransformRealHeat.builder()
                .chargeCnt(realHeat.getChargeCnt())
                .indexData(JSONObject.toJSONString(realHeat.getIndexData()))
                .md5(MD5Util.getObjectMD5(realHeat)).build();
    }
}
