package com.deepinnet.spatiotemporalplatform.model.tour.customer;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**

 *
 * <AUTHOR>
 * @since 2024-08-27 星期二
 **/
@Data
public class CustomerFlowDetectGrid {

    @JSONField(name = "charge_cnt")
    private Integer chargeCnt;
    @JSONField(name = "index_data")
    private IndexData indexData;

    @Data
    public static class IndexData {
        private Query query;
    }

    @Data
    public static class Query {
        @JSONField(name = "data_size")
        private int dataSize;
        @JSONField(name = "area_data")
        private Map<String, List<Integer>> areaData;
        @JSONField(name = "max_index")
        private int maxIndex;
    }
}
