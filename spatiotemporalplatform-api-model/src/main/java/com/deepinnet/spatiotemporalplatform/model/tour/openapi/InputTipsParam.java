package com.deepinnet.spatiotemporalplatform.model.tour.openapi;

import com.deepinnet.spatiotemporalplatform.common.constants.OpenApiConstant;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <PERSON><PERSON> zeng<PERSON>
 * Date 2025-07-28
 **/

@EqualsAndHashCode(callSuper = true)
@Data
public class InputTipsParam extends UrlParams {

    private String key = OpenApiConstant.INPUT_TIPS_KEY;

    private String location;

    private String city;

    private String keywords;
}
