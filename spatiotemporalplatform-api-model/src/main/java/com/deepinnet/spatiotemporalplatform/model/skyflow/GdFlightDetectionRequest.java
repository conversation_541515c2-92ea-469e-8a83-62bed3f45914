package com.deepinnet.spatiotemporalplatform.model.skyflow;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.OpenApiConstant;
import com.deepinnet.spatiotemporalplatform.common.request.*;
import lombok.Data;

/**
 * 高德获取飞行监测数据
 */
@Data
public class GdFlightDetectionRequest implements HttpApiRequest {

    private GdFlightDetectionParam urlParams;

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {
        };
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<GdFlightDetectionResponse>() {
        };
    }

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public String apiUrl() {
        return OpenApiConstant.GD_FLIGHT_DETECTION_URL;
    }
}
