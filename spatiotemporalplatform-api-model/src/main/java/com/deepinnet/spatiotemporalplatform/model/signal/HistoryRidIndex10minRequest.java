package com.deepinnet.spatiotemporalplatform.model.signal;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.enums.SignTypeEnum;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.PostBody;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonArrayResponse;
import lombok.Data;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;

/**
 * 查询历史10分钟级RID的指标接口请求类
 * 
 * <AUTHOR>
 * @version 2025-06-09
 */
@Data
public class HistoryRidIndex10minRequest implements HttpApiRequest {

    /**
     * URL参数
     */
    private HistoryRidIndex10minUrlParams urlParams;

    /**
     * 设置URL参数
     */
    public void setUrlParams(HistoryRidIndex10minUrlParams urlParams) {
        this.urlParams = urlParams;
    }

    @Override
    public UrlParams urlParams() {
        return urlParams;
    }

    @Override
    public String apiUrl() {
        return "https://et-api.amap.com/diagnosis/rid/hisRidIndex10min?clientKey=%s&timestamp=%s&rid=%s&startDs=%s&endDs=%s&adcode=%s&district=%s";
    }

    @Override
    public HttpMethod httpMethod() {
        return HttpMethod.GET;
    }

    @Override
    public HttpHeaders httpHeaders() {
        return null;
    }

    @Override
    public PostBody postBody() {
        return null;
    }

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {};
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<HttpApiJsonArrayResponse<HistoryRidIndex10min>>() {};
    }

    @Override
    public SignTypeEnum signType() {
        return SignTypeEnum.DIGEST;
    }

    @Override
    public boolean useTempApiKey() {
        return true;
    }

    public String saveDataTableName() {
        return "history_rid_index_10min";
    }

} 