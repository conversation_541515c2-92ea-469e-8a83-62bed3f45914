package com.deepinnet.spatiotemporalplatform.model.skyflow;

import com.deepinnet.spatiotemporalplatform.common.request.PostBody;
import com.deepinnet.spatiotemporalplatform.enums.WeatherGridFactor;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2025/3/11 09:57
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WeatherGridPostBody extends PostBody {
    /**
     * wkt
     */
    private String bounds;

    /**
     * 网格的等级，目前支持4-7级
     */
    private Integer level;

    /**
     * 天气数据因子
     * */
    private WeatherGridFactor factor;

    /**
     * 风力高度
     * */
    private Integer windForceAltitude;
}
