package com.deepinnet.spatiotemporalplatform.model.reggov.flow.people;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 实时人流
 *
 * <AUTHOR>
 * @version 2024-07-30
 */
@Data
public class RealTimePeopleFlowData {
    /**
     * 自定义区域ID
     */
    @ApiModelProperty(value = "自定义区域ID")
    private String customAreaId;

    /**
     * 时间（yyyy-MM-dd HH:mm:ss）
     */
    @ApiModelProperty(value = "时间（yyyy-MM-dd HH:mm:ss）")
    private String time;

    /**
     * 人流活力指数（数值越大，人流越活跃）
     */
    @ApiModelProperty(value = "人流活力指数（数值越大，人流越活跃）")
    private Double activityIndex;

    /**
     * 人流量（人次）
     */
    @ApiModelProperty(value = "人流量（人次）")
    private Integer flow;


    private Integer preFlow;

    /**
     * 今日累计人流量（从今日凌晨到当前批次累计去重后的人流量）（仅升级版）
     */
    private Integer todayCumulativeFlow;

    /**
     * 历史平均人流量（仅升级版）
     */
    private Double avgFlow;

    /**
     * 历史最大人流量（仅升级版）
     */
    private Integer maxFlow;
}
