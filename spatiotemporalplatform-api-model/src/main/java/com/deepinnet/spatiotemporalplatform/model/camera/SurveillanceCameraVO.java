package com.deepinnet.spatiotemporalplatform.model.camera;

import com.deepinnet.spatiotemporalplatform.model.common.GeoPoint;
import lombok.Data;

import java.util.Date;

/**
 * Creator zeng<PERSON>ui
 * Date 2024-08-03
 **/

@Data
public class SurveillanceCameraVO {

    /**
     * 自增ID
     */
    private Integer id;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 监控视频编号
     */
    private String cameraCode;

    /**
     * 名称
     */
    private String name;

    /**
     * 类型
     */
    private String type;

    /**
     * 安装位置
     */
    private String installationLocation;

    /**
     * 经纬度
     */
    private GeoPoint point;
}
