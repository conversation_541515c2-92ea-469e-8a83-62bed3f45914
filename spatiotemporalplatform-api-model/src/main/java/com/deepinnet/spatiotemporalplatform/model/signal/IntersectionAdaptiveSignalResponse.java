package com.deepinnet.spatiotemporalplatform.model.signal;

import lombok.Data;

import java.util.List;

/**
 * 智慧交通-实时路口-自适应信控路口数据服务 响应体
 */
@Data
public class IntersectionAdaptiveSignalResponse {
    /** 当前页数 */
    private Integer currentPage;
    /** 总页数 */
    private Integer totalPage;
    /** 数据总量 */
    private Integer recordNum;
    /** 路口指标列表 */
    private List<IntersectionIndex> list;

    @Data
    public static class IntersectionIndex {
        /** 数据计算批次，单位毫秒 */
        private Long batchTime;
        /** 路口ID */
        private String interId;
        /** 路口名称 */
        private String interName;
        /** 路口主体经度 */
        private Double lng;
        /** 路口主体纬度 */
        private Double lat;
        /** 路口类型，0-主路口 */
        private Integer turnDirNo;
        /** 最大排队长度，单位米 */
        private Double queueLenMax;
        /** 平均排队长度，单位米 */
        private Double queueLenAvg;
        /** 通过路口的全部流量，单位车辆数 */
        private Double passFlow;
        /** 车均灯前停车时间，单位秒 */
        private Double stopTime;
        /** 车均停车次数 */
        private Double stopTimes;
        /** 车均通过速度 */
        private Double passSpeed;
        /** 车均不停车通过速度 */
        private Double noStopPassSpeed;
        /** 延误指数 */
        private Double delayIndex;
        /** 路口状态：0-正常、1-失衡、2-溢出、3-失衡溢出 */
        private Integer idxState;
        /** 路口评级：A-F */
        private String los;
        /** 置信度 1低2中3高 */
        private Integer confidence;
        /** 行政区ADCODE */
        private String district;
        /** 城市ADCODE */
        private String adcode;
        /** 路口分方向详情 */
        private List<DirectionInterIndex> directionInterIndex;
    }

    @Data
    public static class DirectionInterIndex {
        /** 数据计算批次，单位毫秒 */
        private Long batchTime;
        /** 路口ID */
        private String interId;
        /** 进口道ID */
        private String frid;
        /** 进口道坐标 */
        private String lngLatSeq;
        /** 分方向类型 0-方向聚合，1-左转（含调头）、2-直、3-右 */
        private Integer turnDirNo;
        /** 最大排队长度，单位米 */
        private Double queueLenMax;
        /** 平均排队长度，单位米 */
        private Double queueLenAvg;
        /** 通过路口的全部流量，单位车辆数 */
        private Double passFlow;
        /** 车均灯前停车时间，单位秒 */
        private Double stopTime;
        /** 车均停车次数 */
        private Double stopTimes;
        /** 车均通过速度 */
        private Double passSpeed;
        /** 车均不停车通过速度 */
        private Double noStopPassSpeed;
        /** 分方向延误指数 */
        private Double delayIndex;
        /** 分方向评级：A-F */
        private String los;
        /** 分方向等级 */
        private Integer level;
        /** 转向/进口道状态 0正常，1溢出/失衡 */
        private Integer idxState;
        /** 行政区ADCODE */
        private String district;
        /** 城市ADCODE */
        private String adcode;
    }
} 