package com.deepinnet.spatiotemporalplatform.model.parkinglot;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;

/**
 * AreaParkingLotSummaryDTO
 * Author: chenkaiyang
 * Date: 2024/8/2
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AreaParkingLotSummaryDTO {
    @ApiModelProperty(value = "区域编码")
    private String areaCode;
    @ApiModelProperty(value = "停车场列表")
    private List<ParkingLotDTO> parkingLotList;
    @ApiModelProperty(value = "总车位数")
    private Integer totalSpaces;
    @ApiModelProperty(value = "可用车位数")
    private Integer availableSpaces;
    @ApiModelProperty(value = "车位饱和度")
    private Double saturation;
    @ApiModelProperty(value = "停车场数量")
    private Integer size;

    private Integer calculateTotalSpaces() {
        return parkingLotList != null ? parkingLotList.stream().mapToInt(p -> Optional.ofNullable(p.getTotalSpaces()).orElse(0)).sum() : 0;
    }

    private Integer calculateAvailableSpaces() {
        return parkingLotList != null ? parkingLotList.stream().mapToInt(p -> Optional.ofNullable(p.getAvailableSpaces()).orElse(0)).sum() : 0;
    }

    public void buildSaturation() {
        this.totalSpaces = this.calculateTotalSpaces();
        this.availableSpaces = this.calculateAvailableSpaces();
        if (totalSpaces > 0) {
            this.saturation = new BigDecimal((this.totalSpaces.doubleValue() - this.availableSpaces.doubleValue()) / this.totalSpaces.doubleValue()).setScale(2, RoundingMode.FLOOR).doubleValue();
        }
    }
}
