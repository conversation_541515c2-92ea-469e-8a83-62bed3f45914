package com.deepinnet.spatiotemporalplatform.model.dify;

import com.alibaba.fastjson2.annotation.JSONField;
import com.deepinnet.spatiotemporalplatform.common.request.PostBody;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <PERSON><PERSON> z<PERSON>
 * Date 2025-01-22
 **/

@EqualsAndHashCode(callSuper = true)
@Data
public class DifyRunWorkflowPostBody<T> extends PostBody {

    @JSONField(name = "response_mode")
    private String responseMode;

    private String user;

    private T inputs;
}
