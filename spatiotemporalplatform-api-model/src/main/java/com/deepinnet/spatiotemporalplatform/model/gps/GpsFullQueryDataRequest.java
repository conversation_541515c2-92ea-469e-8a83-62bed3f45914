package com.deepinnet.spatiotemporalplatform.model.gps;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;

/**
 * <AUTHOR> wong
 * @create 2024/11/23 10:35
 * @Description
 */
@Data
public class GpsFullQueryDataRequest implements HttpApiRequest {
    @Override
    public TypeReference responseBodyType() {
        return null;
    }

    @Override
    public UrlParams urlParams() {
        return null;
    }

    @Override
    public String apiUrl() {
        return null;
    }
}
