package com.deepinnet.spatiotemporalplatform.model.tour.flow;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.tour.PassengerFlowConstant;
import com.deepinnet.spatiotemporalplatform.common.enums.SignTypeEnum;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonResponse;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.transform.TransformPassengerFlow;
import com.deepinnet.spatiotemporalplatform.model.util.MD5Util;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**

 *
 * <AUTHOR>
 * @since 2024-08-26 星期一
 * #490
 **/
@Data
public class PassengerFlowRequest implements HttpApiRequest {

    private PassengerFlowUrlParams urlParams;

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {};
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<HttpApiJsonResponse<PassengerFlow>>() {};
    }

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public String apiUrl() {
        return PassengerFlowConstant.PASSENGER_FLOW_API_URL;
    }

    @Override
    public SignTypeEnum signType() {
        return SignTypeEnum.TOKEN;
    }

    @Override
    public String saveDataTableName() {
        return "gd_passenger_flow";
    }

    @Override
    public Object transformData(Object responseData) {

        if (ObjectUtil.isNull(responseData)) {
            return null;
        }

        PassengerFlow passengerFlow = (PassengerFlow) responseData;

        if (ObjectUtil.isNull(passengerFlow.getIndexData()) || CollUtil.isEmpty(passengerFlow.getIndexData().getQuery())) {
            return null;
        }

        return passengerFlow.getIndexData().getQuery().stream().map(e -> TransformPassengerFlow.builder()
                .chargeCnt(passengerFlow.getChargeCnt())
                .index(e.getIndex())
                .md5(MD5Util.getObjectMD5(passengerFlow)).build()).collect(Collectors.toList());
    }
}
