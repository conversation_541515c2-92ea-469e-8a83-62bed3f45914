package com.deepinnet.spatiotemporalplatform.model.traffic.diagnosis;

import com.deepinnet.spatiotemporalplatform.common.constants.DistrictIndexConstant;
import lombok.Data;

/**
 * 实时区域交通指数全量排行API请求
 *
 * <AUTHOR>
 * @version 2024-07-29
 */
@Data
public class RealTimeDistrictIndexRankingAllRequest extends RealTimeDistrictIndexRankingRequest {
    @Override
    public String apiUrl() {
        return DistrictIndexConstant.REAL_TIME_DISTRICT_INDEX_RANKING_ALL_API_URL;
    }
}
