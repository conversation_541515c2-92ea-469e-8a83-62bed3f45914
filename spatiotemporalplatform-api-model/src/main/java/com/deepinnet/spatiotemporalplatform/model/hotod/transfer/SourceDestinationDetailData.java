package com.deepinnet.spatiotemporalplatform.model.hotod.transfer;

import lombok.Data;

import java.time.LocalDateTime;

/**

 *
 * <AUTHOR>
 * @since 2024-11-18 星期一
 **/
@Data
public class SourceDestinationDetailData {

    /**
     * 城市code
     */
    private String adcode;

    /**
     * 来源地/⽬的地id
     */
    private String sourceId;

    /**
     * 来源地/⽬的地名称
     */
    private String sourceName;

    /**
     * 来源地/⽬的地中⼼点坐标
     */
    private String sourceCenter;

    /**
     * ⻋流量
     */
    private Double flow;

    /**
     * 批次号
     */
    private String batchId;

    /**
     * 时间戳
     */
    private LocalDateTime timestamp;

    /**
     * 唯一约束
     */
    private String md5;

}
