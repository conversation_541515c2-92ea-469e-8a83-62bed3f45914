package com.deepinnet.spatiotemporalplatform.model.firecontrol;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.DifyConstant;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.PostBody;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.model.dify.DifyRunWorkflowPostBody;
import com.deepinnet.spatiotemporalplatform.model.dify.DifyRunWorkflowResponse;
import lombok.Data;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;

import static com.deepinnet.spatiotemporalplatform.common.constants.DifyConstant.*;

/**
 * Creator zengjuerui
 * Date 2025-01-22
 **/

@Data
public class CompleteFireAlarmAddressesRequest implements HttpApiRequest {

    private CompleteFireAlarmAddressesParams params;

    @Override
    public TypeReference<DifyRunWorkflowResponse> responseBodyType() {
        return new TypeReference<>() {
        };
    }

    @Override
    public UrlParams urlParams() {
        return this.params;
    }

    @Override
    public String apiUrl() {
        return DifyConstant.RUN_WORK_FLOW_URL;
    }

    @Override
    public HttpHeaders httpHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", "Bearer " + APP_ID);
        return headers;
    }

    @Override
    public HttpMethod httpMethod() {
        return HttpMethod.POST;
    }

    @Override
    public PostBody postBody() {
        CompleteFireAlarmAddressesContent content = new CompleteFireAlarmAddressesContent();
        content.setLocation(params.getLocation());
        content.setDesc(params.getDesc());

        CompleteFireAlarmAddressesPostBody res = new CompleteFireAlarmAddressesPostBody();
        res.setUser(DEFAULT_USER);
        res.setInputs(content);
        res.setResponseMode(DEFAULT_RESPONSE_MODE);
        return res;
    }
}
