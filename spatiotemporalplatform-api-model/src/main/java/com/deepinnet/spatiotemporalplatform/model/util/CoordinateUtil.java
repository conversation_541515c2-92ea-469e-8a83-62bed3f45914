package com.deepinnet.spatiotemporalplatform.model.util;

import com.deepinnet.digitaltwin.common.util.CoordinateTransform;
import com.deepinnet.digitaltwin.common.util.PointCoordinate;
import org.locationtech.jts.geom.Point;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2025-03-15
 */
public class CoordinateUtil {

    // wgs84坐标系转gcj02坐标系
    public static PointCoordinate wgs84ToGcj02(String lng, String lat) {
        CoordinateConverter coordinateConverter = CoordinateConverterFactory.getConverter(CoordinateSystemEnum.WGS84, CoordinateSystemEnum.GCJ02);
        PointCoordinate convertPointCoordinate = coordinateConverter.convert(lng, lat);
        return convertPointCoordinate;
    }

    // gcj02坐标系转wgs84坐标系
    public static PointCoordinate gcj02ToWgs84(String lng, String lat) {
        CoordinateConverter coordinateConverter = CoordinateConverterFactory.getConverter(CoordinateSystemEnum.GCJ02, CoordinateSystemEnum.WGS84);
        PointCoordinate convertPointCoordinate = coordinateConverter.convert(lng, lat);
        return convertPointCoordinate;
    }

    public static class CoordinateConverterFactory {

        private static final Map<String, CoordinateConverter> converters = new HashMap<>();

        static {
            // 注册转换函数
            register(CoordinateSystemEnum.WGS84, CoordinateSystemEnum.GCJ02, (lng, lat) -> CoordinateTransform.transformWGS84ToGCJ02(Double.parseDouble(lng), Double.parseDouble(lat)));
            register(CoordinateSystemEnum.WGS84, CoordinateSystemEnum.BD09, (lng, lat) -> CoordinateTransform.transformWGS84ToBD09(Double.parseDouble(lng), Double.parseDouble(lat)));
            register(CoordinateSystemEnum.BD09, CoordinateSystemEnum.GCJ02, (lng, lat) -> CoordinateTransform.transformBD09ToGCJ02(Double.parseDouble(lng), Double.parseDouble(lat)));
            register(CoordinateSystemEnum.BD09, CoordinateSystemEnum.WGS84, (lng, lat) -> CoordinateTransform.transformBD09ToWGS84(Double.parseDouble(lng), Double.parseDouble(lat)));
            register(CoordinateSystemEnum.GCJ02, CoordinateSystemEnum.BD09, (lng, lat) -> CoordinateTransform.transformGCJ02ToBD09(Double.parseDouble(lng), Double.parseDouble(lat)));
            register(CoordinateSystemEnum.GCJ02, CoordinateSystemEnum.WGS84, (lng, lat) -> CoordinateTransform.transformGCJ02ToWGS84(Double.parseDouble(lng), Double.parseDouble(lat)));
            register(CoordinateSystemEnum.GCJ02, CoordinateSystemEnum.GCJ02, (lng, lat) -> new PointCoordinate(Double.parseDouble(lng), Double.parseDouble(lat)));
            register(CoordinateSystemEnum.WGS84, CoordinateSystemEnum.WGS84, (lng, lat) -> new PointCoordinate(Double.parseDouble(lng), Double.parseDouble(lat)));
            register(CoordinateSystemEnum.BD09, CoordinateSystemEnum.BD09, (lng, lat) -> new PointCoordinate(Double.parseDouble(lng), Double.parseDouble(lat)));
        }

        public static void register(CoordinateSystemEnum sourceType, CoordinateSystemEnum targetType, CoordinateConverter converter) {
            String key = generateKey(sourceType, targetType);
            converters.put(key, converter);
        }

        public static CoordinateConverter getConverter(CoordinateSystemEnum sourceType, CoordinateSystemEnum targetType) {
            String key = generateKey(sourceType, targetType);
            CoordinateConverter converter = converters.get(key);
            if (converter == null) {
                throw new IllegalArgumentException("Unsupported conversion type: " + key);
            }
            return converter;
        }

        private static String generateKey(CoordinateSystemEnum sourceType, CoordinateSystemEnum targetType) {
            return sourceType.name() + "_" + targetType.name();
        }

    }
}

