package com.deepinnet.spatiotemporalplatform.model.skyflow;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 2025-03-20
 */
@Data
public class LandingPoint implements Serializable {

    /**
     * 编号
     */
    private String code;

    /**
     * 高度
     */
    private String height;

    /**
     * 海拔高度
     */
    private String alt;

    /**
     * 名称
     */
    private String name;

    /**
     * 起降点坐标
     */
    private String position;

    /**
     * 半径，单位千米
     */
    private String radius;

    /**
     * 地址
     */
    private String address;

    /**
     * 状态
     */
    private String status;

}
