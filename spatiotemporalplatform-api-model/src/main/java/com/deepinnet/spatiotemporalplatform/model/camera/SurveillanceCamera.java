package com.deepinnet.spatiotemporalplatform.model.camera;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Creator zeng<PERSON>erui
 * Date 2024-07-30
 **/

@Data
public class SurveillanceCamera implements Serializable {

    /**
     * 自增ID
     */
    private Integer id;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 监控视频编号
     */
    private String cameraCode;

    /**
     * 名称
     */
    private String name;

    /**
     * 类型
     */
    private String type;

    /**
     * 安装位置
     */
    private String installationLocation;

    /**
     * 经纬度
     */
    private String coordinates;

    /**
     * 纬度
     */
    private String lat;
    /**
     * 经度
     */
    private String lng;

    /**
     * 本地调用使用
     */
    private String videoStreamUrl;

    /**
     * 有无过车数据
     */
    private Boolean hasPassCarData;

    /**
     * 摄像头在线状态
     */
    private String status;

    /**
     * 摄像头来源
     */
    private String source;
}
