package com.deepinnet.spatiotemporalplatform.model.tour.openapi;

import com.deepinnet.spatiotemporalplatform.common.constants.OpenApiConstant;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
public class ReverseGeocodingParam extends UrlParams {

    /**
     * 用户key
     */
    private String key = OpenApiConstant.USER_KEY;

    /**
     * 坐标，用,分割
     */
    private String location;
}
