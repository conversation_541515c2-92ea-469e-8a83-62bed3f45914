package com.deepinnet.spatiotemporalplatform.model.intercom;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Description: 
 * Date: 2024/11/7
 * Author: lijunheng
 */

/**
 * 对讲机位置记录表
 */
@Data
public class IntercomPointRecord implements Serializable {

    private Long id;

    /**
     * 对讲机编号
     */
    private String code;

    /**
     * 用户账号
     */
    private String userAccount;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 经度
     */
    private String lng;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 最近一次位置更新时间
     */
    private Date lastUpdateTime;

    /**
     * 所在区域编号
     */
    private String areaCode;

    /**
     * 经纬度对象
     */
    private String coordinate;

    private Date gpsArriveTime;

    private Boolean online;

    private Date onlineTime;

    private Date offlineTime;

    private String enterpriseId;

    private String enterpriseName;

    private String contact;
}