package com.deepinnet.spatiotemporalplatform.model.tour.openapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

/**
 * <AUTHOR> wong
 * @create 2024/9/26 10:30
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AddressComponent {
    private String province;

    private String adcode;

    private String district;

    private String towncode;

    @JsonProperty("streetNumber")
    private StreetNumber streetNumber;

    @JsonProperty("neighborhood")
    private Neighborhood neighborhood;

    @JsonProperty("building")
    private Neighborhood building;

    private String country;

    private String township;

    private String citycode;
}
