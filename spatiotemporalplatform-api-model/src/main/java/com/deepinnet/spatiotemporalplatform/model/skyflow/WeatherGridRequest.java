package com.deepinnet.spatiotemporalplatform.model.skyflow;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.OpenApiConstant;
import com.deepinnet.spatiotemporalplatform.common.request.*;
import lombok.Data;
import org.springframework.http.HttpMethod;

/**
 * <AUTHOR> wong
 * @create 2025/3/11 10:25
 * @Description
 */
@Data
public class WeatherGridRequest implements HttpApiRequest {

    /**
     * post请求body
     */
    private WeatherGridPostBody postBody;

    private WeatherGridUrlParams urlParams;

    @Override
    public PostBody postBody() {
        return this.postBody;
    }

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {
        };
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<WeatherGridQueryResponse>() {
        };
    }

    @Override
    public UrlParams urlParams() {
        return urlParams;
    }

    /**
     * 请求方式：POST
     *
     * @return
     */
    public HttpMethod httpMethod() {
        return HttpMethod.POST;
    }

    @Override
    public String apiUrl() {
        return OpenApiConstant.WEATHER_GRID_QUERY_URL;
    }

}
