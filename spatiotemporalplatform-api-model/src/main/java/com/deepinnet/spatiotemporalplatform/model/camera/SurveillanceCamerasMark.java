package com.deepinnet.spatiotemporalplatform.model.camera;

import lombok.Data;

import java.util.Date;

/**
 * Description:  标记监控摄像头
 * Date: 2024/8/5
 * Author: lijunheng
 */
@Data
public class SurveillanceCamerasMark {

    /**
     * 摄像头编码
     */
    private String cameraCode;

    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 作用对象，比如某某租户
     */
    private String objectCode;

    private Boolean isDeleted;

    private Date gmtCreated;

    private Date gmtModified;

    /**
     * 摄像头类型，普通摄像头、重点摄像头
     */
    private String cameraType;
}