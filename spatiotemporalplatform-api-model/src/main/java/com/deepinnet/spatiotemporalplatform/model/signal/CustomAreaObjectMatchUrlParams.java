package com.deepinnet.spatiotemporalplatform.model.signal;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 评诊治-自定义区域-对象匹配接口URL参数类
 * 
 * <AUTHOR>
 * @version 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CustomAreaObjectMatchUrlParams extends UrlParams {

    /**
     * 对象类型（必填）
     * INTER：路口
     * RID：路段
     * POI：POI
     * INDEX：指数对象
     */
    private String objType;

    /**
     * 自定义区域（可选）
     * 经纬度空格间隔，多个逗号间隔
     * 如：116.489678 39.979974,116.481696 39.98512,...
     * 面积需小于100平方公里
     */
    private String xys;

    /**
     * poi类型（可选）
     * poi抓取必填
     * ALL：全部
     * GAS_STATION：加油站
     * CHARGING_STATION：充电站
     * MARKET：商场
     * STADIUM：体育馆
     * HOSPITAL：医院
     * INDUSTRIAL_PARK：产业园
     * SCENE：景区
     * COMPANY：公司
     * HOME：住宅
     * SCHOOL：学校
     * HUB：交通枢纽
     * RAILWAY_STATION：火车站
     * BUS_STATION：公交站
     * SUBWAY_STATION：地铁站
     * PARKING_LOT：停车场
     * 多个逗号间隔
     */
    private String poiTypes;

    /**
     * 指数类型（可选）
     * 指数抓取必填
     * CITY：城市
     * ROAD：道路，起点或终点在区域内的道路
     * ADMIN_AREA：行政区
     * TOLL：收费站
     * AIRPORT：机场
     * RAILWAY_STATION：火车站
     * STREET：街道
     * HOSPITAL：医院
     * UNIVERSITY：大学
     * HIGH_SCHOOL：中学
     * MARKET：超市
     * SCENIC：景区
     * FUNCTIONAL_ZONE：功能区
     * PRIMARY_SCHOOL：小学
     * 多个逗号间隔
     */
    private String linksTypes;

    /**
     * 行政区（可选）
     * 行政区编码
     */
    private String district;

    /**
     * 数据日期
     */
    private String ds;
} 