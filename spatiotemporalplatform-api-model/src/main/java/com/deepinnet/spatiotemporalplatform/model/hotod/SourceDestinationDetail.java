package com.deepinnet.spatiotemporalplatform.model.hotod;

import lombok.Data;

import java.util.List;

/**

 *
 * <AUTHOR>
 * @since 2024-11-18 星期一
 **/
@Data
public class SourceDestinationDetail {

    private Integer currentPage;

    private Integer totalPage;

    private Integer recordNum;

    private List<Item> list;

    @Data
    public static class Item {
        /**
         * 来源地/⽬的地id
         */
        private String sourceId;

        /**
         * 来源地/⽬的地名称
         */
        private String sourceName;

        /**
         * 来源地/⽬的地中⼼点坐标
         */
        private String sourceCenter;

        /**
         * ⻋流量
         */
        private Double flow;
    }

}
