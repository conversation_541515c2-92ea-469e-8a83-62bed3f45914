package com.deepinnet.spatiotemporalplatform.model.signal;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.enums.SignTypeEnum;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.PostBody;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonResponse;
import lombok.Data;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;

/**
 * 路口自适应历史数据请求
 */
@Data
public class IntersectionAdaptiveHistoryRequest implements HttpApiRequest {

    private IntersectionAdaptiveHistoryUrlParams urlParams;

    /**
     * API地址
     */
    private String apiUrl = "http://et-api.amap.com/vrc/inter/hisIndex?clientKey=%s&timestamp=%s&adcode=%s&interId=%s&ds=%s&timeGrading=%s";


    @Override
    public HttpMethod httpMethod() {
        return HttpMethod.GET;
    }

    @Override
    public HttpHeaders httpHeaders() {
        return null;
    }

    @Override
    public PostBody postBody() {
        return null;
    }

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {};
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<HttpApiJsonResponse<IntersectionAdaptiveSignalResponse>>() {};
    }

    @Override
    public SignTypeEnum signType() {
        return SignTypeEnum.DIGEST;
    }

    @Override
    public boolean useTempApiKey() {
        return true;
    }

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public String apiUrl() {
        return apiUrl;
    }
}