package com.deepinnet.spatiotemporalplatform.model.reggov.flow.people;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 网格历史人流和活力指数数据
 *
 * <AUTHOR>
 * @version 2024-08-05
 */
@Data
@ApiModel(description = "网格历史人流和活力指数数据")
public class GridHistoryPeopleFlowData {
    /**
     * 网格中心点坐标
     */
    @ApiModelProperty(value = "网格中心点坐标")
    private String gridCenter;

    /**
     * 网格名称
     */
    @ApiModelProperty(value = "网格名称")
    private String gridName;

    /**
     * 网格数据列表
     */
    @ApiModelProperty(value = "网格数据列表")
    private List<FlowData> list;

    @Data
    @ApiModel(description = "流量数据")
    public static class FlowData {

        /**
         * 时间，格式根据时间粒度决定
         */
        @ApiModelProperty(value = "时间，格式根据时间粒度决定[⼩时格式:yyyyMMddHH; 天格式:yyyyMMdd]")
        private String time;

        /**
         * 人流量
         */
        @ApiModelProperty(value = "人流量")
        private int peopleFlow;

        /**
         * 网格人流密度（每平方米人流），小数
         */
        @ApiModelProperty(value = "网格人流密度（每平方米人流），小数")
        private String density;

        /**
         * 历史人流量均值
         */
        @ApiModelProperty(value = "历史人流量均值")
        private String historyAvgFlow;

        /**
         * 历史人流量最大值
         */
        @ApiModelProperty(value = "历史人流量最大值")
        private String historyMaxFlow;

        /**
         * 活力指数，小数
         */
        @ApiModelProperty(value = "活力指数，小数")
        private String activityIndex;

        /**
         * 饱和度指数，小数
         */
        @ApiModelProperty(value = "饱和度指数，小数")
        private String saturationIndex;
    }
}
