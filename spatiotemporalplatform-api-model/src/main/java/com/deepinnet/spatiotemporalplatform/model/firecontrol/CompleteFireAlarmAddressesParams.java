package com.deepinnet.spatiotemporalplatform.model.firecontrol;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * C<PERSON> zeng<PERSON>ui
 * Date 2025-01-22
 **/

@EqualsAndHashCode(callSuper = true)
@Data
public class CompleteFireAlarmAddressesParams extends UrlParams {

    /**
     * 地址坐标信息
     * lng,lat
     * */
    private String location;
    /**
     * 地址描述
     * */
    private String desc;
}
