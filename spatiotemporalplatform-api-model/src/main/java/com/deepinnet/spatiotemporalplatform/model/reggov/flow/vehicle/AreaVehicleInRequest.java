package com.deepinnet.spatiotemporalplatform.model.reggov.flow.vehicle;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.AreaVehicleInConstant;
import com.deepinnet.spatiotemporalplatform.common.constants.GlobalConstant;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonArrayResponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 2024-07-30
 */
@Data
public class AreaVehicleInRequest implements HttpApiRequest {

    private AreaVehicleInUrlParams urlParams;

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<HttpApiJsonArrayResponse<AreaVehicleInLink>>() {
        };
    }

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public String apiUrl() {
        return AreaVehicleInConstant.AREA_VEHICLE_IN_API_URL;
    }

}
