package com.deepinnet.spatiotemporalplatform.model.signal;

import lombok.Data;

import java.util.List;

/**
 * 路口信号配时数据对象
 * 
 * <AUTHOR>
 * @version 2025-06-09
 */
@Data
public class IntersectionSignalTiming {

    /**
     * 路口id
     */
    private String interId;

    /**
     * 路口下各个进口的信号配时
     */
    private List<RoadSignalTiming> ridSignLightTimes;

    /**
     * 进口信号配时数据
     */
    @Data
    public static class RoadSignalTiming {
        /**
         * 进口Id
         */
        private String frid;

        /**
         * 进口方向
         */
        private String dir;

        /**
         * 进口各个相位(左直右的信号配时)
         */
        private List<SignalLightTime> signLightTimeVOS;
    }

    /**
     * 信号灯配时数据
     */
    @Data
    public static class SignalLightTime {
        /**
         * 转向类型：0:直行,1:左转,2:右转
         */
        private Integer turnDirNo;

        /**
         * 红灯时间，单位s
         */
        private Double redTime;

        /**
         * 绿灯时间，单位s
         */
        private Double greenTime;

        /**
         * 周期时间，单位s
         */
        private Double cycleTime;
    }
} 