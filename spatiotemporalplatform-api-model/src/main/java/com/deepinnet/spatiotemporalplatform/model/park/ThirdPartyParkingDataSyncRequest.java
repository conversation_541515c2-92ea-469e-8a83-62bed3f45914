package com.deepinnet.spatiotemporalplatform.model.park;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.PostBody;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import org.springframework.http.HttpMethod;

/**
 * <AUTHOR>
 * @version 2025-05-10
 */
@Data
public class ThirdPartyParkingDataSyncRequest implements HttpApiRequest {

    private ThirdPartyParkingData postBody;

    public HttpMethod httpMethod() {
        return HttpMethod.POST;
    }

    public PostBody postBody() {
        return postBody;
    }

    @Override
    public TypeReference responseBodyType() {
        return null;
    }

    @Override
    public UrlParams urlParams() {
        return null;
    }

    @Override
    public String apiUrl() {
        return "";
    }
}
