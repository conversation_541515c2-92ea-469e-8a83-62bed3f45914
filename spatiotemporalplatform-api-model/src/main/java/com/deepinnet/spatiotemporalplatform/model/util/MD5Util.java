package com.deepinnet.spatiotemporalplatform.model.util;

import cn.hutool.crypto.digest.DigestUtil;
import com.deepinnet.digitaltwin.common.log.LogUtil;

import java.lang.reflect.Field;

/**
 * <AUTHOR> wong
 * @create 2024/11/6 10:57
 * @Description
 */
public class MD5Util {

    public static String getObjectMD5(Object obj) {
        if (obj == null) {
            return null;
        }

        try {
            Field[] fields = obj.getClass().getDeclaredFields();
            StringBuilder concatenatedValues = new StringBuilder();
            for (Field field : fields) {
                field.setAccessible(true); // 设置字段可访问
                Object value = field.get(obj); // 获取字段的值
                if (value != null) {
                    concatenatedValues.append(value.toString()); // 拼接值
                }
            }

            return DigestUtil.md5Hex(concatenatedValues.toString());
        } catch (Exception e) {
            LogUtil.error("计算md5失败，异常信息为：", e);
            return null;
        }
    }
}
