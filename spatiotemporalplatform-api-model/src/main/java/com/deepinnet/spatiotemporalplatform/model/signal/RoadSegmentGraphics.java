package com.deepinnet.spatiotemporalplatform.model.signal;

import lombok.Data;

/**
 * 评诊治-图形-路段数据对象
 * 
 * <AUTHOR>
 * @version 2025-06-09
 */
@Data
public class RoadSegmentGraphics {

    /**
     * 路段id
     */
    private String rid;

    /**
     * 路段名称
     */
    private String ridName;

    /**
     * rid长度
     * 单位：米
     */
    private Integer ridLen;

    /**
     * 坐标串
     * 经纬度逗号间隔，多个坐标分号间隔
     */
    private String lngLatSeq;

    /**
     * rid道路等级
     */
    private Integer roadClass;

    /**
     * 方位角
     */
    private Double angle;

    /**
     * 四方位编码
     * 1-南向北；2-西向东；3-北向南；4-东向西
     */
    private Integer dir4No;

    /**
     * 上游（起点）路口编码
     */
    private String startObjId;

    /**
     * 上游（起点）路口经度
     */
    private Double startLng;

    /**
     * 上游（起点）路口纬度
     */
    private Double startLat;

    /**
     * 下游（终点）路口编码
     */
    private String endObjId;

    /**
     * 下游（终点）路口经度
     */
    private Double endLng;

    /**
     * 下游（终点）路口纬度
     */
    private Double endLat;

    /**
     * 车道信息
     */
    private LaneInfoVO laneInfoVO;

    /**
     * 行政区编码
     */
    private String district;

    /**
     * 城市编码
     */
    private String adcode;

    /**
     * 时间版本
     * 月版本
     */
    private String ds;

    /**
     * 车道信息VO
     */
    @Data
    public static class LaneInfoVO {
        /**
         * 车道数量
         */
        private Integer laneNum;

        /**
         * 车道方向数组
         */
        private String[] laneDirs;
    }
} 