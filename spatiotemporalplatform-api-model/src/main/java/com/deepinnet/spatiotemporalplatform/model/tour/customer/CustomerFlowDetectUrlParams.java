package com.deepinnet.spatiotemporalplatform.model.tour.customer;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**

 *
 * <AUTHOR>
 * @since 2024-08-26 星期一
 * #488
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class CustomerFlowDetectUrlParams extends UrlParams {

    /**
     * 定位id
     */
    private String areaId;

    /**
     * 查询开始时间 如20200405
     */
    private Integer startDate;

    /**
     * 查询结束时间 如20200410
     */
    private Integer endDate;

    /**
     * total_flow或visit_flow
     * 类型
     *
     * @see com.deepinnet.spatiotemporalplatform.model.tour.enums.FlowTypeEnum
     */
    private String flowType;

    /**
     * hour|day|week|month
     *
     * @see com.deepinnet.spatiotemporalplatform.model.tour.enums.TimeTypeEnum
     */
    private String timeType;
}
