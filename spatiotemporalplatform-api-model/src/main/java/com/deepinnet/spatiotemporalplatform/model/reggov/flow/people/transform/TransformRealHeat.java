package com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.transform;

import com.deepinnet.spatiotemporalplatform.model.tour.heat.RealHeat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/11/12
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransformRealHeat {

    private Integer chargeCnt;

    private String indexData;

    private String md5;

}
