package com.deepinnet.spatiotemporalplatform.model.signal;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.enums.SignTypeEnum;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.PostBody;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonResponse;
import lombok.Data;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;

/**
 * 智慧交通-实时路口-自适应信控路口数据服务 GET请求
 */
@Data
public class IntersectionAdaptiveSignalRequest implements HttpApiRequest {
    /**
     * URL参数
     */
    private IntersectionAdaptiveSignalUrlParams urlParams;

    /**
     * 设置URL参数
     */
    public void setUrlParams(IntersectionAdaptiveSignalUrlParams urlParams) {
        this.urlParams = urlParams;
    }

    @Override
    public UrlParams urlParams() {
        return urlParams;
    }

    @Override
    public String apiUrl() {
        return "https://et-api.amap.com/vrc/inter/realIndex?clientKey=%s&timestamp=%s&adcode=%s&district=%s&interIds=%s&losType=%s&confidence=%s&idxState=%s&orderIndex=%s&orderType=%s&pageSize=%s&pageNum=%s";
    }

    @Override
    public HttpMethod httpMethod() {
        return HttpMethod.GET;
    }

    @Override
    public HttpHeaders httpHeaders() {
        return null;
    }

    @Override
    public PostBody postBody() {
        return null;
    }

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {};
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<HttpApiJsonResponse<IntersectionAdaptiveSignalResponse>>() {};
    }

    @Override
    public SignTypeEnum signType() {
        return SignTypeEnum.DIGEST;
    }

    @Override
    public boolean useTempApiKey() {
        return true;
    }
} 