package com.deepinnet.spatiotemporalplatform.model.area;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.OpenApiConstant;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;

/**
 * Description: 搜索POI-关键字搜索
 * Date: 2025/4/29
 * Author: lijunheng
 */
@Data
public class PoiKeywordRequest implements HttpApiRequest {

    private PoiKeywordQueryParams poiKeywordQueryParams;

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<PoiKeywordResponse>() {
        };
    }

    @Override
    public UrlParams urlParams() {
        return poiKeywordQueryParams;
    }

    @Override
    public String apiUrl() {
        return OpenApiConstant.POI_KEYWORD_QUERY_URL;
    }
}
