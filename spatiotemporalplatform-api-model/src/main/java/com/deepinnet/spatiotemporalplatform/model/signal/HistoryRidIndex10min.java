package com.deepinnet.spatiotemporalplatform.model.signal;

import lombok.Data;

/**
 * 查询历史10分钟级RID的指标数据对象
 * 
 * <AUTHOR>
 * @version 2025-06-09
 */
@Data
public class HistoryRidIndex10min {

    /**
     * 10min时间片
     * 0-143
     */
    private String timeSlice;

    /**
     * 日期类型
     * 0:工作日
     * 1:节假日
     */
    private Integer dayType;

    /**
     * 自由流平均速度
     */
    private Double freeFlowSpeed;

    /**
     * 平均速度
     */
    private Double realSeed;

    private Double signalRealSpeed;

    private Double signalFreeFlowSpeed;

    /**
     * 指数
     */
    private Double idx;

    /**
     * 信控指数
     */
    private Double signalIdx;

    /**
     * 城市编码
     * 直辖市或地级市编码
     */
    private String adcode;

    /**
     * 进口道id
     */
    private String rid;

    /**
     * 日期
     * 格式：yyyyMMdd
     */
    private String ds;
} 