package com.deepinnet.spatiotemporalplatform.model.reggov.flow.people;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 来源地请求参数
 *
 * <AUTHOR>
 * @version 2024-08-05
 */

@Data
@ApiModel(description = "来源地请求URL参数")
@EqualsAndHashCode(callSuper = true)
public class RegionPeopleSourceFlowParams extends RealTimePeopleFlowParams {

    /**
     * 城市code
     */
    @ApiModelProperty(value = "城市code", required = true)
    private String adcode;

    /**
     * 流⼊（来源地）或流出（⽬的地） 1:流⼊
     * 2:流出
     */
    @ApiModelProperty(value = "流⼊（来源地）或流出（⽬的地） 1:流⼊ 2:流出", required = true)
    private Integer fromOrTo;

    /**
     * obj类型 1:城市；2:区县；3:乡镇或街道；4:千⽶⽹格；5:百⽶⽹格
     */
    @ApiModelProperty(value = "obj类型 1:城市；2:区县；3:乡镇或街道；4:千⽶⽹格；5:百⽶⽹格", required = true)
    private Integer type;

    @ApiModelProperty(value = "页码")
    private Integer pageNo = 1;

    @ApiModelProperty(value = "每页条数")
    private Integer pageSize = 1000;

}

