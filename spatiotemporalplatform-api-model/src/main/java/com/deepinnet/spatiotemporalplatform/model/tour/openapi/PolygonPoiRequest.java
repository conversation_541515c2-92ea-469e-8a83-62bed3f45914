package com.deepinnet.spatiotemporalplatform.model.tour.openapi;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.OpenApiConstant;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;

@Data
public class PolygonPoiRequest implements HttpApiRequest {

    private PolygonPoiParam polygonPoiParam;

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {
        };
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<PoiResponse>() {
        };
    }

    @Override
    public UrlParams urlParams() {
        return this.polygonPoiParam;
    }

    @Override
    public String apiUrl() {
        return OpenApiConstant.POLYGON_POI_URL;
    }
}
