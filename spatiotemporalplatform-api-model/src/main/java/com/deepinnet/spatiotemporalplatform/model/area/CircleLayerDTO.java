package com.deepinnet.spatiotemporalplatform.model.area;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 圈层
 */
@Data
public class CircleLayerDTO implements Serializable {
    /**
     * 自增ID
     */
    private Integer id;


    /**
     * 所属区域code
     */
    @ApiModelProperty(value = "所属区域code")
    private String areaCode;

    /**
     * 圈层ID
     */
    @ApiModelProperty(value = "圈层ID")
    private String layerId;

    /**
     * 圈层名称
     */
    @ApiModelProperty(value = "圈层名称")
    private String layerName;

    /**
     * 圈层类型
     */
    @ApiModelProperty(value = "圈层类型,core-核心，evacuation-疏导区")
    private String layerType = "core";

    /**
     * 边界经纬度
     */
    @ApiModelProperty(value = "边界经纬度")
    private String boundaryCoordinates;

    private static final long serialVersionUID = 1L;

}