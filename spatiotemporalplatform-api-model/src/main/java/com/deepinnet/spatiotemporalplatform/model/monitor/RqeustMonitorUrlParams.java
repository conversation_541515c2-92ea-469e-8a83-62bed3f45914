package com.deepinnet.spatiotemporalplatform.model.monitor;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**

 *
 * <AUTHOR>
 * @since 2025-01-02 星期四
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class RqeustMonitorUrlParams extends MonitorUrlParams {

    /**
     * 监测类型 默认心跳
     */
    private String monitorType = MonitorType.REQUEST_STATISTICS.name();

    private String api;

    private Object params;

    private Object body;

    private Boolean isSuccess;

    private String errorMsg;

    public Object getParams() {
        if (ObjUtil.isEmpty(params)) {
            return null;
        }
        return BeanUtil.beanToMap(params);
    }

    public Object getBody() {
        if (ObjUtil.isEmpty(body)) {
            return null;
        }
        return BeanUtil.beanToMap(body);
    }
}
