package com.deepinnet.spatiotemporalplatform.model.reggov.flow.people;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 网格实时人流和活力指数数据
 *
 * <AUTHOR>
 * @version 2024-08-05
 */
@Data
@ApiModel(description = "网格实时人流和活力指数数据")
public class GridRealTimePeopleFlowData {
    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码")
    private int page;

    /**
     * 数据总数
     */
    @ApiModelProperty(value = "数据总数")
    private int total;

    /**
     * 每⻚条数
     */
    @ApiModelProperty(value = "每⻚条数")
    private int size;

    /**
     * 数据列表
     */
    @ApiModelProperty(value = "数据列表")
    private List<GridRealTimePeopleFlow> list;
}
