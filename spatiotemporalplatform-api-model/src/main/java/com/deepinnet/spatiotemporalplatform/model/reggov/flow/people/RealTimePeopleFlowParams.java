package com.deepinnet.spatiotemporalplatform.model.reggov.flow.people;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 实时人流请求参数
 *
 * <AUTHOR>
 * @version 2024-07-30
 */
@Data
@ApiModel(value = "区域实时人数查询参数")
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RealTimePeopleFlowParams extends UrlParams {

    /**
     * 自定义区域ID
     */
    @ApiModelProperty(value = "自定义区域ID")
    private String customAreaId;
}
