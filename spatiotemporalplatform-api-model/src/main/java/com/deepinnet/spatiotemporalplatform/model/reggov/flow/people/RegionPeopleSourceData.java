package com.deepinnet.spatiotemporalplatform.model.reggov.flow.people;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *  人流来源数据
 * </p>
 *
 * <AUTHOR>
 * @since 2024/8/21
 */

@Data
@ApiModel(description = "人流来源地数据")
public class RegionPeopleSourceData implements Serializable {

    /**
     * id（城市、区县、乡镇的编码或百⽶/千⽶⽹格的id）
     */
    @ApiModelProperty(value = "id（城市、区县、乡镇的编码或百⽶/千⽶⽹格的id）")
    private String objId;

    /**
     * obj类型 1:城市；2:区县；3:乡镇或街道；4:千⽶⽹
     * 格；5:百⽶⽹格
     */
    @ApiModelProperty(value = "obj类型 1:城市；2:区县；3:乡镇或街道；4:千⽶⽹格；5:百⽶⽹格")
    private Integer objType;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String objName;

    /**
     * 历史人流量均值
     */
    @ApiModelProperty(value = "中⼼点 POINT(lng,lat)格式")
    private String objGeo;

    /**
     * 流入意愿(0-1)
     */
    @ApiModelProperty(value = "流入意愿(0-1)")
    private String wish;

    /**
     * 流⼊意愿环⽐(0-1)
     */
    @ApiModelProperty(value = "流⼊意愿环⽐")
    private String withRatio;

    /**
     * 流量等级（1:0~1w; 2:1w~3w; 3:3w+）
     */
    @ApiModelProperty(value = "流量等级（1:0~1w; 2:1w~3w; 3:3w+）")
    private Integer routeLevel;

    /**
     * 时间戳 yyyyMMddHHmm
     */
    @ApiModelProperty(value = "时间戳 yyyyMMddHHmm")
    private String dt;
}
