package com.deepinnet.spatiotemporalplatform.model.tour.gd;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 游客画像
 *
 * @TableName tourist_portrait
 */
@Data
public class GdTouristPortrait implements Serializable {

    private Long id;

    /**
     * 城市code
     */
    private String adCode;

    /**
     * 区域code
     */
    private String areaCode;

    /**
     * 业务类型
     */
    private String type;

    /**
     * 编码：如职业编码/省市区编码等
     */
    private String code;

    /**
     * 游客数量
     */
    private Integer count;

    private Date gmtCreated;


    private Date gmtModified;

    /**
     * 名称：如职业名称/省市区名称等
     */
    private String name;

    private static final long serialVersionUID = 1L;
}