package com.deepinnet.spatiotemporalplatform.model.signal;

import lombok.Data;

import java.util.List;

/**
 * 智慧交通-实时路口-路口静态信息查询服务 响应体
 */
@Data
public class IntersectionStaticsResponse {
    /** 当前页数 */
    private Integer currentPage;
    /** 总页数 */
    private Integer totalPage;
    /** 数据总量 */
    private Integer recordNum;
    /** 路口信息列表 */
    private List<MainInterInfo> list;

    @Data
    public static class MainInterInfo {
        /** 城市ADCODE */
        private String adcode;
        /** 区县ADCODE */
        private String district;
        /** 路口ID */
        private String interId;
        /** 路口名称 */
        private String interName;
        /** 路口主体经度 */
        private Double lng;
        /** 路口主体纬度 */
        private Double lat;
        /** 置信度 0无,1低,2中,3高 */
        private Integer confidence;
        /** 进口道详情列表 */
        private List<FridInfo> fridInfo;

        @Data
        public static class FridInfo {
            /** 进口道ID */
            private String frid;
            /** 进口道方向 */
            private String direction;
            /** 进口道名称 */
            private String fridName;
            /** 进口道坐标，可能因为月度更新问题有缺失 */
            private String fridCoords;
            /** 进口道长度 */
            private Integer length;
            /** 分方向车道数量 */
            private Integer laneNum;
            /** 分方向车道信息（如A左转掉头，B左转，C直行等） */
            private String laneDirs;
        }
    }
} 