package com.deepinnet.spatiotemporalplatform.model.hotod;

import lombok.Data;

import java.util.List;

/**

 *
 * <AUTHOR>
 * @since 2024-11-18 星期一
 **/
@Data
public class AreaArriveCarPredict {

    /**
     * ⽬的地区域ID：⾃定义区域id/poi id
     */
    private String id;

    /**
     * ⽬的地区域名称
     */
    private String name;

    /**
     * 预测时间分布
     */
    private List<Item> remainTimeItemList;

    /**
     * 预测距离分布
     */
    private List<Item> remainDistanceItemList;


    @Data
    public static class Item {
        /**
         * 时间/距离范围最⼩值
         */
        private Double min;
        /**
         * 时间/距离范围最⼤值
         */
        private Double max;
        /**
         * 车流
         */
        private Double flow;
        /**
         * 时间/距离平均值，单位分钟/⽶
         */
        private Double avg;
    }
}
