package com.deepinnet.spatiotemporalplatform.model.reggov.create;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.CustomAreaCreateConstant;
import com.deepinnet.spatiotemporalplatform.common.constants.GlobalConstant;
import com.deepinnet.spatiotemporalplatform.common.request.*;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonResponse;
import lombok.Data;
import org.springframework.http.HttpMethod;

/**
 * 自定义区域创建请求
 *
 * <AUTHOR>
 * @version 2024-07-31
 */
@Data
public class CustomAreaCreateRequest implements HttpApiRequest {

    private CustomAreaCreatePostBody postBody;

    private CustomAreaCreateUrlParams urlParams;

    public HttpMethod httpMethod() {
        return HttpMethod.POST;
    }

    public PostBody postBody() {
        return postBody;
    }

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {
        };
    }

    public TypeReference deserializeType() {
        return new TypeReference<HttpApiJsonResponse<String>>() {
        };
    }

    @Override
    public UrlParams urlParams() {
        return urlParams;
    }

    @Override
    public String apiUrl() {
        return CustomAreaCreateConstant.CUSTOM_AREA_CREATE_API_URL;
    }

    @Override
    public boolean useTempApiKey(){
        return true;
    }

}
