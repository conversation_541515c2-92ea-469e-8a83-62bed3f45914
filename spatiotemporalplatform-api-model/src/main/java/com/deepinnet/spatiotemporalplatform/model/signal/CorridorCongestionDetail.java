package com.deepinnet.spatiotemporalplatform.model.signal;

import lombok.Data;

/**
 * 评诊治-通勤-走廊拥堵详情数据对象
 * 
 * <AUTHOR>
 * @version 2025-06-09
 */
@Data
public class CorridorCongestionDetail {

    /**
     * 城市编码
     */
    private String adcode;

    /**
     * 时段
     * 1-早高峰、2-晚高峰
     */
    private String timespan;

    /**
     * 走廊id
     */
    private String corridorId;

    /**
     * 走廊名称
     */
    private String corridorName;

    /**
     * 平均速度
     * 单位：公里/小时
     */
    private Double realSpeed;

    /**
     * 自由流速度
     * 单位：公里/小时
     */
    private Double freeSpeed;

    /**
     * 拥堵延时指数
     */
    private Double idx;

    /**
     * 日期
     * yyyyMM
     */
    private String ds;
} 