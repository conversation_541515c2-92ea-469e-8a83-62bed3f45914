package com.deepinnet.spatiotemporalplatform.model.hotod;

import lombok.Data;

import java.util.List;

/**

 *
 * <AUTHOR>
 * @since 2024-11-18 星期一
 **/
@Data
public class DriverDestinationRanking {

    private Integer currentPage;

    private Integer totalPage;

    private Integer recordNum;

    private List<Item> list;

    @Data
    public static class Item {
        /**
         * 城市编码
         */
        private String adcode;

        /**
         * 对象id
         */
        private String objId;

        /**
         * 对象类型
         */
        private Integer objType;

        /**
         * 对象名称
         */
        private String objName;

        /**
         * 坐标
         */
        private String geo;

        /**
         * ⻋流，既当前⽬的地为该区域的⻋流总数
         */
        private Double flow;

        /**
         * 历史平均⻋流
         */
        private Double avgFlow;
    }

}
