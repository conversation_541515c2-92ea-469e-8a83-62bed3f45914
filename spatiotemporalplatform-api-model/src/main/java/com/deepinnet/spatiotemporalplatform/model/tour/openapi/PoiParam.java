package com.deepinnet.spatiotemporalplatform.model.tour.openapi;

import com.deepinnet.spatiotemporalplatform.common.constants.OpenApiConstant;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
public class PoiParam extends UrlParams {

    /**
     * 用户key
     */
    private String key = OpenApiConstant.USER_KEY;
    /**
     * 中心点坐标 116.473168,39.993015
     */
    private String location;
    /**
     * 半径 500, 默认500米
     * */
    private Integer radius = 500;

    /**
     * 指定地点类型,地点文本搜索接口支持按照设定的POI类型限定地点搜索结果；
     * 地点类型与 poi typecode 是同类内容，可以传入多个poi typecode，
     * 相互之间用“|”分隔，内容可以参考 POI 分类码表；
     * 地点（POI）列表的排序会按照高德搜索能力进行综合权重排序；
     */
    private String types;

    /**
     * 页码，默认1, 最大100
     */
    private Integer page_num = 1;

    /**
     * 每页条数，默认25, 最大25
     */
    private Integer page_size = 25;
}
