package com.deepinnet.spatiotemporalplatform.model.common;

import com.deepinnet.spatiotemporalplatform.model.util.WktUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.locationtech.jts.geom.Point;

/**
 * Creator zengjuerui
 * Date 2024-07-29
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GeoPoint {
    /**
     * 经度
     * */
    private String lng;
    /**
     * 纬度
     * */
    private String lat;


    public GeoPoint(Point point) {
        this.lng = String.valueOf(point.getX());
        this.lat = String.valueOf(point.getY());
    }

     public GeoPoint(String wkt) {
        Point point = WktUtil.toPoint(wkt);
        if(point == null) {
            return;
        }

         this.lng = String.valueOf(point.getX());
         this.lat = String.valueOf(point.getY());
     }


     public String toWkt() {
         return Point.TYPENAME_POINT +
                 "(" +
                 lng +
                 " " +
                 lat +
                 ")";
     }
}
