package com.deepinnet.spatiotemporalplatform.model.reggov.flow.people;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 人流来源地数据
 *
 * <AUTHOR>
 * @version 2024-08-05
 */
@Data
@ApiModel(description = "人流来源地数据")
public class RegionPeopleSourceFlowData implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "页码")
    private Integer page;

    @ApiModelProperty(value = "每页条数")
    private Integer size;

    @ApiModelProperty(value = "总条数")
    private Integer total;

    @ApiModelProperty(value = "人流来源地数据")
    private List<RegionPeopleSourceData> list;
}
