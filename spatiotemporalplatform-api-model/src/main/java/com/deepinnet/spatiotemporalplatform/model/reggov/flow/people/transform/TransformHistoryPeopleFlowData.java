package com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.transform;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 历史人流
 *
 * <AUTHOR>
 * @version 2024-07-30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransformHistoryPeopleFlowData {

    /**
     * 自定义区域ID
     */
    private String customAreaId;

    /**
     * 时间，格式根据时间粒度决定
     * <p>
     *     小时：yyyy-MM-dd HH
     * </p>
     * <p>
     *     天：yyyy-MM-dd
     * </p>
     * <p>
     *     周：yyyy-MM-dd 取周一为一周的第一天
     * </p>
     * <p>
     *     月: yyyy-MM
     * </p>
     * 总和, time为null
     */
    private String time;

    /**
     * 人流量
     */
    private Integer peopleFlow;

    /**
     * 人流活力指数
     */
    private String activityIndex;

    /**
     * 人流饱和指数
     */
    private String saturationIndex;

    /**
     * MD5
     */
    private String md5;
}
