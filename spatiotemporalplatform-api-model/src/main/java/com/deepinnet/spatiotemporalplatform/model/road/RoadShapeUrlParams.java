package com.deepinnet.spatiotemporalplatform.model.road;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**

 *
 * <AUTHOR>
 * @since 2024-11-13 星期三
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class RoadShapeUrlParams extends UrlParams {

    /**
     * 道路类型 取值2
     */
    private Integer type;

    /**
     * 道路ID 多个用“,”分割
     */
    private String ids;

    /**
     * 是否抽希，取值： true-是 false-否
     */
    private Boolean isRarefy;

    /**
     * 抽希参数最⼩距离（单位：⽶）
     */
    private Integer rarefyMeter;

    /**
     * 抽希最⼩点个数，即点数少于此数值不抽希
     */
    private String rarefyPointNum;

    /**
     * 抽稀是否保证有效拓扑，取值： true-是 false-否。 为false时速度更快，但有个别区域抽稀 结果可能为⽆效拓扑
     */
    private Boolean validTopology;

}
