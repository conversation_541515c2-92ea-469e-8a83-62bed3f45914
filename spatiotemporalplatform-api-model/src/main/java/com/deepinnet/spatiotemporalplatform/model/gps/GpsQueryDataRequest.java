package com.deepinnet.spatiotemporalplatform.model.gps;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.request.*;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonArrayResponse;
import lombok.Data;

/**
 * <AUTHOR> wong
 * @create 2024/11/23 10:35
 * @Description
 */
@Data
public class GpsQueryDataRequest implements HttpApiRequest {

    private GpsParam urlParams;

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<HttpApiJsonArrayResponse<DeviceGpsModel>>() {
        };
    }

    @Override
    public UrlParams urlParams() {
        return urlParams;
    }

    @Override
    public String apiUrl() {
        return "**************";
    }
}
