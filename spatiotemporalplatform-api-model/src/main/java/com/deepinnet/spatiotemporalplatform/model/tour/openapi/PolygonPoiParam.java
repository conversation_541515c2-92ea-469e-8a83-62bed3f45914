package com.deepinnet.spatiotemporalplatform.model.tour.openapi;

import com.deepinnet.spatiotemporalplatform.common.constants.OpenApiConstant;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
public class PolygonPoiParam extends UrlParams {

    /**
     * 用户key
     */
    private String key = OpenApiConstant.USER_KEY;
    /**
     * 多边形区域,多个坐标对集合，坐标对用"|"分割。多边形为矩形时，可传入左上右下两顶点坐标对；其他情况下首尾坐标对需相同。
     */
    private String polygon;

    /**
     * 指定地点类型,地点文本搜索接口支持按照设定的POI类型限定地点搜索结果；
     * 地点类型与 poi typecode 是同类内容，可以传入多个poi typecode，
     * 相互之间用“|”分隔，内容可以参考 POI 分类码表；
     * 地点（POI）列表的排序会按照高德搜索能力进行综合权重排序；
     */
    private String types;

    /**
     * 页码，默认1, 最大100
     */
    private Integer page_num = 1;

    /**
     * 每页条数，默认25, 最大25
     */
    private Integer page_size = 25;
}
