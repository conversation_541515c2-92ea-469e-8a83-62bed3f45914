package com.deepinnet.spatiotemporalplatform.model.road;

import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.RoadConstant;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonArrayResponse;
import lombok.Data;

/**

 *
 * <AUTHOR>
 * @since 2024-12-20 星期五
 **/
@Data
public class RoadHistoryRankingRequest implements HttpApiRequest {

    private RoadHistoryRankingUrlParams urlParams;

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<String>() {};
    }

    @Override
    public TypeReference deserializeType() {
        return new TypeReference<HttpApiJsonArrayResponse<RoadHistoryRanking>>() {
        };
    }

    @Override
    public UrlParams urlParams() {
        return urlParams;
    }

    @Override
    public boolean useTempApiKey() {
        return false;
    }

    @Override
    public String apiUrl() {
        return RoadConstant.ROAD_HISTORY_RANKING_API_URL;
    }
}
