package com.deepinnet.spatiotemporalplatform.model.reggov.flow.people;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.TypeReference;
import com.deepinnet.spatiotemporalplatform.common.constants.DistrictIndexConstant;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import com.deepinnet.spatiotemporalplatform.common.response.HttpApiJsonResponse;
import com.deepinnet.spatiotemporalplatform.model.reggov.flow.people.transform.TransformRealTimePeopleFlowData;
import com.deepinnet.spatiotemporalplatform.model.util.MD5Util;
import lombok.Data;

/**
 * 实时人流数据请求
 *
 * <AUTHOR>
 * @version 2024-07-29
 */
@Data
public class RealTimePeopleFlowRequest implements HttpApiRequest {
    /**
     * URL参数
     */
    private RealTimePeopleFlowParams urlParams;

    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public TypeReference responseBodyType() {
        return new TypeReference<HttpApiJsonResponse<RealTimePeopleFlowData>>() {
        };
    }

    @Override
    public String apiUrl() {
        return DistrictIndexConstant.REAL_TIME_DISTRICT_PEOPLE_FLOW_API_URL;
    }

    @Override
    public Object transformData(Object responseData) {

        if (ObjectUtil.isNull(responseData)) {
            return null;
        }

        RealTimePeopleFlowData realTimePeopleFlowData = (RealTimePeopleFlowData) responseData;

        return TransformRealTimePeopleFlowData.builder()
                .customAreaId(realTimePeopleFlowData.getCustomAreaId())
                .time(realTimePeopleFlowData.getTime())
                .activityIndex(realTimePeopleFlowData.getActivityIndex())
                .flow(realTimePeopleFlowData.getFlow())
                .avgFlow(realTimePeopleFlowData.getAvgFlow())
                .maxFlow(realTimePeopleFlowData.getMaxFlow())
                .preFlow(realTimePeopleFlowData.getPreFlow())
                .todayCumulativeFlow(realTimePeopleFlowData.getTodayCumulativeFlow())
                .md5(MD5Util.getObjectMD5(realTimePeopleFlowData)).build();
    }

    @Override
    public String saveDataTableName() {
        return "gd_real_time_people_flow";
    }
}
