package com.deepinnet.spatiotemporalplatform.model.gps;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * GpsModel
 * Author: chenkaiyang
 * Date: 2024/11/22
 */
@Data
public class GpsData {
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "设备编号")
    private String deviceId;

    @ApiModelProperty(value = "经度-bd09")
    private String longitude;

    @ApiModelProperty(value = "纬度-bd09")
    private String latitude;

    @ApiModelProperty(value = "经度-wgs84")
    private String lng;

    @ApiModelProperty(value = "纬度-wgs84")
    private String lat;

    @ApiModelProperty(value = "采集时间，格式：yyyy-MM-dd HH:mm:ss")
    private String gpsArriveTime;

    @ApiModelProperty(value = "最后更新时间，格式：yyyy-MM-dd HH:mm:ss")
    private String lastUpdateTime;

    @ApiModelProperty(value = "在线状态，0-离线，1-在线")
    private Integer online;

    @ApiModelProperty(value = "在线时间，格式：yyyy-MM-dd HH:mm:ss")
    private String onlineTime;

    @ApiModelProperty(value = "离线时间，格式：yyyy-MM-dd HH:mm:ss")
    private String offlineTime;

    @ApiModelProperty(value = "设备名称")
    private String enterpriseName;

}
