package com.deepinnet.spatiotemporalplatform.model.signal;

import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 评诊治-图形-路口静态信息接口URL参数类
 * 
 * <AUTHOR>
 * @version 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IntersectionUrlParams extends UrlParams {

    /**
     * 路口id列表（必填）
     * 多个逗号间隔，上限20个
     */
    private String interIds;

    /**
     * 时间版本（必填）
     * 月版本，格式：yyyyMM
     */
    private String ds;
} 