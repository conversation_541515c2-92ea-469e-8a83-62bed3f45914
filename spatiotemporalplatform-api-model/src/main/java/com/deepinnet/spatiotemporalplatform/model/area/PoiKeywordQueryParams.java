package com.deepinnet.spatiotemporalplatform.model.area;

import com.deepinnet.spatiotemporalplatform.common.constants.OpenApiConstant;
import com.deepinnet.spatiotemporalplatform.common.request.UrlParams;
import lombok.Data;

import java.io.Serializable;

/**
 * Description:
 * Date: 2025/8/12
 * Author: lijunheng
 */
@Data
public class PoiKeywordQueryParams extends UrlParams implements Serializable {

    /**
     * 回调函数  callback值是用户定义的函数名称，此参数只在output=JSON时有效
     */
    private String callback;
    /**
     * 是否按照层级展示子POI数据  可选值：children=1  当为0的时候，子POI都会显示。  当为1的时候，子POI会归类到父POI之中。
     * 仅在extensions=all的时候生效
     */
    private String children;
    /**
     * 查询城市  可选值：城市中文、中文全拼、citycode、adcode  如：北京/beijing/010/110000
     * 填入此参数后，会尽量优先返回此城市数据，但是不一定仅局限此城市结果，若仅需要某个城市数据请调用citylimit参数。  如：在深圳市搜天安门，返回北京天安门结果。
     */
    private String city;
    /**
     * 仅返回指定城市数据  可选值：true/false
     */
    private String citylimit;
    /**
     * 返回结果控制  此项默认返回基本地址信息；取值为all返回地址信息、附近POI、道路以及道路交叉口信息。
     */
    private String extensions;
    /**
     * 用户在高德地图官网[申请Web服务API类型Key](https://lbs.amap.com/dev/)
     */
    private String key = OpenApiConstant.USER_KEY;
    /**
     * 查询关键字  规则： 多个关键字用“|”分割  若不指定city，并且搜索的为泛词（例如“美食”）的情况下，返回的内容为城市列表以及此城市内有多少结果符合要求。
     */
    private String keywords;
    /**
     * 每页记录数据  强烈建议不超过25，若超过25可能造成访问报错
     */
    private String offset;
    /**
     * 返回数据格式类型  可选值：JSON，XML
     */
    private String output;
    /**
     * 当前页数  最大翻页数100
     */
    private String page;
    private String parameters;
    /**
     * [数字签名获取和使用方法](https://lbs.amap.com/faq/account/key/72)
     */
    private String sig;
    /**
     * 查询POI类型  可选值：分类代码 或 汉字（若用汉字，请严格按照附件之中的汉字填写）
     * 分类代码由六位数字组成，一共分为三个部分，前两个数字代表大类；中间两个数字代表中类；最后两个数字代表小类。  若指定了某个大类，则所属的中类、小类都会被显示。
     * 例如：010000为汽车服务（大类）  ​       010100为加油站（中类）  ​        010101为中国石化（小类）  ​
     * 010900为汽车租赁（中类）  ​        010901为汽车租赁还车（小类）
     * 当指定010000，则010100等中类、010101等小类都会被包含，当指定010900，则010901等小类都会被包含。  下载[POI分类编码和城市编码表
     * ](https://lbs.amap.com/api/webservice/download)    若不指定city，返回的内容为城市列表以及此城市内有多少结果符合要求。
     * **当您的keywords和types都是空时，默认指定types为120000（商务住宅）&150000（交通设施服务）**
     */
    private String types;

}
