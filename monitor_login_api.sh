#!/bin/bash

# 登录接口监控脚本
# 用途：每10秒调用一次登录接口，记录异常情况和堆栈信息

# 配置参数
API_URL="http://192.168.3.200:8056/api/user/login"
INTERVAL=10  # 间隔时间（秒）
LOG_FILE="login_api_monitor_$(date +%Y%m%d_%H%M%S).log"
ERROR_LOG="login_api_errors_$(date +%Y%m%d_%H%M%S).log"
SUCCESS_COUNT=0
ERROR_COUNT=0

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${BLUE}[INFO]${NC} $timestamp - $1" | tee -a "$LOG_FILE"
}

log_success() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${GREEN}[SUCCESS]${NC} $timestamp - $1" | tee -a "$LOG_FILE"
}

log_error() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${RED}[ERROR]${NC} $timestamp - $1" | tee -a "$LOG_FILE" | tee -a "$ERROR_LOG"
}

log_warning() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${YELLOW}[WARNING]${NC} $timestamp - $1" | tee -a "$LOG_FILE"
}

# 发送登录请求
send_login_request() {
    local response_file=$(mktemp)
    local http_code
    local response_body
    local curl_exit_code
    
    # 执行curl请求
    http_code=$(curl -w "%{http_code}" -s -o "$response_file" \
        --max-time 30 \
        --connect-timeout 10 \
        -H 'Accept: application/json, text/plain, */*' \
        -H 'Accept-Language: zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7' \
        -H 'Connection: keep-alive' \
        -H 'Content-Type: application/json' \
        -H 'Origin: http://192.168.3.200:8056' \
        -H 'Referer: http://192.168.3.200:8056/login' \
        -H 'Tenant-Id: sz_unifly' \
        -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36' \
        --data-raw '{"account":"***********","password":"6b86b273ff34fce19d6b804eff5a3f5747ada4eaa22f1d49c01e52ddb7875b4b","scene":"matching_platform"}' \
        --insecure \
        "$API_URL" 2>&1)
    
    curl_exit_code=$?
    response_body=$(cat "$response_file")
    rm -f "$response_file"
    
    # 分析结果
    if [ $curl_exit_code -ne 0 ]; then
        log_error "CURL请求失败，退出码: $curl_exit_code"
        log_error "错误详情: $http_code"
        ((ERROR_COUNT++))
        return 1
    fi
    
    # 检查HTTP状态码
    if [ "$http_code" != "200" ]; then
        log_error "HTTP状态码异常: $http_code"
        log_error "响应内容: $response_body"
        ((ERROR_COUNT++))
        return 1
    fi
    
    # 检查响应内容是否包含错误信息
    if echo "$response_body" | grep -q '"success":false\|"code":[^0]\|"error"\|"exception"'; then
        log_error "业务逻辑错误"
        log_error "响应内容: $response_body"
        
        # 尝试提取错误信息
        if echo "$response_body" | grep -q '"message"'; then
            local error_msg=$(echo "$response_body" | grep -o '"message":"[^"]*"' | cut -d'"' -f4)
            log_error "错误消息: $error_msg"
        fi
        
        if echo "$response_body" | grep -q '"code"'; then
            local error_code=$(echo "$response_body" | grep -o '"code":[^,}]*' | cut -d':' -f2)
            log_error "错误代码: $error_code"
        fi
        
        ((ERROR_COUNT++))
        return 1
    fi
    
    # 成功情况
    log_success "请求成功 (HTTP: $http_code)"
    ((SUCCESS_COUNT++))
    return 0
}

# 显示统计信息
show_stats() {
    local total=$((SUCCESS_COUNT + ERROR_COUNT))
    local success_rate=0
    
    if [ $total -gt 0 ]; then
        success_rate=$(echo "scale=2; $SUCCESS_COUNT * 100 / $total" | bc -l 2>/dev/null || echo "0")
    fi
    
    echo -e "\n${BLUE}=== 统计信息 ===${NC}"
    echo -e "总请求数: $total"
    echo -e "成功次数: ${GREEN}$SUCCESS_COUNT${NC}"
    echo -e "失败次数: ${RED}$ERROR_COUNT${NC}"
    echo -e "成功率: ${YELLOW}${success_rate}%${NC}"
    echo -e "日志文件: $LOG_FILE"
    echo -e "错误日志: $ERROR_LOG"
}

# 信号处理函数
cleanup() {
    echo -e "\n${YELLOW}收到停止信号，正在清理...${NC}"
    show_stats
    echo -e "\n${BLUE}监控已停止${NC}"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 主函数
main() {
    echo -e "${BLUE}=== 登录接口监控脚本 ===${NC}"
    echo -e "目标URL: $API_URL"
    echo -e "监控间隔: ${INTERVAL}秒"
    echo -e "日志文件: $LOG_FILE"
    echo -e "错误日志: $ERROR_LOG"
    echo -e "按 Ctrl+C 停止监控\n"
    
    log_info "开始监控登录接口"
    
    local request_count=0
    
    while true; do
        ((request_count++))
        
        echo -e "\n${BLUE}--- 第 $request_count 次请求 ---${NC}"
        
        # 发送请求
        send_login_request
        
        # 显示当前统计
        echo -e "成功: ${GREEN}$SUCCESS_COUNT${NC} | 失败: ${RED}$ERROR_COUNT${NC}"
        
        # 如果连续失败超过5次，发出警告
        if [ $ERROR_COUNT -gt 0 ] && [ $((ERROR_COUNT % 5)) -eq 0 ] && [ $SUCCESS_COUNT -eq 0 ]; then
            log_warning "连续失败 $ERROR_COUNT 次，请检查服务状态"
        fi
        
        # 等待下次请求
        echo -e "等待 ${INTERVAL} 秒..."
        sleep $INTERVAL
    done
}

# 检查依赖
check_dependencies() {
    if ! command -v curl &> /dev/null; then
        echo -e "${RED}错误: curl 命令未找到，请先安装 curl${NC}"
        exit 1
    fi
    
    if ! command -v bc &> /dev/null; then
        echo -e "${YELLOW}警告: bc 命令未找到，统计功能可能受限${NC}"
    fi
}

# 脚本入口
check_dependencies
main
